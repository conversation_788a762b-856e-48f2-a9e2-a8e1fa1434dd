-- INSERT PHỤC VỤ CHO PHẦN EXPORT DATA, DASHBOARD

-- vnpt_dev.permission
INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 1, 'Tài khoản', 'BAO_CAO_TAI_KHOAN', (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE') , 10000200 + 1),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 2, 'Thuê bao', 'BAO_CAO_THUE_BAO', (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE') , 10000200 + 2),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 3, '<PERSON><PERSON><PERSON> phẩm <PERSON> vụ', 'BAO_CAO_SAN_PHAM_DICH_VU', (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE') , 10000200 + 3),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 4, 'Gói cước', 'BAO_CAO_GOI_CUOC', (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE') , 10000200 + 4),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 5, 'Chương trình KM', 'BAO_CAO_CHUONG_TRINH_KM', (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE') , 10000200 + 5),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 7, 'Hóa đơn', 'BAO_CAO_HOA_DON', (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE') , 10000200 + 6),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 9, 'Đánh giá', 'BAO_CAO_DANH_GIA', (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE') , 10000200 + 7),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 10, 'Yêu cầu Hỗ trợ', 'BAO_CAO_YEU_CAU_HO_TRO', (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THONG_KE') , 10000200 + 8)
;

INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 11, 'Dashboard', 'DASHBOARD', -1, 11000000)
;

INSERT INTO vnpt_dev.permission (id, name, code, parent_id, priority) VALUES
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 12, 'Dashboard Thống kê Doanh thu', 'DASHBOARD_DOANH_THU', (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD'), 11000000 + 1),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 13, 'Dashboard Thống kê Gói cước', 'DASHBOARD_GOI_CUOC', (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD'), 11000000 + 2),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 14, 'Dashboard Thống kê Khách hàng', 'DASHBOARD_KHACH_HANG', (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD'), 11000000 + 3),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 15, 'Dashboard Thống kê Khuyến mại', 'DASHBOARD_KHUYEN_MAI', (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD'), 11000000 + 4),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 16, 'Dashboard Thống kê Tài khoản', 'DASHBOARD_TAI_KHOAN', (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD'), 11000000 + 5),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 17, 'Dashboard Thống kê Tình trạng dùng thử', 'DASHBOARD_TINH_TRANG_DUNG_THU', (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD'), 11000000 + 6),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 18, 'Dashboard Thống kê Tổng', 'DASHBOARD_THONG_KE_TONG', (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD'), 11000000 + 7),
((SELECT id FROM vnpt_dev.permission ORDER BY id DESC LIMIT 1) + 19, 'Dashboard Đánh giá hiệu năng sản phẩm - gói cước', 'DASHBOARD_DANH_GIA_HIEU_NANG_SP_GC', (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD'), 11000000 + 8)
;

INSERT INTO vnpt_dev.permission_portal (permission_id, portal_id) VALUES
                                                                      ( (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_TAI_KHOAN' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THUE_BAO' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_GOI_CUOC' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_CHUONG_TRINH_KM' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_HOA_DON' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_DANH_GIA' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_YEU_CAU_HO_TRO' ORDER BY id DESC LIMIT 1), 1 ),
-- phần này dành cho dashboard
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_DOANH_THU' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_GOI_CUOC' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_KHACH_HANG' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_KHUYEN_MAI' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_TAI_KHOAN' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_TINH_TRANG_DUNG_THU' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_THONG_KE_TONG' ORDER BY id DESC LIMIT 1), 1 ),
( (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_DANH_GIA_HIEU_NANG_SP_GC' ORDER BY id DESC LIMIT 1), 1 )
;

-- vnpt_dev.menu_report
INSERT INTO vnpt_dev.menu_report (display_name, url, type, permission_id, code, status) VALUES
('Tài khoản', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/RZc6JRuVywKrWjgrlmsrvgMFuyj5xZko58UMKDmh?org_slug=default', 0, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_TAI_KHOAN' ORDER BY id DESC LIMIT 1), 'BAO_CAO_TAI_KHOAN', 1),
('Thuê bao', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/ePsQPKnMndpIxvqYcSC2h4CNGXCkELLOVxHGzJEc?org_slug=default', 0, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THUE_BAO' ORDER BY id DESC LIMIT 1), 'BAO_CAO_THUE_BAO', 1),
('Sản phẩm Dịch vụ', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/zpmVk3ZOmm1aN5MmTWLOrpPPHEOsKKbckSfpqfr5?org_slug=default', 0, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1), 'BAO_CAO_SAN_PHAM_DICH_VU', 1),
('Gói cước', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/ZktXeqLgz5ocwLF8hIx9bA0DO2wqTs7ejpluYdWQ?org_slug=default', 0, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_GOI_CUOC' ORDER BY id DESC LIMIT 1), 'BAO_CAO_GOI_CUOC', 1),
('Chương trình KM', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/jzujSziumQwVcx3TdAV2I8YiFMlfPGiNaV4Wr9cs?org_slug=default', 0, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_CHUONG_TRINH_KM' ORDER BY id DESC LIMIT 1), 'BAO_CAO_CHUONG_TRINH_KM', 1),
('Hóa đơn', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/bXQlFRqIfGIhlihdSIYZj4BoAbHOwximIcMgKiOX?org_slug=default', 0, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_HOA_DON' ORDER BY id DESC LIMIT 1), 'BAO_CAO_HOA_DON', 1),
('Đánh giá', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/mNOxjpkeKfYk1qCz4NO5HbdGTaPFRV3DL5Btxnkj?org_slug=default', 0, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_DANH_GIA' ORDER BY id DESC LIMIT 1), 'BAO_CAO_DANH_GIA', 0),
('Yêu cầu Hỗ trợ', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/mNOxjpkeKfYk1qCz4NO5HbdGTaPFRV3DL5Btxnkj?org_slug=default', 0, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_YEU_CAU_HO_TRO' ORDER BY id DESC LIMIT 1), 'BAO_CAO_YEU_CAU_HO_TRO', 0),
-- phần này cho dashboard
('Thống kê Doanh thu', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/2rlSY2aLzZXfADlaN73FDHfXRwldQJJz34ulpddx?org_slug=default', 1, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_DOANH_THU' ORDER BY id DESC LIMIT 1), 'DASHBOARD_DOANH_THU', 1),
('Thống kê Gói cước', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/mNOxjpkeKfYk1qCz4NO5HbdGTaPFRV3DL5Btxnkj?org_slug=default', 1, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_GOI_CUOC' ORDER BY id DESC LIMIT 1), 'DASHBOARD_GOI_CUOC', 0),
('Thống kê Khách hàng', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/2j2UCMhQejgweSDgZrXsu9Qldeif4ySbTjFKSJBi?org_slug=default', 1, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_KHACH_HANG' ORDER BY id DESC LIMIT 1), 'DASHBOARD_KHACH_HANG', 1),
('Thống kê Khuyến mại', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/mNOxjpkeKfYk1qCz4NO5HbdGTaPFRV3DL5Btxnkj?org_slug=default', 1, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_KHUYEN_MAI' ORDER BY id DESC LIMIT 1), 'DASHBOARD_KHUYEN_MAI', 0),
('Thống kê Tài khoản', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/mNOxjpkeKfYk1qCz4NO5HbdGTaPFRV3DL5Btxnkj?org_slug=default', 1, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_TAI_KHOAN' ORDER BY id DESC LIMIT 1), 'DASHBOARD_TAI_KHOAN', 1),
('Thống kê Tình trạng dùng thử', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/mNOxjpkeKfYk1qCz4NO5HbdGTaPFRV3DL5Btxnkj?org_slug=default', 1, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_TINH_TRANG_DUNG_THU' ORDER BY id DESC LIMIT 1), 'DASHBOARD_TINH_TRANG_DUNG_THU', 0),
('Thống kê Tổng', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/mNOxjpkeKfYk1qCz4NO5HbdGTaPFRV3DL5Btxnkj?org_slug=default', 1, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_THONG_KE_TONG' ORDER BY id DESC LIMIT 1), 'DASHBOARD_THONG_KE_TONG', 0),
('Đánh giá hiệu năng sản phẩm - gói cước', 'https://onedx-report.vnpt-technology.vn:44328/public/dashboards/mNOxjpkeKfYk1qCz4NO5HbdGTaPFRV3DL5Btxnkj?org_slug=default', 1, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_DANH_GIA_HIEU_NANG_SP_GC' ORDER BY id DESC LIMIT 1), 'DASHBOARD_DANH_GIA_HIEU_NANG_SP_GC', 0)
;

-- vnpt_dev.roles_permissions

INSERT INTO vnpt_dev.roles_permissions (role_id, permission_id, allow_edit) VALUES
                                                                                (9, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_TAI_KHOAN' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_THUE_BAO' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_SAN_PHAM_DICH_VU' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_GOI_CUOC' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_CHUONG_TRINH_KM' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_HOA_DON' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_DANH_GIA' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'BAO_CAO_YEU_CAU_HO_TRO' ORDER BY id DESC LIMIT 1), 1 ),
-- phần này dành cho dashboard
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_DOANH_THU' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_GOI_CUOC' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_KHACH_HANG' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_KHUYEN_MAI' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_TAI_KHOAN' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_TINH_TRANG_DUNG_THU' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_THONG_KE_TONG' ORDER BY id DESC LIMIT 1), 1 ),
(9, (SELECT id FROM vnpt_dev.permission WHERE code = 'DASHBOARD_DANH_GIA_HIEU_NANG_SP_GC' ORDER BY id DESC LIMIT 1), 1 )
;
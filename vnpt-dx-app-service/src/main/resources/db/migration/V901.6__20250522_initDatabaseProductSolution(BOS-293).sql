-- XÓA BẢNG NẾU ĐÃ TỒN TẠI
DROP TABLE IF EXISTS product_solutions;
DROP TABLE IF EXISTS product_solution_draft;
DROP TABLE IF EXISTS solution_domains;
DROP TABLE IF EXISTS solution_packages;
DROP TABLE IF EXISTS packages;
DROP TABLE IF EXISTS package_draft;
DROP TABLE IF EXISTS package_mappings;
DROP TABLE IF EXISTS package_items;
DROP TABLE IF EXISTS package_item_addons;
DROP TABLE IF EXISTS package_item_prices;
DROP TABLE IF EXISTS package_item_addon_prices;
DROP TABLE IF EXISTS package_item_addon_promotions;
DROP TABLE IF EXISTS package_item_promotions;
DROP TABLE IF EXISTS sections;
DROP TABLE IF EXISTS section_mappings;
DROP TABLE IF EXISTS feature_mappings;
DROP TABLE IF EXISTS suggestion_mappings;
DROP TABLE IF EXISTS product_approval_history;

-- BẢNG solution_domains: Thông tin lĩnh vực của các bộ giải pháp sản phẩm
CREATE TABLE solution_domains (
                                  id bigserial PRIMARY KEY,
                                  type int2,
                                  name varchar(50)
);
-- Comment cho các cột trong bảng solution_domains
COMMENT ON COLUMN solution_domains.id IS 'ID lĩnh vực';
COMMENT ON COLUMN solution_domains.type IS 'Loại giải pháp sản phẩm (1: IOT, 2: SMARTHOME, 3: SAAS)';
COMMENT ON COLUMN solution_domains.name IS 'Tên lĩnh vực';

-- BẢNG product_solution_draft: Thông tin giải pháp nháp, chưa phê duyệt
CREATE TABLE product_solution_draft (
                                        id bigserial PRIMARY KEY,
                                        code varchar(255) UNIQUE,
                                        name varchar(255),
                                        provider_id int8,
                                        visibility int2,
                                        state int2,
                                        customer_types text,
                                        domain_ids int8[],
                                        category_ids int8[],
                                        avatar_url text,
                                        video_urls text,
                                        feature_visible boolean,
                                        descriptions text,
                                        multisub_enabled boolean,
                                        seo_id int8,
                                        approve_reason text,
                                        created_by int8,
                                        created_at timestamp,
                                        modified_by int8,
                                        modified_at timestamp,
                                        deleted_flag int2
);

-- BẢNG product_solutions: Thông tin giải pháp sản phẩm đã được phê duyệt
CREATE TABLE product_solutions (
                                   id bigserial PRIMARY KEY,
                                   code varchar(255),
                                   name varchar(255),
                                   provider_id int8,
                                   visibility int2,
                                   customer_types text,
                                   domain_ids int8[],
                                   category_ids int8[],
                                   avatar_url varchar(255),
                                   video_urls varchar(255),
                                   feature_visible boolean,
                                   descriptions text,
                                   multisub_enabled boolean,
                                   seo_id int8,
                                   draft_id int8,
                                   created_by int8,
                                   created_at timestamp,
                                   modified_by int8,
                                   modified_at timestamp,
                                   deleted_flag int2
);

-- Comment cho các cột trong bảng product_solutions
COMMENT ON COLUMN product_solutions.id IS 'ID giải pháp sản phẩm';
COMMENT ON COLUMN product_solutions.code IS 'Mã giải pháp sản phẩm';
COMMENT ON COLUMN product_solutions.name IS 'Tên giải pháp sản phẩm';
COMMENT ON COLUMN product_solutions.provider_id IS 'ID nhà cung cấp';
COMMENT ON COLUMN product_solutions.visibility IS 'Trạng thái hiển thị của giải pháp sản phẩm (0: INVISIBLE, 1: VISIBLE)';
COMMENT ON COLUMN product_solutions.customer_types IS 'Danh sách các loại khách hàng có thể mua giải pháp';
COMMENT ON COLUMN product_solutions.domain_ids IS 'Danh sách các ID lĩnh vực tương ứng với giải pháp';
COMMENT ON COLUMN product_solutions.category_ids IS 'Danh sách các ID danh mục mà giải pháp thuộc về';
COMMENT ON COLUMN product_solutions.avatar_url IS 'Url hình ảnh mô tả giải pháp';
COMMENT ON COLUMN product_solutions.video_urls IS 'Url video mô tả giải pháp';
COMMENT ON COLUMN product_solutions.descriptions IS 'Mô tả chi tiết giải pháp';
COMMENT ON COLUMN product_solutions.multisub_enabled IS 'Cấu hình multi-subscription';
COMMENT ON COLUMN product_solutions.seo_id IS 'ID thông tin SEO của solution';
COMMENT ON COLUMN product_solutions.draft_id IS 'ID giải pháp nháp liên kết với giải pháp sản phẩm';
COMMENT ON COLUMN product_solutions.created_by IS 'ID người tạo giải pháp sản phẩm';
COMMENT ON COLUMN product_solutions.created_at IS 'Thời gian tạo giải pháp sản phẩm';
COMMENT ON COLUMN product_solutions.modified_by IS 'ID người chỉnh sửa giải pháp sản phẩm gần nhất';
COMMENT ON COLUMN product_solutions.modified_at IS 'Thời gian chỉnh sửa giải pháp sản phẩm gần nhất';


-- Bảng solution_packages
create table solution_packages (
                                   id bigserial primary key,
                                   solution_id int8,
                                   solution_draft_id int8,
                                   package_id int8,
                                   package_draft_id int8,
                                   default_package bool
);
comment on column solution_packages.solution_id is 'ID giải pháp (ID bảng product_solutions)';
comment on column solution_packages.package_id is 'ID gói nằm trong giải pháp (ID bảng package)';
comment on column solution_packages.default_package is 'Gói mặc định';

-- Bảng packages
create table packages (
                          id bigserial primary key,
                          code varchar(255),
                          name varchar(255),
                          provider_id int8,
                          visibility int2,
                          banner_urls text,
                          icon_url text,
                          descriptions text,
                          discount_type varchar(20),
                          discount_value float8,
                          price float8,
                          price_from float8,
                          recommended boolean,
                          seo_id int8,
                          suggestion_mode varchar(10) default 'NONE',
                          payment_method varchar(20),
                          feature_visible boolean,
                          draft_id int8,
                          created_by int8,
                          created_at timestamp,
                          modified_by int8,
                          modified_at timestamp,
                          deleted_flag int2,
                          apply_condition jsonb,
                          guidelines jsonb,
                          domain_ids int8[],
                          category_ids int8[],
                          config_apply_time int2
);
comment on table packages is 'Bảng thông tin của gói sản phẩm dạng bundling (phiên bản chính thức)';
comment on column packages.code is 'Mã package (duy nhất trong bảng package_draft)';
comment on column packages.name is 'Tên gói sản phẩm dạng bundling';
comment on column packages.provider_id is 'ID nhà cung cấp';
comment on column packages.visibility is 'Trạng thái hiển thị (0: INVISIBLE, 1: VISIBLE)';
comment on column packages.banner_urls is 'Danh sách các URL banner package';
comment on column packages.descriptions is 'Mô tả chi tiết về package';
comment on column packages.discount_type is 'Loại chiết khấu (PERCENT/PRICE)';
comment on column packages.discount_value is 'Số tiền/% chiết khấu';
comment on column packages.price is 'Giá của package sau chiết khấu';
comment on column packages.price_from is 'Giá của package sau chiết khấu, chưa tính thuế';
comment on column packages.recommended is 'Trạng thái được đề xuất (true/false)';
comment on column packages.seo_id is 'ID thông tin SEO của package (ID bảng seo)';
comment on column packages.payment_method is 'Phương thức thanh toán: VNPTPAY (qua vnptpay hoặc qrcode), BY_CASH (tiền mặt)';
comment on column packages.draft_id is 'ID draft của package';
comment on column packages.created_by is 'ID người tạo';
comment on column packages.created_at is 'Thời gian tạo';
comment on column packages.modified_by is 'ID người chỉnh sửa gần nhất';
comment on column packages.modified_at is 'Thời gian chỉnh sửa gần nhất';
comment on column packages.apply_condition is 'Điều kiện để khách hàng có thể mua được package';
comment on column packages.guidelines is 'Thông tin hướng dẫn sử dụng package';
COMMENT ON COLUMN packages.suggestion_mode IS 'Chế độ gợi ý sản phẩm dịch vụ';
COMMENT ON COLUMN packages.product_apply IS 'Thành phần gói áp dụng tại luồng mua(1:áp dụng lúc tạo, 2: áp dụng realtime)';

-- Bảng package_draft
create table package_draft (
                               id bigserial primary key,
                               code varchar(255),
                               name varchar(255),
                               provider_id int8,
                               visibility int2,
                               state int2,
                               banner_urls text,
                               icon_url text,
                               descriptions text,
                               discount_type varchar(20),
                               discount_value float8,
                               price float8,
                               price_from float8,
                               recommended boolean,
                               seo_id int8,
                               suggestion_mode varchar(10) default 'NONE',
                               payment_method varchar(20),
                               feature_visible boolean,
                               approve_reason text,
                               created_by int8,
                               created_at timestamp,
                               modified_by int8,
                               modified_at timestamp,
                               deleted_flag int2,
                               apply_condition jsonb,
                               guidelines jsonb,
                               domain_ids int8[],
                               category_ids int8[],
                               config_apply_time int2
);


-- Bảng package_mappings
create table package_mappings (
                                  id bigserial primary key,
                                  package_id int8,
                                  package_draft_id int8,
                                  package_item_id int8
);
comment on table package_mappings is 'Quản lý các chiến lược thanh toán của các thành phần trực thuộc package';
comment on column package_mappings.package_id is 'ID gói sản phẩm (trạng thái đã approve)';
comment on column package_mappings.package_draft_id is 'ID draft của gói sản phẩm (trạng thái chưa approve)';
comment on column package_mappings.package_item_id is 'ID thông tin thành phần trực thuộc (ID bảng package_items)	';

-- Bảng package_items
create table package_items (
                               id bigserial primary key,
                               plan_id int8,
                               quantity int8,
                               total_amount float8,
                               metadata jsonb
);

comment on column package_items.plan_id is 'ID bảng multiplan của gói thành phần trực thuộc (pricing/device/variant)	
';
comment on column package_items.quantity is 'Số lượng thành phần riêng biệt trong gói	';
comment on column package_items.total_amount is 'Thành tiền';
comment on column package_items.metadata is 'Thông tin chi tiết: variant/pricing	';

-- Bảng package_item_addons
create table package_item_addons (
                                     id bigserial primary key,
                                     package_item_id int8,
                                     addon_id int8,
                                     addon_plan_id int8,
                                     quantity int4,
                                     total_amount float8
);
comment on table package_item_addons is 'Quản lý các addon tương ứng với các thành phần trực thuộc package';
comment on column package_item_addons.package_item_id is 'ID mapping package với plan (ID bảng package_item)';
comment on column package_item_addons.addon_id is 'ID addon gắn với plan';
comment on column package_item_addons.addon_plan_id is 'ID bảng multiplan của addon';
comment on column package_item_addons.quantity is 'Số lượng addon được đính kèm';

-- Bảng package_item_prices
create table package_item_prices (
                                     id bigserial primary key,
                                     package_item_id int8,
                                     pricing_plan varchar(20),
                                     unit_from int4,
                                     unit_to int4,
                                     price float8
);
comment on table package_item_prices is 'Giá đã cập nhật dành cho mỗi plan thuộc package';
comment on column package_item_prices.package_item_id is 'ID mapping package với plan (ID bảng package_items)';
comment on column package_item_prices.pricing_plan is 'Loại định giá của plan (FLAT_RATE, UNIT, TIER, STAIR_STEP, VOLUME)';
comment on column package_item_prices.unit_from is 'Số lượng đơn vị bắt đầu từ (cho các loại TIER, STAIR_STEP, VOLUME)';
comment on column package_item_prices.unit_to is 'Số lượng đơn vị đến (cho các loại TIER, STAIR_STEP, VOLUME)';
comment on column package_item_prices.price is 'Giá của plan sau cập nhật';

-- Bảng package_item_addon_prices
create table package_item_addon_prices (
                                           id bigserial primary key,
                                           package_item_addon_id int8,
                                           pricing_plan varchar(20),
                                           unit_from int4,
                                           unit_to int4,
                                           price float8
);
comment on table package_item_addon_prices is 'Giá đã cập nhật dành cho mỗi addon plan thuộc item';
comment on column package_item_addon_prices.package_item_addon_id is 'ID mapping package với package_item_addons';
comment on column package_item_addon_prices.pricing_plan is 'Loại định giá của plan (FLAT_RATE, UNIT, TIER, STAIR_STEP, VOLUME)';
comment on column package_item_addon_prices.unit_from is 'Số lượng đơn vị bắt đầu từ (cho các loại TIER, STAIR_STEP, VOLUME)';
comment on column package_item_addon_prices.unit_to is 'Số lượng đơn vị đến (cho các loại TIER, STAIR_STEP, VOLUME)';
comment on column package_item_addon_prices.price is 'Giá của plan sau cập nhật';

-- Bảng package_taxes
create table package_item_addon_promotions (
                                               id bigserial primary key,
                                               package_item_addon_id int8,
                                               coupon_id int8,
                                               mc_id int8,
                                               activity_idx int8,
);
comment on table package_item_addon_promotions is 'Quản lý các loại phí gắn với package';
comment on column package_item_addon_promotions.package_item_addon_id is 'ID của package_item_addon';
comment on column package_item_addon_promotions.coupon_id is 'ID coupons';
comment on column package_item_promotions.mc_id is 'ID marketing campage';
comment on column package_item_promotions.activity_idx is 'Index bảng activity';

-- Bảng package_item_promotions
create table package_item_promotions (
                                         id bigserial primary key,
                                         package_item_id int8,
                                         coupon_id int8,
                                         mc_id int8,
                                         activity_idx int8,
);
comment on table package_item_promotions is 'Quản lý các loại phí gắn với package';
comment on column package_item_promotions.package_item_id is 'ID của package_items';
comment on column package_item_promotions.coupon_id is 'ID coupons';
comment on column package_item_promotions.mc_id is 'ID marketing campage';
comment on column package_item_promotions.activity_idx is 'Index bảng activity';

-- Bảng sections
create table sections (
                          id bigserial primary key,
                          name varchar,
                          description text,
                          image_url text,
                          video_url text
);

comment on column sections.name is 'Tên bố cục';
comment on column sections.description is 'Mô tả cho bố cục';
comment on column sections.image_url is 'URL ảnh bố cục';
comment on column sections.video_url is 'URL video bố cục';

-- Bảng section_mappings
create table section_mappings (
                                  id bigserial,
                                  idx int2,
                                  section_id int8,
                                  object_id int8,
                                  object_draft_id int8,
                                  object_type varchar(20),
                                  PRIMARY KEY (id, object_type)
) PARTITION BY LIST (object_type);
comment on table section_mappings is 'Quản lý mapping giữa các bố cục với các đối tượng tương ứng trong hệ thống';
comment on column section_mappings.section_id is 'ID bố cục (ID bảng sections)';
comment on column section_mappings.object_id is 'ID của đối tượng áp dụng bố cục (đã được approve)';
comment on column section_mappings.object_draft_id is 'ID draft của đối tượng áp dụng bố cục (chờ approve)';
comment on column section_mappings.object_type is 'Loại đối tượng áp dụng bố cục (vd: SOLUTION, PACKAGE)';

CREATE TABLE section_mappings_service
    PARTITION OF section_mappings
    FOR VALUES IN ('SERVICE');

CREATE TABLE section_mappings_combo
    PARTITION OF section_mappings
    FOR VALUES IN ('COMBO');

CREATE TABLE section_mappings_packages
    PARTITION OF section_mappings
    FOR VALUES IN ('PACKAGE');

CREATE TABLE section_mappings_solutions
    PARTITION OF section_mappings
    FOR VALUES IN ('SOLUTION');

-- Bảng feature_mappings
create table feature_mappings (
                                  id bigserial,
                                  idx int2,
                                  feature_id int8,
                                  object_type varchar(20),
                                  object_id int8,
                                  object_draft_id int8,
                                  PRIMARY KEY (id, object_type)
) PARTITION BY LIST (object_type);
comment on table feature_mappings is 'Quản lý mapping giữa các tính năng với các đối tượng tương ứng trong hệ thống';
comment on column feature_mappings.idx is 'Thứ tự của tính năng';
comment on column feature_mappings.feature_id is 'ID tính năng (ID bảng features)';
comment on column feature_mappings.object_type is 'Loại đối tượng áp dụng tính năng (vd: SOLUTIONS, PACKAGES)';
comment on column feature_mappings.object_id is 'ID đối tượng sở hữu tính năng (đã được approve)';
comment on column feature_mappings.object_draft_id is 'ID draft đối tượng sở hữu tính năng (chờ approve)';

CREATE TABLE feature_mappings_service
    PARTITION OF feature_mappings
    FOR VALUES IN ('SERVICE');

CREATE TABLE feature_mappings_combo
    PARTITION OF feature_mappings
    FOR VALUES IN ('COMBO');

CREATE TABLE feature_mappings_packages
    PARTITION OF feature_mappings
    FOR VALUES IN ('PACKAGE');

CREATE TABLE feature_mappings_solutions
    PARTITION OF feature_mappings
    FOR VALUES IN ('SOLUTION');

-- suggestion_mappings
DROP TABLE IF EXISTS suggestion_mappings;
create table suggestion_mappings (
   id bigserial,
   object_type varchar(20) not null,
   object_id int8,
   object_draft_id int8,
   suggest_object_type varchar(20) not null,
   suggest_object_draft_id int8 not null,
   suggest_metadata jsonb,
   PRIMARY KEY (id, object_type)
) PARTITION BY LIST (object_type);

comment on column suggestion_mappings.object_type is 'Loại đối tượng được cấu hình gợi ý SPDV liên quan';
comment on column suggestion_mappings.object_id is 'ID của đối tượng được cấu hình gợi ý SPDV liên quan (đã được approve)';
comment on column suggestion_mappings.object_draft_id is 'ID draft của đối tượng được cấu hình gợi ý SPDV liên quan (chờ approve)';
comment on column suggestion_mappings.suggest_object_type is 'Loại đối tượng gợi ý SPDV (vd: SOLUTIONS, PACKAGES)';
comment on column suggestion_mappings.suggest_object_draft_id is 'ID draft của đối tượng gợi ý';
comment on column suggestion_mappings.suggest_metadata is 'Metadata của đối tượng gợi ý';

--
CREATE TABLE suggestion_mappings_service
    PARTITION OF suggestion_mappings
    FOR VALUES IN ('SERVICE');

CREATE TABLE suggestion_mappings_combo
    PARTITION OF suggestion_mappings
    FOR VALUES IN ('COMBO');

CREATE TABLE suggestion_mappings_packages
    PARTITION OF suggestion_mappings
    FOR VALUES IN ('PACKAGE');

CREATE TABLE suggestion_mappings_solutions
    PARTITION OF suggestion_mappings
    FOR VALUES IN ('SOLUTION');


insert into solution_domains (type, name)
values
    (1, 'Nông nghiệp'),
    (1, 'Smart City'),
    (1, 'Y tế'),
    (1, 'Sức khỏe'),
    (1, 'Nhà thông tin'),
    (1, 'Giao thông vận tải'),
    (1, 'Công nghiệp 4.0');

ALTER TABLE "vnpt_dev"."subscription_metadata"
    ADD COLUMN IF NOT EXISTS "solution_id" int8,
    ADD COLUMN IF NOT EXISTS  "package_id" int8;

COMMENT ON COLUMN "vnpt_dev"."subscription_metadata"."solution_id" IS 'ID giải pháp';

COMMENT ON COLUMN "vnpt_dev"."subscription_metadata"."package_id" IS 'ID gói bundling';

ALTER TABLE "vnpt_dev"."topic_faqs_service" RENAME COLUMN "service_id" TO "object_id";

ALTER TABLE "vnpt_dev"."topic_faqs_service"
    ADD COLUMN IF NOT EXISTS "object_draft_id" int8;

COMMENT ON COLUMN "vnpt_dev"."topic_faqs_service"."object_type" IS 'Loại SPDV: 1: service, 2: combo, 3: Giải pháp, 4: bundling';


CREATE TABLE product_approval_history (
                                          id  bigserial,
                                          version         int4,
                                          object_type varchar(20),
                                          object_name     varchar,
                                          object_id       int8,
                                          object_draft_id int8,
                                          description     text,
                                          created_by      int8,
                                          created_at timestamp,
                                          approval_status int2 NOT NULL,
                                          PRIMARY KEY (id, object_type)
) PARTITION BY LIST (object_type);

COMMENT ON COLUMN product_approval_history.id              IS 'ID tự tăng (khóa chính)';
COMMENT ON COLUMN product_approval_history.version         IS 'Số phiên bản của đối tượng';
COMMENT ON COLUMN product_approval_history.object_type     IS 'Loại đối tượng: PACKAGE (Gói SP), SOLUTION (Giải pháp)';
COMMENT ON COLUMN product_approval_history.object_name     IS 'Tên đối tượng SPDV - dùng để hiển thị/tìm kiếm nhanh';
COMMENT ON COLUMN product_approval_history.object_id       IS 'ID đối tượng SPDV đã được phê duyệt';
COMMENT ON COLUMN product_approval_history.object_draft_id IS 'ID bản nháp SPDV đang yêu cầu thay đổi/phê duyệt';
COMMENT ON COLUMN product_approval_history.description     IS 'Mô tả nội dung thay đổi yêu cầu phê duyệt';
COMMENT ON COLUMN product_approval_history.created_by      IS 'ID người thực hiện phê duyệt';
COMMENT ON COLUMN product_approval_history.created_at      IS 'Thời gian thực hiện phê duyệt';
COMMENT ON COLUMN product_approval_history.approval_status IS 'Trạng thái phê duyệt SPDV (0.Chưa duyệt, 1.Đã duyệt, 2. Chờ duyệt, 3. Từ chối)';

CREATE TABLE product_approval_history_service
    PARTITION OF product_approval_history
    FOR VALUES IN ('SERVICE');

CREATE TABLE product_approval_history_combo
    PARTITION OF product_approval_history
    FOR VALUES IN ('COMBO');

CREATE TABLE product_approval_history_packages
    PARTITION OF product_approval_history
    FOR VALUES IN ('PACKAGE');

CREATE TABLE product_approval_history_solutions
    PARTITION OF product_approval_history
    FOR VALUES IN ('SOLUTION');


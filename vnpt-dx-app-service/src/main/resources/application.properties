spring.profiles.active=db70
spring.main.allow-bean-definition-overriding=true
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=500MB
storage.path=/
url.download=https://www.journaldev.com/sitemap.xml
file.path = upload/file/
report.template= report/template
report.excel= report/excel
report.pdf= report/pdf
resource.file = {0}/{1}
bill_export_prefix = billing_
prefix.resource.config = /resources/
master.app.jwtSecret= masterSecret
master.app.jwtExpirationMs= 3600000
spring.jackson.parser.allow-numeric-leading-zeros=true
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
batch.email.change.status.hour = 1
url.api.taxCode=https://thongtindoanhnghiep.co/api/company/
spring.jpa.properties.hibernate.jdbc.batch_size = 50
spring.datasource.hikari.data-source-properties.reWriteBatchedInserts = true
#spring.datasource.hikari.leak-detection-threshold=2000
logging.level.com.zaxxer.hikari=DEBUG
spring.cache.type=redis
# Time to live (5 minutes)
spring.cache.redis.time-to-live=300000
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dxServiceCache@

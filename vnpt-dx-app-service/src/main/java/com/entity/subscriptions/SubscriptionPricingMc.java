package com.entity.subscriptions;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.*;

/**
 * <AUTHOR> nghiapt
 * @version    : 1.0
 * 1/6/2021
 */
@Data
@ToString(callSuper = true)
@Entity
@Table(name = "subscription_pricing_mc")
@AllArgsConstructor
@NoArgsConstructor
public class SubscriptionPricingMc {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "subscription_id")
    private Long subscriptionId;

    @Column(name = "pricing_id")
    private Long pricingId;

    @Column(name = "pmp_id")
    private Long pmpId;

    @Column(name = "mc_id")
    private Long mcId;

    @Column(name = "acitivity_idx")
    private Long activityIdx;

    @Column(name = "rule_idx")
    private Long ruleIdx;

    @Column(name = "item_set_idx")
    private Long itemSetIdx;

    @Column(name = "item_idx")
    private Long itemIdx;
}

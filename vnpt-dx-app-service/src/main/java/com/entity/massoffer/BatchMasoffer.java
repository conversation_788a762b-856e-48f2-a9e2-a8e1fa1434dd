package com.entity.massoffer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.component.SuperBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> HaLT3
 * @version : 1.0 10/12/2021
 */
@Getter
@Setter
@Entity
@Table(name = "batch_masoffer")
@AllArgsConstructor
@NoArgsConstructor
public class BatchMasoffer extends SuperBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "subscription_id")
    private Long subscriptionId;

    @Column(name = "action_status")
    private Integer actionStatus;

    @Column(name = "action_content")
    private String actionContent;

    @Column(name = "deleted_flag")
    protected Integer deletedFlag = 1;
}

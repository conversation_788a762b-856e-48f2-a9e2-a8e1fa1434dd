/**
 * 
 */
package com.entity.categories;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import com.onedx.common.converter.SetConverter;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Entity
@Data
@NoArgsConstructor
@Table(name = "categories")
public class Category implements Serializable {
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "sme_category_id")
    private Long smeCategoryId;

    @Column(name = "level")
    private Integer level;

    @Column(name = "parent_id")
    private Long parentId;

    @Convert(converter = SetConverter.class)
    @Column(name = "apply_code")
    private Set<String> applyCode = new HashSet<>();

    @Column(name = "banner_id")
    private Long bannerId;

    @Column(name = "categories_id_migration")
    private Long migrationId;

    @Column(name = "priority")
    private Long priority;

    @Column(name = "deleted_flag")
    protected Integer deletedFlag = 1;

    @Column(name = "created_by")
    protected Long createdBy;

    @Column(name = "created_at")
    @CreatedDate
    protected Date createdAt;

    @Column(name = "modified_by")
    protected Long modifiedBy;

    @Column(name = "modified_at")
    @LastModifiedDate
    protected Date modifiedAt;

    @Column(name = "status")
    protected Integer status;

@Builder
	public Category(Integer deletedFlag, Long createdBy, Date createdAt, Long modifiedBy, Date modifiedAt,
			Integer status, Long id, String name, String description, Long smeCategoryId) {
		this.id = id;
		this.name = name;
		this.description = description;
		this.smeCategoryId= smeCategoryId;
		this.deletedFlag = deletedFlag;
		this.createdBy = createdBy;
		this.createdAt = createdAt;
		this.modifiedBy = modifiedBy;
		this.modifiedAt = modifiedAt;
		this.status = status;
	}

}

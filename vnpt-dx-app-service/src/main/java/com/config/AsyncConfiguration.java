package com.config;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import com.monitor.executors.ThreadPoolTaskExecutorStore;

/**
 * <AUTHOR> KhanhND2
 * @version    : 1.0
 * 22/3/2021
 */
@Configuration
@EnableAsync
public class AsyncConfiguration implements AsyncConfigurer {

    public static final String ASYNC_EXECUTOR_SEND_MASOFFER = "asyncExecutorSendMasoffer";
    public static final String ASYNC_EXECUTOR_UPDATE_PARTITION_MAPPING = "asyncExecutorUpdatePartitionMapping";
    public static final String ASYNC_EXECUTOR_UPDATE_RULE_IMMEDIATE = "getAsyncExecutorUpdateRuleImmediate";
    public static final String ASYNC_EXECUTOR_CREATE_RULE_IMMEDIATE = "getAsyncExecutorCreateRuleImmediate";
    public static final String ASYNC_EXECUTOR_SEND_REQUEST_APPROVE_NOTIF = "asyncExecutorSendRequestApproveNotif";
    public static final String ASYNC_SME_TRACKING_ORDER_EXECUTOR = "asyncSmeTrackingOrderExecutor";
    public static final String ASYNC_EXECUTOR_COMMON = "asyncExecutorCommon";
    public static final String ASYNC_EXECUTOR_SEND_MAIL_HELPER = "asyncExecutorSendMailHelper";
    public static final String ASYNC_EXECUTOR_READ_MESSAGE = "asyncExecutorReadMessage";
    public static final String ASYNC_EXECUTOR_SEND_MESSAGE = "asyncExecutorSendMessage";
    public static final String ASYNC_EXECUTOR_READ_MAIL = "asyncExecutorReadMail";
    public static final String ASYNC_EXECUTOR_READ_SMS = "asyncExecutorReadSMS";
    public static final String ASYNC_EXECUTOR_READ_AUTO_BIZFLY = "asyncExecutorReadAutoBizfly";
    public static final String ASYNC_EXECUTOR_SEND_AUTO_BIZFLY_EMAIL = "asyncExecutorSendAutoBizflyEmail";
    public static final String ASYNC_EXECUTOR_SEND_SMS = "asyncExecutorSendSMS";
    public static final String ASYNC_EXECUTOR_SEND_MAIL = "asyncExecutorSendMail";
    public static final String ASYNC_EXECUTOR_CHANGE_EVENT = "asyncExecutorChangeEvent";
    @Value(value = "${email.core_thread_send_mail:5}")
    private int coreThreadSendMail;

    @Value(value = "${email.max_thread_send_mail:10}")
    private int maxThreadSendMail;

    @Value(value = "${email.queue_capacity_send_mail:500}")
    private int queueCapacitySendMail;

    @Value(value = "${email.core_thread_read_mail:1}")
    private int coreThreadReadMail;

    @Value(value = "${email.max_thread_read_mail:1}")
    private int maxThreadReadMail;

    @Value(value = "${email.queue_capacity_read_mail:2}")
    private int queueCapacityReadMail;

    @Value(value = "${message.core_thread_send_message:10}")
    private int coreThreadSendMessage;

    @Value(value = "${message.core_thread_send_message:20}")
    private int maxThreadSendMessage;

    @Value(value = "${message.queue_capacity_send_message:500}")
    private int queueCapacitySendMessage;

    @Value(value = "${message.core_thread_read_message:1}")
    private int coreThreadReadMessage;

    @Value(value = "${message.max_thread_read_message:1}")
    private int maxThreadReadMessage;

    @Value(value = "${message.queue_capacity_read_message:2}")
    private int queueCapacityReadMessage;

    @Value(value = "${email-helper.core_thread_send_mail:1}")
    private int coreThreadSendMailHelper;

    @Value(value = "${email-helper.max_thread_send_mail:1}")
    private int maxThreadSendMailHelper;

    @Value(value = "${email-helper.queue_capacity_send_mail:100}")
    private int queueCapacitySendMailHelper;

    @Value(value = "${masoffer.core_thread_send_postback:1}")
    private int masofferCoreThreadSendPostback;

    @Value(value = "${masoffer.max_thread_send_postback:1}")
    private int masofferMaxThreadSendPostback;

    @Value(value = "${masoffer.queue_capacity_send_postback:100}")
    private int masofferQueueCapacitySendPostback;

    @Value(value = "${batch.core_thread:10}")
    private int coreThreadCommon;

    @Value(value = "${batch.max_thread:10}")
    private int maxThreadCommon;

    @Value(value = "${batch.queue_capacity:100}")
    private int queueCapacityCommon;

    @Autowired
    ThreadPoolTaskExecutorStore executorStore;

    @Bean(name = ASYNC_EXECUTOR_SEND_MASOFFER)
    public Executor getAsyncExecutorSenMasofer() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(masofferCoreThreadSendPostback);
        executor.setMaxPoolSize(masofferMaxThreadSendPostback);
        executor.setQueueCapacity(masofferQueueCapacitySendPostback);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncSendMasoffer-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_SEND_MASOFFER, executor);
        return executor;
    }

    @Override
    @Bean(name = ASYNC_EXECUTOR_SEND_MAIL)
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadSendMail);
        executor.setMaxPoolSize(maxThreadSendMail);
        executor.setQueueCapacity(queueCapacitySendMail);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncSendMail-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_SEND_MAIL, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_SEND_SMS)
    public Executor getAsyncExecutorSendSMS() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadSendMail);
        executor.setMaxPoolSize(maxThreadSendMail);
        executor.setQueueCapacity(queueCapacitySendMail);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncSendSMS-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_SEND_SMS, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_SEND_AUTO_BIZFLY_EMAIL)
    public Executor getAsyncExecutorSendAtuBizflyEmail() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadSendMail);
        executor.setMaxPoolSize(maxThreadSendMail);
        executor.setQueueCapacity(queueCapacitySendMail);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncSendAutoBizflyEmail-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_SEND_AUTO_BIZFLY_EMAIL, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_READ_AUTO_BIZFLY)
    public Executor getAsyncExecutorReadAutoBizflyBatch() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadReadMail);
        executor.setMaxPoolSize(maxThreadReadMail);
        executor.setQueueCapacity(queueCapacityReadMail);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncReadAutoBizfly-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_READ_AUTO_BIZFLY, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_READ_SMS)
    public Executor getAsyncExecutorReadSMSBatch() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadReadMail);
        executor.setMaxPoolSize(maxThreadReadMail);
        executor.setQueueCapacity(queueCapacityReadMail);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncReadSMS-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_READ_SMS, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_READ_MAIL)
    public Executor getAsyncExecutorReadBatch() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadReadMail);
        executor.setMaxPoolSize(maxThreadReadMail);
        executor.setQueueCapacity(queueCapacityReadMail);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncReadMail-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_READ_MAIL, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_SEND_MESSAGE)
    public Executor getAsyncExecutorSendMessage() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadSendMessage);
        executor.setMaxPoolSize(maxThreadSendMessage);
        executor.setQueueCapacity(queueCapacitySendMessage);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncSendMessage-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_SEND_MESSAGE, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_READ_MESSAGE)
    public Executor getAsyncExecutorReadMessage() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadReadMessage);
        executor.setMaxPoolSize(maxThreadReadMessage);
        executor.setQueueCapacity(queueCapacityReadMessage);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncReadMessage-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_READ_MESSAGE, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_SEND_MAIL_HELPER)
    public Executor getAsyncExecutorSendMailHelper() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadSendMailHelper);
        executor.setMaxPoolSize(maxThreadSendMailHelper);
        executor.setQueueCapacity(queueCapacitySendMailHelper);
        executor.setThreadNamePrefix("VNPT-ThreadAsyncSendMailHelper-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_SEND_MAIL_HELPER, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_COMMON)
    public Executor getAsyncExecutorCommon() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadCommon);
        executor.setMaxPoolSize(maxThreadCommon);
        executor.setQueueCapacity(queueCapacityCommon);
        executor.setThreadNamePrefix("VNPT-AsyncCommon-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_COMMON, executor);
        return executor;
    }

    @Bean(name = ASYNC_SME_TRACKING_ORDER_EXECUTOR)
    public Executor getAsyncExecutorSmeTrackingOrder() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadCommon);
        executor.setMaxPoolSize(maxThreadCommon);
        executor.setQueueCapacity(queueCapacityCommon);
        executor.setThreadNamePrefix("VNPT-AsyncSmeTrackingOrder-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_SME_TRACKING_ORDER_EXECUTOR, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_SEND_REQUEST_APPROVE_NOTIF)
    public Executor getAsyncExecutorSendRequestApproveNotif() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadCommon);
        executor.setMaxPoolSize(maxThreadCommon);
        executor.setQueueCapacity(queueCapacityCommon);
        executor.setThreadNamePrefix("VNPT-AsyncSendRequestApproveNotif-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_SEND_REQUEST_APPROVE_NOTIF, executor);
        return executor;
    }

    @Bean(name = "asyncExecutorSingleton")
    public Executor getAsyncExecutorSingleton() {
        return Executors.newSingleThreadExecutor();
    }

    @Bean(name = ASYNC_EXECUTOR_CREATE_RULE_IMMEDIATE)
    public Executor getAsyncExecutorCreateRuleImmediate() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadCommon);
        executor.setMaxPoolSize(maxThreadCommon);
        executor.setQueueCapacity(queueCapacityCommon);
        executor.setThreadNamePrefix("AsyncCreateRuleImmediate");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_CREATE_RULE_IMMEDIATE, executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_UPDATE_RULE_IMMEDIATE)
    public Executor getAsyncExecutorUpdateRuleImmediate() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadCommon);
        executor.setMaxPoolSize(maxThreadCommon);
        executor.setQueueCapacity(queueCapacityCommon);
        executor.setThreadNamePrefix("AsyncUpdateRuleImmediate");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_UPDATE_RULE_IMMEDIATE, executor);
        return executor;
    }


    @Bean(name = ASYNC_EXECUTOR_UPDATE_PARTITION_MAPPING)
    public Executor getAsyncExecutorUpdatePartitionMapping() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadCommon);
        executor.setMaxPoolSize(maxThreadCommon);
        executor.setQueueCapacity(queueCapacityCommon);
        executor.setThreadNamePrefix("VNPT-AsyncEUpdatePartitionMapping-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_UPDATE_PARTITION_MAPPING, executor);
        return executor;
    }

    @Bean(name = "asyncExecutorPublisherStatusEvent")
    public Executor getAsyncExecutorPublisherStatusEvent() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadCommon);
        executor.setMaxPoolSize(maxThreadCommon);
        executor.setQueueCapacity(queueCapacityCommon);
        executor.setThreadNamePrefix("VNPT-AsyncExecutorPublisherStatusEvent-");
        executor.initialize();
        executorStore.registerMonitor("AsyncExecutorPublisherStatusEvent", executor);
        return executor;
    }

    @Bean(name = ASYNC_EXECUTOR_CHANGE_EVENT)
    public Executor getAsyncExecutorChangeEvent() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThreadCommon);
        executor.setMaxPoolSize(maxThreadCommon);
        executor.setQueueCapacity(queueCapacityCommon);
        executor.setThreadNamePrefix("VNPT-" + ASYNC_EXECUTOR_CHANGE_EVENT + "-");
        executor.initialize();
        executorStore.registerMonitor(ASYNC_EXECUTOR_CHANGE_EVENT, executor);
        return executor;
    }
}

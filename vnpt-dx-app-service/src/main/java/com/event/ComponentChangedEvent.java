package com.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Event được publish khi có sự thay đổi component (coupon, pricing, etc.)
 */
@Getter
public class ComponentChangedEvent extends ApplicationEvent {

    private final Long componentId;
    private final String componentType;
    private final String action;
    private final Object metadata;

    public ComponentChangedEvent(Object source, Long componentId, String componentType, String action, Object metadata) {
        super(source);
        this.componentId = componentId;
        this.componentType = componentType;
        this.action = action;
        this.metadata = metadata;
    }

    public ComponentChangedEvent(Object source, Long componentId, String componentType, String action) {
        this(source, componentId, componentType, action, null);
    }
}

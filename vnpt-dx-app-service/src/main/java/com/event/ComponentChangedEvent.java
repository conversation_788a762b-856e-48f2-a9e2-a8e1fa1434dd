package com.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Event được publish khi có sự thay đổi component (coupon, pricing, etc.)
 * Chỉ chứa ID của event đã được tạo sẵn trong bảng events
 */
@Getter
public class ComponentChangedEvent extends ApplicationEvent {

    private final Long eventId;

    public ComponentChangedEvent(Object source, Long eventId) {
        super(source);
        this.eventId = eventId;
    }
}

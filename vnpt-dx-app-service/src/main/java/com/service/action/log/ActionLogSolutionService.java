package com.service.action.log;

import com.constant.enums.action.log.ActionLogVersionEnum;
import com.dto.action.log.IActionLogSolutionResDTO;
import com.enums.ApproveStatusEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ActionLogSolutionService {

    /**
     * l<PERSON>y danh sách lịch sử giải pháp / gói bundling
     */
    Page<IActionLogSolutionResDTO> getAllActionLogSolution(Long objectDraftId, String type, String name, ActionLogVersionEnum version, ApproveStatusEnum status, Pageable pageable);
}

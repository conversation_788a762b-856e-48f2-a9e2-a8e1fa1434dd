package com.service.seo.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.dto.seo.SeoReqDTO;
import com.onedx.common.exception.MessageKeyConstant;
import com.constant.enums.seo.SeoObjectTypeEnum;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.dto.customerTicket.AttachesDTO;
import com.dto.report.dashboardSme.FilterResDTO;
import com.dto.seo.SeoAllDTO;
import com.dto.seo.SeoCategoryDetailDTO;
import com.dto.seo.SeoDTO;
import com.dto.seo.SeoDetailDTO;
import com.dto.seo.SeoDetailResDTO;
import com.dto.seo.SeoPricingClassDTO;
import com.dto.seo.SeoPricingDTO;
import com.dto.seo.SeoResDTO;
import com.dto.seo.SeoServiceDTO;
import com.dto.seo.SeoUpdateDTO;
import com.entity.categories.Category;
import com.entity.file.attach.FileAttach;
import com.entity.seo.KeyWord;
import com.entity.seo.Seo;
import com.entity.pricing.PricingDraft;
import com.entity.services.ServiceEntity;
import com.onedx.common.constants.enums.DeletedFlag;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.exception.type.BadRequestException;
import com.constant.SeoTypeCodeConstant;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.model.dto.PricingNotSeoDTO;
import com.repository.categories.CategoryRepository;
import com.repository.file.attach.FileAttachRepository;
import com.repository.pricing.PricingDraftRepository;
import com.repository.seo.KeyWordRepository;
import com.repository.seo.SeoRepository;
import com.repository.services.ServiceRepository;
import com.service.seo.SeoService;
import com.util.FileUtil;
import com.util.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> HuynhBQ
 * @version : 1.0 03/11/2021
 */
@Service
@Slf4j
@PropertySource(value = "classpath:email_template.properties", encoding = "UTF-8")
public class SeoServiceImpl implements SeoService {

    @Autowired
    private SeoRepository seoRepository;

    @Autowired
    private KeyWordRepository keyWordRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private PricingDraftRepository pricingDraftRepository;

    @Autowired
    private ServiceRepository serviceRepository;

    public static final String[] SEO_MESSAGE = new String[]{"Seo"};
    public static final String[] CATEGORY_MESSAGE = new String[]{"Category"};
    public static final String[] SERVICE_MESSAGE = new String[]{"Service"};
    private final String[] seoM = { "seo" };

    @Autowired
    public FileAttachRepository fileAttachRepository;

    /**
     */
    @Override
    public Page<SeoResDTO> getAllSeoCategory(Long categoryId, String urlProduct, String titlePage, String descriptionMeta, Pageable pageable) {
        return seoRepository.getAllSeoCategory(categoryId, urlProduct, titlePage, descriptionMeta, pageable);
    }

    /**
     */
    @Override
    public List<FilterResDTO> getCategoryFilter(String name) {
        return seoRepository.getCategoryFilter(name);
    }

    /**
     * @return SeoDTO
     */
    @Override
    public SeoDTO getGeneralSeo(String seoTypeCode) {
        SeoDTO seoDTO = seoRepository.getGeneralSeo(seoTypeCode);
        seoDTO.setKeyWord(seoRepository.getAllKeyword(seoTypeCode));
        return seoDTO;
    }

    /**
     * list seo dịch vụ
     *
     */
    @Override
    public Page<SeoServiceDTO> getAllSeoService(Long categoryId, String serviceName, String pricingName, String urlProduct, String titlePage, String descriptionMeta, Pageable pageable) {
        Page<SeoServiceDTO> seoServiceDTOs = seoRepository.getAllSeoService(categoryId, serviceName, urlProduct, titlePage, pricingName, descriptionMeta, pageable);

        List<Long> serviceId = seoServiceDTOs.stream().map(x -> x.getServiceId()).collect(Collectors.toList());
        List<SeoPricingDTO> pricingDTOList = seoRepository.getAllPricingByServiceId(pricingName, urlProduct, serviceId, titlePage, descriptionMeta);
        seoServiceDTOs.forEach(s -> {
            List<SeoPricingDTO> pricingDTOListNew = new ArrayList<>();
            pricingDTOList.forEach(p -> {
                if(Objects.equals(s.getServiceId(), p.getServiceId()))
                    pricingDTOListNew.add(p);
            });
            s.setPricingList(pricingDTOListNew);
        });
        return seoServiceDTOs;
    }

    /**
     * list catetegory name filter
     *
     */
    @Override
    public List<FilterResDTO> getServiceFilter(String name) {
        return seoRepository.getServiceFilter(name);
    }

    /**
     * Chi tiết SEO của service
     *
     */
    @Override
    public SeoDetailResDTO getSeoServiceDetail(Long serviceId) {
        SeoDetailDTO currentSEO = seoRepository.getSeoServiceByServiceId(serviceId).orElseThrow(() -> {
            String errorMessage = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, SEO_MESSAGE, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(errorMessage, Resources.SEO, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });

        SeoDetailResDTO response = new SeoDetailResDTO();
        response.setId(currentSEO.getId());
        response.setTitlePage(currentSEO.getTitlePage());
        response.setPlanUrl(currentSEO.getPlanUrl());
        response.setMetaDescription(currentSEO.getMetaDescription());
        response.setTitle(currentSEO.getIsAvailableTitle() ? null : currentSEO.getTitle());
        response.setDescription(currentSEO.getIsAvailableDescription() ? null : currentSEO.getDescription());
        response.setObjectId(currentSEO.getServiceId());
        response.setType(SeoObjectTypeEnum.SERVICE);
        response.setIsAvailableTitle(currentSEO.getIsAvailableTitle());
        response.setIsAvailableDescription(currentSEO.getIsAvailableDescription());
        response.setObjectName(currentSEO.getServiceName());

        response.setExternalLink(currentSEO.getExternalLink());
        response.setImagePath(currentSEO.getImagePath());
        response.setIsImage(currentSEO.getIsImage());
        response.setFileAttachId(currentSEO.getFileAttachId());
        response.setFilePathBanner(currentSEO.getFilePathBanner());
        response.setExternalLinkBanner(currentSEO.getExternalLinkBanner());
        response.setObjectDescription(currentSEO.getObjectDescription());

        // lấy ra list keywords
        List<String> seoKeywords = seoRepository.getAllKeywordBySeoId(currentSEO.getId());
        response.setKeywords(seoKeywords);

        List<SeoPricingDTO> seoPricing = seoRepository.getAllPricingByServiceId("", "", Collections.singletonList(serviceId), "", "");
        List<SeoPricingClassDTO> seoPricingClassDTOS = new ArrayList<>();
        seoPricing.forEach(p -> {
            SeoPricingClassDTO seoPricingClass = new SeoPricingClassDTO();
            BeanUtils.copyProperties(p, seoPricingClass);
            seoPricingClassDTOS.add(seoPricingClass);
            List<String> pricingKeywords = seoRepository.getAllKeywordBySeoId(Objects.nonNull(p.getId()) ? p.getId() : -99);
            seoPricingClass.setKeywords(pricingKeywords);
        });

        response.setPricing(seoPricingClassDTOS);

        return response;
    }

    /**
     * Chi tiết SEO của category
     *
     */
    @Override
    public SeoDetailResDTO getSeoCategoryDetail(Long categoryId) {

        SeoCategoryDetailDTO currentSEO = seoRepository.getSeoCategoryByCategoryId(categoryId).orElseThrow(() -> {
            String errorMessage = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, SEO_MESSAGE, LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(errorMessage, Resources.SEO, ErrorKey.ID,
                MessageKeyConstant.NOT_FOUND);
        });

        SeoDetailResDTO response = new SeoDetailResDTO();
        response.setId(currentSEO.getId());
        response.setFileAttachId(currentSEO.getFileAttachId());
        response.setTitlePage(currentSEO.getTitlePage());
        response.setPlanUrl(currentSEO.getPlanUrl());
        response.setMetaDescription(currentSEO.getMetaDescription());
        response.setTitle(currentSEO.getTitle());
        response.setDescription(currentSEO.getDescription());
        response.setObjectId(currentSEO.getCategoryId());
        response.setType(SeoObjectTypeEnum.CATEGORY);
        response.setIsAvailableTitle(currentSEO.getIsAvailableTitle());
        response.setIsAvailableDescription(currentSEO.getIsAvailableDescription());
        response.setObjectName(currentSEO.getCategoryName());

        response.setExternalLink(currentSEO.getExternalLink());
        response.setImagePath(currentSEO.getImagePath());
        response.setIsImage(currentSEO.getIsImage());

        // lấy ra list keywords
        List<String> seoKeywords = seoRepository.getAllKeywordBySeoId(currentSEO.getId());
        response.setKeywords(seoKeywords);

        return response;
    }

    @Override
    public Seo findById(Long id) {
        return seoRepository.findById(id).orElseThrow(() -> {
            String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                    new String[] { Resources.SEO }, null);
            return new ResourceNotFoundException(messageNotFound, Resources.SEO, ErrorKey.ID,
                    MessageKeyConstant.NOT_FOUND);
        });
    }

    @Override
    public Seo save(Seo seo) {
        return seoRepository.save(seo);
    }

    @Override
    @Transactional
    public Long updateSeo(SeoUpdateDTO seoUpdateDTO) {
        Optional<Seo> seo = seoRepository.findById(seoUpdateDTO.getId());
        // Check seo exists or not
        if (!seo.isPresent()) {
            String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, seoM, Locale.US);
            throw new BadRequestException(message, Resources.SEO, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }
        Seo seoUpdate = seo.get();
        // Update seo
        seoUpdate.setTitlePage(seoUpdateDTO.getTitlePage());
        seoUpdate.setPlanUrl(seoUpdateDTO.getPlanUrl());
        seoUpdate.setMetaDescription(seoUpdateDTO.getMetaDescription());
        seoUpdate.setIsAvailableTitle(seoUpdateDTO.getIsAvailableTitle());
        seoUpdate.setIsAvailableDescription(seoUpdateDTO.getIsAvailableDescription());
        seoUpdate.setIsImage(seoUpdateDTO.getIsImage());
        if (Objects.nonNull(seoUpdateDTO.getIsAvailableTitle()) && !seoUpdateDTO.getIsAvailableTitle()) {
            seoUpdate.setTitle(seoUpdateDTO.getTitle());
        }
        if (Objects.nonNull(seoUpdateDTO.getIsAvailableDescription()) && !seoUpdateDTO.getIsAvailableDescription()) {
            seoUpdate.setDescription(seoUpdateDTO.getDescription());
        }
        keyWordRepository.deleteBySeoId(seoUpdateDTO.getId());
        List<KeyWord> keyWordLst = new ArrayList<>();
        if (!CollectionUtils.isEmpty(seoUpdateDTO.getKeywords())) {
            for (KeyWord keyword : seoUpdateDTO.getKeywords()) {
                keyword.setSeoId(seoUpdateDTO.getId());
                keyWordLst.add(keyword);
            }
        }
        seoUpdate.setKeyWords(keyWordLst);
        Seo seoRes = seoRepository.save(seoUpdate);

        // Update file attach of seo
        List<FileAttach> currentAttachs = fileAttachRepository.findAttachesOfSeoId(seoUpdateDTO.getId());
        // truong hop co ca file moi va xoa file cu
        if (!CollectionUtils.isEmpty(seoUpdateDTO.getAttachs())) {
            // ticket da co file
            if (!CollectionUtils.isEmpty(currentAttachs)) {
                fileAttachRepository.deleteAll(deleteStaticFile(currentAttachs,
                        seoUpdateDTO.getAttachs().stream().map(AttachesDTO::getId).collect(Collectors.toList())));
            }
            seoUpdateDTO.getAttachs().forEach(i -> {
                fileAttachRepository.updateFilesAttachOfSeo(seoUpdateDTO.getId(), i.getId());
            });
        } else {
            // truong hop xoa tat ca file cua ticket
            if (!CollectionUtils.isEmpty(currentAttachs)) {
                deleteStaticFile(currentAttachs,
                        currentAttachs.stream().map(FileAttach::getId).collect(Collectors.toList()));
                fileAttachRepository.deleteAll(currentAttachs);
            }
        }
        return seoRes.getId();
    }


    public List<FileAttach> deleteStaticFile(List<FileAttach> currentAttachs, List<Long> attachs) {
        List<FileAttach> fileNeedDelete = new ArrayList<>();
        for (FileAttach attach : currentAttachs) {
            // File nao khong ton tai trong danh sach file moi thi xoa khoi DB va xoa file static
            if (!attachs.contains(attach.getId())) {
                fileNeedDelete.add(attach);
                FileUtil.deleteFileByPath(attach.getFilePath());
            }
        }
        return fileNeedDelete;
    }

    @Override
    public Page<SeoAllDTO> findAllSeo(String modifiedAt, Pageable pageable) {
        Page<SeoAllDTO> res = seoRepository.findAllSeo(modifiedAt, pageable);
        return res;
    }

    public Seo saveSeoDefault(String urlDefault, String titlePageDefault, String description, Long categoryId, Long serviceId, String codeType, boolean isImage) {
        Seo seo = Seo.builder()
            .planUrl(urlDefault)
            .titlePage(titlePageDefault)
            .metaDescription(description)
            .categoriesId(categoryId)
            .serviceId(serviceId)
            .seoTypeCode(codeType)
            .isImage(isImage)
            .isAvailableTitle(true)
            .isAvailableDescription(true)
            .build();
        seoRepository.save(seo);
        return seo;
    }

    /**
     * Lưu thông tin seo cho giải pháp và gói bundling
     * @param codeType
     * @param seoDTO
     * @return
     */
    @Override
    public Seo saveSeoSolution(String codeType, SeoReqDTO seoDTO) {
        Seo seo = Seo.builder()
            .titlePage(seoDTO.getTitlePage())
            .planUrl(seoDTO.getProductUrl())
            .metaDescription(seoDTO.getDescriptionMeta())
            .title(BooleanUtils.isTrue(seoDTO.getIsTitle()) ? null : seoDTO.getTitle())
            .description(BooleanUtils.isTrue(seoDTO.getIsDescription()) ? null : seoDTO.getDescription())
            .seoTypeCode(codeType)
            .isImage(seoDTO.getIsImage())
            .isAvailableTitle(seoDTO.getIsTitle())
            .isAvailableDescription(seoDTO.getIsDescription())
            .build();
        Seo newSeo = seoRepository.save(seo);


        // save file attach of seo
        if (!CollectionUtils.isEmpty(seoDTO.getAttachs())) {
            List<FileAttach> currentAttachs = fileAttachRepository
                .findByIdIn(seoDTO.getAttachs().stream().map(AttachesDTO::getId).collect(Collectors.toList()));

            if (!CollectionUtils.isEmpty(currentAttachs)) {
                currentAttachs.get(0).setSeoId(newSeo.getId());
                fileAttachRepository.save(currentAttachs.get(0));
            }
        }

        //save list keywword
        keyWordRepository.saveAll(
            seoDTO.getKeywords().stream().map(k ->
                new KeyWord(null, newSeo.getId(), 1, k)
            ).collect(Collectors.toList())
        );

        return newSeo;
    }

    /**
     * Chi tiết SEO của Service
     *
     */
    @Override
    public SeoDTO getSeoDetailSme(Long typeId, String typeCode) {
        SeoDTO seoDTO;
        switch (typeCode) {
            case SeoTypeCodeConstant.CAU_HINH_DANH_MUC:
                seoDTO = seoRepository.getSeoDetailHomePageSme(typeId, SeoTypeCodeConstant.All, SeoTypeCodeConstant.All, typeCode);
                break;
            case SeoTypeCodeConstant.CAU_HINH_DICH_VU:
                seoDTO = seoRepository.getSeoDetailHomePageSme(SeoTypeCodeConstant.All, typeId, SeoTypeCodeConstant.All, typeCode);
                break;
            case SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU:
                seoDTO = seoRepository.getSeoDetailHomePageSme(SeoTypeCodeConstant.All, SeoTypeCodeConstant.All, typeId, typeCode);
                break;
            default:
                seoDTO = seoRepository.getSeoDetailHomePageSme(SeoTypeCodeConstant.All, SeoTypeCodeConstant.All, SeoTypeCodeConstant.All, typeCode);
        }
        if (Objects.isNull(seoDTO)) {
            return new SeoDTO();
        }
        seoDTO.setKeyWord(seoRepository.getAllKeywordBySeoId(seoDTO.getSeoId()));
        return seoDTO;
    }

    /**
     * createSeoDefault
     *
     */
    @Override
    @Transactional
    public BaseResponseDTO createSeoDefault() {
        // lay list pricing
        List<PricingNotSeoDTO> pricingDrafts = seoRepository.getAllPricingDraftNotSeo();
        List<PricingDraft> pricingDraftList = new ArrayList<>();
        // lưu SEO cho list pricingDaft chưa có seo
        if (!CollectionUtils.isEmpty(pricingDrafts)) {
            pricingDrafts.forEach(pd -> {
                Optional<PricingDraft> pricingDraft = pricingDraftRepository
                    .findByIdAndDeletedFlag(pd.getId(), DeletedFlag.NOT_YET_DELETED.getValue());
                String pricingName = StringUtil.replace(pd.getPricingName());
                Seo seoNew = saveSeoDefault(pricingName, //createSeoDefault Pricing
                    pd.getPricingName() + SeoTypeCodeConstant.TITLE_PAGE_PRICING, pd.getDescription(), null,
                    pd.getServiceId(), SeoTypeCodeConstant.CAU_HINH_GOI_DICH_VU, true);
                if (pricingDraft.isPresent()) {
                    pricingDraft.get().setSeoId(seoNew.getId());
                    pricingDraftList.add(pricingDraft.get());
                }
            });
            pricingDraftRepository.saveAll(pricingDraftList);
        }
        List<PricingDraft> pricingDraftListDB = pricingDraftRepository.findAllByDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());
        if (!CollectionUtils.isEmpty(pricingDraftListDB)) {
            pricingDraftListDB.forEach(pDb -> {
                seoRepository.updatePricingNotSeo(pDb.getSeoId(), pDb.getId());
            });
        }

        //create seo cua service chua co seo
        List<PricingNotSeoDTO> serviceEntities = seoRepository.getAllServiceSeo();
        if (!CollectionUtils.isEmpty(serviceEntities)) {
            serviceEntities.forEach(s -> {
                Optional<ServiceEntity> serviceEntity = serviceRepository.findByIdAndDeletedFlag(s.getId(), DeletedFlag.NOT_YET_DELETED
                    .getValue());
                if (serviceEntity.isPresent()) {
                    String serviceName = StringUtil.replace(s.getServiceName());
                    saveSeoDefault(serviceName, s.getServiceName() + SeoTypeCodeConstant.TITLE_SERVICE,//service createDefault
                        Objects.nonNull(serviceEntity.get().getDescription()) ? serviceEntity.get().getSapoDescription() : SeoTypeCodeConstant.DESCRIPTION_CATEGORY,
                        null, s.getId(), SeoTypeCodeConstant.CAU_HINH_DICH_VU, true);
                }
            });
        }

        //create seo cua category chua co seo
        List<PricingNotSeoDTO> categoryList = seoRepository.getAllCategorySeo();
        if (!CollectionUtils.isEmpty(categoryList)) {
            categoryList.forEach(c -> {
                Optional<Category> category = categoryRepository.findByIdAndDeletedFlag(c.getId(), DeletedFlag.NOT_YET_DELETED.getValue());
                if (category.isPresent()) {
                    String categoryName = StringUtil.replace(category.get().getName());
                    //insertSeo mặc định
                    saveSeoDefault(categoryName, SeoTypeCodeConstant.TITLE_CATEGORY + category.get().getName(), //category createDefault
                        SeoTypeCodeConstant.DESCRIPTION_CATEGORY, category.get().getId(), null, SeoTypeCodeConstant.CAU_HINH_DANH_MUC, true);
                }
            });
        }
            return new BaseResponseDTO("success");
    }
}

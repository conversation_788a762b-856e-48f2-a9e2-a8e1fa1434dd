package com.service.events;

import com.entity.events.Events;
import com.enums.EventTypeEnum;

import java.util.List;

public interface EventsService {

    /**
     * L<PERSON><PERSON> thông tin event
     * @param type Loại event
     * @param metadata Thông tin metadata của event
     * @return Events đã được lưu
     */
    Events saveEvent(EventTypeEnum type, Object metadata);

    /**
     * <PERSON><PERSON><PERSON> danh sách các events chưa được xử lý (status = 0)
     * @return Danh sách events chưa được xử lý
     */
    List<Events> getUnprocessedEvents();

    /**
     * Cập nhật status của event sau khi xử lý
     * @param eventId ID của event cần cập nhật
     * @param status Status mới (1 = đã xử lý thành công, -1 = xử lý thất bại)
     * @return Events đã được cập nhật
     */
    Events updateEventStatus(Long eventId, Short status);

}

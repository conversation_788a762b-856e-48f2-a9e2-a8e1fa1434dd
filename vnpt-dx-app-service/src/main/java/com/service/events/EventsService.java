package com.service.events;

import com.entity.events.Events;
import com.enums.EventTypeEnum;

public interface EventsService {

    /**
     * <PERSON><PERSON><PERSON> thông tin event
     * @param createdBy ID của người tạo event
     * @param type Loại event
     * @param metadata Thông tin metadata của event
     * @return Events đã được lưu
     */
    Events saveEvent(Long createdBy, EventTypeEnum type, Object metadata);

}

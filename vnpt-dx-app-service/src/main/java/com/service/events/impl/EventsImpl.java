package com.service.events.impl;

import com.entity.events.Events;
import com.enums.EventTypeEnum;
import com.repository.events.EventsRepository;
import com.service.events.EventsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class EventsImpl implements EventsService {
    @Autowired
    private EventsRepository eventsRepository;

    @Override
    public Events saveEvent(Long createdBy, EventTypeEnum type, Object metadata) {
        try {
            log.info("Bắt đầu lưu event với type: {}, createdBy: {}", type, createdBy);

            // Tạo đối tượng Events mới
            Events event = new Events();
            event.setCreatedBy(createdBy);
            event.setType(type);
            event.setMetadata(metadata);
            event.setCreatedAt(System.currentTimeMillis());
            event.setPartitionId(new Date()); // Sử dụng ngày hiện tại làm partition
            event.setStatus((short) 1); // Mặc định status = 1 (active)

            // Lưu vào database
            Events savedEvent = eventsRepository.save(event);

            log.info("Đã lưu thành công event với ID: {}", savedEvent.getId());
            return savedEvent;

        } catch (Exception e) {
            log.error("Lỗi khi lưu event: {}", e.getMessage(), e);
            throw new RuntimeException("Không thể lưu event: " + e.getMessage(), e);
        }
    }

}

package com.service.events.impl;

import com.entity.events.Events;
import com.enums.EventTypeEnum;
import com.repository.events.EventsRepository;
import com.service.events.EventsService;
import com.util.AuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class EventsImpl implements EventsService {
    @Autowired
    private EventsRepository eventsRepository;

    @Override
    public Events saveEvent(EventTypeEnum type, Object metadata) {
        try {
            // Lấy ID của user hiện tại
            Long currentUserId = AuthUtil.getCurrentUserId();
            if (currentUserId == null) {
                throw new RuntimeException("Không thể xác định user hiện tại");
            }

            log.info("Bắt đầu lưu event với type: {}, createdBy: {}", type, currentUserId);

            // Tạo đối tượng Events mới
            Events event = new Events();
            event.setCreatedBy(currentUserId);
            event.setType(type);
            event.setMetadata(metadata);
            event.setCreatedAt(System.currentTimeMillis());
            event.setPartitionId(new Date()); // Sử dụng ngày hiện tại làm partition
            event.setStatus(0); // Mặc định status = 0 (chưa xử lý)

            return eventsRepository.save(event);
        } catch (Exception e) {
            log.error("Lỗi khi lưu event: {}", e.getMessage(), e);
            throw new RuntimeException("Không thể lưu event: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Events> getUnprocessedEvents() {
        try {
            log.info("Bắt đầu lấy danh sách events chưa được xử lý");
            List<Events> unprocessedEvents = eventsRepository.findUnprocessedEvents();
            log.info("{} events chưa được xử lý", unprocessedEvents.size());
            return unprocessedEvents;
        } catch (Exception e) {
            log.error("Lỗi khi lấy danh sách events chưa được xử lý: {}", e.getMessage(), e);
            throw new RuntimeException("Không thể lấy danh sách events chưa được xử lý: " + e.getMessage(), e);
        }
    }

    @Override
    public Events updateEventStatus(Long eventId, Integer status) {
        return null;
    }

}

package com.service.utils.condition;

import static com.service.utils.constants.OperandConstant.OperandEnum.ACCOUNT_ACTIVE_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.ACCOUNT_CREATED_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.ACCOUNT_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.ADDRESS;
import static com.service.utils.constants.OperandConstant.OperandEnum.ADMIN_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.ADMIN_ROLE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.AFFILIATE_LEVEL;
import static com.service.utils.constants.OperandConstant.OperandEnum.AFFILIATE_SEGMENTATION;
import static com.service.utils.constants.OperandConstant.OperandEnum.AFFILIATE_STATUS;
import static com.service.utils.constants.OperandConstant.OperandEnum.AFFILIATE_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.AM_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPLIED_COUPON_ITEM_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPLIED_COUPON_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPLIED_COUPON_SET_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPROVED_CHANGE_INFORMATION;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPROVED_CHANGE_PRICING_INFORMATION;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPROVED_DEV;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPROVED_DEV_PRICING;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPROVED_PRICING_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.APPROVED_SERVICE_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.BIRTH_DAY;
import static com.service.utils.constants.OperandConstant.OperandEnum.BUSINESS_FIELD;
import static com.service.utils.constants.OperandConstant.OperandEnum.BUSINESS_REGIS_ADDRESS;
import static com.service.utils.constants.OperandConstant.OperandEnum.CANCEL_ACCOUNT_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CITY_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_ADDRESS;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_ASSIGNEE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_CREATED_SOURCE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_EMAIL;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_ENTERPRISE_DISTRICT_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_ENTERPRISE_IDENTITY_NO;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_ENTERPRISE_PROVINCE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_ENTERPRISE_STREET_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_ENTERPRISE_WARD_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_INTEREST_PRODUCT;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_ORGANIZATION;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_PHONE_NUMBER;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_POSITION;
import static com.service.utils.constants.OperandConstant.OperandEnum.CONTACT_STATUS;
import static com.service.utils.constants.OperandConstant.OperandEnum.COUPON_AMOUNT;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_CODE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_GROUP_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_ORGANIZATION_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_PARTITION;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_STATUS;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_CONTENT;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_CREATED_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_CREATED_SOURCE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_CUSTOMER_CLASSIFICATION;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_PRIORITY;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_SERVICE_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_STATUS;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_TITLE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TICKET_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.CUSTOMER_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.DEPLOYMENT_ADDRESS;
import static com.service.utils.constants.OperandConstant.OperandEnum.DISTRICT_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.EMAIL;
import static com.service.utils.constants.OperandConstant.OperandEnum.ENTERPRISE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.ENTERPRISE_SIZE;
import static com.service.utils.constants.OperandConstant.OperandEnum.FAVOURITE_PRICING_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.FAVOURITE_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.FIRST_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.FIRST_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.INVOICE_ADDRESS;
import static com.service.utils.constants.OperandConstant.OperandEnum.LAST_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.NATION_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.NOT_LOGIN;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_ACTIVE_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_CANCEL_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_CHANGE_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_OFFICIAL_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_REACTIVE_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_REGISTERED_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_RENEW_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_TRIAL_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.NUMBER_WAIT_SUBSCRIPTION;
import static com.service.utils.constants.OperandConstant.OperandEnum.PAID_AMOUNT;
import static com.service.utils.constants.OperandConstant.OperandEnum.PAYMENT_METHOD;
import static com.service.utils.constants.OperandConstant.OperandEnum.PHONE_NUMBER;
import static com.service.utils.constants.OperandConstant.OperandEnum.PURCHASED_CATEGORY_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.PURCHASED_PRICING_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.PURCHASED_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.PURCHASED_SERVICE_TYPE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.REACTIVE_ACCOUNT_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.REF_EMPLOYEE_CODE;
import static com.service.utils.constants.OperandConstant.OperandEnum.REPRESENTATIVE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.REVIEWED_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SECURITY_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_CATEGORY_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_DEVELOPER_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_NUM_SERVICE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_NUM_UNIT;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_PAYMENT_CYCLE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_PAYMENT_TIME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_PRICING;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_PRICING_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_PRICING_PLAN;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_REVIEW_SCORE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_SERVICE_ID;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_SERVICE_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_SETUP_FEE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SER_TRIAL_TIME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SOCIAL_INSURANCE_CODE;
import static com.service.utils.constants.OperandConstant.OperandEnum.STREET_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_ADDON_COST;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_ADDON_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_ADDRESS;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_BUSINESS_FIELD;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_BUSINESS_REGIS_ADDRESS;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_CANCEL_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_CATEGORY_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_CODE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_CREATED_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_CURRENT_PAYMENT_CYCLE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_DEPLOYMENT_ADDRESS;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_DISCOUNT_ADDON;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_DISCOUNT_MAIN;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_DISCOUNT_WHOLE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_EMAIL;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_ENTERPRISE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_ENTERPRISE_SIZE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_EXPIRED_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_EXPIRED_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_MAIN_AFTER_TAX;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_MAIN_ALL_ADDON_FEE_AFTER_TAX;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_MAIN_ALL_ADDON_PRE_TAX;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_MAIN_FEE_AFTER_TAX;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_MAIN_FEE_PRE_TAX;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_MAIN_MAN_ADDON_FEE_AFTER_TAX;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_MAIN_MAN_ADDON_PRE_TAX;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_MAIN_PRE_TAX;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_NUM_ADDON;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_NUM_CHANGE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_NUM_CYCLE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_NUM_REGISTRATION;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_NUM_RENEW;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_NUM_UNIT;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_ORDER_STATUS;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_PAYMENT_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_PAYMENT_METHOD;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_PAYMENT_STATUS;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_PHONE_NUMBER;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_PRICING_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_PRICING_PLAN;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_REACTIVE_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_REACTIVE_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_REF_EMPLOYEE_CODE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_RENEW_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_RENEW_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_REPRESENTATIVE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_SERVICE_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_SERVICE_TYPE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_SETUP_FEE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_SOCIAL_INSURANCE_CODE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_STATUS;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_TAX_CODE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_TOTAL_PAID;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_UPDATED_DATE;
import static com.service.utils.constants.OperandConstant.OperandEnum.SUB_WEBSITE;
import static com.service.utils.constants.OperandConstant.OperandEnum.TAX_CODE;
import static com.service.utils.constants.OperandConstant.OperandEnum.TIME_NOT_ACCESSED;
import static com.service.utils.constants.OperandConstant.OperandEnum.WARD_NAME;
import static com.service.utils.constants.OperandConstant.OperandEnum.WEBSITE;

import java.lang.reflect.Constructor;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Component;
import com.onedx.common.utils.GsonMapperUtil;
import com.service.utils.condition.impl.affiliate.AffLevelCondItem;
import com.service.utils.condition.impl.affiliate.AffSegmentationCondItem;
import com.service.utils.condition.impl.affiliate.AffTypeCondItem;
import com.service.utils.condition.impl.affiliate.AffiliateStatusCondItem;
import com.service.utils.condition.impl.approved.ApprovedChangeInformationCondItem;
import com.service.utils.condition.impl.approved.ApprovedDevCondItem;
import com.service.utils.condition.impl.approved.ApprovedPricingTypeCondItem;
import com.service.utils.condition.impl.approved.ApprovedServiceTypeCondItem;
import com.service.utils.condition.impl.customer.AccountTypeCondItem;
import com.service.utils.condition.impl.customer.ActiveAccountDateCondItem;
import com.service.utils.condition.impl.customer.AddressCondItem;
import com.service.utils.condition.impl.customer.AppliedCouponCodeCondItem;
import com.service.utils.condition.impl.customer.AppliedCouponCondItem;
import com.service.utils.condition.impl.customer.AppliedCouponSetCondItem;
import com.service.utils.condition.impl.customer.BirthDayCondItem;
import com.service.utils.condition.impl.customer.BusinessAreaCondItem;
import com.service.utils.condition.impl.customer.BusinessRegistrationAddressCondItem;
import com.service.utils.condition.impl.customer.BusinessSizeCondItem;
import com.service.utils.condition.impl.customer.CancelAccountDateCondItem;
import com.service.utils.condition.impl.customer.ContactEmailCondItem;
import com.service.utils.condition.impl.customer.ContactNameCondItem;
import com.service.utils.condition.impl.customer.CreateAccountDateCondItem;
import com.service.utils.condition.impl.customer.CustomerClassificationCondItem;
import com.service.utils.condition.impl.customer.CustomerCodeCondItem;
import com.service.utils.condition.impl.customer.CustomerGroupNameCondItem;
import com.service.utils.condition.impl.customer.CustomerStatusCondItem;
import com.service.utils.condition.impl.customer.CustomerTypeCondItem;
import com.service.utils.condition.impl.customer.DataPartitionCondItem;
import com.service.utils.condition.impl.customer.DistrictNameCondItem;
import com.service.utils.condition.impl.customer.EmailCondItem;
import com.service.utils.condition.impl.customer.EnterpriseNameCondItem;
import com.service.utils.condition.impl.customer.FirstNameCondItem;
import com.service.utils.condition.impl.customer.FirstServiceNameCondItem;
import com.service.utils.condition.impl.customer.InvoiceDeliveryAddressCondItem;
import com.service.utils.condition.impl.customer.LastNameCondItem;
import com.service.utils.condition.impl.customer.LikedPricingCondItem;
import com.service.utils.condition.impl.customer.LikedServiceCondItem;
import com.service.utils.condition.impl.customer.NationCondItem;
import com.service.utils.condition.impl.customer.NotLoginCondItem;
import com.service.utils.condition.impl.customer.NumActiveSubCondItem;
import com.service.utils.condition.impl.customer.NumCancelSubCondItem;
import com.service.utils.condition.impl.customer.NumChangeSubCondItem;
import com.service.utils.condition.impl.customer.NumOfficialSubCondItem;
import com.service.utils.condition.impl.customer.NumReactivateSubCondItem;
import com.service.utils.condition.impl.customer.NumRenewSubCondItem;
import com.service.utils.condition.impl.customer.NumSubscriptionCondItem;
import com.service.utils.condition.impl.customer.NumTrialSubCondItem;
import com.service.utils.condition.impl.customer.NumWaitSubCondItem;
import com.service.utils.condition.impl.customer.PaymentMethodCondItem;
import com.service.utils.condition.impl.customer.PhoneNumberCondItem;
import com.service.utils.condition.impl.customer.ProvinceNameCondItem;
import com.service.utils.condition.impl.customer.PurchasedCategoryCondItem;
import com.service.utils.condition.impl.customer.PurchasedPricingNameCondItem;
import com.service.utils.condition.impl.customer.PurchasedServiceNameCondItem;
import com.service.utils.condition.impl.customer.PurchasedServiceTypeCondItem;
import com.service.utils.condition.impl.customer.ReactiveAccountDateCondItem;
import com.service.utils.condition.impl.customer.RefEmployeeCodeCondItem;
import com.service.utils.condition.impl.customer.RepresentativeNameCondItem;
import com.service.utils.condition.impl.customer.ReviewServiceCondItem;
import com.service.utils.condition.impl.customer.SecurityCondItem;
import com.service.utils.condition.impl.customer.SetupAddressCondItem;
import com.service.utils.condition.impl.customer.SocialInsuranceCodeCondItem;
import com.service.utils.condition.impl.customer.StreetNameCondItem;
import com.service.utils.condition.impl.customer.TaxCodeCondItem;
import com.service.utils.condition.impl.customer.TimeNotAccessedCondItem;
import com.service.utils.condition.impl.customer.TotalDiscountCondItem;
import com.service.utils.condition.impl.customer.TotalPaidCondItem;
import com.service.utils.condition.impl.customer.WardNameCondItem;
import com.service.utils.condition.impl.customer.WebsiteCondItem;
import com.service.utils.condition.impl.customerTicket.TicketContentCondItem;
import com.service.utils.condition.impl.customerTicket.TicketCreatedDateCondItem;
import com.service.utils.condition.impl.customerTicket.TicketCreatedSourceCondItem;
import com.service.utils.condition.impl.customerTicket.TicketPriorityCondItem;
import com.service.utils.condition.impl.customerTicket.TicketServiceNameCondItem;
import com.service.utils.condition.impl.customerTicket.TicketServiceTypeCondItem;
import com.service.utils.condition.impl.customerTicket.TicketStatusCondItem;
import com.service.utils.condition.impl.customerTicket.TicketTitleCondItem;
import com.service.utils.condition.impl.customerTicket.TicketTypeCondItem;
import com.service.utils.condition.impl.cutomerContact.ContactAddressCondItem;
import com.service.utils.condition.impl.cutomerContact.ContactAssigneeCondItem;
import com.service.utils.condition.impl.cutomerContact.ContactCreatedSourceCondItem;
import com.service.utils.condition.impl.cutomerContact.ContactOrganizationCondItem;
import com.service.utils.condition.impl.cutomerContact.ContactPhoneCondItem;
import com.service.utils.condition.impl.cutomerContact.ContactPositionCondItem;
import com.service.utils.condition.impl.cutomerContact.ContactStatusCondItem;
import com.service.utils.condition.impl.cutomerContact.EnterpriseDistrictNameCondItem;
import com.service.utils.condition.impl.cutomerContact.EnterpriseIdentityNoCondItem;
import com.service.utils.condition.impl.cutomerContact.EnterpriseProvinceNameCondItem;
import com.service.utils.condition.impl.cutomerContact.EnterpriseStreetNameCondItem;
import com.service.utils.condition.impl.cutomerContact.EnterpriseWardNameCondItem;
import com.service.utils.condition.impl.cutomerContact.InterestProductCondItem;
import com.service.utils.condition.impl.permission.AdminNameCondItem;
import com.service.utils.condition.impl.permission.AdminRoleNameCondItem;
import com.service.utils.condition.impl.permission.AmNameCondItem;
import com.service.utils.condition.impl.service.SerDeveloperNameCondItem;
import com.service.utils.condition.impl.service.SerNumServiceCondItem;
import com.service.utils.condition.impl.service.SerNumUnitCondItem;
import com.service.utils.condition.impl.service.SerPricingCondItem;
import com.service.utils.condition.impl.service.SerPricingPlanCondItem;
import com.service.utils.condition.impl.service.SerReviewScoreCondItem;
import com.service.utils.condition.impl.service.SerServiceIdCondItem;
import com.service.utils.condition.impl.service.SerSetupFeeCondItem;
import com.service.utils.condition.impl.service.SerTrialTimeCondItem;
import com.service.utils.condition.impl.subscription.SubAddonCostCondItem;
import com.service.utils.condition.impl.subscription.SubAddonTypeCondItem;
import com.service.utils.condition.impl.subscription.SubCancelServiceNameCondItem;
import com.service.utils.condition.impl.subscription.SubCodeCondItem;
import com.service.utils.condition.impl.subscription.SubCreateDateCondItem;
import com.service.utils.condition.impl.subscription.SubCurrentPaymentCycleCondItem;
import com.service.utils.condition.impl.subscription.SubDiscountAddonCondItem;
import com.service.utils.condition.impl.subscription.SubDiscountMainCondItem;
import com.service.utils.condition.impl.subscription.SubDiscountWholeCondItem;
import com.service.utils.condition.impl.subscription.SubExpiredDateCondItem;
import com.service.utils.condition.impl.subscription.SubExpiredServiceNameCondItem;
import com.service.utils.condition.impl.subscription.SubMainAfterTaxCondItem;
import com.service.utils.condition.impl.subscription.SubMainAllAddonFeeAfterTaxCondItem;
import com.service.utils.condition.impl.subscription.SubMainAllAddonPreTaxCondItem;
import com.service.utils.condition.impl.subscription.SubMainFeeAfterTaxCondItem;
import com.service.utils.condition.impl.subscription.SubMainFeePreTaxCondItem;
import com.service.utils.condition.impl.subscription.SubMainManAddonFeeAfterTaxCondItem;
import com.service.utils.condition.impl.subscription.SubMainManAddonPreTaxCondItem;
import com.service.utils.condition.impl.subscription.SubMainPreTaxCondItem;
import com.service.utils.condition.impl.subscription.SubNumAddonCondItem;
import com.service.utils.condition.impl.subscription.SubNumChangeCondItem;
import com.service.utils.condition.impl.subscription.SubNumPaymentCycleCondItem;
import com.service.utils.condition.impl.subscription.SubNumRenewCondItem;
import com.service.utils.condition.impl.subscription.SubNumUnitCondItem;
import com.service.utils.condition.impl.subscription.SubOrderStatusCondItem;
import com.service.utils.condition.impl.subscription.SubPaymentDateCondItem;
import com.service.utils.condition.impl.subscription.SubPaymentStatusCondItem;
import com.service.utils.condition.impl.subscription.SubReactiveDateCondItem;
import com.service.utils.condition.impl.subscription.SubReactiveServiceNameCondItem;
import com.service.utils.condition.impl.subscription.SubRenewDateCondItem;
import com.service.utils.condition.impl.subscription.SubRenewServiceNameCondItem;
import com.service.utils.condition.impl.subscription.SubSetupFeeCondItem;
import com.service.utils.condition.impl.subscription.SubTotalPaidCondItem;
import com.service.utils.condition.impl.subscription.SubUpdateDateCondItem;
import com.service.utils.condition.impl.subscription.SubscriptionStatusCondItem;
import com.service.utils.jsonObject.McIfConditionDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ConditionItemFactory {

    private final ConcurrentHashMap<Integer, Class<?>> mapOperandClass = new ConcurrentHashMap<>();
    @Getter
    private String cause;

    public ConditionItemFactory() {
        mapOperandClass.put(ACCOUNT_CREATED_DATE.getValue(), CreateAccountDateCondItem.class);
        mapOperandClass.put(FIRST_SERVICE_NAME.getValue(), FirstServiceNameCondItem.class);
        mapOperandClass.put(NUMBER_REGISTERED_SUBSCRIPTION.getValue(), NumSubscriptionCondItem.class);
        mapOperandClass.put(NUMBER_ACTIVE_SUBSCRIPTION.getValue(), NumActiveSubCondItem.class);
        mapOperandClass.put(NUMBER_TRIAL_SUBSCRIPTION.getValue(), NumTrialSubCondItem.class);
        mapOperandClass.put(NUMBER_OFFICIAL_SUBSCRIPTION.getValue(), NumOfficialSubCondItem.class);
        mapOperandClass.put(NUMBER_WAIT_SUBSCRIPTION.getValue(), NumWaitSubCondItem.class);
        mapOperandClass.put(NUMBER_RENEW_SUBSCRIPTION.getValue(), NumRenewSubCondItem.class);
        mapOperandClass.put(NUMBER_CANCEL_SUBSCRIPTION.getValue(), NumCancelSubCondItem.class);
        mapOperandClass.put(NUMBER_REACTIVE_SUBSCRIPTION.getValue(), NumReactivateSubCondItem.class);
        mapOperandClass.put(NUMBER_CHANGE_SUBSCRIPTION.getValue(), NumChangeSubCondItem.class);
        mapOperandClass.put(PAID_AMOUNT.getValue(), TotalPaidCondItem.class);
        mapOperandClass.put(COUPON_AMOUNT.getValue(), TotalDiscountCondItem.class);
        mapOperandClass.put(APPLIED_COUPON_NAME.getValue(), AppliedCouponCondItem.class);
        mapOperandClass.put(APPLIED_COUPON_SET_NAME.getValue(), AppliedCouponSetCondItem.class);
        mapOperandClass.put(APPLIED_COUPON_ITEM_NAME.getValue(), AppliedCouponCodeCondItem.class);
        mapOperandClass.put(REVIEWED_SERVICE_NAME.getValue(), ReviewServiceCondItem.class);
        //mapOperandClass.put(SEARCH_KEYWORD.getValue(), McPurchasedServiceTypeCondItem.class);
        //mapOperandClass.put(SEARCH_SERVICE_NAME.getValue(), McPurchasedServiceNameCondItem.class);
        mapOperandClass.put(FAVOURITE_SERVICE_NAME.getValue(), LikedServiceCondItem.class);
        mapOperandClass.put(FAVOURITE_PRICING_NAME.getValue(), LikedPricingCondItem.class);
        mapOperandClass.put(PURCHASED_CATEGORY_NAME.getValue(), PurchasedCategoryCondItem.class);
        mapOperandClass.put(PURCHASED_SERVICE_TYPE_NAME.getValue(), PurchasedServiceTypeCondItem.class);
        mapOperandClass.put(PURCHASED_SERVICE_NAME.getValue(), PurchasedServiceNameCondItem.class);
        mapOperandClass.put(PURCHASED_PRICING_NAME.getValue(), PurchasedPricingNameCondItem.class);
        mapOperandClass.put(CUSTOMER_CODE.getValue(), CustomerCodeCondItem.class);
        mapOperandClass.put(EMAIL.getValue(), EmailCondItem.class);
        mapOperandClass.put(BIRTH_DAY.getValue(), BirthDayCondItem.class);
        mapOperandClass.put(FIRST_NAME.getValue(), FirstNameCondItem.class);
        mapOperandClass.put(LAST_NAME.getValue(), LastNameCondItem.class);
        mapOperandClass.put(ENTERPRISE_NAME.getValue(), EnterpriseNameCondItem.class);
        mapOperandClass.put(NATION_NAME.getValue(), NationCondItem.class);
        mapOperandClass.put(CITY_NAME.getValue(), ProvinceNameCondItem.class);
        mapOperandClass.put(DISTRICT_NAME.getValue(), DistrictNameCondItem.class);
        mapOperandClass.put(WARD_NAME.getValue(), WardNameCondItem.class);
        mapOperandClass.put(STREET_NAME.getValue(), StreetNameCondItem.class);
        mapOperandClass.put(ADDRESS.getValue(), AddressCondItem.class);
        mapOperandClass.put(PHONE_NUMBER.getValue(), PhoneNumberCondItem.class);
        mapOperandClass.put(SOCIAL_INSURANCE_CODE.getValue(), SocialInsuranceCodeCondItem.class);
        mapOperandClass.put(WEBSITE.getValue(), WebsiteCondItem.class);
        mapOperandClass.put(TAX_CODE.getValue(), TaxCodeCondItem.class);
        mapOperandClass.put(ENTERPRISE_SIZE.getValue(), BusinessSizeCondItem.class);
        mapOperandClass.put(BUSINESS_FIELD.getValue(), BusinessAreaCondItem.class);
        mapOperandClass.put(REPRESENTATIVE_NAME.getValue(), RepresentativeNameCondItem.class);
        mapOperandClass.put(DEPLOYMENT_ADDRESS.getValue(), SetupAddressCondItem.class);
        mapOperandClass.put(BUSINESS_REGIS_ADDRESS.getValue(), BusinessRegistrationAddressCondItem.class);
        mapOperandClass.put(CANCEL_ACCOUNT_DATE.getValue(), CancelAccountDateCondItem.class);
        mapOperandClass.put(REACTIVE_ACCOUNT_DATE.getValue(), ReactiveAccountDateCondItem.class);
        mapOperandClass.put(PAYMENT_METHOD.getValue(), PaymentMethodCondItem.class);
        mapOperandClass.put(REF_EMPLOYEE_CODE.getValue(), RefEmployeeCodeCondItem.class);
        mapOperandClass.put(CUSTOMER_GROUP_NAME.getValue(), CustomerGroupNameCondItem.class);
        mapOperandClass.put(NOT_LOGIN.getValue(), NotLoginCondItem.class);
        mapOperandClass.put(CUSTOMER_TYPE.getValue(), CustomerClassificationCondItem.class);
        mapOperandClass.put(CONTACT_NAME.getValue(), ContactNameCondItem.class);
        mapOperandClass.put(CONTACT_EMAIL.getValue(), ContactEmailCondItem.class);
        mapOperandClass.put(CUSTOMER_ORGANIZATION_TYPE.getValue(), CustomerTypeCondItem.class);
        mapOperandClass.put(INVOICE_ADDRESS.getValue(), InvoiceDeliveryAddressCondItem.class);
        mapOperandClass.put(CUSTOMER_PARTITION.getValue(), DataPartitionCondItem.class);
        mapOperandClass.put(ACCOUNT_ACTIVE_DATE.getValue(), ActiveAccountDateCondItem.class);
        mapOperandClass.put(CUSTOMER_STATUS.getValue(), CustomerStatusCondItem.class);
        mapOperandClass.put(ACCOUNT_TYPE.getValue(), AccountTypeCondItem.class);
        mapOperandClass.put(SECURITY_TYPE.getValue(), SecurityCondItem.class);


        mapOperandClass.put(CONTACT_PHONE_NUMBER.getValue(), ContactPhoneCondItem.class);
        mapOperandClass.put(CONTACT_ADDRESS.getValue(), ContactAddressCondItem.class);
        mapOperandClass.put(CONTACT_ORGANIZATION.getValue(), ContactOrganizationCondItem.class);
        mapOperandClass.put(CONTACT_POSITION.getValue(), ContactPositionCondItem.class);
        mapOperandClass.put(CONTACT_CREATED_SOURCE.getValue(), ContactCreatedSourceCondItem.class);
        mapOperandClass.put(CONTACT_ASSIGNEE.getValue(), ContactAssigneeCondItem.class);
        mapOperandClass.put(CONTACT_ENTERPRISE_PROVINCE_NAME.getValue(), EnterpriseProvinceNameCondItem.class);
        mapOperandClass.put(CONTACT_ENTERPRISE_DISTRICT_NAME.getValue(), EnterpriseDistrictNameCondItem.class);
        mapOperandClass.put(CONTACT_ENTERPRISE_WARD_NAME.getValue(), EnterpriseWardNameCondItem.class);
        mapOperandClass.put(CONTACT_ENTERPRISE_STREET_NAME.getValue(), EnterpriseStreetNameCondItem.class);
        mapOperandClass.put(CONTACT_ENTERPRISE_IDENTITY_NO.getValue(), EnterpriseIdentityNoCondItem.class);
        mapOperandClass.put(CONTACT_INTEREST_PRODUCT.getValue(), InterestProductCondItem.class);
        mapOperandClass.put(CONTACT_STATUS.getValue(), ContactStatusCondItem.class);

        mapOperandClass.put(SUB_MAIN_AFTER_TAX.getValue(), SubMainAfterTaxCondItem.class);
        mapOperandClass.put(SUB_MAIN_PRE_TAX.getValue(), SubMainPreTaxCondItem.class);
        mapOperandClass.put(SUB_MAIN_FEE_PRE_TAX.getValue(), SubMainFeePreTaxCondItem.class);
        mapOperandClass.put(SUB_MAIN_FEE_AFTER_TAX.getValue(), SubMainFeeAfterTaxCondItem.class);
        mapOperandClass.put(SUB_MAIN_MAN_ADDON_PRE_TAX.getValue(), SubMainManAddonPreTaxCondItem.class);
        mapOperandClass.put(SUB_MAIN_MAN_ADDON_FEE_AFTER_TAX.getValue(), SubMainManAddonFeeAfterTaxCondItem.class);
        mapOperandClass.put(SUB_MAIN_ALL_ADDON_PRE_TAX.getValue(), SubMainAllAddonPreTaxCondItem.class);
        mapOperandClass.put(SUB_MAIN_ALL_ADDON_FEE_AFTER_TAX.getValue(), SubMainAllAddonFeeAfterTaxCondItem.class);
        mapOperandClass.put(SUB_TOTAL_PAID.getValue(), SubTotalPaidCondItem.class);
        mapOperandClass.put(SUB_DISCOUNT_MAIN.getValue(), SubDiscountMainCondItem.class);
        mapOperandClass.put(SUB_DISCOUNT_ADDON.getValue(), SubDiscountAddonCondItem.class);
        mapOperandClass.put(SUB_DISCOUNT_WHOLE.getValue(), SubDiscountWholeCondItem.class);
        mapOperandClass.put(SUB_NUM_RENEW.getValue(), SubNumRenewCondItem.class);
        mapOperandClass.put(SUB_NUM_REGISTRATION.getValue(), NumOfficialSubCondItem.class);
        mapOperandClass.put(SUB_CATEGORY_NAME.getValue(), PurchasedCategoryCondItem.class);
        mapOperandClass.put(SUB_SERVICE_TYPE.getValue(), PurchasedServiceTypeCondItem.class);
        mapOperandClass.put(SUB_NUM_CHANGE.getValue(), SubNumChangeCondItem.class);
        mapOperandClass.put(SUB_CREATED_DATE.getValue(), SubCreateDateCondItem.class);
        mapOperandClass.put(SUB_SERVICE_NAME.getValue(), PurchasedServiceNameCondItem.class);
        mapOperandClass.put(SUB_PRICING_NAME.getValue(), PurchasedPricingNameCondItem.class);
        mapOperandClass.put(SUB_CODE.getValue(), SubCodeCondItem.class);
        mapOperandClass.put(SUB_CURRENT_PAYMENT_CYCLE.getValue(), SubCurrentPaymentCycleCondItem.class);
        mapOperandClass.put(SUB_NUM_CYCLE.getValue(), SubNumPaymentCycleCondItem.class);
        mapOperandClass.put(SUB_PAYMENT_METHOD.getValue(), PaymentMethodCondItem.class);
        mapOperandClass.put(SUB_PAYMENT_STATUS.getValue(), SubPaymentStatusCondItem.class);
        mapOperandClass.put(SUB_REF_EMPLOYEE_CODE.getValue(), RefEmployeeCodeCondItem.class);
        mapOperandClass.put(SUB_UPDATED_DATE.getValue(), SubUpdateDateCondItem.class);
        mapOperandClass.put(SUB_EXPIRED_DATE.getValue(), SubExpiredDateCondItem.class);
        mapOperandClass.put(SUB_RENEW_DATE.getValue(), SubRenewDateCondItem.class);
        mapOperandClass.put(SUB_ADDON_TYPE.getValue(), SubAddonTypeCondItem.class);
        mapOperandClass.put(SUB_ADDON_COST.getValue(), SubAddonCostCondItem.class);
        mapOperandClass.put(SUB_SETUP_FEE.getValue(), SubSetupFeeCondItem.class);
        mapOperandClass.put(SUB_NUM_ADDON.getValue(), SubNumAddonCondItem.class);
        mapOperandClass.put(SUB_NUM_UNIT.getValue(), SubNumUnitCondItem.class);
        mapOperandClass.put(SUB_CANCEL_SERVICE_NAME.getValue(), SubCancelServiceNameCondItem.class);
        mapOperandClass.put(SUB_EXPIRED_SERVICE_NAME.getValue(), SubExpiredServiceNameCondItem.class);
        mapOperandClass.put(SUB_RENEW_SERVICE_NAME.getValue(), SubRenewServiceNameCondItem.class);
        mapOperandClass.put(SUB_REACTIVE_SERVICE_NAME.getValue(), SubReactiveServiceNameCondItem.class);
        mapOperandClass.put(SUB_REACTIVE_DATE.getValue(), SubReactiveDateCondItem.class);
        mapOperandClass.put(SUB_PRICING_PLAN.getValue(), SerPricingPlanCondItem.class);
        mapOperandClass.put(SUB_PAYMENT_DATE.getValue(), SubPaymentDateCondItem.class);
        mapOperandClass.put(SUB_EMAIL.getValue(), EmailCondItem.class);
        mapOperandClass.put(SUB_ENTERPRISE_NAME.getValue(), EnterpriseNameCondItem.class);
        mapOperandClass.put(SUB_ADDRESS.getValue(), AddressCondItem.class);
        mapOperandClass.put(SUB_PHONE_NUMBER.getValue(), PhoneNumberCondItem.class);
        mapOperandClass.put(SUB_SOCIAL_INSURANCE_CODE.getValue(), SocialInsuranceCodeCondItem.class);
        mapOperandClass.put(SUB_WEBSITE.getValue(), WebsiteCondItem.class);
        mapOperandClass.put(SUB_TAX_CODE.getValue(), TaxCodeCondItem.class);
        mapOperandClass.put(SUB_ENTERPRISE_SIZE.getValue(), BusinessSizeCondItem.class);
        mapOperandClass.put(SUB_BUSINESS_FIELD.getValue(), BusinessAreaCondItem.class);
        mapOperandClass.put(SUB_REPRESENTATIVE_NAME.getValue(), RepresentativeNameCondItem.class);
        mapOperandClass.put(SUB_DEPLOYMENT_ADDRESS.getValue(), SetupAddressCondItem.class);
        mapOperandClass.put(SUB_BUSINESS_REGIS_ADDRESS.getValue(), BusinessRegistrationAddressCondItem.class);
        mapOperandClass.put(SUB_ORDER_STATUS.getValue(), SubOrderStatusCondItem.class);
        mapOperandClass.put(SUB_STATUS.getValue(), SubscriptionStatusCondItem.class);

        mapOperandClass.put(SER_NUM_SERVICE.getValue(), SerNumServiceCondItem.class);
        mapOperandClass.put(SER_CATEGORY_NAME.getValue(), PurchasedCategoryCondItem.class);
        mapOperandClass.put(SER_SERVICE_TYPE.getValue(), PurchasedServiceTypeCondItem.class);
        mapOperandClass.put(SER_SERVICE_NAME.getValue(), PurchasedServiceNameCondItem.class);
        mapOperandClass.put(SER_PRICING_NAME.getValue(), PurchasedPricingNameCondItem.class);
        mapOperandClass.put(SER_SERVICE_ID.getValue(), SerServiceIdCondItem.class);
        mapOperandClass.put(SER_PAYMENT_CYCLE.getValue(), SubCurrentPaymentCycleCondItem.class);
        mapOperandClass.put(SER_PRICING_PLAN.getValue(), SerPricingPlanCondItem.class);
        mapOperandClass.put(SER_NUM_UNIT.getValue(), SerNumUnitCondItem.class);
        mapOperandClass.put(SER_TRIAL_TIME.getValue(), SerTrialTimeCondItem.class);
        mapOperandClass.put(SER_SETUP_FEE.getValue(), SerSetupFeeCondItem.class);
        mapOperandClass.put(SER_PAYMENT_TIME.getValue(), SubPaymentDateCondItem.class);
        mapOperandClass.put(SER_DEVELOPER_NAME.getValue(), SerDeveloperNameCondItem.class);
        mapOperandClass.put(SER_PRICING.getValue(), SerPricingCondItem.class);
        mapOperandClass.put(SER_REVIEW_SCORE.getValue(), SerReviewScoreCondItem.class);

        mapOperandClass.put(CUSTOMER_TICKET_TITLE.getValue(), TicketTitleCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_CONTENT.getValue(), TicketContentCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_CREATED_DATE.getValue(), TicketCreatedDateCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_SERVICE_TYPE.getValue(), TicketServiceTypeCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_CUSTOMER_CLASSIFICATION.getValue(), CustomerTypeCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_SERVICE_NAME.getValue(), TicketServiceNameCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_PRIORITY.getValue(), TicketPriorityCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_TYPE.getValue(), TicketTypeCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_STATUS.getValue(), TicketStatusCondItem.class);
        mapOperandClass.put(CUSTOMER_TICKET_CREATED_SOURCE.getValue(), TicketCreatedSourceCondItem.class);

        mapOperandClass.put(ADMIN_NAME.getValue(), AdminNameCondItem.class);
        mapOperandClass.put(ADMIN_ROLE_NAME.getValue(), AdminRoleNameCondItem.class);
        mapOperandClass.put(AM_NAME.getValue(), AmNameCondItem.class);

        mapOperandClass.put(AFFILIATE_TYPE.getValue(), AffTypeCondItem.class);
        mapOperandClass.put(AFFILIATE_SEGMENTATION.getValue(), AffSegmentationCondItem.class);
        mapOperandClass.put(AFFILIATE_LEVEL.getValue(), AffLevelCondItem.class);
        mapOperandClass.put(AFFILIATE_STATUS.getValue(), AffiliateStatusCondItem.class);

        mapOperandClass.put(APPROVED_DEV.getValue(), ApprovedDevCondItem.class);
        mapOperandClass.put(APPROVED_DEV_PRICING.getValue(), ApprovedDevCondItem.class);
        mapOperandClass.put(APPROVED_SERVICE_TYPE.getValue(), ApprovedServiceTypeCondItem.class);
        mapOperandClass.put(APPROVED_PRICING_TYPE.getValue(), ApprovedPricingTypeCondItem.class);
        mapOperandClass.put(APPROVED_CHANGE_INFORMATION.getValue(), ApprovedChangeInformationCondItem.class);
        mapOperandClass.put(APPROVED_CHANGE_PRICING_INFORMATION.getValue(), ApprovedChangeInformationCondItem.class);

        mapOperandClass.put(TIME_NOT_ACCESSED.getValue(), TimeNotAccessedCondItem.class);
    }

    public ConditionItemExecute makeConditionItem(McIfConditionDTO condItemJson) {
        try {
            int operandCode = condItemJson.operandId;
            Class<?> concreteClass = mapOperandClass.get(operandCode);
            if (concreteClass == null) {
                cause = String.format("Cannot find concreteClass from condition operandCode [%d], condItemJson [%s]", operandCode,
                    GsonMapperUtil.toJson(condItemJson));
                log.error(cause);
                return null;
            }
            Constructor<?> constructor = concreteClass.getConstructor(McIfConditionDTO.class);
            return (ConditionItemExecute) constructor.newInstance(condItemJson);
        } catch (Exception e) {
            e.printStackTrace();
            cause = e.getLocalizedMessage();
            return null;
        }
    }

}

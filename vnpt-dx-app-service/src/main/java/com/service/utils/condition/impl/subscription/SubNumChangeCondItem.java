package com.service.utils.condition.impl.subscription;

import com.service.utils.CondDisplayText;
import com.service.utils.LogicExpression;
import com.service.utils.condition.impl.CondItemBase;
import com.service.utils.condition.ConditionParam;
import com.service.utils.condition.ExtraCondItemExecute;
import com.service.utils.constants.OperatorConstant;
import com.service.utils.jsonObject.McIfConditionDTO;
import com.service.utils.repository.ChangeSubUserInfo;
import com.service.utils.values.ValueManager;
import lombok.val;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.service.utils.constants.OperatorConstant.OperatorEnum.*;

public class SubNumChangeCondItem extends CondItemBase {
    private ExtraCondItemExecute extraCondItem;

    @Override
    public String getDisplayText() {
        try {
            String timeDispText = CondDisplayText.dateTimeDisplayText(
                    extraCondItem.getTimeSelectionOperator(),
                    extraCondItem.getTimeSelectionValue());
            if (operator == NOT_EQUAL || operator == NOT_BETWEEN || operator == NOT_IN) {
                return null;
            } else if (operator == NOT_AVAILABLE) {
                return "Khách hàng chưa thực hiện đổi gói dịch vụ " + timeDispText;
            } else if (operator == ANY) {
                return "Khách hàng đã thực hiện đổi gói trên nền tảng " + timeDispText;
            } else {
                String condText = CondDisplayText.longDisplayText(operator, valueBase);
                if (condText != null) {
                    return "Khách hàng đã thực hiện đổi gói " + condText + " lần " + timeDispText;
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public SubNumChangeCondItem(McIfConditionDTO condItemJson) throws Exception {
        super(condItemJson);
        parseJson();
    }

    private void parseJson() throws Exception {
        // Parse extra condition
        if (condItemJson.getExtendCondition() != null) {
            extraCondItem = new ExtraCondItemExecute(condItemJson.getExtendCondition());
        } else {
            extraCondItem = new ExtraCondItemExecute();
        }
    }

    @Override
    public boolean verify(ConditionParam conditionParam) {
        try {
            boolean verifyResult = false;
            ValueManager.CValueBase extraTimeValue = extraCondItem.getTimeSelectionValue();
            OperatorConstant.OperatorEnum extraTimeOperationCode = extraCondItem.getTimeSelectionOperator();

            // Get user information
            List<ChangeSubUserInfo> changeSubInfoOfUserList = condExecuteRepository.getChangeInfoOfSub(conditionParam.customerId);
            long totalChange = changeSubInfoOfUserList
                .stream()
                .filter(subUserInfo -> subUserInfo.getChangeDate() != null &&
                    LogicExpression.dateTimeExecute(
                        subUserInfo.getChangeDate(),
                        extraTimeOperationCode,
                        extraTimeValue))
                .count();

            if (totalChange == 0) {
                return operator == NOT_AVAILABLE;
            } else {
                val mapChangeSub = changeSubInfoOfUserList
                        .stream()
                        .collect(Collectors.groupingBy(ChangeSubUserInfo::getSubId));

                for (Map.Entry<Long, List<ChangeSubUserInfo>> entry : mapChangeSub.entrySet()) {
                    long countChange = entry.getValue()
                        .stream()
                        .filter(renewSubUserInfo -> renewSubUserInfo.getChangeDate() != null &&
                            LogicExpression.dateTimeExecute(
                                renewSubUserInfo.getChangeDate(),
                                extraTimeOperationCode,
                                extraTimeValue))
                        .count();

                    if (countChange == 0) {
                        continue;
                    }

                    if (LogicExpression.longExecute(countChange, operator, valueBase)) {
                        return true;
                    }
                }
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public Set<Long> filterEnterprise() {
        return null;
    }
}

package com.service.utils.condition.impl.subscription;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.service.utils.CondDisplayText;
import com.service.utils.LogicExpression;
import com.service.utils.condition.ConditionParam;
import com.service.utils.condition.ExtraCondItemExecute;
import com.service.utils.condition.impl.CondItemBase;
import com.service.utils.constants.OperatorConstant;
import com.service.utils.jsonObject.McIfConditionDTO;
import com.service.utils.repository.SubExpiredInfo;
import com.service.utils.values.ValueManager;

import static com.service.utils.constants.OperatorConstant.OperatorEnum.*;

public class SubExpiredServiceNameCondItem extends CondItemBase {

    private ExtraCondItemExecute extraCondItem;

    public SubExpiredServiceNameCondItem(McIfConditionDTO condItemJson) throws Exception {
        super(condItemJson);
        parseJson();
    }

    private void parseJson() throws Exception {
        // Parse extra condition
        if (condItemJson.getExtendCondition() != null) {
            extraCondItem = new ExtraCondItemExecute(condItemJson.getExtendCondition());
        } else {
            extraCondItem = new ExtraCondItemExecute();
        }
    }

    @Override
    public String getDisplayText() {
        try {
            String extraDispText = CondDisplayText.dateTimeDisplayText(
                    extraCondItem.getTimeSelectionOperator(),
                    extraCondItem.getTimeSelectionValue());
            if (operator == NOT_EQUAL) {
                return null;
            } else if (operator == NOT_AVAILABLE) {
                return "Khách hàng chưa hết hạn thuê bao nào trên hệ thống " + extraDispText;
            } else if (operator == ANY) {
                return "Khách hàng đã hết hạn thuê bao " + extraDispText;
            } else {
                String displayTemplate;
                if (operator == EQUAL) {
                    displayTemplate = "Khách hàng đã hết hạn thuê bao của dịch vụ %s %s";
                } else {
                    displayTemplate = "Khách hàng đã hết hạn thuê bao có tên sản phẩm %s %s";
                }
                String condText = CondDisplayText.stringDisplayText(operator, valueBase);
                if (condText != null) {
                    return String.format(displayTemplate, condText, extraDispText);
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public boolean verify(ConditionParam conditionParam) {
        try {
            boolean verifyResult = false;

            // Get value of extra condition
            ValueManager.CValueBase extraTimeSelectionValue = extraCondItem.getTimeSelectionValue();
            OperatorConstant.OperatorEnum timeSelectionOperation = extraCondItem.getTimeSelectionOperator();

            // Query to get expired subscription for each service
            List<SubExpiredInfo> firstSubInfoOpt = condExecuteRepository.getSubExpiredTimeServiceOfUser(conditionParam.customerId);
            List<SubExpiredInfo> validItems = firstSubInfoOpt.stream()
                .filter(firstSubInfo -> LogicExpression.dateTimeExecute(
                    firstSubInfo.getExpiredTime() != null ? new Date(firstSubInfo.getExpiredTime().getTime()) : new Date(Long.MAX_VALUE), timeSelectionOperation,
                    extraTimeSelectionValue))
                .collect(Collectors.toList());

            if (validItems.size() == 0) {
                verifyResult = LogicExpression.stringExecute(null, operator, valueBase);
            } else {
                for (SubExpiredInfo subInfo : validItems) {
                    // Verify name of service
                    String nameOfService = subInfo.getServiceName();
                    if (nameOfService == null) {
                        nameOfService = subInfo.getComboName();
                    }
                    verifyResult = LogicExpression.stringExecute(nameOfService, operator, valueBase);
                    if (verifyResult) {
                        break;
                    }
                }
            }

            return verifyResult;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public Set<Long> filterEnterprise() {
        try {
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}

package com.service.utils.constants;

import lombok.Getter;

public class OperatorConstant {

    @Getter
    public enum OperatorEnum {

        EQUAL ( 1 ),
        NOT_EQUAL ( 2 ),
        CONTAIN ( 3 ),
        NOT_CONTAIN ( 4 ),
        START_WITH ( 5 ),
        END_WITH ( 6 ),
        NOT_START_WITH ( 7 ),
        NOT_END_WITH ( 8 ),
        NOT_AVAILABLE ( 9 ),
        ANY ( 10 ),
        LESS_THAN ( 11 ),
        BIGGER_THAN ( 12 ),
        LESS_OR_EQUAL ( 13 ),
        BIGGER_OR_EQUAL ( 14 ),
        BETWEEN ( 15 ),
        NOT_BETWEEN ( 16 ),
        IN ( 17 ),
        NOT_IN ( 18 ),
        NO_LIMIT ( 19 ),
        DURING ( 20 ),
        NOT_COMBINE ( 21 ),
        COMBINE ( 22 ),
        EQUAL_MAIN (23 ),
        LIMIT(24),
        ALTER(25),
        UPDATE(26),
        RANGE_BIGGER_THAN(27),
        RANGE_EQUAL(28),
        RANGE_LESS_THAN(29),
        BEFORE_X_DAY(30);

        private final int value;

        OperatorEnum(int value) {
            this.value = value;
        }

        public static OperatorEnum fromValue(int value) {
            OperatorEnum[] allValues = OperatorEnum.values();
            for (OperatorEnum item : allValues) {
                if(item.getValue() == value) {
                    return item;
                }
            }
            return null;
        }
    }
}

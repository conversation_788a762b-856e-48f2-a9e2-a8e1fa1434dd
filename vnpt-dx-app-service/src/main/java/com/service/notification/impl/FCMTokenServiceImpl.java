package com.service.notification.impl;

import java.time.LocalDateTime;

import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import com.entity.notification.FCMToken;
import com.repository.notification.FCMTokenRepository;
import com.service.notification.FCMTokenService;
import com.util.AuthUtil;
import lombok.RequiredArgsConstructor;
import lombok.val;

@Service
@RequiredArgsConstructor
public class FCMTokenServiceImpl implements FCMTokenService {

    private final FCMTokenRepository fcmTokenRepository;

    @Override
    public synchronized void save(String token) {
        try {
            // Lấy thông tin người dùng đăng nhập
            val userId = AuthUtil.getLoggedInUser().getId();
            // Kiểm tra token đã tồn tại chưa
            try {
                if (!fcmTokenRepository.existsByUserIdAndToken(userId, token)) {
                    FCMToken fcmToken = FCMToken.builder()
                            .token(token)
                            .userId(userId)
                            .createdBy(userId)
                            .createdAt(LocalDateTime.now())
                            .clientId(AuthUtil.getOauth2Authentication().getClientId())
                            .build();
                    fcmTokenRepository.save(fcmToken);
                }
            } catch (DataAccessException e) {
                throw new RuntimeException("Cannot save FCM token due to database error", e);
            }
        } catch (SecurityException e) {
            throw e;
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("Cannot save FCM Token", e);
        }
    }
}
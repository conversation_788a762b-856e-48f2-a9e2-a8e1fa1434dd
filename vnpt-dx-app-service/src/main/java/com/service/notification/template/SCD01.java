package com.service.notification.template;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import com.dto.actionNotification.EmailParamDTO;
import com.dto.subscriptions.mail.NotifScDetailDTO;
import com.dto.subscriptions.mail.NotifScSubDetailDTO;
import com.onedx.common.dto.notification.NotificationDTO;
import com.enums.ActionNotificationEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.StatusEnum;
import com.service.notification.impl.ActionNotificationTemplateBase;
import com.util.NotifyUtil;

public class SCD01 extends ActionNotificationTemplateBase {

    public SCD01(List<NotifScDetailDTO> lstDetail) {

        // Type
        this.actionNotificationEnum = ActionNotificationEnum.SCD_01;

        // Email
        for (NotifScDetailDTO detail : lstDetail) {
            EmailParamDTO emailParamDTO = new EmailParamDTO();
            emailParamDTO.setEmail(detail.getAssigneeEmail());
            HashMap<String, String> lstParam = emailParamDTO.getLstParam();
            lstParam.put("$STAFF_NAME", detail.getAssigneeName());
            lstParam.put("$TABLE", getTableHTML(detail.getLstSubDetail()));
            this.lstEmailParam.add(emailParamDTO);

            // Notification
            for (NotifScSubDetailDTO subscription : detail.getLstSubDetail()) {
                NotificationDTO notificationDTO = NotificationDTO.builder()
                    .title(NotifyUtil.getContent(actionNotificationEnum.getTitle()))
                    .body(NotifyUtil.getContent(actionNotificationEnum.getContent(), subscription.getServiceName(), subscription.getPricingName(),
                        subscription.getDaysLeft().toString()))
                    .screenId(actionNotificationEnum.getScreentId())
                    .portalType(PortalType.ADMIN.getType())
                    .status(StatusEnum.INACTIVE.value)
                    .createdAt(LocalDateTime.now())
                    .userId(detail.getAssigneeId())
                    .build();
                this.lstNotificationParam.add(notificationDTO);
            }
        }

    }

    public static String getTableHTML(List<NotifScSubDetailDTO> lstSubDetail) {
        String tableTemplate = NotificationContentConstant.SCD_01_DETAIL_TABLE;
        String rowTemplate = NotificationContentConstant.SCD_01_DETAIL_ROW;
        StringBuilder tableRowHTML = new StringBuilder();
        Integer rowNumber = 1;
        for (NotifScSubDetailDTO subscription : lstSubDetail) {
            // append vao template
            tableRowHTML.append(String.format(rowTemplate, rowNumber, subscription.getServiceName(), subscription.getPricingName(),
                subscription.getSmeName(), subscription.getDaysLeft().toString()));
            rowNumber++;
        }

        return String.format(tableTemplate, tableRowHTML);
    }
}

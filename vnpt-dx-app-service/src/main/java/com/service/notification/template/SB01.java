package com.service.notification.template;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import com.constant.SubscriptionConstant;
import com.dto.actionNotification.EmailParamDTO;
import com.enums.ActionNotificationEnum;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.dto.base.ICommonIdNameEmail;
import com.service.notification.impl.ActionNotificationTemplateBase;

public class SB01 extends ActionNotificationTemplateBase {

    public SB01(List<ICommonIdNameEmail> lstReceiver, String webHost) {
        // Type
        this.actionNotificationEnum = ActionNotificationEnum.SB01;

        // Email
        for (ICommonIdNameEmail receiver : lstReceiver) {
            EmailParamDTO emailParamDTO = new EmailParamDTO();
            emailParamDTO.setEmail(receiver.getAssigneeEmail());
            HashMap<String, String> lstParam = emailParamDTO.getLstParam();
            lstParam.put("$USER", receiver.getAssigneeName());
            lstParam.put("$NAME_SERVICE", receiver.getServiceName());
            lstParam.put("$NAME_PRICING", receiver.getPricingName());
            lstParam.put("$REMAIN_DAYS", String.valueOf(receiver.getDaysLeft()));
            lstParam.put("$SUB_CODE", receiver.getSubCode());
            lstParam.put("$TOTAL_AMOUNT", formatMoneyVietnamese(receiver.getTotalAmount()));
            lstParam.put("$NOTE", getNote(receiver));

            String typeLinkRenewing = SubscriptionConstant.LINK_SME_EXTEND_SERVICE;
            if (receiver.getPortalName() == PortalType.DEV) {
                typeLinkRenewing = SubscriptionConstant.LINK_DEV_EXTEND_SERVICE;
            } else if (receiver.getPortalName() == PortalType.ADMIN) {
                typeLinkRenewing = SubscriptionConstant.LINK_ADMIN_EXTEND_SERVICE;
            }
            String linkRenewing = String.format(typeLinkRenewing, webHost, receiver.getSubId());
            lstParam.put("$LINK_RENEWING", linkRenewing);
            this.lstEmailParam.add(emailParamDTO);
        }
    }

    public static String formatMoneyVietnamese(BigDecimal totalAmount) {
        Locale localeVN = new Locale("vi", "VN");
        NumberFormat currencyVN = NumberFormat.getCurrencyInstance(localeVN);
        return currencyVN.format(totalAmount);
    }

    public static String getNote(ICommonIdNameEmail data) {
        return String.format("Thanh toán cho chu kì %s (từ %s đến %s)", data.getCurrentCycle(), data.getBillingDate(), data.getEndDate());
    }

}

package com.service.actionHistory.impl;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.component.BaseController;
import com.component.BaseController.ListRequest;
import com.dto.actionHistory.ActionHistoryCreateDTO;
import com.dto.actionHistory.ActionHistoryReqDTO;
import com.dto.actionHistory.ActionHistoryTypeReqDTO;
import com.dto.actionHistory.AllActionHisSMSEmailReqDTO;
import com.dto.actionHistory.EmailSendingReqDTO;
import com.dto.actionHistory.IGetContactEmailPhoneDTO;
import com.dto.actionHistory.IGetObjectActionHistory;
import com.dto.actionHistory.IGetPageAllActionHistoryDTO;
import com.dto.customerContact.ContactEmailPhoneDTO;
import com.dto.users.IEmailSmsHistoryDTO;
import com.entity.actionHistory.ActionHistory;
import com.entity.actionHistory.ActionHistoryType;
import com.entity.file.attach.FileAttach;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.onedx.common.constants.enums.emails.EmailSendStatusEnum;
import com.onedx.common.constants.enums.fileAttach.FileAttachTypeEnum;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryObjectTypeEnum;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryTypeEnum;
import com.onedx.common.constants.enums.history.actionHistory.ActionNoteObjectTypeEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.dto.base.ICommonIdNameEmail;
import com.onedx.common.entity.emails.MailSendHistory;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.repository.emails.EmailRepository;
import com.onedx.common.utils.ObjectUtil;
import com.repository.actionHistory.ActionHistoryRepository;
import com.repository.actionHistory.ActionHistoryTypeRepository;
import com.repository.customerContact.CustomerContactRepository;
import com.repository.file.attach.FileAttachRepository;
import com.repository.users.UserRepository;
import com.service.actionHistory.ActionHistoryService;
import com.util.AuthUtil;
import com.util.FileUtil;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ActionHistoryServiceImpl implements ActionHistoryService {

    @Autowired
    private ActionHistoryRepository actionHistoryRepository;
    @Autowired
    private ActionHistoryTypeRepository actionHistoryTypeRepository;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private FileAttachRepository fileAttachRepository;
    @Autowired
    private CustomerContactRepository customerContactRepository;
    @Autowired
    private EmailRepository emailRepository;
    @Autowired
    private Environment environment;

    @Override
    public Page<IGetObjectActionHistory> getListActionHistory(ActionHistoryObjectTypeEnum objectType, Long objectId, Date startDate,
        Date endDate, Integer actionType, Pageable pageable) {
        return actionHistoryRepository.getPageObjectActionHistory(AuthUtil.getCurrentUserId(), objectType.getValue(), objectId, startDate,
            endDate, actionType, AuthUtil.isSuperAdmin(), pageable);
    }

    @Transactional
    @Override
    public ActionHistory saveActionHistory(ActionHistoryTypeEnum actionType, ActionHistoryObjectTypeEnum objectType, Long objectId,
        Long createdBy, String content) {
        ActionHistory actionHistory = new ActionHistory(actionType, objectType, objectId, createdBy, content);
        return actionHistoryRepository.save(actionHistory);
    }

    @Transactional
    @Override
    public void saveListActionHistory(ActionHistoryTypeEnum actionType, ActionHistoryObjectTypeEnum objectType, Set<Long> lstObjectId,
        Long createdBy, String content) {
        if (!Collections.isEmpty(lstObjectId)) {
            List<ActionHistory> lstActionHistory = lstObjectId.stream()
                .map(objectId -> new ActionHistory(actionType, objectType, objectId, createdBy, content)).collect(Collectors.toList());
            actionHistoryRepository.saveAll(lstActionHistory);
        } else {
            actionHistoryRepository.save(new ActionHistory(actionType, objectType, -1L, createdBy, content));
        }
    }

    @Override
    public void saveUserPasswordLeakActionHistory(Set<Long> lstUserId) {
        List<ActionHistory> lstActionHistory = new ArrayList<>();
        List<ICommonIdNameEmail> lstUserDetail = userRepository.getLstDetailUserForActionHistory(lstUserId);
        for (ICommonIdNameEmail userDetail : lstUserDetail) {
            String content = String.format("Tài khoản đăng nhập %s của bạn bị lộ mật khẩu", userDetail.getUserName());
            lstActionHistory.add(
                new ActionHistory(getPasswordLeakActionHistoryTypeEnum(userDetail), userDetail.getActionHistoryObjectType(), userDetail.getId(),
                    -1L,
                    content));
        }
        actionHistoryRepository.saveAll(lstActionHistory);
    }

    @Override
    public void saveUserAccountLockedActionHistory(Set<Long> lstUserId) {
        List<ActionHistory> lstActionHistory = new ArrayList<>();
        List<ICommonIdNameEmail> lstUserDetail = userRepository.getLstDetailUserForActionHistory(lstUserId);
        for (ICommonIdNameEmail userDetail : lstUserDetail) {
            String content = String.format("Tài khoản đăng nhập %s của bạn bị khóa khi không đổi mật khẩu", userDetail.getUserName());
            lstActionHistory.add(
                new ActionHistory(getAccountLockActionHistoryTypeEnum(userDetail), userDetail.getActionHistoryObjectType(), userDetail.getId(),
                    -1L,
                    content));
        }
        actionHistoryRepository.saveAll(lstActionHistory);
    }

    private static ActionHistoryTypeEnum getPasswordLeakActionHistoryTypeEnum(ICommonIdNameEmail userDetail) {
        ActionHistoryTypeEnum actionEnum = ActionHistoryTypeEnum.USER_PASSWORD_LEAKED;
        if (Objects.equals(userDetail.getActionHistoryObjectType(), ActionHistoryObjectTypeEnum.ADMIN)) {
            actionEnum = ActionHistoryTypeEnum.ADMIN_PASSWORD_LEAKED;
        } else if (Objects.equals(userDetail.getActionHistoryObjectType(), ActionHistoryObjectTypeEnum.AFFILIATE)) {
            actionEnum = ActionHistoryTypeEnum.AFFILIATE_PASSWORD_LEAKED;
        }
        return actionEnum;
    }

    private static ActionHistoryTypeEnum getAccountLockActionHistoryTypeEnum(ICommonIdNameEmail userDetail) {
        ActionHistoryTypeEnum actionEnum = ActionHistoryTypeEnum.USER_ACCOUNT_LOCKED;
        if (Objects.equals(userDetail.getActionHistoryObjectType(), ActionHistoryObjectTypeEnum.ADMIN)) {
            actionEnum = ActionHistoryTypeEnum.ADMIN_ACCOUNT_LOCKED;
        } else if (Objects.equals(userDetail.getActionHistoryObjectType(), ActionHistoryObjectTypeEnum.AFFILIATE)) {
            actionEnum = ActionHistoryTypeEnum.AFFILIATE_ACCOUNT_LOCKED;
        }
        return actionEnum;
    }

    @Override
    public Integer createActionHistoryType(ActionHistoryTypeReqDTO createDTO) {
        if (actionHistoryTypeRepository.existsByNameIgnoreCase(createDTO.getName())) {
            throw exceptionFactory.badRequest(MessageKeyConstant.DUPLICATE_NAME, Resources.ACTION_HISTORY_TYPE, ErrorKey.ActionHistory.ACTION_NAME,
                createDTO.getName());
        }
        ActionHistoryType actionHistoryType = new ActionHistoryType();
        actionHistoryType.setCreatedBy(AuthUtil.getCurrentUserId());
        actionHistoryType.setContent(createDTO.getContent());
        actionHistoryType.setName(createDTO.getName());
        actionHistoryType.setPredefined(Boolean.FALSE);
        actionHistoryType.setSubCategory(createDTO.getObjectType().name());
        // set group và subgroup
        return actionHistoryTypeRepository.save(actionHistoryType).getCode();
    }

    @Transactional
    @Override
    public Long createActionHistory(ActionHistoryCreateDTO createDTO) {
        Long currentUserId = AuthUtil.getCurrentUserId();
        ActionHistoryType actionHistoryType;
        if (!Objects.isNull(createDTO.getActionType())) { // Nếu tạo với actionType chọn trước
            actionHistoryType = actionHistoryTypeRepository.findByCode(createDTO.getActionType())
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.ACTION_HISTORY_TYPE, ErrorKey.ActionHistory.ACTION_TYPE,
                    String.valueOf(createDTO.getActionType())));
        } else if (!Objects.isNull(createDTO.getActionTypeName())) { // Nếu tạo trực tiếp bằng actionTypeName
            if (actionHistoryTypeRepository.existsByNameIgnoreCase(createDTO.getActionTypeName())) {
                throw exceptionFactory.badRequest(MessageKeyConstant.DUPLICATE_NAME, Resources.ACTION_HISTORY_TYPE, ErrorKey.ActionHistory.ACTION_NAME,
                    createDTO.getActionTypeName());
            }
            actionHistoryType = ActionHistoryType.builder()
                .name(createDTO.getActionTypeName())
                .subCategory(createDTO.getObjectType().name())
                .content(createDTO.getContent())
                .predefined(Boolean.FALSE)
                .createdBy(currentUserId)
                .build();
            actionHistoryType = actionHistoryTypeRepository.save(actionHistoryType);
            // refresh entity
            entityManager.refresh(actionHistoryType);
        } else {
            throw exceptionFactory.dataInvalid(MessageKeyConstant.INVALID_DATA, Resources.ACTION_HISTORY_TYPE,
                ErrorKey.ActionHistory.ACTION_TYPE);
        }
        ActionHistory newActionHistory = new ActionHistory(actionHistoryType, createDTO.getObjectType(), createDTO.getObjectId(),
            ObjectUtil.getOrDefault(createDTO.getActorId(), currentUserId), createDTO.getContent(), createDTO.getCreationTime());
        Long actionHistoryId = actionHistoryRepository.save(newActionHistory).getId();
        setAttachment(actionHistoryId, createDTO.getAttachmentIds());
        return actionHistoryRepository.save(newActionHistory).getId();
    }

    @Transactional
    @Override
    public void editActionHistory(Long id, ActionHistoryReqDTO updateDTO) {
        Long currentUserId = AuthUtil.getCurrentUserId();
        //Kiểm tra tồn tại nhật ký hoạt động theo id
        ActionHistory actionHistory = actionHistoryRepository.findById(id)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.ACTION_HISTORY, ErrorKey.ActionHistory.ID, String.valueOf(id)));

        // Check existed actionType
        ActionHistoryType actionHistoryType;
        if (!Objects.isNull(updateDTO.getActionType())) { // Nếu sửa với actionType chọn trước
            actionHistoryType = actionHistoryTypeRepository.findByCode(updateDTO.getActionType())
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.ACTION_HISTORY_TYPE, ErrorKey.ActionHistory.ACTION_TYPE,
                    String.valueOf(updateDTO.getActionType())));
        } else if (!Objects.isNull(updateDTO.getActionTypeName())) { // Nếu sửa trực tiếp bằng actionTypeName
            if (actionHistoryTypeRepository.existsByNameIgnoreCase(updateDTO.getActionTypeName())) {
                throw exceptionFactory.badRequest(MessageKeyConstant.DUPLICATE_NAME, Resources.ACTION_HISTORY_TYPE,
                    ErrorKey.ActionHistory.ACTION_NAME, updateDTO.getActionTypeName());
            }
            actionHistoryType = ActionHistoryType.builder()
                .name(updateDTO.getActionTypeName())
                .subCategory(String.valueOf(ActionHistoryObjectTypeEnum.valueOf(actionHistory.getObjectType())))
                .content(updateDTO.getContent())
                .predefined(Boolean.FALSE)
                .createdBy(currentUserId)
                .build();
            actionHistoryType = actionHistoryTypeRepository.save(actionHistoryType);
            // refresh entity
            entityManager.refresh(actionHistoryType);
        } else {
            throw exceptionFactory.dataInvalid(MessageKeyConstant.INVALID_DATA, Resources.ACTION_HISTORY_TYPE,
                ErrorKey.ActionHistory.ACTION_TYPE);
        }

        // Cập nhật thông tin nhật ký hoạt động
        actionHistory.setActionName(actionHistoryType.getName());
        actionHistory.setContent(updateDTO.getContent());
        actionHistory.setActionCode("CST" + actionHistoryType.getCode()); // CST : custom
        actionHistory.setActionType(actionHistoryType.getCode());
        actionHistory.setCreatedBy(updateDTO.getActorId());
        actionHistory.setModifiedAt(LocalDateTime.now());
        actionHistory.setCreationTime(updateDTO.getCreationTime());
        removeAttachment(id);
        setAttachment(id, updateDTO.getAttachmentIds());
        actionHistoryRepository.save(actionHistory);
    }

    @Override
    public Page<IGetPageAllActionHistoryDTO> getPageAllObjectActionHistory(AllActionHisSMSEmailReqDTO requestDTO) {
        List<String> lstReceiver = new ArrayList<>();

        if (Objects.equals(requestDTO.getObjectType(), ActionHistoryObjectTypeEnum.CUSTOMER_CONTACT) ||
            Objects.equals(requestDTO.getObjectNoteType(), ActionNoteObjectTypeEnum.CUSTOMER_CONTACT)) {
            // Lấy thông tin email, phone liên hệ
            IGetContactEmailPhoneDTO contactEmailPhone = customerContactRepository.getContactEmailPhone(requestDTO.getObjectId());
            ContactEmailPhoneDTO contactEmailPhoneDTO = new ContactEmailPhoneDTO(contactEmailPhone);
            if (Objects.nonNull(contactEmailPhoneDTO.getEmails())) {
                lstReceiver.addAll(contactEmailPhoneDTO.getEmails());
            }
            if (Objects.nonNull(contactEmailPhoneDTO.getPhones())) {
                lstReceiver.addAll(contactEmailPhoneDTO.getPhones());
            }
        } else {
            lstReceiver.add("ALL"); // default receiver
        }
        lstReceiver.removeIf(Objects::isNull);

        BaseController.ListRequest pageInfo = new ListRequest(requestDTO.getSize(), requestDTO.getPage(), requestDTO.getSort());

        return actionHistoryRepository.getPageAllObjectActionHistory(requestDTO.getSearch(), AuthUtil.getCurrentUserId(),
            requestDTO.getObjectType().getValue(), requestDTO.getObjectId(), requestDTO.getUserId(), requestDTO.getEnterpriseId(),
            requestDTO.getStartDate(), requestDTO.getEndDate(),
            requestDTO.getActionType(), AuthUtil.isSuperAdmin(), requestDTO.getObjectNoteType().getValue(), lstReceiver, pageInfo.getPageable());
    }

    @Override
    public Page<IEmailSmsHistoryDTO> getPageEmailSmsHistory(Long id, Integer type, Date startDate, Date endDate, Pageable pageable) {

        // Lấy thông tin liên hệ
        ContactEmailPhoneDTO contactEmailPhoneDTO = new ContactEmailPhoneDTO(customerContactRepository.getContactEmailPhone(id));

        return actionHistoryRepository.getPageEmailSmsHistory(type, contactEmailPhoneDTO.getEmails(), contactEmailPhoneDTO.getPhones(),
            startDate, endDate, pageable);
    }


    @Transactional
    @Override
    public void sendActionHistoryEmail(EmailSendingReqDTO requestDTO) {
        Integer objectType = null;
        String module;
        switch (requestDTO.getObjectType()) {
            case QUOTATION:
                objectType = ActionNoteObjectTypeEnum.QUOTATION.getValue();
                module = Resources.QUOTATION;
                break;
            case USER:
            case ENTERPRISE:
            case CUSTOMER_CONTACT:
            default:
                // TODO: Phát triển theo các CR bổ sung
                module = Resources.USER;
                break;
        }
        // Lưu file tải lên
        String attachmentUri;
        if (requestDTO.getAttachments() != null && !requestDTO.getAttachments().isEmpty()) {
            attachmentUri = requestDTO.getAttachments().stream()
                .map(file -> StringUtils.removeStart(FileUtil.uploadFile(file, module, environment), File.separator + "resources"))
                .collect(Collectors.joining(CharacterConstant.SEMICOLON));
        } else {
            attachmentUri = null;
        }
        Integer finalObjectType = objectType;
        emailRepository.saveAll(requestDTO.getLstReceiver().stream()
            .map(receiver -> MailSendHistory.builder()
                .receiver(receiver)
                .subject(requestDTO.getSubject())
                .content(requestDTO.getContent())
                .attachment(attachmentUri)
                .sendStatus(EmailSendStatusEnum.NOT_YET_SENT.getValue())
                .createBy(AuthUtil.getCurrentUserId())
                .objectType(finalObjectType)
                .objectId(requestDTO.getObjectId())
                .build())
            .collect(Collectors.toList()));
    }

    @Transactional
    @Override
    public void deleteActionHistory(Long id) {
        // TODO: check quyền xóa (nếu có)
        removeAttachment(id);
        actionHistoryRepository.deleteById(id);
    }

    private void setAttachment(Long id, Set<Long> attachmentIds) {
        if (attachmentIds != null && !attachmentIds.isEmpty()) {
            List<FileAttach> fileAttaches = fileAttachRepository.findAllByIdIn(attachmentIds);
            for (FileAttach fileAttach : fileAttaches) {
                fileAttach.setObjectType(FileAttachTypeEnum.ACTION_HISTORY.value);
                fileAttach.setObjectId(id);
                fileAttachRepository.save(fileAttach);
            }
        }
    }

    private void removeAttachment(Long id) {
        List<FileAttach> fileAttaches = fileAttachRepository.findAllByObjectIdAndObjectType(id, FileAttachTypeEnum.ACTION_HISTORY.value);
        fileAttaches.forEach(file -> file.setObjectId(null));
        fileAttachRepository.saveAll(fileAttaches);
    }
}

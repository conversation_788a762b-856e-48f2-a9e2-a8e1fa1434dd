package com.service.coupon.impl;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toSet;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import com.component.BaseEntity;
import com.constant.AddonsConstant;
import com.constant.CouponConst;
import com.constant.CouponConst.MailParam;
import com.constant.CreditNoteConst.SqlVar;
import com.constant.EntitiesConstant;
import com.constant.FileAttachConst;
import com.constant.PricingConst;
import com.constant.SubscriptionConstant;
import com.constant.SystemParamConstant;
import com.constant.enums.ActionType;
import com.constant.enums.coupon.AddonTypeEnum;
import com.constant.enums.coupon.CodeTypeEnum;
import com.constant.enums.coupon.CouponEnterprisePopupEnum;
import com.constant.enums.coupon.CouponPricingApplyTypeEnum;
import com.constant.enums.coupon.CouponPricingPlanTypeEnum;
import com.constant.enums.coupon.CouponRestoreStatus;
import com.constant.enums.coupon.DiscountSupplierTypeEnum;
import com.constant.enums.coupon.EnterpriseTypeEnum;
import com.constant.enums.coupon.LimitedTypeEnum;
import com.constant.enums.coupon.SupplierTypeEnum;
import com.constant.enums.coupon.TotalBillTypeEnum;
import com.constant.enums.coupon.TypeApplyEnum;
import com.constant.enums.file.attach.FileTypeEnum;
import com.constant.enums.file.attach.ResolutionEnum;
import com.constant.enums.subscription.SubsTypeEnum;
import com.dto.common.IBundle;
import com.dto.coupons.CouponAddonServiceDetailDTO;
import com.dto.coupons.CouponAddonsDetailDTO;
import com.dto.coupons.CouponAdminReqDTO;
import com.dto.coupons.CouponAdminReqDTO.CouponSupplierId;
import com.dto.coupons.CouponApproveDTO;
import com.dto.coupons.CouponCategoryResDTO;
import com.dto.coupons.CouponDeleteReqDTO;
import com.dto.coupons.CouponDeleteResDTO;
import com.dto.coupons.CouponDetailDTO;
import com.dto.coupons.CouponDevReqDTO;
import com.dto.coupons.CouponDevReqDTO.CouponAddonIds;
import com.dto.coupons.CouponDevReqDTO.CouponEnterpriseIds;
import com.dto.coupons.CouponDevReqDTO.CouponPricingApplyIds;
import com.dto.coupons.CouponDevReqDTO.CouponPricingIds;
import com.dto.coupons.CouponDevReqDTO.CouponVariantIds;
import com.dto.coupons.CouponEnterpriseDetailDTO;
import com.dto.coupons.CouponEnterpriseResponse;
import com.dto.coupons.CouponIdAndNameDTO;
import com.dto.coupons.CouponListAddonsResDTO;
import com.dto.coupons.CouponListAddonsResDTO.AddonStrategyResDTO;
import com.dto.coupons.CouponPopupTreeResDTO;
import com.dto.coupons.CouponPricingDetailDTO;
import com.dto.coupons.CouponPricingPlanResDTO;
import com.dto.coupons.CouponPricingServiceDetailDTO;
import com.dto.coupons.CouponSendMailDTO;
import com.dto.coupons.CouponServiceDTO;
import com.dto.coupons.CouponServiceResDTO;
import com.dto.coupons.CouponUpdateApproveResDTO;
import com.dto.coupons.CreatedByResDTO;
import com.dto.coupons.ICouponListResDTO;
import com.dto.coupons.PopupTreeResDTO;
import com.dto.coupons.PricingCouponResDTO;
import com.dto.coupons.mailParam.CouponMailParamDTO;
import com.dto.coupons.mailParam.MailSendParamDTO;
import com.dto.coupons.mailParam.ObjectCouponNameDTO;
import com.dto.file.attach.FileAttachResDTO;
import com.dto.marketingCampaign.adminPortal.McCouponDTO;
import com.dto.marketingCampaign.adminPortal.SimpleCouponDTO;
import com.dto.marketingCampaign.jsonObject.McAdsPositionDTO;
import com.dto.marketingCampaign.jsonObject.McCommonBannerDTO;
import com.dto.marketingCampaign.jsonObject.McHomepageDTO;
import com.dto.marketingCampaign.jsonObject.McImageDTO;
import com.dto.marketingCampaign.jsonObject.McPopupDTO;
import com.dto.marketingCampaign.jsonObject.McServicePageDTO;
import com.dto.pricing.ICommonPricingServiceDTO;
import com.dto.pricing.PricingApplyDTO;
import com.dto.pricing.PricingSaaSResDTO;
import com.dto.product_solustions.IGetCouponMcDTO;
import com.dto.product_solustions.IGetCouponPackageDTO;
import com.dto.subscriptions.CouponMcDevAdminReqDTO;
import com.dto.subscriptions.CouponPopupDTO;
import com.dto.subscriptions.CouponPopupItfDTO;
import com.dto.subscriptions.responseDTO.PriceAddonDTO;
import com.entity.addons.Addon;
import com.entity.combo.Combo;
import com.entity.combo.ComboPlan;
import com.entity.couponSet.CouponSet;
import com.entity.coupons.Coupon;
import com.entity.coupons.CouponAddon;
import com.entity.coupons.CouponAdsPosition;
import com.entity.coupons.CouponComboPlan;
import com.entity.coupons.CouponComboPlanApply;
import com.entity.coupons.CouponDraft;
import com.entity.coupons.CouponEnterprise;
import com.entity.coupons.CouponPricing;
import com.entity.coupons.CouponPricingApply;
import com.entity.coupons.CouponPricingPlan;
import com.entity.coupons.CouponPromotionCondition;
import com.entity.coupons.CouponSupplier;
import com.entity.coupons.CouponVariant;
import com.entity.coupons.CouponVariantApply;
import com.entity.department.Department;
import com.entity.events.Events;
import com.entity.file.attach.FileAttach;
import com.entity.pricing.CouponPricingPlanApply;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingMultiPlan;
import com.entity.product_variant.Variant;
import com.entity.services.ServiceEntity;
import com.enums.ApproveStatusEnum;
import com.enums.ComponentVisible;
import com.enums.EventTypeEnum;
import com.enums.SystemParamEnum;
import com.event.ComponentChangedEvent;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import com.mapper.CouponAdminMapper;
import com.mapper.CouponDevMapper;
import com.mapper.CouponMapper;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.enums.coupons.TimeUsedTypeEnum;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.emails.ParamEmailEnum;
import com.onedx.common.constants.enums.fileAttach.FileAttachTypeEnum;
import com.onedx.common.constants.enums.pricings.BonusTypeEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.MessageConst;
import com.onedx.common.constants.values.RoleConst;
import com.onedx.common.constants.values.SubscriptionHistoryConstant;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingAddonDTO;
import com.onedx.common.dto.mail.MailParamResDTO;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.dto.users.UserDepartmentDTO;
import com.onedx.common.entity.subscriptions.SubscriptionHistory;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.MessageKeyConstant.Validation;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.repository.emails.mailTemplate.ParamEmailRepository;
import com.onedx.common.repository.subscriptions.SubscriptionHistoryRepository;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.ObjectUtil;
import com.onedx.common.utils.SqlUtils;
import com.repository.addons.AddonRepository;
import com.repository.categories.CategoryRepository;
import com.repository.combo.ComboPlanRepository;
import com.repository.combo.ComboRepository;
import com.repository.couponSet.CouponSetRepository;
import com.repository.coupons.CouponAddonRepository;
import com.repository.coupons.CouponAdsPositionRepository;
import com.repository.coupons.CouponComboPlanApplyRepository;
import com.repository.coupons.CouponComboPlanRepository;
import com.repository.coupons.CouponDraftRepository;
import com.repository.coupons.CouponEnterpriseRepository;
import com.repository.coupons.CouponPricingApplyRepository;
import com.repository.coupons.CouponPricingPlanApplyRepository;
import com.repository.coupons.CouponPricingPlanRepository;
import com.repository.coupons.CouponPricingRepository;
import com.repository.coupons.CouponPromotionConditionRepository;
import com.repository.coupons.CouponRepository;
import com.repository.coupons.CouponSupplierRepository;
import com.repository.coupons.CouponVariantApplyRepository;
import com.repository.coupons.CouponVariantRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.file.attach.FileAttachRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingRepository;
import com.repository.product_variant.VariantRepository;
import com.repository.services.ServiceRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.users.UserRepository;
import com.service.combo.ComboService;
import com.service.coupon.CouponService;
import com.service.couponSet.CouponSetService;
import com.service.email.EmailService;
import com.service.email.EmailTemplateService;
import com.service.events.EventsService;
import com.service.pricing.PricingService;
import com.service.report.dashboardSme.ReportService;
import com.service.system.param.SystemParamService;
import com.service.utils.condition.ConditionExecute;
import com.service.utils.jsonObject.McConditionItemGroupDTO;
import com.util.AuthUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CouponServiceImpl implements CouponService {

    private static final String ROLE_TYPE_DEV_ADMIN = "DEV_ADMIN";
    private static final String ROLE_TYPE_DEV_EMPLOYEE = "DEV";
    private static final String COUPON_PARAM = "COUPON";
    private static final String MESSAGE_SUCCESS = "Xóa chương trình khuyến mại thành công";
    private static final String MESSAGE_SUCCESS_WITHOUT_SUBSCRIPTION = "Chương trình khuyến mại chưa được đăng ký đã được xóa thành công";
    private static final Long DEFAULT_PARENT_ID = -1L;
    private static final String TITLE_CYCLE_TYPE_DAILY = "ngày";
    private static final String TITLE_CYCLE_TYPE_WEEKLY = "tuần";
    private static final String TITLE_CYCLE_TYPE_MONTHLY = "tháng";
    private static final String TITLE_CYCLE_TYPE_YEARLY = "năm";
    private static final String TITLE_CYCLE_TYPE_UNLIMITED = "Không giới hạn";
    private static final String TITLE_ADDON_ONCE = "Một lần";
    private static final String SEARCH_PRICING_TYPE = "PRICING";
    private static final String[] COUPON = new String[]{"coupon"};
    private static final String[] couponM = {"coupon"};

    @Autowired
    private CouponRepository couponRepository;
    @Autowired
    private ComboService comboService;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private CouponAddonRepository couponAddonRepository;
    @Autowired
    private CouponEnterpriseRepository couponEnterpriseRepository;
    @Autowired
    private CouponPricingRepository couponPricingRepository;
    @Autowired
    private CouponComboPlanRepository couponComboPlanRepository;
    @Autowired
    private CouponPricingApplyRepository couponPricingApplyRepository;
    @Autowired
    private ParamEmailRepository paramEmailRepository;
    @Autowired
    private EmailService emailService;
    @Autowired
    private ReportService reportService;
    @Autowired
    private CouponComboPlanApplyRepository couponComboPlanApplyRepository;
    @Autowired
    private CouponMapper couponMapper;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private AddonRepository addonRepository;
    @Autowired
    private CouponDevMapper couponDevMapper;
    @Autowired
    private CouponSupplierRepository couponSupplierRepository;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private CouponAdminMapper couponAdminMapper;
    @Autowired
    private CouponDraftRepository couponDraftRepository;
    @Autowired
    private DepartmentsRepository departmentsRepository;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private SubscriptionHistoryRepository subscriptionHistoryRepository;
    @Autowired
    private ComboRepository comboRepository;
    @Autowired
    private ServiceRepository serviceRepository;
    @Autowired
    private CategoryRepository categoryRepository;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;
    @Autowired
    private CouponPricingPlanRepository couponPricingPlanRepository;
    @Autowired
    private CouponPricingPlanApplyRepository couponPricingPlanApplyRepository;
    @Autowired
    private ComboPlanRepository comboPlanRepository;
    @Autowired
    private EmailTemplateService emailTemplateService;
    @Autowired
    private CouponConditionService couponConditionService;
    @Autowired
    private CouponSetService couponSetService;
    @Autowired
    private CouponSetRepository couponSetRepository;
    @Autowired
    private PricingService pricingService;
    @Autowired
    private FileAttachRepository fileAttachRepository;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private CouponAdsPositionRepository couponAdsPositionRepository;
    @Autowired
    private CouponPromotionConditionRepository couponPromotionConditionRepository;
    @Autowired
    private CouponVariantRepository couponVariantRepository;
    @Autowired
    private CouponVariantApplyRepository couponVariantApplyRepository;
    @Autowired
    private VariantRepository variantRepository;
    @Autowired
    private EventsService eventsService;

    private final ApplicationEventPublisher publisher;

    public CouponServiceImpl(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }

    @Override
    public Coupon findByIdAndDeletedFlag(Long couponId, Integer deletedFlag) {
        return couponRepository.findByIdAndDeletedFlag(couponId, deletedFlag)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.COUPON, ErrorKey.ID, String.valueOf(couponId)));
    }

    @Override
    @Transactional(readOnly = true)
    public CouponDetailDTO getCouponDetail(Long id, boolean isAdmin) {
        Coupon coupon = couponRepository
            .findCouponByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED
                .getValue()).orElseThrow(() -> {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, couponM,
                    LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(message, Resources.COUPON, ErrorKey.ID,
                    MessageKeyConstant.NOT_FOUND);
            });

        if (isAdmin) {
            if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(),
                RoleType.FULL_ADMIN.getValue(), RoleType.CUSTOMER_SUPPORT.getValue()))) {
                // CÙng tỉnh thành mới xem chi tiết coupon
                Long provinceIdPlan = AuthUtil.getDepartment().getProvinceId();
                if (Objects.nonNull(provinceIdPlan)) {
                    Long provinceId = departmentsRepository.getProvinceIdByUserId(coupon.getCreatedBy());
                    if (PortalType.ADMIN.getType() == coupon.getPortal() && !Objects.equals(provinceId, provinceIdPlan) && Objects.nonNull(provinceId)) {
                        throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
                    }
                }
            }
        }

//        Lấy dữ liệu bảng CouponDraft
//        Khi cập nhật coupon hoạt động sẽ lưu thông tin vào CouponDraft
//        Khi click xác nhận : Bản ghi coponDraft đó bị xóa
        Optional<CouponDraft> couponDraft = couponDraftRepository.findByCouponId(id);
        // kiểm tra xem user đang login là Dev Admin hay không
        boolean checkUserAdminViewDetail = AuthUtil.checkUserRoles(Collections.singletonList(RoleType.DEVELOPER.getValue()));
        // Check xem coupon có phải là User đang login tạo hay không
        if (Objects.equals(coupon.getPortal(), PortalType.ADMIN.getType()) && !isAdmin) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        Long currentUserId = AuthUtil.getCurrentUser().getId();
        if (!isAdmin &&
            !Objects.equals(coupon.getCreatedBy(), currentUserId) &&
            !Objects.equals(coupon.getUserId(), currentUserId)) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        CouponDetailDTO couponDetailDTO = couponMapper.toDto(coupon);

        // lấy ra danh sách coupon pricing
        List<CouponPricingServiceDetailDTO> couponPricingServiceDetailDB = couponPricingApplyRepository
            .getCouponPricingServiceApplyDetail(couponDetailDTO.getId());
        List<CouponPricingDetailDTO> couponPricings = getPricingsMultiPlanOfCouponDetailDTO(couponDetailDTO.getId(),
            couponPricingServiceDetailDB, true);

        Set<Long> ids  = new HashSet<>();
        ids.add(couponDetailDTO.getId());
        Set<Long> allowDelete = couponRepository.getCouponIdSubscription(ids);

        if (allowDelete.isEmpty()) {
            couponDetailDTO.setAllowDelete(YesNoEnum.YES);
        } else {
            couponDetailDTO.setAllowDelete(YesNoEnum.NO);
        }

        // lấy ra danh sách coupon combo plan
        List<CouponPricingServiceDetailDTO> couponComboDetailDB = couponComboPlanRepository
            .getCouponComboPlanDetail(couponDetailDTO.getId());
        List<CouponPricingDetailDTO> couponComboPlans = getComboOldPlanOfCouponDetailDTO(couponComboDetailDB);

        if (!CollectionUtils.isEmpty(couponComboPlans)) {
            couponPricings.addAll(couponComboPlans);
        }

        // lấy ra danh sách coupon pricing apply
        List<CouponPricingServiceDetailDTO> couponPricingServiceApplyDetailDB = couponPricingPlanRepository
            .getCouponPricingServiceDetail(couponDetailDTO.getId());
        List<CouponPricingDetailDTO> couponPricingApply = getPricingsMultiPlanOfCouponDetailDTO(couponDetailDTO.getId(),
            couponPricingServiceApplyDetailDB, false);

        // lấy ra danh sách coupon combo plan apply
        List<CouponPricingServiceDetailDTO> couponComboApplyDetailDB = couponComboPlanApplyRepository
            .getCouponComboPlanApplyDetail(couponDetailDTO.getId());
        List<CouponPricingDetailDTO> couponComboPlanApplies = getComboOldPlanOfCouponDetailDTO(couponComboApplyDetailDB);

        if (!CollectionUtils.isEmpty(couponComboPlanApplies)) {
            couponPricingApply.addAll(couponComboPlanApplies);
        }

        List<CouponEnterpriseDetailDTO> enterpriseDetailDTOS = couponEnterpriseRepository
            .getCouponEnterprise(couponDetailDTO.getId());

        // lấy ra danh sách addon
        List<CouponAddonServiceDetailDTO> couponAddonServiceApplyDetailDB = couponPricingPlanRepository
            .getCouponAddonServiceDetail(couponDetailDTO.getId());
        Set<CouponAddonsDetailDTO> couponAddonsDetailDTOS = getAddonsMultiPlanOfCouponDetailDTO(couponDetailDTO.getId(),
            couponAddonServiceApplyDetailDB);

        couponDetailDTO.setCouponPricing(couponPricings);
        couponDetailDTO.setCouponPricingApply(couponPricingApply);
        couponDetailDTO.setCouponEnterprise(enterpriseDetailDTOS);
        couponDetailDTO.setCouponAddons(couponAddonsDetailDTOS);
        // Trường hợp Admin xem chi tiết thì thêm couponSupplier (Nhà cung cấp)
        if (isAdmin) {
            List<CouponEnterpriseDetailDTO> couponSupplier = couponSupplierRepository
                .getCouponSupplier(couponDetailDTO.getId());
            couponDetailDTO.setSuppliers(couponSupplier);
            String adminType = couponRepository.getAdminTypeById(id);
            couponDetailDTO.setAdminType(Objects.isNull(adminType) ? "" : adminType);
        }
        if (couponDraft.isPresent()) {
            couponDetailDTO.setNameDraft(couponDraft.get().getName());
            couponDetailDTO.setMaxUsedDraft(couponDraft.get().getMaxUsed());
            couponDetailDTO.setMaximumPromotionDraft(couponDraft.get().getMaximumPromotion());
            couponDetailDTO.setStatusDraft(StatusEnum.valueOf(couponDraft.get().getStatus()));
            couponDetailDTO.setEndDateDraft(couponDraft.get().getEndDate());
            couponDetailDTO.setVisibleStatusDraft(couponDraft.get().getVisibleStatus());
        }
        getBanner(couponDetailDTO);
        CouponAdsPosition couponAdsPosition = couponAdsPositionRepository.getByCouponId(couponDetailDTO.getId());
        if (couponAdsPosition != null) {
            couponDetailDTO.setAdvertisePosition(couponAdsPosition.getJsonObject());
        }
        // Lấy thông tin condition
        CouponPromotionCondition couponCondition = couponPromotionConditionRepository.findByCouponId(couponDetailDTO.getId()).orElse(null);
        if (Objects.nonNull(couponCondition)) {
            couponDetailDTO.setCondition(couponCondition.getCondition());
        }
        return couponDetailDTO;
    }

    /**
     * hàm lấy ra list addons cho api chi tiết coupon
     */
    public Set<CouponAddonsDetailDTO> getAddonsMultiPlanOfCouponDetailDTO(Long couponId,
        List<CouponAddonServiceDetailDTO> couponAddonServiceApplyDetailDB) {
        Set<CouponAddonsDetailDTO> response = new HashSet<>();
        couponAddonServiceApplyDetailDB.forEach(addon -> {
            boolean isMultiPlan = pricingMultiPlanRepository
                .existsByAddonIdAndDeletedFlag(addon.getAddonId(), DeletedFlag.NOT_YET_DELETED.getValue());
            String addonKey = addon.getServiceName() + CharacterConstant.UNDERLINED + addon.getServiceId() + CharacterConstant.SLASH + addon
                .getAddonCode() + CharacterConstant.UNDERLINED + addon.getAddonId();
            // nếu addon là loại nhiều kế hoạch định giá
            if (YesNoEnum.YES.equals(addon.getIsMultiPlan())) {
                List<PricingMultiPlan> pricingMultiPlanDBs = pricingMultiPlanRepository
                    .getAddonMultiPlanIsUsingByCouponIdAndPricingId(couponId, addon.getAddonId());
                pricingMultiPlanDBs.forEach(pricingMultiPlanDB -> {
                    // set thong tin addon
                    CouponAddonsDetailDTO element = new CouponAddonsDetailDTO();
                    element.setServiceName(addon.getServiceName());
                    element.setServiceId(addon.getServiceId());
                    element.setAddonsId(addon.getAddonId());
                    element.setCode(addon.getAddonCode());
                    element.setName(addon.getAddonName());
                    // format lại key giống popup gửi lên
                    // convert lại tên pricing plan (vd: 1 tháng)
                    String currentCycleType = getTitleCycleTypeForCouponDetailDTO(pricingMultiPlanDB.getCircleType());

                    element.setId(pricingMultiPlanDB.getId());
                    element.setKey(addonKey + CharacterConstant.SLASH + pricingMultiPlanDB.getAddonId() + CharacterConstant.UNDERLINED
                        + pricingMultiPlanDB.getId());
                    element.setTitle(pricingMultiPlanDB.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType);
                    element.setIsMultiPlan(addon.getIsMultiPlan());
                    element.setType(addon.getBonusType());
                    response.add(element);
                });
                // nếu addon la Periodic && khong multi plan
            } else if (BonusTypeEnum.PERIODIC.equals(addon.getBonusType()) && !isMultiPlan) {
                // set thong tin addon
                CouponAddonsDetailDTO element = new CouponAddonsDetailDTO();
                element.setServiceName(addon.getServiceName());
                element.setServiceId(addon.getServiceId());
                element.setAddonsId(addon.getAddonId());
                element.setCode(addon.getAddonCode());
                element.setName(addon.getAddonName());
                // nếu mà là loại định kỳ
                String currentCycleType = getTitleCycleTypeForCouponDetailDTO(addon.getType());
                element.setId(addon.getAddonId());
                element.setKey(addonKey + CharacterConstant.SLASH + addon.getBonusValue() + CharacterConstant.UNDERLINED + addon
                    .getType());
                element.setTitle(addon.getBonusValue() + CharacterConstant.SPACE + currentCycleType);
                element.setIsMultiPlan(addon.getIsMultiPlan());
                element.setType(addon.getBonusType());
                response.add(element);
            }
            // nếu addon la Periodic && co multi plan
            else if (BonusTypeEnum.PERIODIC.equals(addon.getBonusType()) && isMultiPlan) {
                // set thong tin addon
                List <CouponAddonsDetailDTO> elements = new ArrayList<>();
                pricingMultiPlanRepository.findByAddonIdAndDeletedFlag(addon.getAddonId(), DeletedFlag.NOT_YET_DELETED.getValue()).forEach(
                    multi -> {
                        CouponAddonsDetailDTO element = new CouponAddonsDetailDTO();
                        element.setServiceName(addon.getServiceName());
                        element.setServiceId(addon.getServiceId());
                        element.setAddonsId(addon.getAddonId());
                        element.setCode(addon.getAddonCode());
                        element.setName(addon.getAddonName());
                        // nếu mà là loại định kỳ
                        String currentCycleType = getTitleCycleTypeForCouponDetailDTO(addon.getType());
                        element.setId(addon.getAddonId());
                        element.setKey(addonKey + CharacterConstant.SLASH + multi.getPaymentCycle() + CharacterConstant.UNDERLINED
                            + multi.getCircleType());
                        element.setTitle(multi.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType);
                        element.setIsMultiPlan(addon.getIsMultiPlan());
                        element.setType(addon.getBonusType());
                        elements.add(element);
                    }
                );
                response.addAll(elements);
            }
            else {
                // set thong tin addon
                CouponAddonsDetailDTO element = new CouponAddonsDetailDTO();
                element.setServiceName(addon.getServiceName());
                element.setServiceId(addon.getServiceId());
                element.setAddonsId(addon.getAddonId());
                element.setCode(addon.getAddonCode());
                element.setName(addon.getAddonName());
                // nếu không là loại 1 lần
                // Trả về 1 phần tử có tile là: Một lần
                element.setId(addon.getAddonId());
                element.setKey(addonKey + CharacterConstant.SLASH + addon.getBonusType().value);
                element.setTitle(TITLE_ADDON_ONCE);
                element.setIsMultiPlan(addon.getIsMultiPlan());
                element.setType(addon.getBonusType());
                response.add(element);
            }
        });
        return response;
    }

    /**
     * Hàm lấy ra danh sách combo plan cho api chi tiết coupon
     */
    public List<CouponPricingDetailDTO> getComboOldPlanOfCouponDetailDTO(
        List<CouponPricingServiceDetailDTO> couponPricingServiceDetailDB) {
        List<CouponPricingDetailDTO> response = new ArrayList<>();

        couponPricingServiceDetailDB.forEach(couponPricingDB -> {
            CouponPricingDetailDTO element = new CouponPricingDetailDTO();
            ComboPlan comboPlan = comboPlanRepository.findByIdAndDeletedFlag(couponPricingDB.getPricingId(), DeletedFlag.NOT_YET_DELETED
                .getValue()).orElseThrow(() -> {
                String message = messageSource.getMessage(
                    MessageKeyConstant.NOT_FOUND, COUPON,
                    LocaleContextHolder.getLocale());
                return new ResourceNotFoundException(message, Resources.COUPON,
                    ErrorKey.Coupon.COMBO_PLAN_ID, MessageKeyConstant.NOT_FOUND);
            });
            // format lại key của children như lúc tạo coupon truyền lên
            String pricingPlanKey =
                couponPricingDB.getServiceName() + CharacterConstant.UNDERLINED + couponPricingDB.getServiceId()
                    + CharacterConstant.SLASH + couponPricingDB
                    .getPricingCode() + CharacterConstant.UNDERLINED + couponPricingDB.getPricingId() + CharacterConstant.SLASH
                    + comboPlan.getPaymentCycle() + CharacterConstant.UNDERLINED + comboPlan.getCycleType();
            // format lại title của children như lúc tạo khi truyền lên
            String currentCycleType = getTitleCycleTypeForCouponDetailDTO(comboPlan.getCycleType());
            element.setPricingId(couponPricingDB.getPricingId());
            element.setPricingName(couponPricingDB.getPricingName());
            element.setServiceId(couponPricingDB.getServiceId());
            element.setServiceName(couponPricingDB.getServiceName());
            element.setServiceName(couponPricingDB.getServiceName());
            element.setPrice(comboPlan.getPrice());
            element.setType(couponPricingDB.getType());
            element.setId(comboPlan.getId());
            element.setKey(pricingPlanKey);
            element.setIsMultiPlan(couponPricingDB.getIsMultiPlan());
            element.setTitle(comboPlan.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType);
            response.add(element);
        });

        return response;
    }

    /**
     * Lấy ra list pricing cho api chi tiết coupon
     */
    public List<CouponPricingDetailDTO> getPricingsMultiPlanOfCouponDetailDTO(Long couponId,
        List<CouponPricingServiceDetailDTO> couponPricingServiceDetailDB, boolean isPlanApply) {
        List<CouponPricingDetailDTO> response = new ArrayList<>();
        couponPricingServiceDetailDB.forEach(couponPricingDB -> {
            // lấy ra chu kỳ pricing đang sử dụng
            // nếu là multi plan thì lấy trong bảng pricing_multi_plan
            List<CouponPricingDetailDTO> element;
            if (YesNoEnum.YES.equals(couponPricingDB.getIsMultiPlan())) {
                // lấy list chu kỳ thanh toán cho coupon pricing plan
                element = getPricingHasMultiPlanForCouponDetailDTO(couponId, couponPricingDB, isPlanApply);
            } else {
                // nếu không là multi plan thì lấy trong bảng pricing
                element = Collections.singletonList(getPricingOldPlanForCouponDetailDTO(couponId, couponPricingDB));
            }
            response.addAll(element);
        });

        return response;
    }

    /**
     * Lấy ra coupon pricing dto mà pricing không phải là multi period
     */
    private CouponPricingDetailDTO getPricingOldPlanForCouponDetailDTO(Long couponId, CouponPricingServiceDetailDTO couponPricingDB) {
        CouponPricingDetailDTO element = new CouponPricingDetailDTO();
        Pricing pricing = pricingRepository.findByIdAndDeletedFlag(couponPricingDB.getPricingId(), DeletedFlag.NOT_YET_DELETED
            .getValue()).orElseThrow(() -> {
            String message = messageSource.getMessage(
                MessageKeyConstant.NOT_FOUND, COUPON,
                LocaleContextHolder.getLocale());
            return new ResourceNotFoundException(message, Resources.COUPON,
                ErrorKey.Coupon.PRICING_ID, MessageKeyConstant.NOT_FOUND);
        });
        // format lại key của children như lúc tạo coupon truyền lên
        String pricingPlanKey =
            couponPricingDB.getServiceName() + CharacterConstant.UNDERLINED + couponPricingDB.getServiceId()
                + CharacterConstant.SLASH + couponPricingDB
                .getPricingCode() + CharacterConstant.UNDERLINED + couponPricingDB.getPricingId() + CharacterConstant.SLASH
                + pricing.getPaymentCycle() + CharacterConstant.UNDERLINED + pricing.getPaymentCycle();
        // format lại title của children như lúc tạo khi truyền lên
        String currentCycleType = getTitleCycleTypeForCouponDetailDTO(pricing.getCycleType());
        element.setPricingId(couponPricingDB.getPricingId());
        element.setPricingName(couponPricingDB.getPricingName());
        element.setServiceId(couponPricingDB.getServiceId());
        element.setServiceName(couponPricingDB.getServiceName());
        element.setServiceName(couponPricingDB.getServiceName());
        element.setPrice(pricing.getPrice());
        element.setType(couponPricingDB.getType());
        element.setId(pricing.getId());
        element.setKey(pricingPlanKey);
        element.setIsMultiPlan(couponPricingDB.getIsMultiPlan());
        element.setTitle(pricing.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType);
        return element;
    }

    /**
     * Lấy ra title của chu kỳ thanh toán pricing cho coupon detail response
     */
    private String getTitleCycleTypeForCouponDetailDTO(Integer type) {
        type = Objects.nonNull(type) ? type : -1;
        return type.equals(CycleTypeEnum.DAILY.value) ? TITLE_CYCLE_TYPE_DAILY
            : type.equals(CycleTypeEnum.WEEKLY.value) ? TITLE_CYCLE_TYPE_WEEKLY
                : type.equals(CycleTypeEnum.MONTHLY.value) ? TITLE_CYCLE_TYPE_MONTHLY
                    : type.equals(CycleTypeEnum.YEARLY.value) ? TITLE_CYCLE_TYPE_YEARLY
                        : TITLE_CYCLE_TYPE_UNLIMITED;
    }


    /**
     * Lấy ra coupon pricing dto mà pricing là multi period
     */
    private List<CouponPricingDetailDTO> getPricingHasMultiPlanForCouponDetailDTO(Long couponId, CouponPricingServiceDetailDTO couponPricingDB,
        boolean isPlanApply) {
        List<CouponPricingDetailDTO> elements = new ArrayList<>();
        List<PricingMultiPlan> pricingMultiPlanDBs ;
        if (isPlanApply) {
            pricingMultiPlanDBs = pricingMultiPlanRepository
                .getPricingMultiPlanApplyIsUsingByCouponIdAndPricingId(couponId, couponPricingDB.getPricingId());
        } else {
            // lấy list chu kỳ thanh toán cho coupon pricing plan
            pricingMultiPlanDBs = pricingMultiPlanRepository
                .getPricingMultiPlanIsUsingByCouponIdAndPricingId(couponId, couponPricingDB.getPricingId());
        }
        pricingMultiPlanDBs.forEach(pricingMultiPlanDB -> {
            CouponPricingDetailDTO element = new CouponPricingDetailDTO();
            // format lại key của children như lúc tạo coupon truyền lên
            String pricingPlanKey =
                couponPricingDB.getServiceName() + CharacterConstant.UNDERLINED + couponPricingDB.getServiceId()
                    + CharacterConstant.SLASH + couponPricingDB
                    .getPricingCode() + CharacterConstant.UNDERLINED + couponPricingDB.getPricingId() + CharacterConstant.SLASH
                    + pricingMultiPlanDB
                    .getPricingId() + CharacterConstant.UNDERLINED
                    + pricingMultiPlanDB.getId();

            // format lại title của children như lúc tạo khi truyền lên
            String currentCycleType = getTitleCycleTypeForCouponDetailDTO(pricingMultiPlanDB.getCircleType());

            element.setPricingId(couponPricingDB.getPricingId());
            element.setPricingName(couponPricingDB.getPricingName());
            element.setServiceId(couponPricingDB.getServiceId());
            element.setServiceName(couponPricingDB.getServiceName());
            element.setServiceName(couponPricingDB.getServiceName());
            element.setPrice(pricingMultiPlanDB.getPrice());
            element.setType(couponPricingDB.getType());
            element.setId(pricingMultiPlanDB.getId());
            element.setKey(pricingPlanKey);
            element.setIsMultiPlan(couponPricingDB.getIsMultiPlan());
            element.setTitle(pricingMultiPlanDB.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType);
            element.setPricingMultiPlanId(pricingMultiPlanDB.getId());
            elements.add(element);
        });
        return elements;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponseDTO approveStatus(Long id,
            CouponApproveDTO approveStatusDTO) {
        Coupon coupon = getCurrentCoupon(id);
        List<String> roleList = userRepository.getRole(coupon.getUserId());
        if (roleList.contains(RoleType.ADMIN.getValue()) || roleList.contains(RoleType.FULL_ADMIN.getValue())) {
            Long provinceId = AuthUtil.getDepartment().getProvinceId();
            Long provinceIdDb = departmentsRepository.getProvinceIdByUserId(coupon.getCreatedBy());
            if (PortalType.DEV.getType() == coupon.getPortal()) {
                provinceIdDb = departmentsRepository.getProvinceIdByUserId(coupon.getUserId());
            }

            // Neu admin tong tao
            if (Objects.isNull(provinceIdDb)) {
                // Neu admin tinh thanh => loi
                if (Objects.nonNull(provinceId)) {
                    throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
                }
            } else {
                // Neu admin tinh tao
                // Neu la admin tong hoac admin tinh thanh khac
                if (Objects.isNull(provinceId) || !Objects.equals(provinceId, provinceIdDb)) {
                    throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
                }
            }
        }
        // Kiểm tra quyền phê duyệt
        // Neu portal la ADMIN -> chỉ SUPER_ADMIN được phê duyệt
        // Neu portal la DEV -> cả SUPER_ADMIN và ADMIN đều được phê duyệt
        if (!AuthUtil.checkUserRoles(Collections.singletonList(RoleType.FULL_ADMIN.getValue()))) {
            if (PortalType.DEV.getType()!= coupon.getPortal()) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
        }

        // ApproveStatus khác AWAITING_APPROVAL
        if (ApproveStatusEnum.AWAITING_APPROVAL.value != coupon.getApprove()) {
            throw throwCouponBadRequest(MessageKeyConstant.APPROVE_NOT_CHANGE, ErrorKey.Coupon.APPROVE, Resources.COUPON);
        }

        // set email template code
        EmailCodeEnum emailCodeEnum;
        Map<String, String> mapDefaultValue = new HashMap<>();

        // ApproveStatus là AWAITING_APPROVAL
        if (ApproveStatusEnum.APPROVED.equals(approveStatusDTO.getApproveStatus())) {
            emailCodeEnum = EmailCodeEnum.CP13;
        } else if (ApproveStatusEnum.UNAPPROVED.equals(approveStatusDTO.getApproveStatus())) {
            if (StringUtils.isEmpty(approveStatusDTO.getComment())) {
                throw throwCouponBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Coupon.COMMENT, Resources.COUPON);
            }

            // set email template code
            emailCodeEnum = EmailCodeEnum.CP15;
            mapDefaultValue.putIfAbsent(ParamEmailEnum.UPDATE_REASON.getValue(), approveStatusDTO.getComment());

        } else if (ApproveStatusEnum.REJECTED.equals(approveStatusDTO.getApproveStatus())) {
            if (StringUtils.isEmpty(approveStatusDTO.getComment())) {
                throw throwCouponBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Coupon.COMMENT, Resources.COUPON);
            }

            // set email template code
            emailCodeEnum = EmailCodeEnum.CP14;
            mapDefaultValue.putIfAbsent(ParamEmailEnum.REJECT_REASON.getValue(), approveStatusDTO.getComment());
        } else {
            throw throwCouponBadRequest(MessageKeyConstant.APPROVE_NOT_CHANGE, ErrorKey.Coupon.APPROVE, Resources.COUPON);
        }

        coupon.setComment(approveStatusDTO.getComment());
        coupon.setApprove(approveStatusDTO.getApproveStatus().value);
        couponRepository.save(coupon);
        log.info("===>UpdateCoupon {} {}",coupon.getId(), coupon.getPricingType());

        // Lấy thông tin couponDraft trước khi có thể return
        Optional<CouponDraft> couponDraft = couponDraftRepository.findByCouponId(id);

        Map<String, Object> eventMetadata = new HashMap<>();
        eventMetadata.put("couponId", id);
        if (couponDraft.isPresent()) {
            eventMetadata.put("couponDraftId", couponDraft.get().getId());
        }

        // Lưu event vào database ngay lập tức
        Events savedEvent = eventsService.saveEvent(EventTypeEnum.COUPON_UPGRADED, eventMetadata);

        // Đăng ký gửi event sau khi commit - chỉ gửi eventId
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                publisher.publishEvent(new ComponentChangedEvent(
                    CouponServiceImpl.this,
                    savedEvent.getId()
                ));
            }
        });

        // sent email
        Optional<User> userOpt = userRepository.findByIdAndDeletedFlagAndStatus(coupon.getCreatedBy(), EntitiesConstant.DeleteFlag.ACTIVE, StatusEnum.ACTIVE.value);
        if (!userOpt.isPresent()) {
            return new BaseResponseDTO(id);
        }

        User user = userOpt.get();
        mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_COUPON.getValue(), coupon.getName());
        mapDefaultValue.putIfAbsent(ParamEmailEnum.USER.getValue(), user.getName());

        Optional<com.model.dto.EmailTemplate> emailTemplateOpt = emailTemplateService
            .replaceParamEmailTemplate(emailCodeEnum.getValue(), mapDefaultValue, null);

//        if (emailTemplateOpt.isPresent()) {
//            emailService.save(user.getEmail(), emailTemplateOpt.get());
//        }

        if (Objects.isNull(user.getParentId()) || SubscriptionConstant.PARENT_ID.equals(user.getParentId())) {
            return new BaseResponseDTO(id);
        }

        Optional<User> userParentOpt = userRepository.findByIdAndDeletedFlagAndStatus(user.getParentId(), EntitiesConstant.DeleteFlag.ACTIVE, StatusEnum.ACTIVE.value);
        if (!userParentOpt.isPresent()) {
            return new BaseResponseDTO(id);
        }

        mapDefaultValue.put(ParamEmailEnum.USER.getValue(), userParentOpt.get().getName());
//        Optional<com.model.dto.EmailTemplate> emailTemplateUserParentOpt = emailTemplateService
//            .replaceParamEmailTemplate(emailCodeEnum.getValue(), mapDefaultValue, null);
//        if (emailTemplateUserParentOpt.isPresent()) {
//            emailService.save(userParentOpt.get().getEmail(), emailTemplateOpt.get());
//        }

        return new BaseResponseDTO(id);
    }

    /**
     * check coupon_id exits ?
     *
     */
    private Coupon getCurrentCoupon(Long id) {
        Optional<Coupon> couponOpt = couponRepository
                .findCouponByIdAndDeletedFlag(id,
                        EntitiesConstant.DeleteFlag.ACTIVE);
        if (!couponOpt.isPresent()) {
            String message = messageSource.getMessage(
                    MessageKeyConstant.NOT_FOUND, COUPON,
                    LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.COUPON,
                    ErrorKey.Coupon.ID, MessageKeyConstant.NOT_FOUND);
        }
        return couponOpt.get();
    }

    @Override
    public void updateStatus(Long id, StatusEnum status, PortalType portalType) {
        Coupon coupon = couponRepository.findCouponByIdAndDeletedFlag(id, EntitiesConstant.DeleteFlag.ACTIVE)
                .orElseThrow(() -> {
                    String message = messageSource
                            .getMessage(MessageKeyConstant.NOT_FOUND, COUPON, LocaleContextHolder.getLocale());
                    return new ResourceNotFoundException(message, Resources.COUPON, ErrorKey.Coupon.ID,
                            MessageKeyConstant.NOT_FOUND);
                });
        //Kiểm tra coupon có đang ở trạng thái duyệt không
        if (!coupon.getApprove().equals(ApproveStatusEnum.APPROVED.value)) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.COUPON_NEED_APPROVE, COUPON, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.ID,
                    MessageKeyConstant.COUPON_NEED_APPROVE);
        }
        //Nếu là coupon do dev tạo thì chỉ đc bật tắt coupon do nó tạo hoặc con của nó tạo
        if (Objects.equals(portalType, PortalType.DEV)
            && !AuthUtil.getCurrentUser().getId().equals(coupon.getCreatedBy())
            && !AuthUtil.getCurrentParentId().equals(coupon.getUserId())) {//HiepNT fix doanh nghiệp đăng nhập tạo ra coupon đc sửa
            String message = messageSource
                .getMessage(MessageKeyConstant.USER_NOT_OWN_COUPON, COUPON, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.ID,
                MessageKeyConstant.USER_NOT_OWN_COUPON);
        }
        //Nếu user đăng nhập là admin
        if (Objects.equals(portalType, PortalType.ADMIN)) {
            //Không được bật tắt coupon do dev tạo
            if (Objects.equals(coupon.getPortal(), PortalType.DEV.getType())) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
            //Chỉ admin có thể tắt bật được là admin tổng hoăc admin của tỉnh thành đó
            Long provinceIdDB = comboRepository.getProvinceOfUser(coupon.getCreatedBy());
            Long provinceId = AuthUtil.getDepartment().getProvinceId();
            if (Objects.nonNull(provinceId) && !Objects
                .equals(provinceId, provinceIdDB)) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
        }
        //Case: Không chọn ngày bắt đầu thì sẽ có hiệu lực từ khi phê duyệt và bật hiển thị
        if (null == coupon.getStartDate()) {
            //Nếu ngày bật hiển thị lớn hơn ngày kết thúc thì ngày bắt đầu = ngày kết thúc
            if (null != coupon.getEndDate() && coupon.getEndDate().isBefore(LocalDate.now())) {
                coupon.setStartDate(coupon.getEndDate());
            } else {
                coupon.setStartDate(LocalDate.now());
            }
        }
        if (!Objects.equals(coupon.getStatus(), StatusEnum.ACTIVE.value) && // Nếu trạng thái của coupon đang không là ACTIVE
            Objects.equals(StatusEnum.ACTIVE, status)) { // Coupon được chuyển sang trạng thái ACTIVE
            coupon.setInsertedSendMail(YesNoEnum.NO.value);
        }
        coupon.setStatus(status.value);
        coupon.setModifiedAt(new Date());
        couponRepository.save(coupon);
        log.info("===>UpdateCoupon {} {}",coupon.getId(), coupon.getPricingType());
        // cập nhật trạng thái coupon set
        couponSetService.updateStatus(coupon.getId(), coupon.getStatus());
    }

    /**
     * gửi mail khi active coupon
     */
    @Override
    public void sendMailCouponSmeWhenActiveStatus(CouponSendMailDTO coupon) {
        //Cấu hình gửi mail
        User couponOwner = userRepository.getCouponOwner(coupon.getUserId()).orElse(null);
        if (Objects.nonNull(couponOwner)) {
            String customerTypeCode = coupon.getCustomerTypeCode();
            List<User> lstAllSme = userRepository.getAllValidUserForCouponEmail();
            // Danh sách tất cả SME loại KHDN
            List<User> lstEnterpriseSme = customerTypeCode.contains(CustomerTypeEnum.ENTERPRISE.getValue()) ?
                lstAllSme.stream().filter(item -> isEnterpriseSme(item.getCustomerType())).collect(Collectors.toList()):
                new ArrayList<>();
            // Danh sách tất cả SME loại HKD
            List<User> lstHouseHoldSme = customerTypeCode.contains(CustomerTypeEnum.HOUSE_HOLD.getValue()) ?
                lstAllSme.stream().filter(item -> isHouseHoldSme(item.getCustomerType())).collect(Collectors.toList()):
                new ArrayList<>();
            // Danh sách tất cả SME loại CN
            List<User> lstPersonalSme = customerTypeCode.contains(CustomerTypeEnum.PERSONAL.getValue()) ?
                lstAllSme.stream().filter(item -> isPersonalSme(item.getCustomerType())).collect(Collectors.toList()):
                new ArrayList<>();

            //Send mail
            CouponMailParamDTO couponMailParamDTO = this.getMailParamSme(coupon, couponOwner);
            //Coupon chiết khấu
            if (coupon.getPromotionType().equals(PromotionTypeEnum.DISCOUNT.value)) {
                //ĐK gửi mail CP-10
                if ((Objects.equals(coupon.getSupplierType(), SupplierTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getSupplierType(), SupplierTypeEnum.ALL.value))
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)) {
                    List<User> listUserWhenChooseSupplier = userRepository.getSupplierByCouponId(coupon.getId());
                    this.sendMailCouponSme(couponMailParamDTO, listUserWhenChooseSupplier, EmailCodeEnum.CP10);
                }
                //ĐK gửi mail CP-01
                //TH1: Chỉ lựa "chọn tổng hóa đơn"
                else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    || Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.YES.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP01);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP01);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM01);
                }
                //TH2: Lựa chọn "Doanh nghiệp" và "Tổng hóa đơn"
                else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.YES.value)) {
                    List<User> lstSelectedEnterprise = userRepository.getEnterpriseByCoupon(coupon.getId());
                    this.sendMailCouponSme(couponMailParamDTO, lstSelectedEnterprise, EmailCodeEnum.CP01);
                }

                //ĐK gửi mail CP-02:
                //TH1: Lựa chọn: Doanh nghiệp, sản phẩm, dịch vụ bổ sung
                else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value))
                    && (Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value))
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    List<User> lstSelectedEnterprise = userRepository.getEnterpriseByCoupon(coupon.getId());
                    this.sendMailCouponSme(couponMailParamDTO, lstSelectedEnterprise, EmailCodeEnum.CP02);
                }
                //TH2: Lựa chọn: Sản phẩm, dịch vụ bổ sung
                else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value))
                    && (Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value))
                    && (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    || Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP02);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP02);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM02);
                }

                //ĐK gửi mail CP-04
                //TH1: Chỉ chọn doanh nghiệp + TH3: Lựa chọn Doanh nghiệp và sản phẩm
                else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP04);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP04);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM04);
                } else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    List<User> lstSelectedEnterprise = userRepository.getEnterpriseByCoupon(coupon.getId());
                    this.sendMailCouponSme(couponMailParamDTO, lstSelectedEnterprise, EmailCodeEnum.CP04);
                }
                //TH2: Chỉ chọn sản phẩm
                else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)
                    && (Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value)
                    || Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.OPTION.value))
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP04);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP04);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM04);
                }

                //ĐK gửi mail CP-06: chỉ chọn addon
                else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP06);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP06);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM06);
                }

                //ĐK gửi mail CP-08 chọn doanh nghiệp và dịch vụ bổ sung
                else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP08);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP08);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM08);
                } else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    List<User> lstSelectedEnterprise = userRepository.getEnterpriseByCoupon(coupon.getId());
                    this.sendMailCouponSme(couponMailParamDTO, lstSelectedEnterprise, EmailCodeEnum.CP08);
                }
            }

            //Coupon KM theo sản phẩm
            else if (Objects.equals(coupon.getPromotionType(), PromotionTypeEnum.PRODUCT.value)) {
                //ĐK gửi mail CP-03:
                //TH1: Lựa chọn: Doanh nghiệp, sản phẩm, dịch vụ bổ sung
                if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value))
                    && (Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value))
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    List<User> lstSelectedEnterprise = userRepository.getEnterpriseByCoupon(coupon.getId());
                    this.sendMailCouponSme(couponMailParamDTO, lstSelectedEnterprise, EmailCodeEnum.CP03);
                }
                //TH2: Lựa chọn: Sản phẩm, dịch vụ bổ sung
                else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value))
                    && (Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.OPTION.value)
                    || Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value))
                    && (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    || Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP03);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP03);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM03);
                }

                //ĐK gửi mail CP-05
                //TH1: Chỉ chọn doanh nghiệp + TH3: Lựa chọn Doanh nghiệp và sản phẩm
                else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP05);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP05);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM05);
                } else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    List<User> lstSelectedEnterprise = userRepository.getEnterpriseByCoupon(coupon.getId());
                    this.sendMailCouponSme(couponMailParamDTO, lstSelectedEnterprise, EmailCodeEnum.CP05);
                }
                //TH2: Chỉ chọn sản phẩm
                else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)
                    && (Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value)
                    || Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.OPTION.value))
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP05);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP05);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM05);
                }

                //ĐK gửi mail CP-07: chỉ chọn addon
                else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP07);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP07);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM07);
                }

                //ĐK gửi mail CP-09 chọn doanh nghiệp và dịch vụ bổ sung
                else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    this.sendMailCouponSme(couponMailParamDTO, lstEnterpriseSme, EmailCodeEnum.CP09);
                    this.sendMailCouponSme(couponMailParamDTO, lstHouseHoldSme, EmailCodeEnum.CP09);
                    this.sendMailCouponSme(couponMailParamDTO, lstPersonalSme, EmailCodeEnum.KM09);
                } else if ((Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value)
                    || Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
                    && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
                    && Objects.equals(coupon.getTotalBillType(), YesNoEnum.NO.value)) {
                    List<User> lstSelectedEnterprise = userRepository.getEnterpriseByCoupon(coupon.getId());
                    this.sendMailCouponSme(couponMailParamDTO, lstSelectedEnterprise, EmailCodeEnum.CP09);
                }
            }
        }
    }

    /**
     * Tạo khuyến mại
     *
     * @param couponDto the coupon dto
     * @return the map
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized BaseResponseDTO createCoupon(CouponDevReqDTO couponDto, PortalType portal) {
        Long userId = AuthUtil.getCurrentParentId();
        /*
            Fix SPC_ONEDXINTERNAL-8557: Trường hợp FE không truyền trường visibleStatus
            lên thì mặc định giá trị là VISIBLE(value = 1) (Trạng thái hiển thị: Công khai)
         */
        if(Objects.isNull(couponDto.getVisibleStatus())){
            couponDto.setVisibleStatus(ComponentVisible.VISIBLE.getValue());
        }
//        validateNameCoupon(couponDto.getName(), userId); //NghiaPT fix 5/8: bỏ check trùng tên CTKM
        validateCodeApply(couponDto);
        validatePromotionCode(couponDto);
        validatePromotionType(couponDto, portal, userId);
        validateCodeCoupon(couponDto);
        validateDateTimeCoupon(couponDto.getStartDate(), couponDto.getEndDate(), ActionType.CREATE);
        validateEnterpriseCoupon(couponDto);
        validatePricingOrVariantApplyCoupon(couponDto, portal, userId);
        validateAddonsCoupon(couponDto, portal);
        validateSupplierCoupon(couponDto, portal);
        validateMaximumPromotion(couponDto.getMaxUsed(), couponDto.getMaximumPromotion());
        return saveEntityCoupon(couponDto, portal);
    }

    /**
     * Lấy message khi null
     *
     * @return the message must null
     */
    private String getMessageMustNull() {
        return messageSource.getMessage(MessageKeyConstant.Validation.NULL, null,
                LocaleContextHolder.getLocale());
    }

    /**
     * Lấy message khi không null
     *
     * @return the message not null
     */
    private String getMessageNotNull() {
        return messageSource.getMessage(MessageKeyConstant.Validation.NOT_NULL, null,
                LocaleContextHolder.getLocale());
    }

    /**
     * Lấy message khi không tìm thấy
     *
     * @return the message not found
     */
    private String getMessageNotFound() {
        return messageSource.getMessage(MessageKeyConstant.NOT_FOUND, null,
                LocaleContextHolder.getLocale());
    }

    /**
     * Kiểm tra mã khuyến mãi được áp dụng
     *
     * @param couponDto the coupon dto
     */
    private void validateCodeApply(CouponDevReqDTO couponDto) {
        CodeTypeEnum codeType = couponDto.getCodeType();
        String promotionCode = couponDto.getPromotionCode();
        if (Objects.equals(CodeTypeEnum.USECODE, codeType)
                && StringUtils.isBlank(promotionCode)) {
            throw new BadRequestException(getMessageNotNull(), Resources.COUPON,
                    ErrorKey.Coupon.PROMOTION_CODE, MessageKeyConstant.FIELD_MUST_BE_NOT_NULL);
        } else if (Objects.equals(CodeTypeEnum.AUTO, codeType)
                && StringUtils.isNotBlank(promotionCode)) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                    ErrorKey.Coupon.PROMOTION_CODE, MessageKeyConstant.FIELD_MUST_BE_NULL);
        }
    }

    /**
     * Validate promotion code là duy nhất hệ thống.
     *
     * @param couponDto the coupon dto
     */
    private void validatePromotionCode(CouponDevReqDTO couponDto) {
        if (Objects.nonNull(couponDto.getPromotionCode())
                && couponRepository.checkPromotionCodeExists(couponDto.getPromotionCode())) {
            String msg = messageSource.getMessage(MessageKeyConstant.DATA_EXISTS, new String[] { "promotionCode" },
                    LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.COUPON, ErrorKey.Coupon.PROMOTION_CODE,
                    MessageKeyConstant.DATA_EXISTS);
        }
    }

    /**
     * Validate hình thức khuyến mãi.
     *
     * @param couponDto the coupon dto
     */
    private void validatePromotionType(CouponDevReqDTO couponDto, PortalType portal, Long userId) {
        if (CollectionUtils.isEmpty(couponDto.getCustomerType())) {
            String message = messageSource.getMessage(MessageKeyConstant.Validation.NOT_EMPTY, null,
                LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.COUPON,
                ErrorKey.Coupon.CUSTOMER_TYPE, MessageKeyConstant.Validation.NOT_EMPTY);
        }
        PromotionTypeEnum promotionType = couponDto.getPromotionType();
        DiscountTypeEnum discountType = couponDto.getDiscountType();
        BigDecimal discountValue = couponDto.getDiscountValue();
        BigDecimal discountAmount = couponDto.getDiscountAmount();
        TotalBillTypeEnum totalBillType = couponDto.getTotalBillType();
        Set<CouponPricingIds> couponPricing = couponDto.getCouponPricing();
        LimitedTypeEnum type = couponDto.getType();
        /*
         * 1. chiết khấu 2. theo sản phẩm
         */
        String msgValidatePattern = messageSource.getMessage(MessageKeyConstant.Validation.PATTERN, null,
                LocaleContextHolder.getLocale());
        if (Objects.equals(PromotionTypeEnum.DISCOUNT, promotionType)) {
            validatePromotionDiscount(couponDto, portal, discountType, discountValue, couponPricing, type,
                    msgValidatePattern);
        } else {
            validatePromotionProduct(couponDto, portal, userId, discountType, discountValue, discountAmount,
                    totalBillType, couponPricing, type, msgValidatePattern);
        }
    }

    /**
     * Validate chiết khấu theo sản phẩm.
     *
     * @param couponDto the coupon dto
     * @param portal the portal
     * @param userId the user id
     * @param discountType the discount type
     * @param discountValue the discount value
     * @param discountAmount the discount amount
     * @param totalBillType the total bill type
     * @param couponPricing the coupon pricing
     * @param type the type
     * @param msgValidatePattern the msg validate pattern
     */
    private void validatePromotionProduct(CouponDevReqDTO couponDto, PortalType portal, Long userId,
            DiscountTypeEnum discountType, BigDecimal discountValue, BigDecimal discountAmount,
            TotalBillTypeEnum totalBillType, Set<CouponPricingIds> couponPricing, LimitedTypeEnum type,
            String msgValidatePattern) {
        if (Objects.nonNull(discountType)) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                    ErrorKey.Coupon.DISCOUNT_TYPE, MessageKeyConstant.Validation.NULL);
        }
        if (Objects.nonNull(discountValue)) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                    ErrorKey.Coupon.DISCOUNT_VALUE, MessageKeyConstant.Validation.NULL);
        }
        if (Objects.nonNull(discountAmount)) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                    ErrorKey.Coupon.DISCOUNT_AMOUNT, MessageKeyConstant.Validation.NULL);
        }
        if (Objects.nonNull(totalBillType)) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                    ErrorKey.Coupon.TOTAL_BILL_TYPE, MessageKeyConstant.Validation.NULL);
        }
        if (Objects.equals(type, LimitedTypeEnum.TIMES)) {
            throw new BadRequestException(msgValidatePattern, Resources.COUPON, ErrorKey.Coupon.TYPE,
                    MessageKeyConstant.Validation.PATTERN);
        }
        // check list gói dịch vụ được khuyến mãi (miễn phí theo sản phẩm)
        if (!CollectionUtils.isEmpty(couponPricing)) {
            Set<Long> couponPricingIds = couponDto.getCouponPricing().stream().filter(x->Objects.isNull(x.getType()) || SubsTypeEnum.SERVICE.name().equals(x.getType()))
                    .map(CouponPricingIds::getPricingId).collect(toSet());
            Set<Pricing> pricingDbs = portal == PortalType.DEV
                    ? pricingRepository.getByUserIdAndIds(userId, couponPricingIds)
                    : pricingRepository.getByIds(couponPricingIds);
            Set<Long> pricingIdDbs = pricingDbs.stream().map(Pricing::getId).collect(Collectors.toSet());
            // ids không tồn tại hệ thống
            Set<Long> idsNotFound = new HashSet<>();
            if (!Objects.equals(couponPricingIds.size(), pricingIdDbs.size())) {
                for (Long id : couponPricingIds) {
                    if (!pricingIdDbs.contains(id)) {
                        idsNotFound.add(id);
                    }
                }
            }
            // ids đã bị xóa
            Set<Long> idsDeleted = pricingDbs.stream()
                    .filter(p -> p.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                    .map(Pricing::getId).collect(Collectors.toSet());

            // ids đã bị tắt hoạt động
            Set<Long> idsDisabled =
                    pricingDbs.stream().filter(p -> p.getStatus().equals(StatusEnum.INACTIVE.value))
                            .map(Pricing::getId).collect(Collectors.toSet());

            // Kiểm tra xem multi pricing plan của pricing có hợp lệ
            Set<CouponPricingIds> couponVerifyMultiPlanReq = couponDto.getCouponPricing().stream().filter(
                e -> YesNoEnum.YES.equals(e.getIsMultiPlan())).collect(toSet());
            validatePricingMultiPlanAndCustomerType(couponVerifyMultiPlanReq, couponDto.getCustomerType(), "couponPricing");

            generateDetailErrorCoupon(idsNotFound, idsDeleted, idsDisabled,
                    ErrorKey.Coupon.COUPON_PRICING, MessageKeyConstant.COUPON_PRICING_INVALID);
        }
    }

    /**
     * Trả lỗi chi tiết ids không tồn tại, ids bị xóa, ids tắt hoạt động
     *
     * @param idsNotFound the ids not found
     * @param idsDeleted the ids deleted
     * @param idsDisabled the ids disabled
     */
    private void generateDetailErrorCoupon(Set<Long> idsNotFound, Set<Long> idsDeleted, Set<Long> idsDisabled, String fieldError, String errorCode) {
        StringBuilder msgError = new StringBuilder();
        boolean hasError = false;
        if (!CollectionUtils.isEmpty(idsNotFound)) {
            hasError = true;
            String stringIds = String.join(",",
                    idsNotFound.stream().map(String::valueOf).collect(Collectors.toSet()));
            msgError.append(messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                    new String[] {stringIds}, LocaleContextHolder.getLocale()));
        }
        if (!CollectionUtils.isEmpty(idsDeleted)) {
            hasError = true;
            if (!CollectionUtils.isEmpty(idsNotFound)) {
                msgError.append("|");
            }
            String stringIds = String.join(",",
                    idsDeleted.stream().map(Object::toString).collect(Collectors.toSet()));
            msgError.append(messageSource.getMessage(MessageKeyConstant.DELETED,
                    new String[] {stringIds}, LocaleContextHolder.getLocale()));
        }
        if (!CollectionUtils.isEmpty(idsDisabled)) {
            hasError = true;
            if (!CollectionUtils.isEmpty(idsNotFound) || !CollectionUtils.isEmpty(idsDeleted)) {
                msgError.append("|");
            }
            String stringIds = String.join(",",
                    idsDisabled.stream().map(Object::toString).collect(Collectors.toSet()));
            msgError.append(messageSource.getMessage(MessageKeyConstant.INACTIVE,
                    new String[] {stringIds}, LocaleContextHolder.getLocale()));
        }

        if (hasError) {
            throw new BadRequestException(msgError.toString(), Resources.COUPON, fieldError, errorCode);
        }
    }

    /**
     * Validate khi giảm giá theo chiết khấu.
     *
     * @param couponDto the coupon dto
     * @param portal the portal
     * @param discountType the discount type
     * @param discountValue the discount value
     * @param couponPricing the coupon pricing
     * @param type the type
     * @param msgValidatePattern the msg validate pattern
     */
    private void validatePromotionDiscount(CouponDevReqDTO couponDto, PortalType portal, DiscountTypeEnum discountType,
            BigDecimal discountValue, Set<CouponPricingIds> couponPricing, LimitedTypeEnum type,
            String msgValidatePattern) {
        if (Objects.isNull(discountType)) {
            throw new BadRequestException(getMessageNotNull(), Resources.COUPON, ErrorKey.Coupon.DISCOUNT_TYPE,
                    MessageKeyConstant.Validation.NOT_NULL);
        }
        if (Objects.equals(DiscountTypeEnum.PERCENT, discountType)) {
            if (Objects.isNull(discountValue)) {
                throw new BadRequestException(getMessageNotNull(), Resources.COUPON, ErrorKey.Coupon.DISCOUNT_VALUE,
                        MessageKeyConstant.Validation.NOT_NULL);
            }
         // giá trị giảm không được vượt quá 100%
            if (discountValue.compareTo(BigDecimal.valueOf(CouponConst.DISCOUNT_PERCENT_MAX)) > 0) {
                throw new BadRequestException(msgValidatePattern, Resources.COUPON, ErrorKey.Coupon.DISCOUNT_VALUE,
                        MessageKeyConstant.Validation.PATTERN);
            }
        }
        if (!CollectionUtils.isEmpty(couponPricing)) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON, ErrorKey.Coupon.COUPON_PRICING,
                    MessageKeyConstant.Validation.NULL);
        }
        if (!Objects.equals(type, LimitedTypeEnum.TIMES)) {
            throw new BadRequestException(msgValidatePattern, Resources.COUPON, ErrorKey.Coupon.TYPE,
                    MessageKeyConstant.Validation.PATTERN);
        }
        if (portal == PortalType.ADMIN) {
            CouponAdminReqDTO couponAdminDto = (CouponAdminReqDTO) couponDto;
            if (Objects.isNull(couponAdminDto.getDiscountSupplierType())
               && (Objects.equals(SupplierTypeEnum.OPTION, couponAdminDto.getSupplierType())
                    || Objects.equals(SupplierTypeEnum.ALL, couponAdminDto.getSupplierType()))) {
                throw new BadRequestException(getMessageNotNull(), Resources.COUPON, ErrorKey.Coupon.DISCOUNT_SUPPLIER_TYPE,
                        MessageKeyConstant.Validation.NOT_NULL);
            }
            if (TotalBillTypeEnum.YES == couponAdminDto.getTotalBillType()) {
                String msg = messageSource.getMessage(MessageKeyConstant.INVALID_DATA, null,
                        LocaleContextHolder.getLocale());
                if (couponAdminDto.getPricingType() != CouponPricingApplyTypeEnum.NONE) {
                    throw new BadRequestException(msg, Resources.COUPON,
                            ErrorKey.Coupon.PRICING_TYPE, MessageKeyConstant.INVALID_DATA);
                }
                if (couponAdminDto.getAddonsType() != AddonTypeEnum.NONE) {
                    throw new BadRequestException(msg, Resources.COUPON,
                            ErrorKey.Coupon.ADDONS_TYPE, MessageKeyConstant.INVALID_DATA);
                }
            }
        }
    }

    /**
     * Kiểm tra tên chương trình khuyến mại
     *
     * @param name   the name
     * @param userId the portal
     */
    private void validateNameCoupon(String name, Long userId) {
        if (couponRepository.checkNameExistsByUserId(name, userId)) {
            String msg = messageSource.getMessage(MessageKeyConstant.DATA_EXISTS,
                    new String[] {"name"}, LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.COUPON, ErrorKey.Coupon.NAME,
                    MessageKeyConstant.DATA_EXISTS);
        }
    }

    /**
     * Kiểm tra mã chương trình khuyến mại
     *
     * @param couponDto the coupon dto
     */
    private void validateCodeCoupon(CouponDevReqDTO couponDto) {
        if (couponRepository.checkCodeExists(couponDto.getCode())) {
            String msg = messageSource.getMessage(MessageKeyConstant.DATA_EXISTS,
                    new String[] {"code"}, LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.COUPON, ErrorKey.Coupon.CODE,
                    MessageKeyConstant.DATA_EXISTS);
        }
    }

    /**
     * Kiểm tra ngày tháng cho chương trình khuyến mại
     *
     * @param startDate the start date
     * @param endDate   the end date
     */
    private void validateDateTimeCoupon(LocalDate startDate, LocalDate endDate, ActionType type) {
        LocalDate now = LocalDate.now();
        String msgDateMustFutureOrPresent =
                messageSource.getMessage(MessageKeyConstant.Validation.FUTURE_OR_PRESENT, null,
                        LocaleContextHolder.getLocale());
        if (Objects.equals(type, ActionType.CREATE)) {
            if (Objects.nonNull(startDate) && startDate.isBefore(now)) {
                throw new BadRequestException(msgDateMustFutureOrPresent, Resources.COUPON,
                        ErrorKey.Coupon.START_DATE, MessageKeyConstant.Validation.FUTURE_OR_PRESENT);
            }
            if (Objects.nonNull(endDate) && endDate.isBefore(now)) {
                throw new BadRequestException(msgDateMustFutureOrPresent, Resources.COUPON,
                        ErrorKey.Coupon.END_DATE, MessageKeyConstant.Validation.FUTURE_OR_PRESENT);
            }
        }

        if (Objects.nonNull(startDate) && Objects.nonNull(endDate) && endDate.isBefore(startDate)) {
            String msg = messageSource.getMessage(MessageKeyConstant.END_DATE_MUST_AFTER_START_DATE,
                    null, LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.COUPON, ErrorKey.Coupon.END_DATE,
                    MessageKeyConstant.END_DATE_MUST_AFTER_START_DATE);
        }
    }

    /**
     * Kiểm tra doanh nghiệp
     *
     * @param couponDto the coupon dto
     */
    private void validateEnterpriseCoupon(CouponDevReqDTO couponDto) {
        if ((Objects.equals(EnterpriseTypeEnum.NONE, couponDto.getEnterpriseType())
                || Objects.equals(EnterpriseTypeEnum.ALL, couponDto.getEnterpriseType()))
                && !CollectionUtils.isEmpty(couponDto.getCouponEnterprise())) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                    ErrorKey.Coupon.COUPON_ENTERPRISE, MessageKeyConstant.Validation.NULL);
        } else if (Objects.equals(EnterpriseTypeEnum.OPTION, couponDto.getEnterpriseType())) {
            if (CollectionUtils.isEmpty(couponDto.getCouponEnterprise())) {
                throw new BadRequestException(getMessageNotNull(), Resources.COUPON,
                        ErrorKey.Coupon.COUPON_ENTERPRISE, MessageKeyConstant.Validation.NOT_NULL);
            } else {
                Set<Long> enterpriseIds = couponDto.getCouponEnterprise().stream()
                        .map(CouponEnterpriseIds::getUserId).collect(toSet());
                Set<User> userDbs = userRepository.getUserParentByIdsAndRoleId(enterpriseIds, RoleConst.ROLE_SME);
                Set<Long> userIdDbs = userDbs.stream().map(User::getId).collect(Collectors.toSet());
             // ids không tồn tại hệ thống
                Set<Long> idsNotFound = new HashSet<>();
                if (!Objects.equals(userIdDbs.size(), enterpriseIds.size())) {
                    for (Long id : enterpriseIds) {
                        if (!userIdDbs.contains(id)) {
                            idsNotFound.add(id);
                        }
                    }
                }
                // ids đã bị xóa
                Set<Long> idsDeleted = userDbs.stream()
                        .filter(p -> p.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                        .map(User::getId).collect(Collectors.toSet());

                // ids đã bị tắt hoạt động
                Set<Long> idsDisabled =
                        userDbs.stream().filter(p -> p.getStatus().equals(StatusEnum.INACTIVE.value))
                                .map(User::getId).collect(Collectors.toSet());
                generateDetailErrorCoupon(idsNotFound, idsDeleted, idsDisabled,
                        ErrorKey.Coupon.COUPON_ENTERPRISE, MessageKeyConstant.COUPON_ENTERPRISE_INVALID);
            }
        }
    }

    /**
     * Kiểm tra sản phẩm khuyến mại
     *
     * @param couponDto the coupon dto
     */
    private void validatePricingOrVariantApplyCoupon(CouponDevReqDTO couponDto, PortalType portal, Long userId) {
        if ((Objects.equals(CouponPricingApplyTypeEnum.NONE, couponDto.getPricingType())
            || Objects.equals(CouponPricingApplyTypeEnum.ALL, couponDto.getPricingType()))
                && !CollectionUtils.isEmpty(couponDto.getCouponPricingApply())) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                    ErrorKey.Coupon.COUPON_PRICING_APPLY, MessageKeyConstant.Validation.NULL);
        } else if (Objects.equals(CouponPricingApplyTypeEnum.OPTION, couponDto.getPricingType())) {
            if (CollectionUtils.isEmpty(couponDto.getCouponPricingApply()) && CollectionUtils.isEmpty(couponDto.getCouponVariantsApply())) {
                throw new BadRequestException(getMessageNotNull(), Resources.COUPON,
                        ErrorKey.Coupon.COUPON_PRICING_APPLY + ", " + ErrorKey.Coupon.COUPON_VARIANT_APPLY, MessageKeyConstant.Validation.NOT_NULL);
            } else if (!CollectionUtils.isEmpty(couponDto.getCouponPricingApply())) {
                //Lấy ra số gói dịch vụ apply
                Set<Long> couponPricingApplyIds = couponDto.getCouponPricingApply().stream()
                        .filter(x->Objects.isNull(x.getType()) || SubsTypeEnum.SERVICE.name().equals(x.getType()))
                        .map(CouponPricingApplyIds::getPricingId).collect(toSet());
                Set<Pricing> pricingDbs = portal == PortalType.DEV
                    ? pricingRepository.findAllByIdInAndUserId(couponPricingApplyIds, userId)
                    : pricingRepository.findAllByIdIn(couponPricingApplyIds);
                Set<Long> pricingIdDbs = pricingDbs.stream().map(Pricing::getId).collect(Collectors.toSet());
                Set<Long> serviceIdDbs = pricingDbs.stream().map(Pricing::getServiceId).collect(Collectors.toSet());
                List<ServiceEntity> services = serviceRepository.findAllById(serviceIdDbs);

                //Lấy ra số gói combo apply
                Set<Long> couponComboPlanApplyIds = couponDto.getCouponPricingApply().stream()
                    .filter(x -> SubsTypeEnum.COMBO.name().equals(x.getType()))
                    .map(CouponPricingApplyIds::getPricingId).collect(toSet());
                Set<ComboPlan> comboPlanDbs = portal == PortalType.DEV
                    ? comboPlanRepository.findAllByIdInAndUserId(couponComboPlanApplyIds, userId)
                    : comboPlanRepository.findAllByIdIn(couponComboPlanApplyIds);
                Set<Long> comboPlanIdDbs = comboPlanDbs.stream().map(ComboPlan::getId).collect(Collectors.toSet());
                Set<Long> comboIdDbs = comboPlanDbs.stream().map(ComboPlan::getComboId).collect(Collectors.toSet());
                List<Combo> combos = comboRepository.findAllById(comboIdDbs);

                // ids không tồn tại hệ thống
                Set<Long> idsNotFound = new HashSet<>();
                if (!Objects.equals(couponPricingApplyIds.size(), pricingIdDbs.size())) {
                    for (Long id : couponPricingApplyIds) {
                        if (!pricingIdDbs.contains(id) && !comboPlanIdDbs.contains(id)) {
                            idsNotFound.add(id);
                        }
                    }
                }
                // ids đã bị xóa
                Set<Long> idsDeleted = pricingDbs.stream()
                        .filter(p -> p.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                        .map(Pricing::getId).collect(Collectors.toSet());
                idsDeleted.addAll(comboPlanDbs.stream().filter(c -> c.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                    .map(ComboPlan::getId).collect(Collectors.toSet()));
                // ids đã bị tắt hoạt động
                Set<Long> idsDisabledService = services.stream().filter(s -> s.getStatus().equals(StatusEnum.INACTIVE.value))
                    .map(ServiceEntity::getId).collect(Collectors.toSet());
                Set<Long> idsDisabled = pricingDbs.stream()
                    .filter(p -> p.getStatus().equals(StatusEnum.INACTIVE.value) || idsDisabledService.contains(p.getServiceId()))
                    .map(Pricing::getId).collect(Collectors.toSet());
                Set<Long> idsDisabledCombo = combos.stream().filter(c -> c.getStatus().equals(StatusEnum.INACTIVE.value))
                    .map(Combo::getId).collect(Collectors.toSet());
                idsDisabled.addAll(comboPlanDbs.stream()
                    .filter(c -> c.getStatus().equals(StatusEnum.INACTIVE.value) || idsDisabledCombo.contains(c.getComboId()))
                    .map(ComboPlan::getId).collect(Collectors.toSet()));

                Set<CouponPricingIds> couponVerifyMultiPlanReq = couponDto.getCouponPricingApply().stream().filter(
                    e -> YesNoEnum.YES.equals(e.getIsMultiPlan())).map(e -> {
                    CouponPricingIds pricing = new CouponPricingIds();
                    pricing.setPricingId(e.getPricingId());
                    pricing.setPricingPlanId(e.getPricingPlanId());
                    return pricing;
                }).collect(toSet());
                validatePricingMultiPlanAndCustomerType(couponVerifyMultiPlanReq, couponDto.getCustomerType(), "couponPricingApply");

                generateDetailErrorCoupon(idsNotFound, idsDeleted, idsDisabled,
                        ErrorKey.Coupon.COUPON_PRICING_APPLY, MessageKeyConstant.COUPON_PRICING_APPLY_INVALID);
            } else {
                // validate coupon variant apply
                Set<Long> variantIds = couponDto.getCouponVariantsApply().stream().map(CouponVariantIds::getVariantId).collect(toSet());
                // kiểm tra xem có variant nào không hợp lệ hay không
                if (ObjectUtils.isNotEmpty(variantIds)) {
                    List<Variant> variantListDB = variantRepository.findAllByIdInAndDeletedFlagAndApproved(variantIds,
                        DeletedFlag.NOT_YET_DELETED.getValue(), ApproveStatusEnum.APPROVED.value);
                    Set<Long> variantIdsDB = variantListDB.stream().map(Variant::getId).collect(toSet());
                    Set<Long> notValidIds = variantIds.stream().filter(item -> !variantIdsDB.contains(item)).collect(toSet());
                    if (ObjectUtils.isNotEmpty(notValidIds)) {
                        throw exceptionFactory.badRequest(MessageKeyConstant.NOT_EXIST_VARIANT, Resources.VARIANT, ErrorKey.Variant.ID,
                            notValidIds.toString());
                    }
                }
            }
        }
    }

    /**
     * Kiểm tra kế hoạch định giá của dịch vụ và loại khách hàng
     *
     * @param couponVerifyMultiPlan, customerType
     */
    private void validatePricingMultiPlanAndCustomerType(Set<CouponPricingIds> couponVerifyMultiPlan, Set<CustomerTypeEnum> customerType, String field) {
        Set<Long> pricingHasMultiPlanIdIncorrect = new HashSet<>();
        Set<Long> pricingMultiPlanIdCustomerTypeIncorrect = new HashSet<>();
        couponVerifyMultiPlan.forEach(pricingHasMultiPlan -> {
            Optional<PricingMultiPlan> multiPlanOpt = pricingMultiPlanRepository.findByIdAndPricingIdAndDeletedFlag(
                pricingHasMultiPlan.getPricingPlanId(), pricingHasMultiPlan.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());
            Set<String> customerTypeInp = customerType.stream().map(CustomerTypeEnum::getValue).collect(toSet());
            if (!multiPlanOpt.isPresent()) {
                pricingHasMultiPlanIdIncorrect.add(pricingHasMultiPlan.getPricingId());
            } else if (Collections.disjoint(multiPlanOpt.get().getCustomerTypeCode(), customerTypeInp)) {
                pricingMultiPlanIdCustomerTypeIncorrect.add(pricingHasMultiPlan.getPricingPlanId());
            }
        });
        if (!CollectionUtils.isEmpty(pricingHasMultiPlanIdIncorrect)) {
            String stringIds = String.join(",",
                pricingHasMultiPlanIdIncorrect.stream().map(Object::toString).collect(Collectors.toSet()));
            String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                new String[]{stringIds}, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.PRICING_MULTI_PLAN_ID,
                MessageKeyConstant.NOT_FOUND);
        }
        if (!CollectionUtils.isEmpty(pricingMultiPlanIdCustomerTypeIncorrect)) {
            String stringIds = String.join(CharacterConstant.COMMA,
                pricingMultiPlanIdCustomerTypeIncorrect.stream().map(Object::toString).collect(Collectors.toSet()));
            String message = messageSource.getMessage(MessageKeyConstant.PRICING_PLAN_HAVE_CUSTOMER_TYPE_INVALID,
                new String[]{stringIds}, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, field, ErrorKey.Coupon.PRICING_MULTI_PLAN_ID,
                MessageKeyConstant.PRICING_PLAN_HAVE_CUSTOMER_TYPE_INVALID);
        }
    }

    /**
     * Kiểm tra dịch vụ đi kèm
     *
     * @param couponDto the coupon dto
     */
    private void validateAddonsCoupon(CouponDevReqDTO couponDto, PortalType portal) {
        if ((Objects.equals(AddonTypeEnum.NONE, couponDto.getAddonsType())
                || Objects.equals(AddonTypeEnum.ALL, couponDto.getAddonsType()))
                && !CollectionUtils.isEmpty(couponDto.getCouponAddons())) {
            throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                    ErrorKey.Coupon.COUPON_ADDONS, MessageKeyConstant.Validation.NULL);
        } else if (Objects.equals(AddonTypeEnum.OPTION, couponDto.getAddonsType())) {
            if (CollectionUtils.isEmpty(couponDto.getCouponAddons())) {
                throw new BadRequestException(getMessageNotNull(), Resources.COUPON,
                        ErrorKey.Coupon.COUPON_ADDONS, MessageKeyConstant.Validation.NOT_NULL);
            } else {
                Set<Long> couponAddonIds = couponDto.getCouponAddons().stream()
                        .map(CouponAddonIds::getAddonsId).collect(Collectors.toSet());
                Set<Addon> addonDbs;
                addonDbs = portal == PortalType.DEV ? addonRepository.findByIdInAndUserId(couponAddonIds, AuthUtil.getCurrentParentId())
                        : addonRepository.findAllByIdIn(couponAddonIds);
                Set<Long> addonIdDbs = addonDbs.stream().map(Addon::getId).collect(Collectors.toSet());

             // ids không tồn tại hệ thống
                Set<Long> idsNotFound = new HashSet<>();
                if (!Objects.equals(addonIdDbs.size(), couponAddonIds.size())) {
                    for (Long id : couponAddonIds) {
                        if (!addonIdDbs.contains(id)) {
                            idsNotFound.add(id);
                        }
                    }
                }
                // ids đã bị xóa
                Set<Long> idsDeleted = addonDbs.stream()
                        .filter(p -> p.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                        .map(Addon::getId).collect(Collectors.toSet());

                // ids đã bị tắt hoạt động
                Set<Long> idsDisabled =
                        addonDbs.stream().filter(p -> p.getStatus().equals(StatusEnum.INACTIVE.value))
                                .map(Addon::getId).collect(Collectors.toSet());

                // kiểm tra multi plan có hợp lệ
                Set<Long> couponAddonHasMultiPlanIds = couponDto.getCouponAddons().stream()
                    .filter(e -> YesNoEnum.YES.equals(e.getIsMultiPlan())).map(CouponAddonIds::getAddonsId).collect(
                        Collectors.toSet());

                Set<Addon> addonDbsValidateMultiPlan = portal == PortalType.DEV ? addonRepository
                    .findByIdInAndUserId(couponAddonHasMultiPlanIds, AuthUtil.getCurrentParentId())
                    : addonRepository.findAllByIdIn(couponAddonHasMultiPlanIds);

                Set<Long> addonDBIsNotMultiPlanIds = addonDbsValidateMultiPlan.stream().filter(
                    e -> !e.getBonusType().equals(BonusTypeEnum.PERIODIC.value)).map(Addon::getId).collect(toSet());

                // kiểm tra có id addon nào không hợp lệ không
                if (!CollectionUtils.isEmpty(addonDBIsNotMultiPlanIds)) {
                    String stringIds = String.join(",",
                        addonDBIsNotMultiPlanIds.stream().map(Object::toString).collect(Collectors.toSet()));
                    String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                        new String[]{stringIds}, LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.PRICING_MULTI_PLAN_ID,
                        MessageKeyConstant.NOT_FOUND);
                }

                Set<Long> currentAddonIds = addonDbsValidateMultiPlan.stream().map(Addon::getId).collect(toSet());
                Set<Long> addonMultiPlanIds = pricingMultiPlanRepository
                    .findAllByAddonIdInAndDeletedFlag(currentAddonIds,
                        DeletedFlag.NOT_YET_DELETED.getValue()).stream().map(PricingMultiPlan::getAddonId).collect(Collectors.toSet());
                if (addonMultiPlanIds.size() != currentAddonIds.size()) {
                    Set<Long> addonInvalidIds = currentAddonIds.stream().filter(addonMultiPlanIds::contains).collect(Collectors.toSet());
                    String stringIds = String.join(",",
                        addonInvalidIds.stream().map(Object::toString).collect(Collectors.toSet()));
                    String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                        new String[]{stringIds}, LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.PRICING_MULTI_PLAN_ID,
                        MessageKeyConstant.NOT_FOUND);
                }

                // validate multi plan của addon xem có tồn tại không
                Set<CouponAddonIds> addonVerifyMultiPlanReq = couponDto.getCouponAddons().stream().filter(
                    e -> YesNoEnum.YES.equals(e.getIsMultiPlan())).collect(toSet());

                Set<Long> addonHasMultiPlanIncorrectIds = new HashSet<>();
                addonVerifyMultiPlanReq.forEach(addonHasMultiPlan -> {
                    if (!pricingMultiPlanRepository
                        .existsByIdAndAddonId(addonHasMultiPlan.getPricingPlanId(), addonHasMultiPlan.getAddonsId())) {
                        addonHasMultiPlanIncorrectIds.add(addonHasMultiPlan.getAddonsId());
                    }
                });

                if (!CollectionUtils.isEmpty(addonHasMultiPlanIncorrectIds)) {
                    String stringIds = String.join(",",
                        addonHasMultiPlanIncorrectIds.stream().map(Object::toString).collect(Collectors.toSet()));
                    String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND,
                        new String[]{stringIds}, LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.PRICING_MULTI_PLAN_ID,
                        MessageKeyConstant.NOT_FOUND);
                }



                generateDetailErrorCoupon(idsNotFound, idsDeleted, idsDisabled,
                        ErrorKey.Coupon.COUPON_ADDONS, MessageKeyConstant.COUPON_ADDONS_INVALID);
            }
        }
    }

    /**
     * Kiểm tra nhà cung cấp dịch vụ
     *
     * Chọn miễn phí theo sản phẩm thì mục nhà cung cấp dịch vụ và chiết khấu cho nhà cung cấp null
     *
     * @param couponDto the coupon dto
     * @param portal the portal
     */
    private void validateSupplierCoupon(CouponDevReqDTO couponDto, PortalType portal) {
        if (portal == PortalType.ADMIN) {
            CouponAdminReqDTO couponAdminDto = (CouponAdminReqDTO) couponDto;
            if (PromotionTypeEnum.DISCOUNT == couponAdminDto.getPromotionType()) {
                if ((Objects.equals(SupplierTypeEnum.NONE, couponAdminDto.getSupplierType())
                        || Objects.equals(SupplierTypeEnum.ALL, couponAdminDto.getSupplierType()))
                        && !CollectionUtils.isEmpty(couponAdminDto.getSuppliers())) {
                    throw new BadRequestException(getMessageNotNull(), Resources.COUPON,
                            ErrorKey.Coupon.COUPON_SUPPLIERS, MessageKeyConstant.Validation.NULL);
                } else if (Objects.equals(SupplierTypeEnum.OPTION, couponAdminDto.getSupplierType())) {
                    if (CollectionUtils.isEmpty(couponAdminDto.getSuppliers())) {
                        throw new BadRequestException(getMessageNotNull(), Resources.COUPON,
                                ErrorKey.Coupon.COUPON_SUPPLIERS, MessageKeyConstant.Validation.NOT_NULL);
                    } else {
                        Set<Long> supplierIds = couponAdminDto.getSuppliers().stream()
                                .map(CouponSupplierId::getUserId).collect(toSet());

                        Set<User> userDbs = userRepository.getUserParentByIdsAndRoleId(supplierIds, RoleConst.ROLE_DEVELOPER);
                        Set<Long> userIdDbs = userDbs.stream().map(User::getId).collect(Collectors.toSet());
                     // ids không tồn tại hệ thống
                        Set<Long> idsNotFound = new HashSet<>();
                        if (!Objects.equals(userIdDbs.size(), supplierIds.size())) {
                            for (Long id : supplierIds) {
                                if (!userIdDbs.contains(id)) {
                                    idsNotFound.add(id);
                                }
                            }
                        }
                        // ids đã bị xóa
                        Set<Long> idsDeleted = userDbs.stream()
                                .filter(p -> p.getDeletedFlag().equals(DeletedFlag.DELETED.getValue()))
                                .map(User::getId).collect(Collectors.toSet());

                        // ids đã bị tắt hoạt động
                        Set<Long> idsDisabled =
                                userDbs.stream().filter(p -> p.getStatus().equals(StatusEnum.INACTIVE.value))
                                        .map(User::getId).collect(Collectors.toSet());
                        generateDetailErrorCoupon(idsNotFound, idsDeleted, idsDisabled,
                                ErrorKey.Coupon.COUPON_SUPPLIERS, MessageKeyConstant.COUPON_ENTERPRISE_INVALID);
                    }
                }
            } else {
                DiscountSupplierTypeEnum discountSupplierType = couponAdminDto.getDiscountSupplierType();
                Set<CouponSupplierId> listCouponSupplier = couponAdminDto.getSuppliers();
                if (Objects.nonNull(discountSupplierType)) {
                    throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                            ErrorKey.Coupon.DISCOUNT_SUPPLIER_TYPE,
                            MessageKeyConstant.Validation.NULL);
                }
                if (!CollectionUtils.isEmpty(listCouponSupplier)) {
                    throw new BadRequestException(getMessageMustNull(), Resources.COUPON,
                            ErrorKey.Coupon.COUPON_SUPPLIERS, MessageKeyConstant.Validation.NULL);
                }
            }
        }
    }

    /**
     * Kiểm tra chương trình khuyến mại tối đa
     *
     * @param maxUsed          the max used
     * @param maximumPromotion the maximum promotion
     */
    private void validateMaximumPromotion(Long maxUsed , Long maximumPromotion) {
        if (Objects.nonNull(maxUsed) && Objects.nonNull(maximumPromotion)
                && maxUsed < maximumPromotion) {
            String msg = messageSource.getMessage(MessageKeyConstant.LESS_THAN,
                    new String[] {ErrorKey.Coupon.MAXIMUM_PROMOTION, ErrorKey.Coupon.MAX_USED},
                    LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.COUPON, ErrorKey.Coupon.MAXIMUM_PROMOTION,
                    MessageKeyConstant.LESS_THAN);
        }
    }

    /**
     * Lưu chương trình khuyến mại
     *
     * @param couponDto the coupon dto
     * @return the map
     */
    private BaseResponseDTO saveEntityCoupon(CouponDevReqDTO couponDto, PortalType portal) {
        Coupon coupon;
        if (portal == PortalType.ADMIN) {
            CouponAdminReqDTO couponAdminDto = (CouponAdminReqDTO) couponDto;
            coupon = couponAdminMapper.toEntity(couponAdminDto);
        } else {
            coupon = couponDevMapper.toEntity(couponDto);
        }
        coupon.setUserId(AuthUtil.getCurrentParentId());
        //đối với trường hợp ADMIN tạo thì không cần phải approve, sẽ là status active
        if (portal == PortalType.ADMIN) {
            coupon.setStatus(StatusEnum.ACTIVE.value);
            coupon.setApprove(ApproveStatusEnum.APPROVED.value);
            coupon.setInsertedSendMail(YesNoEnum.NO.value);
        } else {
            coupon.setStatus(StatusEnum.INACTIVE.value);
            coupon.setApprove(ApproveStatusEnum.UNAPPROVED.value);
        }
        coupon.setCreatedBy(AuthUtil.getCurrentUser().getId());
        coupon.setPortal(portal.getType());
        coupon.setProvinceId(
            Objects.equals(portal, PortalType.ADMIN) ? userRepository.getProvinceIdOfDepartmentByUser(AuthUtil.getCurrentUser().getId())
                : userRepository.getProvinceIdByUserId(AuthUtil.getCurrentParentId()));
        if (Objects.equals(PromotionTypeEnum.PRODUCT, couponDto.getPromotionType())) {
            coupon.setPricingType(CouponPricingApplyTypeEnum.ALL.value);
            // Nếu chọn điều kiện khách hàng theo quy tắc thif km chỉ dc áp dụng 1 lần
            if (Objects.equals(EnterpriseTypeEnum.RULES, couponDto.getEnterpriseType())) {
                coupon.setMaximumPromotion(1L);
            }
         } else {
            coupon.setPricingType(Objects.isNull(couponDto.getPricingType()) ? 0 : couponDto.getPricingType().value);
        }
        coupon.setAddonsType(Objects.isNull(couponDto.getAddonsType()) ? 0 : couponDto.getAddonsType().value);
        coupon.setEnterpriseType(Objects.isNull(couponDto.getEnterpriseType()) ? 0 : couponDto.getEnterpriseType().value);
        coupon.setTotalBillType(Objects.isNull(couponDto.getTotalBillType()) ? 0 : couponDto.getTotalBillType().value );
        coupon.setCustomerTypeCode(CollectionUtils.isEmpty(couponDto.getCustomerType()) ? new HashSet<>() :
            couponDto.getCustomerType().stream().map(CustomerTypeEnum::getValue).collect(toSet()));
        couponRepository.save(coupon);
        log.info("===>UpdateCoupon {} {}",coupon.getId(), coupon.getPricingType());

        // save coupon ads position
        McAdsPositionDTO adsPositionDTO = couponDto.getAdvertisePosition();
        // Save ads position img to file_attach
        updateAdsPositionImgInFileAttach(adsPositionDTO, coupon.getId());
        if (adsPositionDTO != null) {
            CouponAdsPosition couponAdsPosition = new CouponAdsPosition(coupon.getId(), adsPositionDTO);
            try {
                ObjectMapper mapper = new ObjectMapper();
                String jsonStr = mapper.writeValueAsString(couponDto.getAdvertisePosition());
                couponAdsPosition.setJsonObject(jsonStr);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            couponAdsPositionRepository.save(couponAdsPosition);
        }

        if (portal == PortalType.ADMIN) {
            CouponAdminReqDTO couponAdminDto = (CouponAdminReqDTO) couponDto;
            if (Objects.equals(SupplierTypeEnum.OPTION, couponAdminDto.getSupplierType())) {
                Set<CouponSupplier> listSupplier = new LinkedHashSet<>();
                for (CouponSupplierId supplierId : couponAdminDto.getSuppliers()) {
                    CouponSupplier couponSupplier =
                            new CouponSupplier(null, coupon.getId(), supplierId.getUserId());
                    listSupplier.add(couponSupplier);
                }
                couponSupplierRepository.saveAll(listSupplier);
            }
        }
        if (Objects.equals(PromotionTypeEnum.PRODUCT, couponDto.getPromotionType())) {
            LinkedHashSet<CouponPricingIds> couponPricingList = couponDto.getCouponPricing();
            LinkedHashSet<CouponVariantIds> couponVariantList = couponDto.getCouponVariants();
            if (CollectionUtils.isEmpty(couponPricingList) && CollectionUtils.isEmpty(couponVariantList)) {
                throw exceptionFactory.badRequest(Validation.NOT_EMPTY, Resources.COUPON, ErrorKey.Coupon.COUPON_PRICING + ", " + ErrorKey.Coupon.COUPON_VARIANT);
            }
            saveCouponPromotion(coupon.getId(), couponPricingList);
            // lưu mapping coupon variant
            saveCouponVariant(coupon.getId(), ObjectUtil.getOrDefault(couponDto.getCouponVariants(), new LinkedHashSet<>()));
            // Lưu thông tin điều kiện áp dụng
            saveCouponPromotionCondition(coupon.getId(), couponDto.getCondition());

        }
        if (Objects.equals(EnterpriseTypeEnum.OPTION, couponDto.getEnterpriseType())) {
            Set<CouponEnterprise> listCouponEnterprise = new LinkedHashSet<>();
            for (CouponEnterpriseIds enterpriseId : couponDto.getCouponEnterprise()) {
                CouponEnterprise couponEnterprise =
                        new CouponEnterprise(null, enterpriseId.getUserId(), coupon.getId());
                listCouponEnterprise.add(couponEnterprise);
            }
            couponEnterpriseRepository.saveAll(listCouponEnterprise);
        }
        if (Objects.equals(CouponPricingApplyTypeEnum.OPTION, couponDto.getPricingType())) {
            saveCouponApply(coupon.getId(), couponDto.getCouponPricingApply());
            // save mapping coupon variant apply mapping
            saveCouponVariantApply(coupon.getId(), ObjectUtil.getOrDefault(couponDto.getCouponVariantsApply(), new LinkedHashSet<>()));
        }

        // lưu thông tin addon
        if (Objects.equals(AddonTypeEnum.OPTION, couponDto.getAddonsType())) {
            // Lấy thông tin addon_draft_id -> lưu thêm vào bảng coupon_addons
            Map<Long, Long> mapAddonIdAndDraftId = getMapIdAndDraftIdByAddonIdIn(couponDto.getCouponAddons().stream().map(CouponAddonIds::getAddonsId).collect(
                Collectors.toList()));
            Set<CouponAddon> listCouponAddon = new LinkedHashSet<>();
            Set<CouponPricingPlan> listCouponPricingPlan = new LinkedHashSet<>();
            for (CouponAddonIds couponAddonId : couponDto.getCouponAddons()) {
                // nếu addon là loại 1 lần hoặc là loại định kỳ nhưng là loại 1 chu kỳ thanh toán
                if (BonusTypeEnum.ONCE.equals(couponAddonId.getType()) || (YesNoEnum.NO.equals(couponAddonId.getIsMultiPlan()))) {
                    // lưu vào bảng coupon addon
                    CouponAddon couponAddon =
                        new CouponAddon(null, couponAddonId.getAddonsId(), coupon.getId(), mapAddonIdAndDraftId.get(couponAddonId.getAddonsId()));
                    listCouponAddon.add(couponAddon);
                } else {
                    // nếu addon là loại định kỳ và là addon multi plan
                    CouponPricingPlan couponPricingPlan = new CouponPricingPlan(null, coupon.getId(), couponAddonId.getPricingPlanId(), null,
                        CouponPricingPlanTypeEnum.ADDON.value);
                    listCouponPricingPlan.add(couponPricingPlan);
                }
            }
            couponAddonRepository.saveAll(listCouponAddon);
            couponPricingPlanRepository.saveAll(listCouponPricingPlan);
        }
        setBanner(couponDto.getDesktopBannerId(), couponDto.getMobileBannerId(), coupon.getId());
        return new BaseResponseDTO(coupon.getId());
    }

    private void saveCouponPromotionCondition(Long couponId, List<McConditionItemGroupDTO> condition) {
        String conditionSql;
        if (Objects.isNull(condition)) {
            conditionSql = "false";
        } else {
            ConditionExecute mcConditionExecute = new ConditionExecute(condition);
            conditionSql = mcConditionExecute.getCondition(CrmObjectTypeEnum.USER.getValue());
        }
        CouponPromotionCondition couponCondition = new CouponPromotionCondition(null, couponId, condition, conditionSql);
        couponPromotionConditionRepository.save(couponCondition);
    }

    /**
     * Save coupon apply.
     *
     * @param couponId               the coupon id
     * @param couponPricingApplyList the coupon pricing apply list
     */
    private void saveCouponApply(Long couponId, LinkedHashSet<CouponPricingApplyIds> couponPricingApplyList) {
        // Lấy thông tin pricing_draft_id và plan_original_id -> tạo mapping
        Map<Long, Long> mapPricingIdAndDraftId = getMapIdAndDraftIdByPricingIdIn(couponPricingApplyList.stream().filter(item -> YesNoEnum.NO.equals(item.getIsMultiPlan())).map(CouponPricingApplyIds::getPricingId).collect(
            Collectors.toList()));
        Map<Long, Long> mapPMPIdAndOriginalId = getMapIdAndDraftIdByPMPIdIn(couponPricingApplyList.stream().filter(item -> YesNoEnum.YES.equals(item.getIsMultiPlan())).map(CouponPricingApplyIds::getPricingPlanId).collect(
            Collectors.toList()));
        // lưu pricing_plan áp dụng CTKM chiết khấu
        Set<CouponPricingApply> listCouponPricingApply = new LinkedHashSet<>();
        Set<CouponPricingPlan> listCouponPricingPlan = new LinkedHashSet<>();
        couponPricingApplyList.stream()
                 .filter(x -> Objects.isNull(x.getType()) || SubsTypeEnum.SERVICE.name().equals(x.getType()))
                 .forEach(element -> {
                     // nếu không phải là multi plan pricing -> lưu trong bảng coupon_pricing_apply
                     if (YesNoEnum.NO.equals(element.getIsMultiPlan())) {
                         CouponPricingApply couponPricingApply =
                             new CouponPricingApply(null, element.getPricingId(), mapPricingIdAndDraftId.get(element.getPricingId()), couponId);
                         listCouponPricingApply.add(couponPricingApply);
                     } else {
                         // nếu là multi plan pricing -> lưu trong bảng coupon_pricing_plan_apply
                         CouponPricingPlan pricingPlanApply = new CouponPricingPlan(null, couponId, element.getPricingPlanId(),
                             mapPMPIdAndOriginalId.get(element.getPricingPlanId()), CouponPricingPlanTypeEnum.PRICING.value);
                         listCouponPricingPlan.add(pricingPlanApply);
                     }

                 });
        couponPricingApplyRepository.saveAll(listCouponPricingApply);
        couponPricingPlanRepository.saveAll(listCouponPricingPlan);

        // Lấy thông tin combo_plan_draft_id -> tạo mapping
        Map<Long, Long> mapCBPlanIdAndDraftId = getMapIdAndDraftIdByComboPlanIdIn(
            couponPricingApplyList.stream().filter(item -> Objects.equals(SubsTypeEnum.COMBO.name(), item.getType()))
                .map(CouponPricingApplyIds::getPricingId).collect(Collectors.toList()));
        Set<CouponComboPlanApply> listCouponComboPlanApply = new LinkedHashSet<>();
        couponPricingApplyList.stream()
            .filter(x -> SubsTypeEnum.COMBO.name().equals(x.getType()))
            .forEach(couponComboPlanItem -> {
                CouponComboPlanApply couponComboPlanApply = new CouponComboPlanApply(null, couponComboPlanItem.getPricingId(),
                        mapCBPlanIdAndDraftId.get(couponComboPlanItem.getPricingId()), couponId);
                listCouponComboPlanApply.add(couponComboPlanApply);
            });
        couponComboPlanApplyRepository.saveAll(listCouponComboPlanApply);
    }

    /**
     * Save coupon promotion.
     *
     * @param couponId          the coupon id
     * @param couponPricingList the coupon pricing list
     */
    @Transactional
    private void saveCouponPromotion(Long couponId,
                                     LinkedHashSet<CouponPricingIds> couponPricingList) {
        // Lấy thông tin pricing_draft_id và plan_original_id -> tạo mapping
        Map<Long, Long> mapPricingIdAndDraftId = getMapIdAndDraftIdByPricingIdIn(couponPricingList.stream().filter(item -> YesNoEnum.NO.equals(item.getIsMultiPlan())).map(CouponPricingIds::getPricingId).collect(
            Collectors.toList()));
        Map<Long, Long> mapPMPIdAndOriginalId = getMapIdAndDraftIdByPMPIdIn(couponPricingList.stream().filter(item -> YesNoEnum.YES.equals(item.getIsMultiPlan())).map(CouponPricingIds::getPricingPlanId)
            .collect(Collectors.toList()));

        Set<CouponPricing> listCouponPricing = new LinkedHashSet<>();
        Set<CouponPricingPlanApply> listCouponPricingPlan = new LinkedHashSet<>();
        couponPricingList.stream()
                         .filter(x -> Objects.isNull(x.getType()) || SubsTypeEnum.SERVICE.name().equals(x.getType()))
                         .forEach(element -> {
                             if (YesNoEnum.NO.equals(element.getIsMultiPlan())) {
                                 CouponPricing couponPricing =
                                     new CouponPricing(null, element.getPricingId(), mapPricingIdAndDraftId.get(element.getPricingId()), couponId);
                                 listCouponPricing.add(couponPricing);
                             } else {
                                 CouponPricingPlanApply couponPricingPlan = new CouponPricingPlanApply(null, couponId, element.getPricingPlanId(), mapPMPIdAndOriginalId.get(element.getPricingPlanId()));
                                 listCouponPricingPlan.add(couponPricingPlan);
                             }
                         });
        couponPricingRepository.saveAll(listCouponPricing);
        couponPricingPlanApplyRepository.saveAll(listCouponPricingPlan);

        // Lấy thông tin combo_plan_draft_id -> tạo mapping
        Map<Long, Long> mapCBPlanIdAndDraftId = getMapIdAndDraftIdByComboPlanIdIn(
            couponPricingList.stream().filter(item -> Objects.equals(SubsTypeEnum.COMBO.name(), item.getType()))
                .map(CouponPricingIds::getPricingId).collect(Collectors.toList()));
        Set<CouponComboPlan> listCouponComboPlan = new LinkedHashSet<>();
        couponPricingList.stream()
            .filter(x -> SubsTypeEnum.COMBO.name().equals(x.getType()))
            .forEach(couponComboPlanItem -> {
                CouponComboPlan couponCbPlanApply = new CouponComboPlan(null, couponComboPlanItem.getPricingId(),
                    mapCBPlanIdAndDraftId.get(couponComboPlanItem.getPricingId()), couponId);
                listCouponComboPlan.add(couponCbPlanApply);
            });
        couponComboPlanRepository.saveAll(listCouponComboPlan);
    }

    /**
     * Lưu mapping coupon variant
     * @param couponId
     * @param couponVariants
     */
    private void saveCouponVariant(Long couponId, LinkedHashSet<CouponVariantIds> couponVariants) {
        // lấy mapping variant_id và variant_draft_id -> tạo mapping
        Map<Long, Long> mapVariantIdAndDraftId = getMapIdAndDraftIdByVariantIdIn(
            couponVariants.stream().map(CouponVariantIds::getVariantId).collect(Collectors.toList()));
        // Lấy thông tin pricing_draft_id và plan_original_id -> tạo mapping
        Map<Long, Long> mapPricingIdAndDraftId = getMapIdAndDraftIdByPricingIdIn(
            couponVariants.stream().filter(item -> YesNoEnum.NO.equals(item.getIsMultiPlan())).map(CouponVariantIds::getPricingId).collect(
                Collectors.toList()));
        Map<Long, Long> mapPMPIdAndOriginalId = getMapIdAndDraftIdByPMPIdIn(
            couponVariants.stream().filter(item -> YesNoEnum.YES.equals(item.getIsMultiPlan())).map(CouponVariantIds::getPricingPlanId)
                .collect(Collectors.toList()));
        List<CouponVariant> couponVariantLst = new ArrayList<>();
        couponVariants.forEach(item -> couponVariantLst.add(
            CouponVariant.builder()
                .couponId(couponId)
                .variantId(item.getVariantId())
                .variantDraftId(mapVariantIdAndDraftId.get(item.getVariantId()))
                .pricingId(item.getPricingId())
                .pricingDraftId(mapPricingIdAndDraftId.get(ObjectUtil.getOrDefault(item.getPricingId(), -1L)))
                .pricingMultiPlanId(item.getPricingPlanId())
                .pricingMultiPlanOrgId(mapPMPIdAndOriginalId.get(ObjectUtil.getOrDefault(item.getPricingPlanId(), -1L)))
                .build()));
        couponVariantRepository.saveAll(couponVariantLst);
    }

    private void saveCouponVariantApply(Long couponId, LinkedHashSet<CouponVariantIds> couponVariantsApply) {
        // lấy mapping variant_id và variant_draft_id -> tạo mapping
        Map<Long, Long> mapVariantIdAndDraftId = getMapIdAndDraftIdByVariantIdIn(
            couponVariantsApply.stream().map(CouponVariantIds::getVariantId).collect(Collectors.toList()));
        // Lấy thông tin pricing_draft_id và plan_original_id -> tạo mapping
        Map<Long, Long> mapPricingIdAndDraftId = getMapIdAndDraftIdByPricingIdIn(
            couponVariantsApply.stream().filter(item -> YesNoEnum.NO.equals(item.getIsMultiPlan())).map(CouponVariantIds::getPricingId).collect(
                Collectors.toList()));
        Map<Long, Long> mapPMPIdAndOriginalId = getMapIdAndDraftIdByPMPIdIn(
            couponVariantsApply.stream().filter(item -> YesNoEnum.YES.equals(item.getIsMultiPlan())).map(CouponVariantIds::getPricingPlanId)
                .collect(Collectors.toList()));
        List<CouponVariantApply> couponVariantApplyLst = new ArrayList<>();
        couponVariantsApply.forEach(item -> couponVariantApplyLst.add(
            CouponVariantApply.builder()
                .couponId(couponId)
                .variantId(item.getVariantId())
                .variantDraftId(mapVariantIdAndDraftId.get(item.getVariantId()))
                .pricingId(item.getPricingId())
                .pricingDraftId(mapPricingIdAndDraftId.get(ObjectUtil.getOrDefault(item.getPricingId(), -1L)))
                .pricingMultiPlanId(item.getPricingPlanId())
                .pricingMultiPlanOrgId(mapPMPIdAndOriginalId.get(ObjectUtil.getOrDefault(item.getPricingPlanId(), -1L)))
                .build()));
        couponVariantApplyRepository.saveAll(couponVariantApplyLst);
    }

    private Map<Long, Long> getMapIdAndDraftIdByPricingIdIn(List<Long> pricingIds) {
        Map<Long, Long> mapPricingIdAndDraftId = new LinkedHashMap<>();
        pricingRepository.getIdAndDraftIdByPricingIdIn(pricingIds).forEach(e -> mapPricingIdAndDraftId.put(e.getId(), e.getRootId()));
        return mapPricingIdAndDraftId;
    }

    private Map<Long, Long> getMapIdAndDraftIdByPMPIdIn(List<Long> pmpIds) {
        Map<Long, Long> mapPMPIdAndOriginalId = new LinkedHashMap<>();
        pricingMultiPlanRepository.getIdAndOriginalIdByPlanIdIn(pmpIds).forEach(e -> mapPMPIdAndOriginalId.put(e.getId(), e.getRootId()));
        return mapPMPIdAndOriginalId;
    }

    private Map<Long, Long> getMapIdAndDraftIdByVariantIdIn(List<Long> variantIds) {
        Map<Long, Long> mapVariantIdAndDraftId = new LinkedHashMap<>();
        variantRepository.getIdAndDraftIdByVariantIdIn(variantIds).forEach(e -> mapVariantIdAndDraftId.put(e.getId(), e.getRootId()));
        return mapVariantIdAndDraftId;
    }

    private Map<Long, Long> getMapIdAndDraftIdByComboPlanIdIn(List<Long> cbPlanIds) {
        Map<Long, Long> mapCBPlanIdAndDraftId = new LinkedHashMap<>();
        comboPlanRepository.getIdAndDraftIdByComboPlanIdIn(cbPlanIds).forEach(e -> mapCBPlanIdAndDraftId.put(e.getId(), e.getRootId()));
        return mapCBPlanIdAndDraftId;
    }

    private Map<Long, Long> getMapIdAndDraftIdByAddonIdIn(List<Long> addonIds) {
        Map<Long, Long> mapCBPlanIdAndDraftId = new LinkedHashMap<>();
        addonRepository.getIdAndDraftIdByAddonIdIn(addonIds).forEach(e -> mapCBPlanIdAndDraftId.put(e.getId(), e.getRootId()));
        return mapCBPlanIdAndDraftId;
    }

    @Override
    public Page<CouponEnterpriseResponse> getCouponEnterprises(String name, CouponEnterprisePopupEnum type, Set<Long> notIds,
        Set<CustomerTypeEnum> customerTypes, Boolean isValidate, Pageable pageable) {
        if (CollectionUtils.isEmpty(customerTypes)) {
            if (BooleanUtils.isTrue(isValidate) && CouponEnterprisePopupEnum.SME.equals(type)) {
                throw throwCouponBadRequest(MessageKeyConstant.Validation.NOT_EMPTY, ErrorKey.Coupon.CUSTOMER_TYPE, "");
            } else {
                customerTypes = Sets.newHashSet(CustomerTypeEnum.ENTERPRISE, CustomerTypeEnum.HOUSE_HOLD, CustomerTypeEnum.PERSONAL);
            }
        }
        List<String> collect = AuthUtil.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());

        Long provinceId = -1L;
        // Truong hop la dev nhung xem doanh nghiep dev khac
        if (
                (collect.contains(RoleType.DEVELOPER.getValue())
                        || collect.contains(RoleType.DEVELOPER_OPERATOR.getValue())
                        || collect.contains(RoleType.DEVELOPER_BUSINESS.getValue()))
                        && type == CouponEnterprisePopupEnum.DEV
        ) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        String nameSearch = SqlUtils.optimizeSearchLike(name);

        return couponRepository.getEnterprises(nameSearch, type.name(), notIds, provinceId,
            customerTypes.stream().map(CustomerTypeEnum::getValue).collect(toSet()),
            pageable);
    }

    @Override
    public Page<ICouponListResDTO> getListCouponDEV(String searchText, Integer status, String duration,
            Integer approveStatus, Set<CustomerTypeEnum> customerTypes, String code, Pageable pageable) {
        CustomUserDetails currentUser = Optional.ofNullable(AuthUtil.getCurrentUser())
                .orElseThrow(()-> new AccessDeniedException("No access resources"));
        Long currentUserId = AuthUtil.getCurrentUserId();
        Long currentParentUserId = AuthUtil.getCurrentParentId();
        //Tài khoản đăng nhập là Dev cha
        String roleType = ROLE_TYPE_DEV_ADMIN;
        //Tài khoản đăng nhập là Dev nhân viên
        if (!Objects.equals(currentUserId, currentParentUserId)) {
            roleType = ROLE_TYPE_DEV_EMPLOYEE;
        }

        if (currentUser.getAuthorities().stream()
                .noneMatch(i -> i.getAuthority().equals(RoleType.ROLE_DEV_GET_LIST_COUPONS.getValue()))) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        String customerTypeStr = CollectionUtils.isEmpty(customerTypes) ? CharacterConstant.BLANK :
            String.join(CharacterConstant.VERTICAL_BAR, customerTypes.stream().map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));
        return couponRepository
                .getListCouponDev(searchText, status, duration, approveStatus, pageable, currentUserId, roleType, customerTypeStr, code);
    }

    @Override
    public Page<ICouponListResDTO> getListCouponADMIN(String value, Integer status, String duration,
            Integer approveStatus, Pageable pageable, String permission, Set<CustomerTypeEnum> customerTypes, Integer isCode, Integer isName) {

        Long provinceId = -1L;
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue()))) {
            Long provinceIdPlan = AuthUtil.getDepartment().getProvinceId();
            if (Objects.nonNull(provinceIdPlan)) {
                provinceId = provinceIdPlan;
            }
        }
        String customerTypeStr = CollectionUtils.isEmpty(customerTypes) ? CharacterConstant.BLANK :
            String.join(CharacterConstant.VERTICAL_BAR, customerTypes.stream().map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));
        return couponRepository.getListCouponAdmin(value, status, duration, approveStatus, pageable, permission, provinceId, customerTypeStr, isCode, isName);
    }

    @Override
    @Transactional
    public BaseResponseDTO updateCoupon(Long id, CouponDevReqDTO couponDto, PortalType portal) {

        Coupon currentCoupon = getCurrentCoupon(id);
        Coupon couponOld;
        BaseResponseDTO result;

        //kiem tra coupon co o trang thai chua duyet hay khong?
        if (Objects.equals(currentCoupon.getApprove(), ApproveStatusEnum.UNAPPROVED.value)) {
            couponOld = validateUpdateCouponUnapprove(id, portal, couponDto, currentCoupon);
            result = updateCouponUnapproved(portal, couponDto, couponOld);
        } else {
            throw throwCouponBadRequest(MessageKeyConstant.REQUIRED_UN_APPROVE, ErrorKey.Coupon.APPROVE, "Coupon");
        }

        // Tạo event ngay trong nghiệp vụ code
        Map<String, Object> eventMetadata = new HashMap<>();
        eventMetadata.put("couponId", id);
        eventMetadata.put("couponName", couponDto.getName());
        eventMetadata.put("portal", portal.name());
        eventMetadata.put("updatedBy", AuthUtil.getCurrentUserId());

        // Lưu event vào database ngay lập tức
        Events savedEvent = eventsService.saveEvent(EventTypeEnum.COUPON_UPGRADED, eventMetadata);

        // Đăng ký gửi event sau khi commit - chỉ gửi eventId
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // Publish ComponentChangedEvent với chỉ eventId
                publisher.publishEvent(new ComponentChangedEvent(
                    CouponServiceImpl.this,
                    savedEvent.getId()
                ));
            }
        });

        return result;
    }


    /**
     * Throw coupon bad request.
     *  @param messageKeyConstant the message key constant
     * @param errorKey           the error key
     * @param param              the param
     */
    private BadRequestException throwCouponBadRequest(String messageKeyConstant, String errorKey, String param) {
        String msg = messageSource.getMessage(messageKeyConstant, new String[]{param}, LocaleContextHolder.getLocale());
        return new BadRequestException(msg, Resources.COUPON, errorKey, messageKeyConstant);
    }


    /**
     * Cập nhật cật trong trường hợp chưa được phê duyệt
     *
     * @param portal    the portal
     * @param couponDto the coupon dto
     * @param couponOld the coupon old
     *
     * @return the base response dto
     */
    private BaseResponseDTO updateCouponUnapproved(PortalType portal, CouponDevReqDTO couponDto, Coupon couponOld){
        Coupon coupon;

        if (portal == PortalType.ADMIN) {
            CouponAdminReqDTO couponAdminDto = (CouponAdminReqDTO) couponDto;
            coupon = couponAdminMapper.toEntity(couponAdminDto);
            couponSupplierRepository.deleteByCouponId(couponOld.getId());
            if (Objects.equals(SupplierTypeEnum.OPTION, couponAdminDto.getSupplierType())) {
                Set<CouponSupplier> listSupplier = new LinkedHashSet<>();
                Set<CouponSupplierId> suppliers = CollectionUtils.isEmpty(couponAdminDto.getSuppliers())
                        ? new LinkedHashSet<>()
                        : couponAdminDto.getSuppliers();
                for (CouponSupplierId supplierId : suppliers) {
                    CouponSupplier couponSupplier =
                            new CouponSupplier(null, couponOld.getId(), supplierId.getUserId());
                    listSupplier.add(couponSupplier);
                }
                couponSupplierRepository.saveAll(listSupplier);
            }
        } else {
            coupon = couponDevMapper.toEntity(couponDto);
        }
        coupon.setId(couponOld.getId());
        coupon.setApprove(couponOld.getApprove());
        coupon.setUserId(couponOld.getUserId());
        coupon.setComment(couponOld.getComment());
        coupon.setDeletedFlag(couponOld.getDeletedFlag());
        coupon.setCreatedBy(couponOld.getCreatedBy());
        coupon.setCreatedAt(couponOld.getCreatedAt());
        coupon.setModifiedBy(AuthUtil.getCurrentUser().getId());
        coupon.setStatus(couponOld.getStatus());
        coupon.setPortal(couponOld.getPortal());
        coupon.setCustomerTypeCode(CollectionUtils.isEmpty(couponDto.getCustomerType()) ? new HashSet<>() :
            couponDto.getCustomerType().stream().map(CustomerTypeEnum::getValue).collect(toSet()));
        // TODO: 5/12/2021 Them truong vui long set them ben duoi

        coupon = couponRepository.save(coupon);
        log.info("===>UpdateCoupon {} {}",coupon.getId(), coupon.getPricingType());

        // update coupon ads Position
        McAdsPositionDTO adsPositionDTO = couponDto.getAdvertisePosition();
        // Save ads position img to file_attach
        updateAdsPositionImgInFileAttach(adsPositionDTO, couponOld.getId());
        List<CouponAdsPosition> adsPositions = couponAdsPositionRepository.getAllByCouponId(couponOld.getId());
        if (adsPositionDTO != null) {
            if (adsPositions.size() > 0) {
                couponAdsPositionRepository.deleteAllByCouponId(couponOld.getId());
            }
            CouponAdsPosition couponAdsPosition = new CouponAdsPosition(couponOld.getId(), adsPositionDTO);
            try {
                ObjectMapper mapper = new ObjectMapper();
                // get object as a json string
                String jsonStr = mapper.writeValueAsString(couponDto.getAdvertisePosition());
                couponAdsPosition.setJsonObject(jsonStr);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            couponAdsPositionRepository.save(couponAdsPosition);
        }

        couponPricingRepository.deleteByCouponId(coupon.getId());
        couponComboPlanRepository.deleteByCouponId(coupon.getId());
        couponPricingPlanApplyRepository.deleteByCouponId(coupon.getId());
        couponVariantRepository.deleteByCouponId(coupon.getId());
        if (Objects.equals(PromotionTypeEnum.PRODUCT, couponDto.getPromotionType())) {
            LinkedHashSet<CouponPricingIds> couponPricings = CollectionUtils.isEmpty(couponDto.getCouponPricing())
                    ? new LinkedHashSet<>()
                    : couponDto.getCouponPricing();

            saveCouponPromotion(coupon.getId(), couponPricings);
            // save coupon variant mapping
            saveCouponVariant(coupon.getId(), ObjectUtil.getOrDefault(couponDto.getCouponVariants(), new LinkedHashSet<>()));
            couponPromotionConditionRepository.deleteByCouponId(coupon.getId());
            saveCouponPromotionCondition(coupon.getId(), couponDto.getCondition());
        }
        couponEnterpriseRepository.deleteByCouponId(coupon.getId());
        if (Objects.equals(EnterpriseTypeEnum.OPTION, couponDto.getEnterpriseType())) {
            Set<CouponEnterprise> listCouponEnterprise = new LinkedHashSet<>();
            Set<CouponEnterpriseIds> couponEnterprises = CollectionUtils.isEmpty(couponDto.getCouponEnterprise())
                    ? new LinkedHashSet<>()
                    : couponDto.getCouponEnterprise();
            for (CouponEnterpriseIds enterpriseId : couponEnterprises) {
                CouponEnterprise couponEnterprise =
                        new CouponEnterprise(null, enterpriseId.getUserId(), coupon.getId());
                listCouponEnterprise.add(couponEnterprise);
            }
            couponEnterpriseRepository.saveAll(listCouponEnterprise);
        }
        couponPricingApplyRepository.deleteByCouponId(coupon.getId());
        couponPricingPlanRepository.deleteByCouponId(coupon.getId());
        couponComboPlanApplyRepository.deleteByCouponId(coupon.getId());
        couponVariantApplyRepository.deleteByCouponId(coupon.getId());
        if (Objects.equals(CouponPricingApplyTypeEnum.OPTION, couponDto.getPricingType())) {
            LinkedHashSet<CouponPricingApplyIds> pricingApplyIds = CollectionUtils.isEmpty(couponDto.getCouponPricingApply())
                    ? new LinkedHashSet<>()
                    : couponDto.getCouponPricingApply();

            saveCouponApply(coupon.getId(), pricingApplyIds);
            // save coupon variant apply mapping
            saveCouponVariantApply(coupon.getId(), ObjectUtil.getOrDefault(couponDto.getCouponVariantsApply(), new LinkedHashSet<>()));
        }

        couponAddonRepository.deleteByCouponId(coupon.getId());
        if (Objects.equals(AddonTypeEnum.OPTION, couponDto.getAddonsType())) {
            Set<CouponAddon> listCouponAddon = new LinkedHashSet<>();
            Set<CouponPricingPlan> listCouponPricingPlan = new LinkedHashSet<>();
            Set<CouponAddonIds> couponAddonIds = CollectionUtils.isEmpty(couponDto.getCouponAddons())
                    ? new LinkedHashSet<>()
                    : couponDto.getCouponAddons();
            // Lấy thông tin addon_draft_id -> lưu vào bảng coupon_addons
            Map<Long, Long> mapAddonIdAndDraftId = getMapIdAndDraftIdByAddonIdIn(couponAddonIds.stream().map(CouponAddonIds::getAddonsId).collect(
                Collectors.toList()));
            for (CouponAddonIds couponAddonId : couponAddonIds) {
                // nếu addon là loại 1 lần hoặc là loại định kỳ nhưng là loại 1 chu kỳ thanh toán
                if (BonusTypeEnum.ONCE.equals(couponAddonId.getType()) || (YesNoEnum.NO.equals(couponAddonId.getIsMultiPlan()))) {
                    // lưu vào bảng coupon addon
                    CouponAddon couponAddon =
                        new CouponAddon(null, couponAddonId.getAddonsId(), coupon.getId(), mapAddonIdAndDraftId.get(couponAddonId.getAddonsId()));
                    listCouponAddon.add(couponAddon);
                } else {
                    // nếu addon là loại định kỳ và là addon multi plan
                    CouponPricingPlan couponPricingPlan = new CouponPricingPlan(null, coupon.getId(), couponAddonId.getPricingPlanId(), null,
                        CouponPricingPlanTypeEnum.ADDON.value);
                    listCouponPricingPlan.add(couponPricingPlan);
                }
            }
            couponAddonRepository.saveAll(listCouponAddon);
            couponPricingPlanRepository.saveAll(listCouponPricingPlan);
        }
        setBanner(couponDto.getDesktopBannerId(), couponDto.getMobileBannerId(), coupon.getId());
        return new BaseResponseDTO(coupon.getId());
    }

    /**
     * Cập nhật cật trong trường hợp đã được phê duyệt
     *
     * @param couponDto the coupon dto
     * @param couponOld the coupon old
     *
     * @return the base response dto
     */
    private BaseResponseDTO updateCouponApproved(CouponUpdateApproveResDTO couponDto, Coupon couponOld, PortalType portal) throws BadRequestException {
        CouponDraft couponDraft = new CouponDraft();

        Optional<CouponDraft> couponDraftOptional = couponDraftRepository.findByCouponId(couponOld.getId());
        if (couponDraftOptional.isPresent()) {
            couponDraft = couponDraftOptional.get();
        }

        // trong truong hop chua duyet => khong duoc phep cap nhat trang thai
        if (Objects.isNull(couponDto.getStatus())) {
            throw throwCouponBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Coupon.STATUS, "Status");
        }
        couponDraft.setStatus(couponDto.getStatus().value);

        // cap nhat ten chuong trinh khuyen mai
//        if (Objects.nonNull(couponDraft.getName()) && !Objects.equals(couponDraft.getName(), couponDto.getName())) {
//            validateNameCoupon(couponDto.getName(), AuthUtil.getCurrentParentId());
//        } NghiaPT fix ngày 5/8: bỏ check trùng tên CTKM
        couponDraft.setName(couponDto.getName());

        couponDraft.setVisibleStatus(couponDto.getVisibleStatus());
        // update thoi gian den ngay
        if (!Objects.equals(couponOld.getEndDate(), couponDto.getEndDate())) {
            validateDateTimeCoupon(couponOld.getStartDate(), couponDto.getEndDate(), ActionType.UPDATE);
        }
        couponDraft.setEndDate(couponDto.getEndDate());

        // cap nhat so lan ap dung
        if (!Objects.equals(couponDto.getMaxUsed(), couponOld.getMaxUsed())) {
            validateMaximumPromotion(couponDto.getMaxUsed(), couponOld.getMaximumPromotion());

            Integer countUsed = subscriptionRepository.countByCouponId(couponOld.getId());
            if (Objects.nonNull(couponDto.getMaxUsed()) && couponDto.getMaxUsed() < countUsed) {
                throw throwCouponBadRequest(MessageKeyConstant.OUT_OF_QUANTITY, ErrorKey.QUANTITY, null);
            }
        }
        couponDraft.setMaxUsed(couponDto.getMaxUsed());

        couponDraft.setMaximumPromotion(couponDto.getMaximumPromotion());


        // thay doi trang thai su dung ban nhap
        couponOld.setCouponDraftStatus(StatusEnum.ACTIVE.value);
        couponOld.setIsConfirm(StatusEnum.INACTIVE.value);
        couponRepository.save(couponOld);
        log.info("===>UpdateCoupon {} {}",couponOld.getId(), couponOld.getPricingType());

        // update coupon ads Position
        McAdsPositionDTO adsPositionDTO = couponDto.getAdvertisePosition();
        // Save ads position img to file_attach
        updateAdsPositionImgInFileAttach(adsPositionDTO, couponOld.getId());
        List<CouponAdsPosition> adsPositions = couponAdsPositionRepository.getAllByCouponId(couponOld.getId());
        if (adsPositionDTO != null) {
            if (adsPositions.size() > 0) {
                couponAdsPositionRepository.deleteAllByCouponId(couponOld.getId());
            }
            CouponAdsPosition couponAdsPosition = new CouponAdsPosition(couponOld.getId(), adsPositionDTO);
            try {
                ObjectMapper mapper = new ObjectMapper();
                // get object as a json string
                String jsonStr = mapper.writeValueAsString(couponDto.getAdvertisePosition());
                couponAdsPosition.setJsonObject(jsonStr);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            couponAdsPositionRepository.save(couponAdsPosition);
        }

        // luu ban nhap chuong trinh khuyen mai
        couponDraft.setCouponId(couponOld.getId());
        couponDraftRepository.save(couponDraft);
        setBanner(couponDto.getDesktopBannerId(), couponDto.getMobileBannerId(), couponOld.getId());
        return new BaseResponseDTO(couponOld.getId());
    }

    /**
     * Kiem tra truoc khi cap nhat chuong trinh khuyen mai
     *
     * @param id            the id
     * @param portal        the portal
     * @param couponDto     the coupon dto
     * @param currentCoupon the current coupon
     *
     * @return the coupon
     */
    private Coupon validateUpdateCouponUnapprove(Long id, PortalType portal, CouponDevReqDTO couponDto, Coupon currentCoupon){
        //kiem tra coupon ton tai
        Long userId = AuthUtil.getCurrentParentId();

        //Kiem tra quyen
        validateRole(portal, currentCoupon.getCreatedBy(), currentCoupon.getUserId(), currentCoupon.getPortal());

        // kiem tra ten chuong trinh khuyen mai
//        if(!Objects.equals(currentCoupon.getName(), couponDto.getName())){
//            validateNameCoupon(couponDto.getName(), userId);
//        } NghiaPT fix ngày 5/8: bỏ check trùng tên CTKM

        // trong truong hop chua duyet => khong duoc phep cap nhat trang thai
        if (!Objects.equals(couponDto.getStatus(), StatusEnum.INACTIVE)) {
            String msg = messageSource.getMessage(MessageKeyConstant.INVALID_STATUS,
                    new String[]{"status"}, LocaleContextHolder.getLocale());
            throw new BadRequestException(msg, Resources.COUPON, ErrorKey.Coupon.STATUS,
                    MessageKeyConstant.INVALID_STATUS);
        }

        //kiem tra code chuong trinh khuyen mai
        if(!Objects.equals(currentCoupon.getCodeType(), couponDto.getCodeType())
                || !Objects.equals(currentCoupon.getPromotionCode(), couponDto.getPromotionCode())){
            validateCodeApply(couponDto);
        }

        if (!Objects.equals(currentCoupon.getPromotionCode(), couponDto.getPromotionCode())) {
            validatePromotionCode(couponDto);
        }

        validatePromotionTypeUpdate(id, portal, couponDto, currentCoupon, userId);

        //kiem tra ma CTKM
        if (!Objects.equals(currentCoupon.getCode(), couponDto.getCode())) {
            validateCodeCoupon(couponDto);
        }

        // Kiem tra thoi gian hieu luc
        if(!Objects.equals(currentCoupon.getStartDate(), couponDto.getStartDate())
                || !Objects.equals(currentCoupon.getEndDate(), couponDto.getEndDate())){
            validateDateTimeCoupon(couponDto.getStartDate(), couponDto.getEndDate(), ActionType.CREATE);
        }

        // kiem tra doanh nghiep
        validateCouponEnterpriseUpdate(id, couponDto, currentCoupon);

        // kiem tra san pham ap dung
        validatePricingApplyUpdate(id, portal, couponDto, currentCoupon, userId);


        // kiem tra dich vu di kem
        validateAddonsUpdate(id, couponDto, currentCoupon, portal);

        // Kiểm tra chương trình khuyến mại tối đa
        if(!Objects.equals(currentCoupon.getMaxUsed(), couponDto.getMaxUsed())
                || !Objects.equals(currentCoupon.getMaximumPromotion(), couponDto.getMaximumPromotion())){
            validateMaximumPromotion(couponDto.getMaxUsed(), couponDto.getMaximumPromotion());
        }

        //kiem tra nha cung cap dich vu
        validateSupplierUpdate(id, portal, couponDto, currentCoupon);

        return currentCoupon;
    }

    /**
     * kiem tra san pham ap dung khi cap nhat
     *
     * @param id            the id
     * @param portal        the portal
     * @param couponDto     the coupon dto
     * @param currentCoupon the current coupon
     * @param userId        the user id
     */
    private void validatePricingApplyUpdate(Long id, PortalType portal, CouponDevReqDTO couponDto, Coupon currentCoupon, Long userId) {
        Set<Long> pricingApplyIds = couponPricingApplyRepository.getCouponPricingApply(id)
                                                                .stream()
                                                                .map(CouponPricingDetailDTO::getPricingId)
                                                                .collect(toSet());

        Set<CouponPricingApplyIds> couponPricingApply = Objects.isNull(couponDto.getCouponPricingApply())
                ? new HashSet<>()
                : couponDto.getCouponPricingApply();

        Set<Long> pricingApplyInputIds = couponPricingApply.stream()
                                                  .map(CouponPricingApplyIds::getPricingId)
                                                  .collect(toSet());

        if (!Objects.equals(currentCoupon.getPricingType(), couponDto.getPricingType())
                || !Objects.equals(pricingApplyIds, pricingApplyInputIds)) {
            validatePricingOrVariantApplyCoupon(couponDto, portal, userId);
        }
    }

    /**
     * kiem tra dich vu di kem khi cap nhat
     *
     * @param id            the id
     * @param couponDto     the coupon dto
     * @param currentCoupon the current coupon
     */
    private void validateAddonsUpdate(Long id, CouponDevReqDTO couponDto, Coupon currentCoupon, PortalType portal) {
        Set<Long> addonIds = couponAddonRepository.getCouponAddon(id)
                                                  .stream()
                                                  .map(CouponAddonsDetailDTO::getAddonsId)
                                                  .collect(toSet());

        Set<CouponAddonIds> couponAddons = Objects.isNull(couponDto.getCouponAddons())
                ? new HashSet<>()
                : couponDto.getCouponAddons();

        Set<Long> addonInputIds = couponAddons.stream().map(CouponAddonIds::getAddonsId).collect(toSet());

        if(!Objects.equals(currentCoupon.getAddonsType(), couponDto.getAddonsType())
                || !Objects.equals(addonIds, addonInputIds)){
            validateAddonsCoupon(couponDto, portal);
        }
    }

    /**
     * kiem tra nha cung cap dich vu khi cap nhat
     *
     * @param id            the id
     * @param portal        the portal
     * @param couponDto     the coupon dto
     * @param currentCoupon the current coupon
     */
    private void validateSupplierUpdate(Long id, PortalType portal, CouponDevReqDTO couponDto, Coupon currentCoupon) {
        if (portal == PortalType.ADMIN) {
            CouponAdminReqDTO couponAdminDto = (CouponAdminReqDTO) couponDto;
            Set<Long> supplierIds = couponSupplierRepository.getCouponSupplier(id)
                                                            .stream()
                                                            .map(CouponEnterpriseDetailDTO::getUserId)
                                                            .collect(toSet());
            Set<CouponSupplierId> suppliers = Objects.isNull(couponAdminDto.getSuppliers())
                    ? new HashSet<>()
                    : couponAdminDto.getSuppliers();
            Set<Long> supplierInputIds = suppliers.stream().map(CouponSupplierId::getUserId).collect(toSet());

            if(!Objects.equals(supplierIds, supplierInputIds)
                    || !Objects.equals(currentCoupon.getSupplierType(), couponAdminDto.getSupplierType())){
                validateSupplierCoupon(couponDto, portal);
            }

        }
    }

    /**
     * kiem tra doanh nghiep khi cap nhat
     *
     * @param id            the id
     * @param couponDto     the coupon dto
     * @param currentCoupon the current coupon
     */
    private void validateCouponEnterpriseUpdate(Long id, CouponDevReqDTO couponDto, Coupon currentCoupon) {
        Set<Long> enterpriseIds = couponEnterpriseRepository.getCouponEnterprise(id)
                                                            .stream()
                                                            .map(CouponEnterpriseDetailDTO::getUserId)
                                                            .collect(toSet());

        Set<CouponEnterpriseIds> couponEnterprise = Objects.isNull(couponDto.getCouponEnterprise())
                ? new HashSet<>()
                : couponDto.getCouponEnterprise();

        Set<Long> enterpriseInputIds = couponEnterprise.stream().map(CouponEnterpriseIds::getUserId).collect(toSet());

        if(!Objects.equals(currentCoupon.getEnterpriseType(), couponDto.getEnterpriseType())
                || !Objects.equals(enterpriseIds, enterpriseInputIds)){
            validateEnterpriseCoupon(couponDto);
        }
    }

    /**
     * kiem tra kieu giam gia khi cap nhat
     *
     * @param id            the id
     * @param portal        the portal
     * @param couponDto     the coupon dto
     * @param currentCoupon the current coupon
     * @param userId        the user id
     */
    private void validatePromotionTypeUpdate(Long id, PortalType portal, CouponDevReqDTO couponDto, Coupon currentCoupon, Long userId) {
        //kiem tra kieu giam gia
        Set<Long> couponPricingIds = couponPricingRepository.getCouponPricing(id)
                                                            .stream()
                                                            .map(CouponPricingDetailDTO::getPricingId)
                                                            .collect(toSet());

        Set<CouponPricingIds> couponPricing = Objects.isNull(couponDto.getCouponPricing()) ? new HashSet<>() : couponDto.getCouponPricing();
        Set<Long> couponPricingInputIds = couponPricing.stream().map(CouponPricingIds::getPricingId).collect(toSet());

        boolean hasValidPromotionType = false;
        if (!Objects.equals(currentCoupon.getPromotionType(), couponDto.getPromotionType())
                || !Objects.equals(currentCoupon.getDiscountType(), couponDto.getDiscountType())
                || !Objects.equals(currentCoupon.getDiscountValue(), couponDto.getDiscountValue())
                || !Objects.equals(currentCoupon.getDiscountAmount(), couponDto.getDiscountAmount())
                || !Objects.equals(currentCoupon.getTotalBillType(), couponDto.getTotalBillType())
                || !Objects.equals(currentCoupon.getType(), couponDto.getType())
                || !Objects.equals(couponPricingIds, couponPricingInputIds)) {
            hasValidPromotionType = true;
            validatePromotionType(couponDto, portal, userId);
        }

        if(!hasValidPromotionType && portal == PortalType.ADMIN
                && !Objects.equals(currentCoupon.getDiscountSupplierType(), ((CouponAdminReqDTO) couponDto).getDiscountSupplierType())){
            validatePromotionType(couponDto, portal, userId);
        }
    }

    /**
     * Kiểm tra quyền
     *
     * @param portal    the portal
     * @param createdBy the created by
     * @param userId    the user id
     */
    private void validateRole(PortalType portal, Long createdBy, Long userId, Integer portalCreated){
        //Kiem tra quyen
        List<String> roles = AuthUtil.getCurrentUser().getAuthorities()
                                     .stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        //Neu portal la ADMIN -> Account khong co quyen supper va khong phai la chinh no tao ra CTKM thi bao loi
        //Neu portal la DEV -> Chi co nguoi tao hoac cha cua no moi duoc phep chinh sua
        if (portal == PortalType.ADMIN) {
            if( !roles.contains(RoleType.FULL_ADMIN.getValue())
                    && !Objects.equals(createdBy, AuthUtil.getCurrentUser().getId())){
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
            Long provinceId = departmentsRepository.getProvinceIdByUserId(AuthUtil.getCurrentUser().getId());
            Long provinceIdDb = departmentsRepository.getProvinceIdByUserId(createdBy);
            if (PortalType.DEV.getType()== portalCreated) {
                provinceIdDb = departmentsRepository.getProvinceIdByUserId(userId);
            }

            if (!Objects.equals(provinceIdDb, provinceId)) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }

        } else if (portal == PortalType.DEV
                && !Objects.equals(userId, AuthUtil.getCurrentParentId()) ) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
    }

    @Override
    public BaseResponseDTO requestApprove(Long id) {
        Coupon coupon = getCurrentCoupon(id);
        validateRoleRequestApprove(coupon);
        if (!coupon.getApprove().equals(ApproveStatusEnum.UNAPPROVED.value)) {
            String message = messageSource.getMessage(
                    MessageKeyConstant.REQUEST_APPROVE, COUPON,
                    LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.COUPON,
                    ErrorKey.Coupon.APPROVE,
                    MessageKeyConstant.REQUEST_APPROVE);
        }
        coupon.setApprove(ApproveStatusEnum.AWAITING_APPROVAL.value);
        couponRepository.save(coupon);
        log.info("===>UpdateCoupon {} {}",coupon.getId(), coupon.getPricingType());

        Map<String, String> mapDefaultValue = new HashMap<>();
        mapDefaultValue.putIfAbsent(ParamEmailEnum.NAME_COUPON.getValue(), coupon.getName());
        mapDefaultValue.putIfAbsent(ParamEmailEnum.USER.getValue(), AuthUtil.getCurrentUser().getName());
        Optional<com.model.dto.EmailTemplate> emailTemplateOpt = emailTemplateService.replaceParamEmailTemplate(EmailCodeEnum.CP11.getValue(), mapDefaultValue, null);
        if (emailTemplateOpt.isPresent()) {
            emailService.save(AuthUtil.getUsernameCurrentUserLogin().get(), emailTemplateOpt.get());
        }

        List<User> users = userRepository.getAllUserByRoleNameIn(Collections.singletonList(RoleType.FULL_ADMIN.getValue()));
        Map<String, String> mapParam = new HashMap<>();
        mapParam.put(ParamEmailEnum.NAME_DEVELOPER.getValue(), AuthUtil.getCurrentUser().getName());
        mapParam.put(ParamEmailEnum.NAME_COUPON.getValue(), coupon.getName());

        //Duyệt list user Superadmin
        List<MailSendParamDTO> param = users.stream().map(user -> {
            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(user.getEmail());

            mapParam.put(ParamEmailEnum.USER.getValue(), user.getName());
            mapParam.put(ParamEmailEnum.EMAIL_ADMIN.getValue(), user.getEmail());
            List<MailParamResDTO> mailParamResDTOs = mapParam.entrySet().stream()
                .map(entry -> new MailParamResDTO(entry.getKey(),
                    StringUtils.isNotEmpty(entry.getValue()) ? entry.getValue() : StringUtils.EMPTY))
                .collect(Collectors.toList());

            mailSendParamDTO.setListMailParam(mailParamResDTOs);
            return mailSendParamDTO;
        }).collect(Collectors.toList());
        emailService.sendMultiMail(EmailCodeEnum.CP12, param);

        if (Objects.isNull(AuthUtil.getCurrentParentId()) || SubscriptionConstant.PARENT_ID.equals(AuthUtil.getCurrentUser().getId())) {
            return new BaseResponseDTO(id);
        }

        Optional<User> userParentOpt = userRepository
            .findByIdAndDeletedFlagAndStatus(AuthUtil.getCurrentParentId(), EntitiesConstant.DeleteFlag.ACTIVE, StatusEnum.ACTIVE.value);
        if (!userParentOpt.isPresent()) {
            return new BaseResponseDTO(id);
        }

        mapDefaultValue.put(ParamEmailEnum.USER.getValue(), userParentOpt.get().getName());
        Optional<com.model.dto.EmailTemplate> emailTemplateUserParentOpt = emailTemplateService
            .replaceParamEmailTemplate(EmailCodeEnum.CP11.getValue(), mapDefaultValue, null);
        if (emailTemplateUserParentOpt.isPresent()) {
            emailService.save(userParentOpt.get().getEmail(), emailTemplateOpt.get());
        }

        return new BaseResponseDTO(id);
    }

    /**
     * Kiểm tra quyền gửi yêu cầu phê duyệt
     *
     */
    private void validateRoleRequestApprove(Coupon coupon) {
        List<String> collect = AuthUtil.getCurrentUser().getAuthorities()
                .stream().map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        // Admin tổng chỉ xem ycpd coupon của nó tạo. Admin tỉnh ycpd coupon của nó tạo
        Long provinceId = comboRepository.getProvinceOfUser(AuthUtil.getCurrentUser().getId());
        if (Objects.isNull(provinceId)) {
            provinceId = -1L;
        }
        Long provinceIdDB = comboRepository.getProvinceOfUser(coupon.getCreatedBy());
        if (PortalType.DEV.getType() == coupon.getPortal()) {
            provinceIdDB = comboRepository.getProvinceOfUser(coupon.getUserId());
        }
        if (collect.contains(RoleType.FULL_ADMIN.getValue()) || collect.contains(RoleType.ADMIN.getValue())) {
            if (!provinceId.equals(Objects.isNull(provinceIdDB) ? -1L : provinceIdDB)) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
        }
//        // Portal là ADMIN -> Chỉ ADMIN tạo ra coupon mới có quyền yêu cầu phê duyệt
//        if (!collect.contains(RoleType.SUPER_ADMIN.getValue())
//                && collect.contains(RoleType.ADMIN.getValue())
//                && !Objects.equals(coupon.getCreatedBy(), AuthUtil.getCurrentUser().getId())) {
//            throw new AccessDeniedException(AddonsConstant.ACCESS_DENIED);
//        }

        // portal là DEV -> DEV tạo ra coupon và DEV Admin sẽ có quyền yêu cầu  phê duyệt
        if (collect.contains(RoleType.DEVELOPER_BUSINESS.getValue())
                || collect.contains(RoleType.DEVELOPER.getValue())
                || collect.contains(RoleType.DEVELOPER_OPERATOR.getValue())) {
            if (!Objects.equals(coupon.getCreatedBy(), AuthUtil.getCurrentUser().getId())
                    && !Objects.equals(coupon.getUserId(), AuthUtil.getCurrentParentId())) {
                throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CouponDeleteResDTO deleteCoupons(CouponDeleteReqDTO couponDeleteReqDTO) {
        CustomUserDetails user = AuthUtil.getCurrentUser();
        List<String> roles = user.getAuthorities().stream().map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        Set<Long> ids = couponDeleteReqDTO.getIds();
        Integer couponNumber = couponRepository.couponNumber(ids);
        //coupon khong ton tai
        if (couponNumber < 1 || couponNumber < ids.size()) {
            throw new BadRequestException(getMessageNotFound(), Resources.COUPON, ErrorKey.Coupon.ID,
                    MessageKeyConstant.NOT_FOUND);
        }
        Map<Long, List<String>> rolesCoupon = getRolesByCreatedCoupon(ids);
        Long parentId = AuthUtil.getCurrentParentId();
        String message = messageSource.getMessage(MessageKeyConstant.USER_NOT_OWN_COUPON, null,
                LocaleContextHolder.getLocale());
        for (Long id : ids) {
            //khong phai la super admin
            if (rolesCoupon.containsKey(id) && !roles.contains(RoleType.FULL_ADMIN.getValue())) {
                //neu la admin, nguoi tao coupon khong phai la admin thi khong duoc xoa
                if (roles.contains(RoleType.ADMIN.getValue())
                        && !rolesCoupon.get(id).contains(RoleType.ADMIN.getValue())) {
                    throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.ID,
                            MessageKeyConstant.USER_NOT_OWN_COUPON);
                }
                //neu la dev admin, nguoi tao coupon khong phai la dev admin cung doanh nghiep
                //hoac dev operator cung doanh nghiep thi khong duoc xoa
                if (roles.contains(RoleType.DEVELOPER.getValue())
                        && !checkCouponEnterprise(id, parentId)) {
                    throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.ID,
                            MessageKeyConstant.USER_NOT_OWN_COUPON);
                }
                //neu user la dev operator va coupon do khong phai la cua user hien tai
                if (roles.contains(RoleType.DEVELOPER_OPERATOR.getValue())
                        && !checkuserOwnCoupon(id, user.getId())) {
                    throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.ID,
                            MessageKeyConstant.USER_NOT_OWN_COUPON);
                }
            }
            // check admin tong hay admin tinh thanh
            if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.FULL_ADMIN.getValue(), RoleType.CUSTOMER_SUPPORT
                .getValue()))) {
                UserDepartmentDTO actorDepartment = AuthUtil.getDepartment();
                // la admin tong
                Coupon currentCoupon = couponRepository.findCouponByIdAndDeletedFlag(id, DeletedFlag.NOT_YET_DELETED.getValue())
                    .orElseThrow(() -> {
                        String errorMessage = messageSource
                            .getMessage(MessageKeyConstant.NOT_FOUND, COUPON, LocaleContextHolder.getLocale());
                        return new ResourceNotFoundException(errorMessage, Resources.COUPON, ErrorKey.Coupon.ID,
                            MessageKeyConstant.NOT_FOUND);
                    });
                // khong duoc xoa nhung coupon do dev tao
                if ((PortalType.DEV.getType() == currentCoupon.getPortal())) {
                    throw new AccessDeniedException(AddonsConstant.ACCESS_DENIED);
                }
                // la admin tinh thanh
                // khong duong xoa coupon do dev, du lieu tong, admin tinh khac tao
                if (Objects.nonNull(actorDepartment.getProvinceId()) && PortalType.ADMIN.getType() == currentCoupon.getPortal()
                    && !(Objects.equals(currentCoupon.getProvinceId(), actorDepartment.getProvinceId()))) {
                    throw new AccessDeniedException(AddonsConstant.ACCESS_DENIED);
                }
            }
        }
        //danh sach coupon da duoc dang ky
        Set<Long> couponIdsSubscription = couponRepository.getCouponIdSubscription(ids);
        //danh sach coupon chua duoc dang ky
        Set<Long> listAddonNonSubscription = ids.stream()
                .filter(x -> !couponIdsSubscription.contains(x)).collect(Collectors.toSet());
        //danh sach chua duoc dang ky rong -> tat ca coupon da duoc su dung
        if (listAddonNonSubscription.isEmpty()) {
            message = messageSource.getMessage(MessageKeyConstant.COUPON_HAS_BEEN_USED, null,
                    LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.COUPON, ErrorKey.Coupon.ID,
                    MessageKeyConstant.COUPON_HAS_BEEN_USED);
        }
        CouponDeleteResDTO couponDeleteResDTO = new CouponDeleteResDTO();
        // neu danh sach coupon da dang ky rong -> tat ca coupon chua duoc dang ky
        if (couponIdsSubscription.isEmpty()) {
            couponDeleteResDTO.setCode(MessageKeyConstant.CODE_ALL_SUCCESS);
            couponDeleteResDTO.setMessage(MESSAGE_SUCCESS);
        }
        //new danh sach coupon da dang ky khong rong -> co coupon da duoc dang ky
        else {
            couponDeleteResDTO.setCode(MessageKeyConstant.CODE_SUCCESS_WITHOUT_SUBSCRIPTION);
            couponDeleteResDTO.setMessage(MESSAGE_SUCCESS_WITHOUT_SUBSCRIPTION);
        }
        //cap nhat trang thai delete_flag
        couponRepository.deletedCoupon(listAddonNonSubscription);
        //xoa row trong coupon_draft
        couponRepository.deletedCouponDraft(listAddonNonSubscription);
        //cập nhật coupon set
        couponSetService.deactivate(listAddonNonSubscription);
        return couponDeleteResDTO;
    }

    /**
     * kiem tra coupon co cung cong ty voi user hien tai
     *
     */
    private boolean checkCouponEnterprise(Long couponId, Long userId) {
        return userId.equals(couponRepository.getCreatedCouponUserId(couponId));
    }

    /**
     * Kiem tra xem co phai la nguoi tao coupon hay khong
     *
     */
    private boolean checkuserOwnCoupon(Long couponId, Long userId) {
        Long parentId = AuthUtil.getCurrentParentId();
        //HiepNT check thêm Dk parentId
        Long createdBy = couponRepository.getUserIdOwnCoupon(couponId);
        return userId.equals(createdBy) || parentId.equals(createdBy);
    }

    @Override
    @Transactional
    public BaseResponseDTO restoreUpdateInfo(Long id, CouponRestoreStatus status) {
        Coupon currentCoupon = getCurrentCoupon(id);
        switch (status) {
            case ACCEPT:
                couponDraftRepository.findByCouponId(id).ifPresent(couponDraft -> {
                    currentCoupon.setMaxUsed(couponDraft.getMaxUsed());
                    currentCoupon.setMaximumPromotion(couponDraft.getMaximumPromotion());
                    currentCoupon.setStatus(couponDraft.getStatus());
                    currentCoupon.setName(couponDraft.getName());
                    currentCoupon.setEndDate(couponDraft.getEndDate());
                    currentCoupon.setCouponDraftStatus(StatusEnum.INACTIVE.value);
                    currentCoupon.setIsConfirm(StatusEnum.ACTIVE.value);
                    currentCoupon.setVisibleStatus(couponDraft.getVisibleStatus());
                    couponRepository.save(currentCoupon);
                    log.info("===>UpdateCoupon {} {}",currentCoupon.getId(), currentCoupon.getPricingType());
                    couponDraftRepository.deleteByCouponId(id);
                });
                return new BaseResponseDTO(id);
            case RESTORE:
                couponDraftRepository.findByCouponId(id).ifPresent(couponDraft -> {
                    currentCoupon.setCouponDraftStatus(StatusEnum.INACTIVE.value);
                    currentCoupon.setIsConfirm(StatusEnum.INACTIVE.value);
                    couponRepository.save(currentCoupon);
                    log.info("===>UpdateCoupon {} {}",currentCoupon.getId(), currentCoupon.getPricingType());
                    couponDraftRepository.deleteByCouponId(id);
                });
                return new BaseResponseDTO(id);
            default:
                throw throwCouponBadRequest(MessageKeyConstant.INVALID_STATUS, ErrorKey.STATUS, null);
        }
    }

    @Override
    public BaseResponseDTO updateApprove(Long id, CouponUpdateApproveResDTO updateInfo, PortalType portal) {
        Coupon currentCoupon = getCurrentCoupon(id);
        if (!Objects.equals(currentCoupon.getApprove(), ApproveStatusEnum.APPROVED.value)) {
            throw throwCouponBadRequest(MessageKeyConstant.COUPON_NEED_APPROVE, ErrorKey.APPROVE_STATUS, null);
        }
        return updateCouponApproved(updateInfo, currentCoupon, portal);
    }

    /**
     * Lay danh sach role nguoi tao coupon
     *
     */
    private Map<Long, List<String>> getRolesByCreatedCoupon(Set<Long> ids) {
        Map<Long, List<String>> roles = new HashMap<>();
        List<CreatedByResDTO> rolesCreated = couponRepository.getCouponCreated(ids);
        List<String> roleName = new ArrayList<>();
        for (CreatedByResDTO c : rolesCreated) {
            if (!roles.containsKey(c.getId())) {
                roleName.add(c.getName());
                roles.put(c.getId(), roleName);
            } else {
                roles.get(c.getId()).add(c.getName());
            }
        }
        return roles;
    }

    /**
     * Get list param
     */
    private List<MailParamResDTO> getListParam(List<MailParamResDTO> listParamNameByCode, CouponMailParamDTO couponMailParamDTO){
        List<MailParamResDTO> cloneDTOs = new ArrayList<>();
        for (MailParamResDTO dto : listParamNameByCode) {
            // clone DTO
            MailParamResDTO mailParamResDTO = new MailParamResDTO(dto.getParamName(), dto.getValue());
            cloneDTOs.add(mailParamResDTO);

            if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.HOTLINE)) {
                mailParamResDTO.setValue(couponMailParamDTO.getHotline());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_COMPANY)) {
                mailParamResDTO.setValue(couponMailParamDTO.getNameCompany());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_DEVELOPER)) {
                mailParamResDTO.setValue(couponMailParamDTO.getNameDeveloper());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.REJECT_REASON)) {
                mailParamResDTO.setValue(couponMailParamDTO.getRejectReason());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.DISCOUNT_VALUE)) {
                mailParamResDTO.setValue(couponMailParamDTO.getDiscountValue());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.DEV_OR_ADMIN)) {
                mailParamResDTO.setValue(couponMailParamDTO.getDevOrAdmin());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.CODE_COUPON)) {
                mailParamResDTO.setValue(couponMailParamDTO.getCodeCoupon());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.DISCOUNT_VALUE_MAX)) {
                mailParamResDTO.setValue(couponMailParamDTO.getDiscountValueMax());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.ON_SME_PLATFORM)) {
                mailParamResDTO.setValue(couponMailParamDTO.getOnSmePlatform());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.TIMES_COUPON)) {
                mailParamResDTO.setValue(couponMailParamDTO.getTimesCoupon());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.TIMES_USE_COUPON)) {
                mailParamResDTO.setValue(couponMailParamDTO.getTimesUseCoupon());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.DATE_EXPIRED_COUPON)) {
                mailParamResDTO.setValue(couponMailParamDTO.getDateExpiredCoupon());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.MINIMUM_APPLY_PRICING)) {
                mailParamResDTO.setValue(couponMailParamDTO.getMiniumApplyPricing());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.MINIMUM_APPLY_AMOUNT)) {
                mailParamResDTO.setValue(couponMailParamDTO.getMiniumApplyAmount());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.LIST_SERVICE_CP)) {
                mailParamResDTO.setValue(couponMailParamDTO.getListServiceCp());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.LIST_SERVICE_ADDON_CP)) {
                mailParamResDTO.setValue(couponMailParamDTO.getListServiceAddonCp());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.QUANTITY_COUPON)) {
                mailParamResDTO.setValue(couponMailParamDTO.getQuantityCoupon());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.SERVICE_ADDON_TXT_CP)) {
                mailParamResDTO.setValue(couponMailParamDTO.getServiceAddonTxtCp());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.SERVICE_TXT_CP)) {
                mailParamResDTO.setValue(couponMailParamDTO.getServiceTxtCp());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.DISCOUNT_CONTENT)) {
                mailParamResDTO.setValue(couponMailParamDTO.getDiscountContent());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME_COUPON)) {
                mailParamResDTO.setValue(couponMailParamDTO.getNameCoupon());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.EMAIL_ADMIN)) {
                mailParamResDTO.setValue(couponMailParamDTO.getEmailAdmin());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.UPDATE_REASON)) {
                mailParamResDTO.setValue(couponMailParamDTO.getUpdateReason());
            } else if (mailParamResDTO.getParamName().equalsIgnoreCase(MailParam.NAME)) {
                mailParamResDTO.setValue(couponMailParamDTO.getNameCompany());
            }
        }
        return cloneDTOs;
    }

    /**
     * gửi mail từ CP01 - CP08
     */
    private void sendMailCouponSme(CouponMailParamDTO couponMail, List<User> users, EmailCodeEnum emailCodeEnum){
        if (users == null || users.isEmpty()) {
            return;
        }
        List<MailParamResDTO> listParamNameByCode = paramEmailRepository.findParamNameByCode(emailCodeEnum.getValue());
        List<MailSendParamDTO> listCouponMailSME = new ArrayList<>();
        List<MailParamResDTO> cloneArr = listParamNameByCode.stream().map(x -> new MailParamResDTO(x.getParamName(), x.getValue())).collect(
            Collectors.toList());
        for (User user: users) {
            if (StringUtils.isEmpty(couponMail.getDiscountContent())) {
                couponMail.setNameCompany(Objects.equals(user.getCustomerType(), CustomerTypeEnum.PERSONAL.getValue())
                    ? StringUtils.join(user.getLastName()," ", user.getFirstName()) : user.getName());


            } else {
                couponMail.setNameDeveloper(user.getName());
            }
            listCouponMailSME.add(new MailSendParamDTO(user.getEmail(), this.getListParam(new ArrayList<>(cloneArr), couponMail)));
        }
        emailService.sendMultiMail(emailCodeEnum, listCouponMailSME);
    }

    /**
     * Lây ra tham số mail CP-01 đến CP-08
     */
    private CouponMailParamDTO getMailParamSme(CouponSendMailDTO coupon, User couponOwner) {
        //Lấy mã coupon code
        String couponCode = Objects.equals(CodeTypeEnum.AUTO.value, coupon.getCodeType()) ? "" : coupon.getCode();
        //Giá trị tối thiểu để được hưởng coupon
        String miniumAmount = convertFormatAmount(BigDecimal.valueOf(coupon.getMinimumAmount())) + CouponConst.PRICE_SUFFIX;
        //Giá trị KM của coupon (loại: chiết khấu)
        String discountValue = "";
        //Giá trị tối đa của coupon (loại: chiết khấu - phần trăm)
        String discountValueMax = "";
        //Chiêt khấu cho doanh nghiệp: 0 - phí đăng ký, 1 - hoa hồng
        String discountContent = "";
        if (Objects.nonNull(coupon.getDiscountSupplierType())) {
            discountContent = coupon.getDiscountSupplierType().equals(DiscountSupplierTypeEnum.REGISTERFEE.value) ? "phí đăng ký" : "phí hoa hồng";
        }
        if (Objects.nonNull(coupon.getDiscountValue())) {
            if (Objects.equals(coupon.getDiscountType(), DiscountTypeEnum.PRICE.value)) {
                discountValue = convertFormatAmount(coupon.getDiscountValue()) + CouponConst.PRICE_SUFFIX;
            } else {
                discountValue = coupon.getDiscountValue().intValue() + CouponConst.PERCENT_SUFFIX;
                discountValueMax = discountValue + " tối đa " + convertFormatAmount(coupon.getDiscountAmount()) + CouponConst.PRICE_SUFFIX;
            }
        }
        //Lấy tên ng phát hành coupon
        String devOrAdmin = " nhà quản trị ";
        String onSmePlatform = " trên sàn ";
        String couponOwnerName = Objects.nonNull(couponOwner.getName()) ? couponOwner.getName() : "";
        if (Objects.equals(coupon.getPortal(), PortalType.DEV.getType())) {
            devOrAdmin = " nhà cung cấp dịch vụ ";
            onSmePlatform = "của doanh nghiệp " + couponOwner.getName();
        }
        //Số lượt được sử dụng chương trình khuyến mại
        String timeUseCoupon = " Vĩnh viễn";
        if (Objects.nonNull(coupon.getTimesUsedType()) && Objects.nonNull(coupon.getType())) {
            if (Objects.equals(TimeUsedTypeEnum.ONCE.value, coupon.getTimesUsedType())) {
                timeUseCoupon = " 1 lần";
            }
            if (Objects.equals(TimeUsedTypeEnum.LIMITED.value, coupon.getTimesUsedType())
                && Objects.nonNull(coupon.getLimitedQuantity())) {
                timeUseCoupon =
                    coupon.getLimitedQuantity() + " " + reportService.transTimeEnumToString(TimeTypeEnum.valueOf(coupon.getType()));
            }
        }
        //Đối tượng áp dụng (khi gói chọn là tất cả)
        String objPlanApply = "";
        if (Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value)) {
            if (Objects.equals(coupon.getPortal(), PortalType.ADMIN.getType())) {
                objPlanApply = "bất cứ sản phẩm nào ";
            }
            if (Objects.equals(coupon.getPortal(), PortalType.DEV.getType())) {
                objPlanApply = "bất cứ sản phẩm nào của doanh nghiệp " + couponOwnerName;
            }
        } else if (Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.OPTION.value)) {
            objPlanApply = "một trong các sản phẩm: ";
        }
        //Đối tượng áp dụng (khi dvbs chọn là tất cả)
        String objAddonApply = "";
        if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value)) {
            if (Objects.equals(coupon.getPortal(), PortalType.ADMIN.getType())) {
                objAddonApply = "bất kỳ dịch vụ bổ sung nào đang bán. ";
            }
            if (Objects.equals(coupon.getPortal(), PortalType.DEV.getType())) {
                objAddonApply = "bất kỳ dịch vụ bổ sung của doanh nghiệp " + couponOwnerName;
            }
        } else if (Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value)) {
            objAddonApply = "một trong các dịch vụ bổ sung: ";
        }
        //gói và addon phải mua mới được hưởng coupon (khi chọn là option)

        // lấy ra danh sách coupon pricing apply
        List<CouponPricingServiceDetailDTO> couponPricingServiceApplyDetailDB = couponPricingPlanRepository
            .getCouponPricingServiceDetail(coupon.getId());
        List<CouponPricingDetailDTO> couponPricingApply = getPricingsMultiPlanOfCouponDetailDTO(coupon.getId(),
            couponPricingServiceApplyDetailDB, false);

        // lấy ra danh sách coupon combo plan apply
        List<CouponPricingServiceDetailDTO> couponComboApplyDetailDB = couponComboPlanApplyRepository
            .getCouponComboPlanApplyDetail(coupon.getId());
        List<CouponPricingDetailDTO> couponComboPlanApplies = getComboOldPlanOfCouponDetailDTO(couponComboApplyDetailDB);
        couponPricingApply.addAll(couponComboPlanApplies);

        String pricingOrComboPlanApplyCoupon = couponPricingApply.stream()
            .map(item -> String.join(StringUtils.EMPTY, " - ", item.getServiceName(), " - ", item.getPricingName(), "</br>"))
            .collect(Collectors.joining());

        String addonApplyCoupon;
            // lấy ra danh sách addon
        List<CouponAddonServiceDetailDTO> couponAddonServiceApplyDetailDB = couponPricingPlanRepository
            .getCouponAddonServiceDetail(coupon.getId());
        Set<CouponAddonsDetailDTO> couponAddonsDetailDTOS = getAddonsMultiPlanOfCouponDetailDTO(coupon.getId(),
            couponAddonServiceApplyDetailDB);

        addonApplyCoupon = couponAddonsDetailDTOS.stream()
            .map(item -> String.join(StringUtils.EMPTY, " - ", item.getServiceName(), " - ", item.getName(), "</br>"))
            .collect(Collectors.joining());
            //Ngày kết thúc của coupon
        String endDate = "Không thời hạn";
        if (Objects.nonNull(coupon.getEndDate())) {
            LocalDate couponEndDate = DateUtil.toLocalDate(coupon.getEndDate());
            DateFormat df = new SimpleDateFormat(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
            endDate = df.format(Date.from(couponEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }

        //Gói được miễn phí khi áp dụng coupon (loại: theo sản phẩm)
        Integer quantityCoupon = 0;
        if (Objects.equals(coupon.getPromotionType(), PromotionTypeEnum.PRODUCT.value)) {
            quantityCoupon = subscriptionRepository.getSizeListProductByCoupon(coupon.getId());
        }

        // hotline nền tảng
        String hotlineParam = systemParamService.getHotlineParam();

        CouponMailParamDTO couponMail = new CouponMailParamDTO();
        couponMail.setDiscountValue(discountValue);
        couponMail.setDevOrAdmin(devOrAdmin);
        couponMail.setTimesUseCoupon(timeUseCoupon);
        couponMail.setNameDeveloper(couponOwnerName);
        couponMail.setCodeCoupon(couponCode);
        couponMail.setHotline(hotlineParam);
        couponMail.setDiscountValueMax(
            Objects.equals(coupon.getDiscountType(), DiscountTypeEnum.PERCENT.value) ? discountValueMax : discountValue);
        couponMail.setOnSmePlatform(onSmePlatform);
        couponMail.setDateExpiredCoupon(endDate);
        couponMail.setMiniumApplyPricing(Objects.isNull(coupon.getMinimum()) ? String.valueOf(1) : coupon.getMinimum().toString());
        couponMail.setMiniumApplyAmount(miniumAmount);
        couponMail.setQuantityCoupon(quantityCoupon.toString());
        couponMail
            .setListServiceCp(
                Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value) ? "" : pricingOrComboPlanApplyCoupon);
        couponMail.setListServiceAddonCp(Objects.equals(coupon.getAddonsType(), AddonTypeEnum.ALL.value) ? "" : addonApplyCoupon);
        couponMail.setServiceTxtCp(objPlanApply);
        couponMail.setServiceAddonTxtCp(objAddonApply);
        couponMail.setTimesCoupon(Objects.isNull(coupon.getMaximumPromotion())
            ? "Không giới hạn " : convertFormatAmount(BigDecimal.valueOf(coupon.getMaximumPromotion())));
        couponMail.setDiscountContent(discountContent);
        return couponMail;
    }

    @Override
    public Page<CouponPopupDTO> getCouponsPopup(Long variantId, Long multiPlanId, Long companyId, String customerType, Long mainId, Integer page, Integer size,
        String sort, String classify, List<Long> couponIds, PortalType portalType, Long price, Long quantity) {
        Pageable pageable1 = this.getPageableForCouponPopup(0, Integer.MAX_VALUE, sort);
        Pageable pageable2 = this.getPageableForCouponPopup(page, size, sort);
        Page<CouponPopupItfDTO> listCoupon = null;
        Integer checkCouponIds = 0;
        if (!couponIds.contains(0L)) {
            checkCouponIds = 1;
        }
        List<Long> ids = new ArrayList<>();
        couponIds.forEach(c -> {
            if (Objects.nonNull(c)) {
                ids.add(c);
            }
        });
        Long createdBy;
        Long parentId;
        if (Objects.nonNull(multiPlanId) && multiPlanId != PricingConst.DEFAULT_ID) {
            mainId = 0L;
        }
        if (CouponConst.COUPON_OF_PRICING.equals(classify)) {
            createdBy = pricingRepository.findByIdAndDeletedFlag(mainId, DeletedFlag.NOT_YET_DELETED.getValue())
                    .map(BaseEntity::getCreatedBy).orElse(-1L);
            // lay partnerId ng tạo pricing;
            parentId = userRepository.getParentId(createdBy);
            // lay draft id của pricing và variant, original id của pricing multi plan
            IBundle iBundle = couponRepository.getBundle(mainId, variantId, multiPlanId);
            listCoupon = subscriptionRepository.getCouponsForPricing(iBundle.getVariantDraftId(),
                iBundle.getMultiPlanOriginalId(), createdBy, Objects.nonNull(parentId) ? parentId : -1L, companyId, customerType,
                iBundle.getPricingDraftId(), pageable1, checkCouponIds, ids);
        } else if (CouponConst.COUPON_OF_ADDON.equals(classify)) {
            if (Objects.isNull(mainId)) {
                mainId = -1L;
            }
            createdBy = addonRepository.findByIdAndDeletedFlag(mainId, DeletedFlag.NOT_YET_DELETED.getValue())
                    .map(BaseEntity::getCreatedBy).orElse(-1L);
            // lay parnetId ng tạo addon;
            parentId = userRepository.getParentId(createdBy);
            //Lấy thông tin addon draft id
            Long addonDraftId = addonRepository.getAddonDraftIdByAddonId(mainId);
            Long multiPlanOriginalId = pricingMultiPlanRepository.getDraftIdByMultiPlanId(multiPlanId);
            listCoupon = subscriptionRepository.getCouponsForAddon(multiPlanOriginalId, createdBy, Objects.nonNull(parentId) ? parentId : -1L, companyId, customerType, addonDraftId, pageable1, checkCouponIds, ids);
        } else if (CouponConst.COUPON_OF_COMBO_PLAN.equals(classify)) {
            createdBy = comboPlanRepository.findByIdAndDeletedFlag(mainId, DeletedFlag.NOT_YET_DELETED.getValue())
                    .map(BaseEntity::getCreatedBy).orElse(-1L);
            // lay parnetId ng tạo comboPlan;
            parentId = userRepository.getParentId(createdBy);
            // lay draft id cua combo plan
            Long comboPlanDraftId = comboPlanRepository.getDraftIdByComboPlanId(mainId);
            listCoupon = subscriptionRepository.getCouponsForComboPlan(companyId, createdBy, Objects.nonNull(parentId) ? parentId : -1L, comboPlanDraftId, customerType, pageable1, checkCouponIds, ids);
        }

        return this.checkConditionsOfUsingCoupon(listCoupon, companyId, pageable2, portalType, price, quantity, classify, null);
    }

    @Override
    public List<CouponPopupDTO> getAllCouponsPopup(Long variantId,
                                                   Long multiPlanId,
                                                   Long companyId,
                                                   String customerType,
                                                   Long mainId,
                                                   String classify,
                                                   List<Long> couponIds,
                                                   PortalType portalType) {
        Pageable pageable = Pageable.unpaged();
        Page<CouponPopupItfDTO> listCoupon = null;
        Long createdBy;
        Long parentId;
        Integer checkCouponIds = 0;
        if (!couponIds.contains(0L)) {
            checkCouponIds = 1;
        }
        List<Long> ids = new ArrayList<>();
        couponIds.forEach(c -> {
            if (Objects.nonNull(c)) {
                ids.add(c);
            }
        });
        if (Objects.nonNull(multiPlanId) && multiPlanId != PricingConst.DEFAULT_ID) {
            mainId = 0L;
        }
        if (CouponConst.COUPON_OF_PRICING.equals(classify)) {
            createdBy = pricingRepository.findByIdAndDeletedFlag(mainId, DeletedFlag.NOT_YET_DELETED.getValue())
                    .map(BaseEntity::getCreatedBy).orElse(-1L);
            // lay parnetId ng tạo pricing;
            parentId = userRepository.getParentId(createdBy);
            IBundle iBundle = couponRepository.getBundle(mainId, variantId, multiPlanId);
            listCoupon = subscriptionRepository.getCouponsForPricing(iBundle.getVariantDraftId(),
                iBundle.getMultiPlanOriginalId(), createdBy, Objects.nonNull(parentId) ? parentId : -1L, companyId, customerType,
                iBundle.getPricingDraftId(), pageable, checkCouponIds, ids);
        } else if (CouponConst.COUPON_OF_ADDON.equals(classify)) {
            if (Objects.isNull(mainId)) {
                mainId = -1L;
            }
            createdBy = addonRepository.findByIdAndDeletedFlag(mainId, DeletedFlag.NOT_YET_DELETED.getValue())
                    .map(BaseEntity::getCreatedBy).orElse(-1L);
            // lay parnetId ng tạo addon;
            parentId = userRepository.getParentId(createdBy);
            //Lấy thông tin addon draft id
            Long addonDraftId = addonRepository.getAddonDraftIdByAddonId(mainId);
            Long multiPlanOriginalId = pricingMultiPlanRepository.getDraftIdByMultiPlanId(multiPlanId);
            listCoupon = subscriptionRepository.getCouponsForAddon(multiPlanOriginalId, createdBy, Objects.nonNull(parentId) ? parentId : -1L, Objects.nonNull(companyId) ? companyId : -1L, customerType, addonDraftId, pageable, checkCouponIds, ids);
        } else if (CouponConst.COUPON_OF_COMBO_PLAN.equals(classify)) {
            createdBy = comboPlanRepository.findByIdAndDeletedFlag(mainId, DeletedFlag.NOT_YET_DELETED.getValue())
                    .map(BaseEntity::getCreatedBy).orElse(-1L);
            // lay parnetId ng tạo comboPlan;
            parentId = userRepository.getParentId(createdBy);
            // lay draft id cua combo plan
            Long comboPlanDraftId = comboPlanRepository.getDraftIdByComboPlanId(mainId);
            listCoupon = subscriptionRepository.getCouponsForComboPlan(Objects.nonNull(companyId) ? companyId : -1L, createdBy, Objects.nonNull(parentId) ? parentId : -1L, comboPlanDraftId, customerType, pageable, checkCouponIds, ids);
        }
        return this.checkConditionsOfUsingCoupon(listCoupon.toList(), companyId, portalType, classify);
    }

    @Override
    public Page<CouponPopupDTO> getCouponsPopupForTotalBill(Long companyId, Integer page,
        Integer size, String sort, String classify, List<Long> couponIds, PortalType portalType, Long price, Long quantity, CustomerTypeEnum customerType) {
        Pageable pageable1 = this.getPageableForCouponPopup(0, Integer.MAX_VALUE, sort);
        Pageable pageable2 = this.getPageableForCouponPopup(page, size, sort);
        Integer checkCouponIds = 0;
        if (!couponIds.contains(0L)) {
            checkCouponIds = 1;
        }
        List<Long> ids = new ArrayList<>();
        couponIds.forEach(c -> {
            if (Objects.nonNull(c)) {
                ids.add(c);
            }
        });
        Page<CouponPopupItfDTO> listCoupon = subscriptionRepository.getCouponsForTotalBillWhenSubsPricing(companyId, pageable1, checkCouponIds, ids);
        return this.checkConditionsOfUsingCoupon(listCoupon, companyId, pageable2, portalType, price, quantity, classify, customerType);
    }

    @Override
    public List<CouponPopupDTO> getAllCouponsPopupForTotalBill(Long companyId, String classify, List<Long> couponIds, PortalType portalType,
        CouponMcDevAdminReqDTO couponMcDevAdminReqDTO) {
        Pageable pageable = Pageable.unpaged();
        int checkCouponIds = 0;
        if (!couponIds.contains(0L)) {
            checkCouponIds = 1;
        }
        List<Long> ids = new ArrayList<>();
        couponIds.forEach(c -> {
            if (Objects.nonNull(c)) {
                ids.add(c);
            }
        });

        Page<CouponPopupItfDTO> listCoupon = subscriptionRepository.getCouponsForTotalBillWhenSubsPricing(companyId, pageable, checkCouponIds, ids);
        return this.checkConditionsOfUsingCoupon(listCoupon.toList(), companyId, portalType, classify);
    }

    /**
     * Kiểm tra 3 điều kiện sử dụng của coupon
     */
    private Page<CouponPopupDTO> checkConditionsOfUsingCoupon (Page<CouponPopupItfDTO> listCoupon, Long companyId,
        Pageable pageable, PortalType portalType, Long price, Long quantity, String classify, CustomerTypeEnum customerType){
        List<CouponPopupDTO> listCouponDTO = new ArrayList<>();
        if(Objects.isNull(customerType)){
            Optional<User> userOpt = userRepository.findById(companyId);
            if (userOpt.isPresent()) {
                customerType = Objects.nonNull(userOpt.get().getCustomerType()) ? CustomerTypeEnum.getValueOf(userOpt.get().getCustomerType())
                    : CustomerTypeEnum.ENTERPRISE;
            }
        }
        for (CouponPopupItfDTO coupon : listCoupon) {
            CouponPopupDTO couponDTO = new CouponPopupDTO();
            //Check loại khuyến mãi là giảm giá hay sản phẩm
            if (coupon.getPromotionType().equals(PromotionTypeEnum.DISCOUNT.value)) {
                String discountValue = convertFormatAmount(coupon.getDiscountValue());
                //Nếu là giảm giá thì loại giảm giá là giảm theo tiền hay %
                if (Objects.equals(coupon.getDiscountType(), DiscountTypeEnum.PRICE.value)) {
                    couponDTO.setPromotionValue(discountValue + CouponConst.PRICE_SUFFIX);
                } else {
                    couponDTO.setPromotionValue(discountValue + CouponConst.PERCENT_SUFFIX);
                }
                couponDTO.setPromotionValueOrder(Objects.nonNull(coupon.getDiscountValue()) ? coupon.getDiscountValue() : BigDecimal.ZERO);
            } else {
                List<CouponPopupDTO.ProductByCoupon> productsByCoupon = subscriptionRepository
                    .getListProductByCoupon(coupon.getId());
                couponDTO.setListProduct(productsByCoupon);
                couponDTO.setPromotionValue(productsByCoupon.size() + CouponConst.PRODUCT_SUFFIX);
                couponDTO.setPromotionValueOrder(BigDecimal.valueOf(productsByCoupon.size()));
            }
            // Lấy thông tin customerType của coupon

            Set<String> customerTypes = new HashSet<>();
            if(Objects.nonNull(coupon.getCustomerType())){
                customerTypes = comboService.convertStringToSet(coupon.getCustomerType());
            }
            //Check quyền của user login
            boolean notHavePermission = false;
            Long creatorProvinceId = userRepository.getProvinceIdOfDepartmentByUser(coupon.getCreatedBy());
            if (Objects.equals(portalType, PortalType.ADMIN) && Objects.equals(coupon.getPortal(), PortalType.ADMIN.getType())){
                //Nếu là admin tổng thì ko lấy đc admin tỉnh tạo
                if (Objects.isNull(AuthUtil.getDepartment().getProvinceId()) && Objects.nonNull(creatorProvinceId)){
                    notHavePermission = true;
                }
                //Nếu là ad tỉnh thì ko xem đc KM do admin tỉnh khác tạo
                if (Objects.nonNull(AuthUtil.getDepartment().getProvinceId()) && Objects.nonNull(creatorProvinceId)
                    && !Objects.equals(AuthUtil.getDepartment().getProvinceId(), creatorProvinceId)) {
                    notHavePermission = true;
                }
            }
            //Nếu là dev thì xem đc  KM của admin và KM của chính nó
            if (Objects.equals(portalType, PortalType.DEV) && Objects.equals(coupon.getPortal(), PortalType.DEV.getType())
                && !Objects.equals(coupon.getUserId(), AuthUtil.getCurrentParentId())) {
                notHavePermission = true;
            }
            //Nếu 4 cái đều ko chọn thì false
            else if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                && Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.NO.value)
                && Objects.equals(coupon.getAddonType(), AddonTypeEnum.NONE.value)) {
                notHavePermission = true;
            }
            //Nếu là KM của pricing mà ko chọn doanh nghiệp hoạc ko chọn gói thì true
            else if ((Objects.equals(classify, CouponConst.COUPON_OF_PRICING) || Objects.equals(classify, CouponConst.COUPON_OF_COMBO_PLAN))
                && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                || (!Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value)
                && !Objects.equals(coupon.getAddonType(), AddonTypeEnum.ALL.value)
                && !Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value)
                && Objects.isNull(couponRepository.checkCouponEnterprise(companyId, coupon.getId())))
                && Objects.nonNull(customerType)
                && !customerTypes.contains(customerType)) {
                notHavePermission = true;
            }
            //Nếu là KM của addon  mà ko chọn DVBS thì true
            else if (Objects.equals(classify, CouponConst.COUPON_OF_ADDON)
                && Objects.equals(coupon.getAddonType(), AddonTypeEnum.NONE.value)
                || (!Objects.equals(coupon.getAddonType(), AddonTypeEnum.ALL.value)
                && !Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.ALL.value)
                && !Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value)
                && Objects.isNull(couponRepository.checkCouponEnterprise(companyId, coupon.getId()))
                && Objects.nonNull(customerType)
                && !customerTypes.contains(customerType))) {
                notHavePermission = true;
            }
            //Số lần coupon này đã đc áp dụng
            Long subscriptionUsedCoupon = subscriptionRepository.countNumberOfTimeHasUsedCoupon(coupon.getId());
            //Số lần khách hàng này sử dụng coupon này
            Long companyUsedCoupon = subscriptionRepository.countNumberOfTimeTheCompanyUsedCoupon(companyId, coupon.getId());
            Date now = new Date();
            //Vì date tính từ 00h của ngày nên ngày kết thúc phải tính sang ngày hôm sau
            Date endDate = Objects.isNull(coupon.getEndDate()) ? SqlVar.MAX_DATE : Date
                .from(Instant.ofEpochMilli(coupon.getEndDate().getTime()).atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1)
                    .atStartOfDay(ZoneId.systemDefault()).toInstant());
            //Check xem số lượng sử dụng CTKM này đã được áp dụng hết số lần áp dụng chưa
            if ((Objects.nonNull(coupon.getMaxUsed()) && subscriptionUsedCoupon >= coupon.getMaxUsed())
                //Check ngày subscription có trong khoảng thời gian mà CTKM này có hiệu lực không
                || (Objects.nonNull(coupon.getStartDate()) && now.before(coupon.getStartDate()))
                || (Objects.nonNull(coupon.getEndDate()) && now.after(endDate))
                //Check xem khách hàng này đã sử dụng coupon này bao nhiêu lần
                || (Objects.nonNull(coupon.getMaximumPromotion()) && companyUsedCoupon >= coupon.getMaximumPromotion())
                || notHavePermission) {
                continue;
            }
            String systemParamCoupon = systemParamService.getParamValueByParamType(SystemParamConstant.PARAM_COUPON);
            if (Integer.valueOf(systemParamCoupon).equals(SystemParamEnum.UNLIMITED.value)){
                couponDTO.setSystemParamCoupon(SystemParamEnum.UNLIMITED);
            }
            if (Integer.valueOf(systemParamCoupon).equals(SystemParamEnum.ONCE.value)){
                couponDTO.setSystemParamCoupon(SystemParamEnum.ONCE);
            }
            couponDTO.setId(coupon.getId());
            couponDTO.setCouponName(coupon.getCouponName());
            couponDTO.setCode(coupon.getCode());
            couponDTO.setMinimumAmount(coupon.getMinimumAmount());
            couponDTO.setMinimum(coupon.getMinimum());
            couponDTO.setDiscountType(Objects.nonNull(coupon.getDiscountType()) ? DiscountTypeEnum.valueOf(coupon.getDiscountType()) : null);
            couponDTO.setPromotionType(Objects.nonNull(coupon.getPromotionType()) ? PromotionTypeEnum .valueOf(coupon.getPromotionType()) : null);
            couponDTO.setMaxUsed(coupon.getMaxUsed());
            couponDTO.setMaximumPromotion(coupon.getMaximumPromotion());
            couponDTO.setDiscountAmount(coupon.getDiscountAmount());
            couponDTO.setLimitedQuantity(coupon.getLimitedQuantity());
            couponDTO.setTimesUsedType(Objects.nonNull(coupon.getTimesUsedType()) ? TimeUsedTypeEnum.valueOf(coupon.getTimesUsedType()) : null);
            couponDTO.setTimeType(Objects.nonNull(coupon.getType()) ? TimeTypeEnum.valueOf(coupon.getType()) : null);
            couponDTO.setConditions(getCouponConditions(coupon, classify));
            couponDTO.setStartDate(coupon.getStartDate());
            couponDTO.setEndDate(coupon.getEndDate());
            couponDTO.setExistCouponSet(couponSetRepository.existsByCouponId(coupon.getId()));
            couponDTO.setCustomerType(coupon.getCustomerType());

            listCouponDTO.add(couponDTO);
        }
        //HiepNT tính lại total để tạo Page
        int total = (int) ((pageable.getOffset() + pageable.getPageSize()) > listCouponDTO.size() ?
            (pageable.getOffset() + pageable.getPageSize()) : listCouponDTO.size());

        // sort promotionValue
        Optional<Comparator<CouponPopupDTO>> comparatorOpt = getComparatorForCouponPopup(pageable);
        if (comparatorOpt.isPresent()) {
            Collections.sort(listCouponDTO, comparatorOpt.get());
        }

        return new PageImpl<>(listCouponDTO, pageable, total);
    }

    /**
     * Kiểm tra 3 điều kiện sử dụng của coupon
     */
    private List<CouponPopupDTO> checkConditionsOfUsingCouponPricingAddon(List<CouponPopupItfDTO> listCoupon,
        Long companyId, PortalType portalType, Long addonId) {
        List<CouponPopupDTO> listCouponDTO = new ArrayList<>();
        Set<Long> couponIds = new HashSet<>();

        String systemParamCoupon = systemParamService.getParamValueByParamType(SystemParamConstant.PARAM_COUPON);
        for (CouponPopupItfDTO coupon : listCoupon) {
            if (couponIds.contains(coupon.getId())) {
                continue;
            }
            couponIds.add(coupon.getId());
            CouponPopupDTO couponDTO = new CouponPopupDTO();

            boolean isCouponAddon = Objects.equals(coupon.getClassify(), CouponConst.COUPON_OF_ADDON);
            //Check loại khuyến mãi là giảm giá hay sản phẩm
            if (coupon.getPromotionType().equals(PromotionTypeEnum.DISCOUNT.value)) {
                String discountValue = convertFormatAmount(coupon.getDiscountValue());
                //Nếu là giảm giá thì loại giảm giá là giảm theo tiền hay %
                if (Objects.equals(coupon.getDiscountType(), DiscountTypeEnum.PRICE.value)) {
                    couponDTO.setPromotionValue(discountValue + CouponConst.PRICE_SUFFIX);
                } else {
                    couponDTO.setPromotionValue(discountValue + CouponConst.PERCENT_SUFFIX);
                }
                couponDTO.setPromotionValueOrder(Objects.nonNull(coupon.getDiscountValue()) ? coupon.getDiscountValue() : BigDecimal.ZERO);
            } else {
                List<CouponPopupDTO.ProductByCoupon> productsByCoupon = subscriptionRepository
                    .getListProductByCoupon(coupon.getId());
                couponDTO.setListProduct(productsByCoupon);
                couponDTO.setPromotionValue(productsByCoupon.size() + CouponConst.PRODUCT_SUFFIX);
                couponDTO.setPromotionValueOrder(BigDecimal.valueOf(productsByCoupon.size()));
            }

            //Check quyền của user login
            boolean notHavePermission = false;
            Long creatorProvinceId = userRepository.getProvinceIdOfDepartmentByUser(coupon.getCreatedBy());
            if (Objects.equals(portalType, PortalType.ADMIN) && Objects.equals(coupon.getPortal(), PortalType.ADMIN.getType())) {
                //Nếu là admin tổng thì ko lấy đc admin tỉnh tạo
                if (Objects.isNull(AuthUtil.getDepartment().getProvinceId()) && Objects.nonNull(creatorProvinceId)) {
                    notHavePermission = true;
                }
                //Nếu là ad tỉnh thì ko xem đc KM do admin tỉnh khác tạo
                if (Objects.nonNull(AuthUtil.getDepartment().getProvinceId()) && Objects.nonNull(creatorProvinceId)
                    && !Objects.equals(AuthUtil.getDepartment().getProvinceId(), creatorProvinceId)) {
                    notHavePermission = true;
                }
            }
            //Nếu là dev thì xem đc  KM của admin và KM của chính nó
            if (Objects.equals(portalType, PortalType.DEV) && Objects.equals(coupon.getPortal(), PortalType.DEV.getType())
                && !Objects.equals(coupon.getUserId(), AuthUtil.getCurrentParentId())) {
                notHavePermission = true;
            }
            //Nếu 4 cái đều ko chọn thì false
            else if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                && Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.NO.value)
                && Objects.equals(coupon.getAddonType(), AddonTypeEnum.NONE.value)) {
                notHavePermission = true;
            }
            //Nếu là KM của pricing mà ko chọn doanh nghiệp hoạc ko chọn gói thì true
            else if ((Objects.equals(coupon.getClassify(), CouponConst.COUPON_OF_PRICING) ||
                Objects.equals(coupon.getClassify(), CouponConst.COUPON_OF_COMBO_PLAN))
                && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)) {
                notHavePermission = true;
            }
            //Nếu là KM của addon  mà ko chọn DVBS thì true
            else if (isCouponAddon && Objects.equals(coupon.getAddonType(), AddonTypeEnum.NONE.value)) {
                notHavePermission = true;
            }
            //Số lần coupon này đã đc áp dụng
            Long subscriptionUsedCoupon = subscriptionRepository.countNumberOfTimeHasUsedCoupon(coupon.getId());
            //Số lần khách hàng này sử dụng coupon này -- Số lần áp dụng ứng với số subscriptions áp dụng CTKM
            Long companyUsedCoupon = subscriptionRepository.countNumberOfSubscriptionTheCompanyUsedCoupon(companyId, coupon.getId());
            Date now = new Date();
            //Vì date tính từ 00h của ngày nên ngày kết thúc phải tính sang ngày hôm sau
            Date endDate = Objects.isNull(coupon.getEndDate()) ? SqlVar.MAX_DATE : Date
                .from(Instant.ofEpochMilli(coupon.getEndDate().getTime()).atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1)
                    .atStartOfDay(ZoneId.systemDefault()).toInstant());
            //Check xem số lượng sử dụng CTKM này đã được áp dụng hết số lần áp dụng chưa
            if ((Objects.nonNull(coupon.getMaxUsed()) && subscriptionUsedCoupon >= coupon.getMaxUsed())
                //Check ngày subscription có trong khoảng thời gian mà CTKM này có hiệu lực không
                || (Objects.nonNull(coupon.getStartDate()) && now.before(coupon.getStartDate()))
                || (Objects.nonNull(coupon.getEndDate()) && now.after(endDate))
                //Check xem khách hàng này đã sử dụng coupon này bao nhiêu lần
                || (Objects.nonNull(coupon.getMaximumPromotion()) && companyUsedCoupon >= coupon.getMaximumPromotion())
                || notHavePermission) {
                continue;
            }
            // Check xem coupon này có được hiển thị lên popup không
            if (Objects.equals(coupon.getVisibleStatus(), CouponConst.HIDDEN_VALUE)) {
                continue;
            }
            if (Integer.valueOf(systemParamCoupon).equals(SystemParamEnum.UNLIMITED.value)) {
                couponDTO.setSystemParamCoupon(SystemParamEnum.UNLIMITED);
            }
            if (Integer.valueOf(systemParamCoupon).equals(SystemParamEnum.ONCE.value)) {
                couponDTO.setSystemParamCoupon(SystemParamEnum.ONCE);
            }
            couponDTO.setId(coupon.getId());
            couponDTO.setCouponName(coupon.getCouponName());
            couponDTO.setCode(coupon.getCode());
            couponDTO.setMinimumAmount(coupon.getMinimumAmount());
            couponDTO.setMinimum(coupon.getMinimum());
            couponDTO.setDiscountType(Objects.nonNull(coupon.getDiscountType()) ? DiscountTypeEnum.valueOf(coupon.getDiscountType()) : null);
            couponDTO.setPromotionType(Objects.nonNull(coupon.getPromotionType()) ? PromotionTypeEnum.valueOf(coupon.getPromotionType()) : null);
            couponDTO.setMaxUsed(coupon.getMaxUsed());
            couponDTO.setRemainQuantity(Objects.nonNull(coupon.getMaxUsed()) ? coupon.getMaxUsed() - subscriptionUsedCoupon : null);
            couponDTO.setSubscriptionUsedPromotion(companyUsedCoupon);
            couponDTO.setMaximumPromotion(coupon.getMaximumPromotion());
            couponDTO.setDiscountAmount(coupon.getDiscountAmount());
            couponDTO.setLimitedQuantity(coupon.getLimitedQuantity());
            couponDTO.setTimesUsedType(Objects.nonNull(coupon.getTimesUsedType()) ? TimeUsedTypeEnum.valueOf(coupon.getTimesUsedType()) : null);
            couponDTO.setTimeType(Objects.nonNull(coupon.getType()) ? TimeTypeEnum.valueOf(coupon.getType()) : null);
            couponDTO.setConditions(getCouponConditions(coupon, coupon.getClassify()));
            couponDTO.setStartDate(coupon.getStartDate());
            couponDTO.setEndDate(coupon.getEndDate());
            couponDTO.setExistCouponSet(coupon.getIsCouponSet());
            couponDTO.setVisibleStatus(coupon.getVisibleStatus());
            couponDTO.setObjectType(coupon.getObjectType());
            couponDTO.setAddonId(coupon.getAddonId());
            if (Objects.isNull(coupon.getAddonId()) && isCouponAddon) { // nếu KM áp dụng cho tất cả addon -> ưu tiên addon có số tiền lớn nhất
                couponDTO.setAddonId(addonId);
            }
            couponDTO.setCustomerType(coupon.getCustomerType());

            listCouponDTO.add(couponDTO);
        }
        return listCouponDTO;
    }

    /**
     * Kiểm tra 3 điều kiện sử dụng của coupon
     */
    public List<CouponPopupDTO> checkConditionsOfUsingCoupon(List<CouponPopupItfDTO> listCoupon,
                                                             Long companyId,
                                                             PortalType portalType,
                                                             String classify){
        List<CouponPopupDTO> listCouponDTO = new ArrayList<>();
        for (CouponPopupItfDTO coupon : listCoupon) {
            CouponPopupDTO couponDTO = new CouponPopupDTO();
            //Check loại khuyến mãi là giảm giá hay sản phẩm
            if (coupon.getPromotionType().equals(PromotionTypeEnum.DISCOUNT.value)) {
                String discountValue = convertFormatAmount(coupon.getDiscountValue());
                //Nếu là giảm giá thì loại giảm giá là giảm theo tiền hay %
                if (Objects.equals(coupon.getDiscountType(), DiscountTypeEnum.PRICE.value)) {
                    couponDTO.setPromotionValue(discountValue + CouponConst.PRICE_SUFFIX);
                } else {
                    couponDTO.setPromotionValue(discountValue + CouponConst.PERCENT_SUFFIX);
                }
                couponDTO.setPromotionValueOrder(Objects.nonNull(coupon.getDiscountValue()) ? coupon.getDiscountValue() : BigDecimal.ZERO);
            } else {
                List<CouponPopupDTO.ProductByCoupon> productsByCoupon = subscriptionRepository
                        .getListProductByCoupon(coupon.getId());
                couponDTO.setListProduct(productsByCoupon);
                couponDTO.setPromotionValue(productsByCoupon.size() + CouponConst.PRODUCT_SUFFIX);
                couponDTO.setPromotionValueOrder(BigDecimal.valueOf(productsByCoupon.size()));
            }

            //Check quyền của user login
            boolean notHavePermission = false;
            Long creatorProvinceId = userRepository.getProvinceIdOfDepartmentByUser(coupon.getCreatedBy());
            if (Objects.equals(portalType, PortalType.ADMIN) && Objects.equals(coupon.getPortal(), PortalType.ADMIN.getType())){
                Long userProvinceId = Objects.nonNull(AuthUtil.getDepartment()) ? AuthUtil.getDepartment().getProvinceId() : null;
                //Nếu là admin tổng thì ko lấy đc admin tỉnh tạo
                if (Objects.isNull(userProvinceId) && Objects.nonNull(creatorProvinceId)) {
                    notHavePermission = true;
                }
                //Nếu là ad tỉnh thì ko xem đc KM do admin tỉnh khác tạo
                if (Objects.nonNull(userProvinceId) && Objects.nonNull(creatorProvinceId)
                        && !Objects.equals(AuthUtil.getDepartment().getProvinceId(), creatorProvinceId)) {
                    notHavePermission = true;
                }
            }
            //Nếu là dev thì xem đc  KM của admin và KM của chính nó
            if (Objects.equals(portalType, PortalType.DEV) && Objects.equals(coupon.getPortal(), PortalType.DEV.getType())
                    && !Objects.equals(coupon.getUserId(), AuthUtil.getCurrentParentId())) {
                notHavePermission = true;
            }
            //Nếu 4 cái đều ko chọn thì false
            else if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
                    && Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.NO.value)
                    && Objects.equals(coupon.getAddonType(), AddonTypeEnum.NONE.value)) {
                notHavePermission = true;
            }
            //Nếu là KM của pricing mà ko chọn doanh nghiệp hoạc ko chọn gói thì true
            else if ((Objects.equals(classify, CouponConst.COUPON_OF_PRICING) || Objects.equals(classify, CouponConst.COUPON_OF_COMBO_PLAN))
                    && Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
                && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)) {
                notHavePermission = true;
            }
            //Nếu là KM của addon  mà ko chọn DVBS thì true
            else if (Objects.equals(classify, CouponConst.COUPON_OF_ADDON)
                    && Objects.equals(coupon.getAddonType(), AddonTypeEnum.NONE.value)) {
                notHavePermission = true;
            }
            //Số lần coupon này đã đc áp dụng
            Long subscriptionUsedCoupon = subscriptionRepository.countNumberOfTimeHasUsedCoupon(coupon.getId());
            //Số lần khách hàng này sử dụng coupon này -- Số lần áp dụng ứng với số subscriptions áp dụng CTKM
            Long companyUsedCoupon = subscriptionRepository.countNumberOfSubscriptionTheCompanyUsedCoupon(Objects.nonNull(companyId) ? companyId : -1L, coupon.getId());
            Date now = new Date();
            //Vì date tính từ 00h của ngày nên ngày kết thúc phải tính sang ngày hôm sau
            Date endDate = Objects.isNull(coupon.getEndDate()) ? SqlVar.MAX_DATE : Date
                    .from(Instant.ofEpochMilli(coupon.getEndDate().getTime()).atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1)
                            .atStartOfDay(ZoneId.systemDefault()).toInstant());
            //Check xem số lượng sử dụng CTKM này đã được áp dụng hết số lần áp dụng chưa
            if ((Objects.nonNull(coupon.getMaxUsed()) && subscriptionUsedCoupon >= coupon.getMaxUsed())
                    //Check ngày subscription có trong khoảng thời gian mà CTKM này có hiệu lực không
                    || (Objects.nonNull(coupon.getStartDate()) && now.before(coupon.getStartDate()))
                    || (Objects.nonNull(coupon.getEndDate()) && now.after(endDate))
                    //Check xem khách hàng này đã sử dụng coupon này bao nhiêu lần
                    || (Objects.nonNull(coupon.getMaximumPromotion()) && companyUsedCoupon >= coupon.getMaximumPromotion())
                    || notHavePermission) {
                continue;
            }
            // Check xem coupon này có được hiển thị lên popup không
            if (Objects.equals(coupon.getVisibleStatus(), CouponConst.HIDDEN_VALUE)) {
                continue;
            }
            String systemParamCoupon = systemParamService.getParamValueByParamType(SystemParamConstant.PARAM_COUPON);
            if (Integer.valueOf(systemParamCoupon).equals(SystemParamEnum.UNLIMITED.value)){
                couponDTO.setSystemParamCoupon(SystemParamEnum.UNLIMITED);
            }
            if (Integer.valueOf(systemParamCoupon).equals(SystemParamEnum.ONCE.value)){
                couponDTO.setSystemParamCoupon(SystemParamEnum.ONCE);
            }
            couponDTO.setId(coupon.getId());
            couponDTO.setCouponName(coupon.getCouponName());
            couponDTO.setCode(coupon.getCode());
            couponDTO.setMinimumAmount(coupon.getMinimumAmount());
            couponDTO.setMinimum(coupon.getMinimum());
            couponDTO.setDiscountType(Objects.nonNull(coupon.getDiscountType()) ? DiscountTypeEnum.valueOf(coupon.getDiscountType()) : null);
            couponDTO.setPromotionType(Objects.nonNull(coupon.getPromotionType()) ? PromotionTypeEnum .valueOf(coupon.getPromotionType()) : null);
            couponDTO.setMaxUsed(coupon.getMaxUsed());
            couponDTO.setRemainQuantity(Objects.nonNull(coupon.getMaxUsed()) ? coupon.getMaxUsed() - subscriptionUsedCoupon : null);
            couponDTO.setSubscriptionUsedPromotion(companyUsedCoupon);
            couponDTO.setMaximumPromotion(coupon.getMaximumPromotion());
            couponDTO.setDiscountAmount(coupon.getDiscountAmount());
            couponDTO.setLimitedQuantity(coupon.getLimitedQuantity());
            couponDTO.setTimesUsedType(Objects.nonNull(coupon.getTimesUsedType()) ? TimeUsedTypeEnum.valueOf(coupon.getTimesUsedType()) : null);
            couponDTO.setTimeType(Objects.nonNull(coupon.getType()) ? TimeTypeEnum.valueOf(coupon.getType()) : null);
            couponDTO.setConditions(getCouponConditions(coupon, classify));
            couponDTO.setStartDate(coupon.getStartDate());
            couponDTO.setEndDate(coupon.getEndDate());
            couponDTO.setExistCouponSet(couponSetRepository.existsByCouponId(coupon.getId()));
            couponDTO.setVisibleStatus(coupon.getVisibleStatus());
            couponDTO.setDiscountValue(coupon.getDiscountValue());
            couponDTO.setCustomerType(coupon.getCustomerType());

            listCouponDTO.add(couponDTO);
        }
        return listCouponDTO;
    }

    /**
     * Lấy ra pageable khi sort theo 3 trường tiền > % > gói
     */
    private Pageable getPageableForCouponPopup (Integer page, Integer size, String sort) {
        List<String> sortList = new ArrayList<>(Arrays.asList(sort.split(",")));
        List<Sort.Order> orders = new ArrayList<>();
        Sort.Order orderDefault = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
            sortList.get(0));
        orders.add(orderDefault);
        Sort.Order orderByDiscountType = new Sort.Order(
            Sort.Direction.valueOf(sortList.get(1).toUpperCase()).equals(ASC) ? DESC : ASC, CouponConst.DISCOUNT_TYPE);
        orders.add(orderByDiscountType);
        Sort.Order orderByPromotionValue = new Sort.Order(Sort.Direction.valueOf(sortList.get(1).toUpperCase()),
            CouponConst.DISCOUNT_VALUE);
        orders.add(orderByPromotionValue);

        Sort sortObj = Sort.by(orders);
        size = size < 1 ? 1 : size;
        page = page < 0 ? 0 : page;
        Pageable pageable = PageRequest.of(page, size, sortObj);

        pageable.getSort().get().map(item -> {
            Comparator comparator = Comparator.comparing(CouponPopupDTO::getPromotionType);
            if (DESC.equals(item.getDirection())) {
                comparator = comparator.reversed();
            }
            return comparator;
        }).collect(Collectors.toList());
        return pageable;
    }

    /**
     * custom sort promotionValue
     *
     * @param pageable pageable
     * @return Optional Comparator CouponPopupDTO by promotionValue
     */
    private Optional<Comparator<CouponPopupDTO>> getComparatorForCouponPopup(Pageable pageable) {
        Optional<Order> orderOpt = pageable.getSort().get().findFirst();
        if (!orderOpt.isPresent() || !"promotionType".equalsIgnoreCase(orderOpt.get().getProperty())) {
            return Optional.empty();
        }

        Comparator<CouponPopupDTO> comparatorPromotionType = Comparator.comparing(CouponPopupDTO::getPromotionType);
        if (DESC.name().equalsIgnoreCase(orderOpt.get().getDirection().name())) {
            comparatorPromotionType = Comparator.comparing(CouponPopupDTO::getPromotionType, Comparator.reverseOrder());
        }

        Comparator<CouponPopupDTO> comparatorDiscountType = Comparator
            .comparing(CouponPopupDTO::getDiscountType, Comparator.nullsFirst(Comparator.naturalOrder()));
        if (DESC.name().equalsIgnoreCase(orderOpt.get().getDirection().name())) {
            comparatorDiscountType = Comparator.comparing(CouponPopupDTO::getDiscountType, Comparator.nullsFirst(Comparator.reverseOrder()));
        }

        Comparator<CouponPopupDTO> comparatorPromotionValue = Comparator.comparing(CouponPopupDTO::getPromotionValueOrder);

        Comparator<CouponPopupDTO> comparator = comparatorPromotionType.thenComparing(comparatorDiscountType)
            .thenComparing(comparatorPromotionValue);

        return Optional.of(comparator);
    }

    /**
     * Đổi định dạng tiền
     */
    @Override
    public String convertFormatAmount(BigDecimal amount){
        amount = Objects.isNull(amount) ? BigDecimal.ZERO : amount;
        String discountValue = NumberFormat.getCurrencyInstance(new Locale("vi", "VN"))
            .format(amount);
        if (discountValue.endsWith(" đ")) {
            int centsIndex = discountValue.lastIndexOf(" đ");
            if (centsIndex != -1) {
                discountValue = discountValue.substring(0, centsIndex);
            }
        }
        return discountValue;
    }

    @Override
    public void saveHistoryCouponOutOfUsed(Long subsId, List<Long> couponIds, String objName) {
        List<Coupon> coupons = couponRepository.getListCouponByIds(couponIds);
        List<SubscriptionHistory> listHist = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long userId = AuthUtil.getCurrentUserId();
        coupons.forEach(x -> {
            boolean isOutOfUsed =
                Objects.nonNull(x.getMaxUsed()) && subscriptionRepository.countNumberOfTimeHasUsedCoupon(x.getId()) >= x.getMaxUsed();
            if (isOutOfUsed){
                String couponParam = getCouponParam(x);
                String hisContent = String.format(SubscriptionHistoryConstant.COUPON_OUT_OF_USED, couponParam, objName);
                listHist.add(SubscriptionHistory.builder()
                    .subscriptionId(subsId)
                    .createdAt(now)
                    .content(hisContent)
                    .contentType(SubscriptionHistoryConstant.ContentType.COUPON_OUT_OF_USED)
                    .createdBy(userId)
                    .build());
            }
        });
        subscriptionHistoryRepository.saveAll(listHist);
    }

    /**
     * Lấy coupon param để lưu lịch sử
     */
    private String getCouponParam(Coupon coupon){
        String couponParam = "";
        //Check loại khuyến mãi là giảm giá hay sản phẩm
        if (coupon.getPromotionType().equals(PromotionTypeEnum.DISCOUNT.value)) {
            String discountValue = convertFormatAmount(coupon.getDiscountValue());
            //Nếu là giảm giá thì loại giảm giá là giảm theo tiền hay %
            if (coupon.getDiscountType().equals(DiscountTypeEnum.PRICE.value)) {
                couponParam = discountValue + CouponConst.PRICE_SUFFIX;
            } else {
                couponParam = discountValue + CouponConst.PERCENT_SUFFIX;
            }
        } else {
            couponParam = coupon.getName();
        }
        return couponParam;
    }

    @Override
    public boolean validCouponApplyFor(Coupon coupon, String subjectApplyCoupon) {
        boolean checkValid = (!Objects.equals(subjectApplyCoupon, CouponConst.COUPON_OF_PRICING)
            && !Objects.equals(subjectApplyCoupon, CouponConst.COUPON_OF_COMBO_PLAN))
            || !Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            || !Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value);
        //Nếu lấy coupon cho gói chính mà pricing và enterprise đều = none thì false
        //Nếu lấy coupon cho addon mà addon type = none là false
        if (Objects.equals(subjectApplyCoupon, CouponConst.COUPON_OF_ADDON)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)) {
            checkValid = false;
        }
        //Nếu 4 cái đều ko chọn thì false
        if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.NO.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)){
            checkValid = false;
        }
        return checkValid;
    }

    /**
     * Tìm kiếm service cho popup chọn dịch vụ của gói dịch vụ hoặc dịch vụ bỏ sung
     */
    @Override
    public List<CouponServiceResDTO> searchServiceMultiPlan(String name, String type) {
        String roleType = RoleType.ADMIN.toString();
        Long userId = DEFAULT_PARENT_ID;
        //user dang nhap co quyen dev-admin hoac dev-operator
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_OPERATOR.getValue()))) {
            roleType = RoleType.DEVELOPER.toString();
            userId = AuthUtil.getCurrentParentId();
        }
        Long provinceId = AddonsConstant.ADDON_COUPON_ALL;
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.FULL_ADMIN.getValue(), RoleType.ADMIN.getValue(), RoleType.CUSTOMER_SUPPORT
            .getValue()))) {
            Long actorDepartmentId = AuthUtil.getCurrentUser().getDepartmentId();
            if (Objects.nonNull(actorDepartmentId)) {
                Department actorDepartment = departmentsRepository
                    .findByIdAndDeletedFlag(actorDepartmentId, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                        String messageError = messageSource
                            .getMessage(MessageKeyConstant.NOT_FOUND, new Object[]{actorDepartmentId}, LocaleContextHolder.getLocale());
                        return new ResourceNotFoundException(messageError, Resources.USER,
                            ErrorKey.DEPARTMENT_ID, MessageKeyConstant.NOT_FOUND);
                    });
                provinceId = Objects.nonNull(actorDepartment.getProvinceId()) ? actorDepartment.getProvinceId() : provinceId;
            }
        }
        return SEARCH_PRICING_TYPE.equals(type) ? serviceRepository.getAllServicesMultiPlanPricingByNameLike(name, userId, roleType)
            : serviceRepository.getAllServicesMultiPlanAddonByNameLike(name, provinceId, userId);
    }

    /**
     * Tìm kiếm gói dịch vụ hoặc dịch vụ bổ
     */
    @Override
    public List<PricingCouponResDTO> searchPricingMultiPlan(String name, String type) {
        String roleType = RoleType.ADMIN.toString();
        Long userId = DEFAULT_PARENT_ID;
        //user dang nhap co quyen dev-admin hoac dev-operator
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_OPERATOR.getValue()))) {
            roleType = RoleType.DEVELOPER.toString();
            userId = AuthUtil.getCurrentParentId();
        }
        Long provinceId = AddonsConstant.ADDON_COUPON_ALL;
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.FULL_ADMIN.getValue(), RoleType.ADMIN.getValue(), RoleType.CUSTOMER_SUPPORT
            .getValue()))) {
            Long actorDepartmentId = AuthUtil.getCurrentUser().getDepartmentId();
            if (Objects.nonNull(actorDepartmentId)) {
                Department actorDepartment = departmentsRepository
                    .findByIdAndDeletedFlag(actorDepartmentId, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                        String messageError = messageSource
                            .getMessage(MessageKeyConstant.NOT_FOUND, new Object[]{actorDepartmentId}, LocaleContextHolder.getLocale());
                        return new ResourceNotFoundException(messageError, Resources.USER,
                            ErrorKey.DEPARTMENT_ID, MessageKeyConstant.NOT_FOUND);
                    });
                provinceId = Objects.nonNull(actorDepartment.getProvinceId()) ? actorDepartment.getProvinceId() : provinceId;
            }
        }
        return SEARCH_PRICING_TYPE.equals(type) ? pricingRepository.getAllLatestPricingAndComboPlanByNameLike(name, userId, roleType)
            : addonRepository.getAllAddonNameByNameLike(name, userId, provinceId);
    }

    /**
     * Tìm kiếm danh danh mục
     */
    @Override
    public List<CouponCategoryResDTO> searchCategory(String name) {
        return categoryRepository.getAllCategoryByNameLike(name);
    }

    /**
     * Tìm kiếm chu kì thanh toán của dịch vụ hoặc của dịch vụ bổ sung
     */
    @Override
    public List<CouponPricingPlanResDTO> searchPricingPlan(String name, String type) {
        String roleType = RoleType.ADMIN.toString();
        Long userId = DEFAULT_PARENT_ID;
        //user dang nhap co quyen dev-admin hoac dev-operator
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_OPERATOR.getValue()))) {
            roleType = RoleType.DEVELOPER.toString();
            userId = AuthUtil.getCurrentParentId();
        }
        Long provinceId = AddonsConstant.ADDON_COUPON_ALL;
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.FULL_ADMIN.getValue(), RoleType.ADMIN.getValue(), RoleType.CUSTOMER_SUPPORT
            .getValue()))) {
            Long actorDepartmentId = AuthUtil.getCurrentUser().getDepartmentId();
            if (Objects.nonNull(actorDepartmentId)) {
                Department actorDepartment = departmentsRepository
                    .findByIdAndDeletedFlag(actorDepartmentId, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                        String messageError = messageSource
                            .getMessage(MessageKeyConstant.NOT_FOUND, new Object[]{actorDepartmentId}, LocaleContextHolder.getLocale());
                        return new ResourceNotFoundException(messageError, Resources.USER,
                            ErrorKey.DEPARTMENT_ID, MessageKeyConstant.NOT_FOUND);
                    });
                provinceId = Objects.nonNull(actorDepartment.getProvinceId()) ? actorDepartment.getProvinceId() : provinceId;
            }
        }
        return SEARCH_PRICING_TYPE.equals(type) ? pricingRepository.getALlPricingPlanOfPricingAndComboPlanByNameLike(name, userId, roleType)
            : addonRepository.getAllPricingPlanOfAddonByNameLike(name, provinceId, userId);
    }

    /**
     * list addon cho popup addon
     */
    @Override
    public CouponPopupTreeResDTO getListAddons(String serviceName, String addonName, Integer bonusValue,
        CycleTypeEnum type, Long categoryId, Long addonId, BonusTypeEnum bonusType, Set<CustomerTypeEnum> customerTypes, Pageable pageable) {
        //neu la admin hoac super admin thi lay toan bo addon dang hoat dong tren he thong
        Long provinceId = AddonsConstant.ADDON_COUPON_ALL;
        Long userId = AuthUtil.getCurrentParentId();
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.FULL_ADMIN.getValue(), RoleType.ADMIN.getValue(), RoleType.CUSTOMER_SUPPORT
            .getValue()))) {
            userId = DEFAULT_PARENT_ID;
            Long actorDepartmentId = AuthUtil.getCurrentUser().getDepartmentId();
            if (Objects.nonNull(actorDepartmentId)) {
                Department actorDepartment = departmentsRepository
                    .findByIdAndDeletedFlag(actorDepartmentId, DeletedFlag.NOT_YET_DELETED.getValue()).orElseThrow(() -> {
                        String messageError = messageSource
                            .getMessage(MessageKeyConstant.NOT_FOUND, new Object[]{actorDepartmentId}, LocaleContextHolder.getLocale());
                        return new ResourceNotFoundException(messageError, Resources.USER,
                            ErrorKey.DEPARTMENT_ID, MessageKeyConstant.NOT_FOUND);
                    });
                provinceId = Objects.nonNull(actorDepartment.getProvinceId()) ? actorDepartment.getProvinceId() : provinceId;
            }
        }

        String customerTypeStr = CollectionUtils.isEmpty(customerTypes) ? CharacterConstant.BLANK :
            String.join(CharacterConstant.VERTICAL_BAR, customerTypes.stream().map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));

        List<CouponServiceDTO> allElements = serviceRepository.getAllServiceAddonMultiPlan(serviceName, addonName, provinceId,
            categoryId, userId, bonusValue, type.value, bonusType.value, customerTypeStr);

        List<CouponServiceDTO> pageService = allElements.stream().filter(pricingService.distinctByKey(CouponServiceResDTO::getId)).collect(
                Collectors.toList());

        // lấy ra các addon id có multi plan
        Set<Long> addonMultiPlanIds = allElements.stream()
            .filter(addon -> addon.getBonusType().equals(BonusTypeEnum.PERIODIC.value) && addon.getIsMultiPlan().equals(YesNoEnum.YES.value))
            .map(CouponServiceDTO::getObjectId).collect(
                Collectors.toSet());

        // lấy ra các multi plan của addon theo id
        List<PricingMultiPlan> allAddonMultiPlans = CollectionUtils.isEmpty(addonMultiPlanIds) ? new ArrayList<>()
            : pricingMultiPlanRepository.getAddonMultiPlanByPricingIdInAndPaymentCycleAndCycleType(addonMultiPlanIds, bonusValue, type.value);

        Map<Long, List<PricingMultiPlan>> multiPlansOfAddon = mappingMultiPlanForAddon(addonMultiPlanIds, allAddonMultiPlans);

        List<PopupTreeResDTO> pageData = pageService.stream().map(e -> convertToCouponListAddonResDTO(e, allElements, multiPlansOfAddon))
            .collect(
                Collectors.toList());

        //lấy index trong list dựa theo page size và vị trí page hiện tại
        final int start = Math.min((int) pageable.getOffset(), pageData.size());
        final int end = Math.min((start + pageable.getPageSize()), pageData.size());
        Page<PopupTreeResDTO> response = new PageImpl<>(pageData.subList(start, end), pageable, pageData.size());
        // đếm số lượng phần tử kế hoạch định giá của page
        return new CouponPopupTreeResDTO(response, (long) allElements.size());
    }

    /**
     * chuyển đổi response trả ra api getListCoupon
     */
    private CouponListAddonsResDTO convertToCouponListAddonResDTO(CouponServiceDTO serviceDTO, List<CouponServiceDTO> services,
        Map<Long, List<PricingMultiPlan>> multiPlansOfAddon) {

        // set thông tin response trả ra
        CouponListAddonsResDTO response = new CouponListAddonsResDTO();

        // set thông tin service response
        response.setServiceId(serviceDTO.getId());
        response.setTitle(serviceDTO.getServiceName());
        String serviceKey = serviceDTO.getServiceName() + CharacterConstant.UNDERLINED + serviceDTO.getId();
        response.setKey(serviceKey);

        // lấy các addon của service
        Set<CouponListAddonsResDTO.AddonResDTO> addonResDTOList = new HashSet<>();

        services.stream().filter(e -> e.getId().equals(serviceDTO.getId())).forEach(serviceAddon -> {
            // set thong tin addon
            CouponListAddonsResDTO.AddonResDTO addonResDTO = new CouponListAddonsResDTO.AddonResDTO();
            addonResDTO.setTitle(serviceAddon.getObjectName());
            addonResDTO.setAddonId(serviceAddon.getObjectId());
            addonResDTO.setType(BonusTypeEnum.valueOf(serviceAddon.getBonusType()));
            String addonKey =
                serviceKey + CharacterConstant.SLASH + serviceAddon.getObjectCode() + CharacterConstant.UNDERLINED + serviceAddon.getObjectId();
            addonResDTO.setKey(addonKey);
            // Nếu là addon là loại định kỳ
            if (serviceAddon.getBonusType().equals(BonusTypeEnum.PERIODIC.value)) {
                // nếu addon có multi plan (data mới) -> lấy multi plan ở bảng pricing_multi_plan
                if (serviceAddon.getIsMultiPlan().equals(YesNoEnum.YES.value)) {
                    addonResDTO.setChildren(getPricingMultiPlanOfAddonResDTO(multiPlansOfAddon.get(serviceAddon.getObjectId()), addonKey));
                } else {
                    // nếu là data cũ -> lấy thông tin ở chính bảng addon
                    addonResDTO.setChildren(Collections.singletonList(getOldPricingPlanOfAddonResDTO(serviceAddon, addonKey)));
                }
            } else {
                // nếu không là loại 1 lần
                // Trả về 1 phần tử có tile là: Một lần
                String key = addonKey + CharacterConstant.SLASH + serviceAddon.getBonusType();
                addonResDTO.setChildren(Collections
                    .singletonList(new AddonStrategyResDTO(serviceAddon.getId(), key, TITLE_ADDON_ONCE, YesNoEnum.NO)));
            }
            addonResDTOList.add(addonResDTO);
        });
        response.setChildren(addonResDTOList);
        return response;
    }

    /**
     * mapping multi plan cho addon theo id
     */
    private Map<Long, List<PricingMultiPlan>> mappingMultiPlanForAddon(Set<Long> addonMultiPlanIds, List<PricingMultiPlan> allAddonMultiPlans) {
        Map<Long, List<PricingMultiPlan>> multiPlansOfAddon = new HashMap<>();
        addonMultiPlanIds.forEach(addonId -> {
            multiPlansOfAddon.put(addonId, allAddonMultiPlans.stream().filter(e -> e.getAddonId().equals(addonId)).collect(Collectors.toList()));
        });
        return multiPlansOfAddon;
    }

    /**
     * Lấy ra ké hoạnh định giá cũ của addon trong bảng addon cho AddonResDTO
     */
    private AddonStrategyResDTO getOldPricingPlanOfAddonResDTO(CouponServiceDTO addon, String addonKey) {
        String key = addonKey + CharacterConstant.SLASH + addon.getPaymentCycle() + CharacterConstant.UNDERLINED + addon
            .getCycleType();
        String title = TITLE_CYCLE_TYPE_UNLIMITED;
        if (Objects.nonNull(addon.getPaymentCycle()) && Objects
            .nonNull(addon.getCycleType())) {
            String currentCycleType = getTitleCycleTypeForCouponDetailDTO(addon.getCycleType());
            title = addon.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType;
        }
        return new AddonStrategyResDTO(addon.getId(), key, title, YesNoEnum.NO);
    }

    /**
     * lấy ra các chu kỳ thanh toán của addoon trong bảng pricing_multi_plan cho AddonResDTO
     */
    private List<AddonStrategyResDTO> getPricingMultiPlanOfAddonResDTO(List<PricingMultiPlan> pricingMultiPlans, String addonKey) {
        return pricingMultiPlans.stream().map(
                pricingMultiPlan -> {
                    String key =
                        addonKey + CharacterConstant.SLASH + pricingMultiPlan.getAddonId() + CharacterConstant.UNDERLINED
                            + pricingMultiPlan.getId();
                    String title = TITLE_CYCLE_TYPE_UNLIMITED;
                    if (Objects.nonNull(pricingMultiPlan.getPaymentCycle()) && Objects
                        .nonNull(pricingMultiPlan.getCircleType())) {
                        String currentCycleType = getTitleCycleTypeForCouponDetailDTO(pricingMultiPlan.getCircleType());
                        title = pricingMultiPlan.getPaymentCycle() + CharacterConstant.SPACE + currentCycleType;
                    }
                    return new CouponListAddonsResDTO.AddonStrategyResDTO(pricingMultiPlan.getId(), key, title, YesNoEnum.YES);
                }
            ).collect(Collectors.toList());
    }

    public Page<CouponPopupDTO> getOnlyOneCouponPopup(Long multiPlanId,Long companyId, Long mainId, Integer page, Integer size,
                                                String sort, String classify, List<Long> couponIds, PortalType portalType, Long price, Long quantity,Long couponId) {
        Pageable pageable1 = this.getPageableForCouponPopup(0, Integer.MAX_VALUE, sort);
        Pageable pageable2 = this.getPageableForCouponPopup(page, size, sort);
        Page<CouponPopupItfDTO> listCoupon = null;
        Integer checkCouponIds = 0;
        if (!couponIds.contains(0L)) {
            checkCouponIds = 1;
        }
        if (CouponConst.COUPON_OF_PRICING.equals(classify)) {
            listCoupon = subscriptionRepository.getOnlyOneCouponForPricing(multiPlanId,companyId, mainId, pageable1, checkCouponIds, couponIds,couponId);
        }
        if (CouponConst.COUPON_OF_ADDON.equals(classify)) {
            listCoupon = subscriptionRepository.getOnlyOneCouponForAddon(multiPlanId,companyId, mainId, pageable1, checkCouponIds, couponIds,couponId);
        }
        if (CouponConst.COUPON_OF_COMBO_PLAN.equals(classify)) {
            if (mainId == null) {
                throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, Resources.COMBO_PLAN, ErrorKey.ID, "comboPlanId");
            }
            listCoupon = subscriptionRepository.getOnlyOneCouponForComboPlan(companyId, mainId, pageable1, checkCouponIds, couponIds,couponId);
        }
        return this.checkConditionsOfUsingCoupon(listCoupon, companyId, pageable2, portalType, price, quantity, classify, null);
    }

    public Page<CouponPopupDTO> getOnlyOneCouponsPopupForTotalBill(Long companyId, Long pricingId, List<Long> addonIds, Integer page,
                                                                   Integer size, String sort, String classify, List<Long> couponIds, PortalType portalType, Long price, Long quantity, Long couponId) {
        Pageable pageable1 = this.getPageableForCouponPopup(0, Integer.MAX_VALUE, sort);
        Pageable pageable2 = this.getPageableForCouponPopup(page, size, sort);
        Page<CouponPopupItfDTO> listCoupon = null;
        List<Long> ids = new ArrayList<>();
        Integer checkCouponIds = 0;
        if (!couponIds.contains(0L)) {
            checkCouponIds = 1;
        }
        couponIds.forEach(c -> {
            if (Objects.nonNull(c)) {
                ids.add(c);
            }
        });
        if (CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING.equals(classify)) {
            listCoupon = subscriptionRepository.getOnlyOneCouponForTotalBillWhenSubsPricing(companyId, pageable1,
                    checkCouponIds, ids,couponId);
        }
        if (CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN.equals(classify)) {
            listCoupon = subscriptionRepository.getOnlyOneCouponForTotalBillWhenSubsComboPlan(companyId, pageable1,
                    checkCouponIds, couponIds,couponId);
        }
        return this.checkConditionsOfUsingCoupon(listCoupon, companyId, pageable2, portalType, price, quantity, classify, null);
    }

    /**
     * Get conditions of coupon
     */
    public List<String> getCouponConditions(CouponPopupItfDTO c, String classify) {
        List<String> conditions = new ArrayList<>();
        try {
            //Nội dung chương trình khuyến mại
            StringBuilder discountCondition = new StringBuilder();
            //Loại KM theo chiết khấu
            if (Objects.equals(c.getPromotionType(), PromotionTypeEnum.DISCOUNT.value)) {
                String priceSuffix = "";
                if (Objects.equals(classify, CouponConst.COUPON_OF_PRICING) || Objects.equals(classify, CouponConst.COUPON_OF_COMBO_PLAN)) {
                    priceSuffix = " trên giá gói dịch vụ chính";
                } else if (Objects.equals(classify, CouponConst.COUPON_OF_ADDON)) {
                    priceSuffix = " trên giá dịch vụ bổ sung";
                } else if (Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING) || Objects
                    .equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN)) {
                    priceSuffix = " trên tổng hóa đơn";
                }
                // điều kiện về số tiền nhỏ nhất, tối đa
                discountCondition.append("Giảm ").append(couponConditionService.getStringCurrency(c.getDiscountValue()));
                switch (c.getDiscountType()) {
                    case 0:
                        discountCondition.append("%");
                        break;
                    case 1:
                        discountCondition.append("VNĐ");
                        break;
                }
                discountCondition.append(priceSuffix);
            }
            //Loại KM theo sản phẩm
            if (Objects.equals(c.getPromotionType(), PromotionTypeEnum.PRODUCT.value)) {
                discountCondition.append("Tặng dịch vụ ");
                List<ObjectCouponNameDTO> listObjPromotion = couponRepository.getServicePlanPromotionNameByCouponId(c.getId());
                if (!CollectionUtils.isEmpty(listObjPromotion)) {
                    Integer count = 1;
                    for (ObjectCouponNameDTO objName: listObjPromotion) {
                        String name =
                            objName.getObjectName() + " - " + objName.getObjectPlanName() + (count < listObjPromotion.size() ? ", " : "");
                        discountCondition.append(name);
                        count++;
                    }
                }
            }
            if (Objects.nonNull(c.getMinimumAmount()) && c.getMinimumAmount() > 0L) {
                discountCondition.append(" cho đơn hàng từ ").append(couponConditionService.getStringCurrency(c.getMinimumAmount())).append("VNĐ");
            }
            if (Objects.nonNull(c.getDiscountAmount()) && c.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                discountCondition.append(" - tối đa ").append(couponConditionService.getStringCurrency(c.getDiscountAmount())).append("VNĐ");
            }
            conditions.add(discountCondition.toString());
        } catch (Exception e) {
            log.info("getCouponConditions error: " + e.getMessage());
        }
        return conditions;
    }


    public List<String> getCouponConditions(Coupon c, String classify) {
        List<String> conditions = new ArrayList<>();
        try {
            //Nội dung chương trình khuyến mại
            StringBuilder discountCondition = new StringBuilder();
            //Loại KM theo chiết khấu
            if (Objects.equals(c.getPromotionType(), PromotionTypeEnum.DISCOUNT.value)) {
                String priceSuffix = "";
                if (Objects.equals(classify, CouponConst.COUPON_OF_PRICING) || Objects.equals(classify, CouponConst.COUPON_OF_COMBO_PLAN)) {
                    priceSuffix = " trên giá gói dịch vụ chính";
                } else if (Objects.equals(classify, CouponConst.COUPON_OF_ADDON)) {
                    priceSuffix = " trên giá dịch vụ bổ sung";
                } else if (Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING) || Objects
                    .equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN)) {
                    priceSuffix = " trên tổng hóa đơn";
                }
                // điều kiện về số tiền nhỏ nhất, tối đa
                discountCondition.append("Giảm ").append(couponConditionService.getStringCurrency(c.getDiscountValue()));
                switch (c.getDiscountType()) {
                    case 0:
                        discountCondition.append("%");
                        break;
                    case 1:
                        discountCondition.append("VNĐ");
                        break;
                }
                discountCondition.append(priceSuffix);
            }
            //Loại KM theo sản phẩm
            if (Objects.equals(c.getPromotionType(), PromotionTypeEnum.PRODUCT.value)) {
                discountCondition.append("Tặng dịch vụ ");
                List<ObjectCouponNameDTO> listObjPromotion = couponRepository.getServicePlanPromotionNameByCouponId(c.getId());
                if (!CollectionUtils.isEmpty(listObjPromotion)) {
                    Integer count = 1;
                    for (ObjectCouponNameDTO objName: listObjPromotion) {
                        String name =
                            objName.getObjectName() + " - " + objName.getObjectPlanName() + (count < listObjPromotion.size() ? ", " : "");
                        discountCondition.append(name);
                        count++;
                    }
                }
            }
            if (Objects.nonNull(c.getMinimumAmount()) && c.getMinimumAmount() > 0L) {
                discountCondition.append(" cho đơn hàng từ ").append(couponConditionService.getStringCurrency(c.getMinimumAmount())).append("VNĐ");
            }
            if (Objects.nonNull(c.getDiscountAmount()) && c.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                discountCondition.append(" - tối đa ").append(couponConditionService.getStringCurrency(c.getDiscountAmount())).append("VNĐ");
            }
            conditions.add(discountCondition.toString());
        } catch (Exception e) {
            log.info("getCouponConditions error: " + e.getMessage());
        }
        return conditions;
    }

    @Override
    public List<String> getCouponConditions(HashMap<Long, List<CouponIdAndNameDTO>> couponIdToCouponName, Coupon c, String classify) {
        List<String> conditions = new ArrayList<>();
        try {
            //Nội dung chương trình khuyến mại
            StringBuilder discountCondition = new StringBuilder();
            //Loại KM theo chiết khấu
            if (Objects.equals(c.getPromotionType(), PromotionTypeEnum.DISCOUNT.value)) {
                String priceSuffix = "";
                if (Objects.equals(classify, CouponConst.COUPON_OF_PRICING) || Objects.equals(classify, CouponConst.COUPON_OF_COMBO_PLAN)) {
                    priceSuffix = " trên giá gói dịch vụ chính";
                } else if (Objects.equals(classify, CouponConst.COUPON_OF_ADDON)) {
                    priceSuffix = " trên giá dịch vụ bổ sung";
                } else if (Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING) || Objects
                    .equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN)) {
                    priceSuffix = " trên tổng hóa đơn";
                }
                // điều kiện về số tiền nhỏ nhất, tối đa
                discountCondition.append("Giảm ").append(couponConditionService.getStringCurrency(c.getDiscountValue()));
                switch (c.getDiscountType()) {
                    case 0:
                        discountCondition.append("%");
                        break;
                    case 1:
                        discountCondition.append("VNĐ");
                        break;
                }
                discountCondition.append(priceSuffix);
            }
            //Loại KM theo sản phẩm
            if (Objects.equals(c.getPromotionType(), PromotionTypeEnum.PRODUCT.value)) {
                discountCondition.append("Tặng dịch vụ ");
                if (!CollectionUtils.isEmpty(couponIdToCouponName) && !CollectionUtils.isEmpty(couponIdToCouponName.get(c.getId()))) {
                    Integer count = 1;
                    for (CouponIdAndNameDTO coupon : couponIdToCouponName.get(c.getId())) {
                        String name =
                            coupon.getObjectName() + " - " + coupon.getObjectPlanName() + (count < couponIdToCouponName.get(c.getId()).size() ? ", " : "");
                        discountCondition.append(name);
                        count++;
                    }

                }
            }
            if (Objects.nonNull(c.getMinimumAmount()) && c.getMinimumAmount() > 0L) {
                discountCondition.append(" cho đơn hàng từ ").append(couponConditionService.getStringCurrency(c.getMinimumAmount())).append("VNĐ");
            }
            if (Objects.nonNull(c.getDiscountAmount()) && c.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0) {
                discountCondition.append(" - tối đa ").append(couponConditionService.getStringCurrency(c.getDiscountAmount())).append("VNĐ");
            }
            conditions.add(discountCondition.toString());
        } catch (Exception e) {
            log.info("getCouponConditions error: " + e.getMessage());
        }
        return conditions;
    }

    public List<String> getCouponConditions(Long couponId, String classify) {
        Coupon coupon = couponRepository.findFirstById(couponId);
        return getCouponConditions(coupon, classify);
    }

    @Override
    @Transactional
    public List<McCouponDTO> getListMcCouponDTO(String couponName, Pageable pageable) {
        List<SimpleCouponDTO> couponList = couponRepository.findAllByNameContainsAndStatus(couponName, 1, pageable);
        List<McCouponDTO> result = new LinkedList<>();
        for (SimpleCouponDTO couponDTO : couponList) {
            McCouponDTO mcCouponDTO = new McCouponDTO();
            mcCouponDTO.setSimpleCouponDTO(couponDTO);
            List<CouponSet> couponSetList = couponSetRepository.findAllByCouponId(couponDTO.getId());
            mcCouponDTO.setCouponSetList(couponSetList);
            result.add(mcCouponDTO);
        }
        return result;
    }

    @Override
    public Page<ICouponListResDTO> getListCouponsFormCouponSet(String searchText, Pageable pageable) {
        return couponRepository.getCouponFromCouponSet(searchText, pageable);
    }

    /**
     * Lấy thông tin pricing bởi coupon_id
     */
    @Override
    public List<SubscriptionPricingAddonDTO.PricingByCouponId> getPricingByCoupon(Long couponId) {
        List<SubscriptionPricingAddonDTO.PricingByCouponId> pricingCoupons = new ArrayList<>();
        couponRepository.getPricingByCoupon(couponId).forEach(p -> {
            SubscriptionPricingAddonDTO.PricingByCouponId pricingCoupon = new SubscriptionPricingAddonDTO.PricingByCouponId();
            pricingCoupon.setPricingId(p.getPricingId());
            pricingCoupon.setPricingMultiPlanId(p.getPricingMultiPlanId());
            pricingCoupon.setType(p.getType());
            pricingCoupon.setServiceName(p.getServiceName());
            pricingCoupon.setPricingName(p.getPricingName());
            pricingCoupon.setType(p.getType());
            pricingCoupons.add(pricingCoupon);
        });
        return pricingCoupons;
    }

    /**
     * Lấy danh sách chương trình khuyến mại theo mã gói
     *
     * @param pricingId the pricing id
     * @return the coupon list by pricing id
     */
    @Override
    public List<PricingSaaSResDTO.Coupon> getCouponListByPricingIdOrPricingMultiPlanId(Long pricingId, Long pricingMultiPlanId) {
        return getCouponListByPricingIdOrPricingMultiPlanId(pricingId, pricingMultiPlanId, null);
    }

    @Override
    public List<PricingSaaSResDTO.Coupon> getCouponListByPricingIdOrPricingMultiPlanId(Long pricingId, Long pricingMultiPlanId,
        CustomerTypeEnum customerTypeEnum) {
        Long currentParentId = AuthUtil.getCurrentParentId();
        Long userId = ObjectUtil.getOrDefault(currentParentId, SubscriptionConstant.GUEST_USER);
        Long pricingOwnerId = pricingRepository.findOwnerPricing(pricingId);
        Long provinceId = userRepository.getProvinceIdByUserId(currentParentId);
        provinceId = ObjectUtil.getOrDefault(provinceId, SubscriptionConstant.GUEST_USER);
        String customerType = customerTypeEnum != null ? customerTypeEnum.getValue() : CharacterConstant.BLANK;
        List<PricingApplyDTO> pricingApplyDTOS = Objects.isNull(pricingMultiPlanId) ?
            couponRepository.findByPricingId(pricingId, userId, pricingOwnerId, provinceId, -1L, customerType) :
            couponRepository.findByPricingMultiPlanId(pricingMultiPlanId, userId, pricingOwnerId, provinceId, customerType);
        return pricingApplyDTOS.stream().map(this::convertCouponPricing).collect(Collectors.toList());
    }

    @Override
    public Optional<PricingSaaSResDTO.Coupon> getLatestCouponByPricingIdOrPricingMultiPlanId(Long pricingId, Long pricingMultiPlanId,
        String customerType, Long userId, Long provinceId, Long pricingOwnerId) {
        customerType = ObjectUtil.getOrDefault(customerType, CharacterConstant.BLANK);
        PricingApplyDTO pricingApplyDTOS = Objects.isNull(pricingMultiPlanId) ?
            couponRepository.findByPricingLatestId(pricingId, userId, pricingOwnerId, provinceId, -1L, customerType) :
            couponRepository.findByPricingMultiPlanLatestId(pricingMultiPlanId, userId, pricingOwnerId, provinceId, customerType);
        return Optional.ofNullable(pricingApplyDTOS).map(this::convertCouponPricing);
    }

    @Override
    public PricingSaaSResDTO.Coupon convertCouponPricing(PricingApplyDTO coupon) {
        if (coupon == null) {
            return null;
        }
        PricingSaaSResDTO.Coupon couponRes = new PricingSaaSResDTO.Coupon();
        couponRes.setId(coupon.getId());
        if (Objects.nonNull(coupon.getDiscountType())) {
            couponRes.setDiscountType(DiscountTypeEnum.valueOf(coupon.getDiscountType()));
        }

        couponRes.setEnterpriseType(Objects.isNull(coupon.getEnterpriseType()) ?
            AddonTypeEnum.NONE : AddonTypeEnum.valueOf(coupon.getEnterpriseType()));

        couponRes.setPricingType(Objects.isNull(coupon.getPricingType()) ?
            AddonTypeEnum.NONE : AddonTypeEnum.valueOf(coupon.getPricingType()));

        couponRes.setAddonsType(Objects.isNull(coupon.getAddonsType()) ?
            AddonTypeEnum.NONE : AddonTypeEnum.valueOf(coupon.getAddonsType()));

        if (Objects.nonNull(coupon.getTotalBillType())) {
            couponRes.setTotalBillType(YesNoEnum.valueOf(coupon.getTotalBillType()));
        }

        couponRes.setPromotionType(Objects.isNull(coupon.getPromotionType())
            ? null : PromotionTypeEnum.valueOf(coupon.getPromotionType()));
        couponRes.setStartDate(coupon.getStartDate());
        couponRes.setEndDate(coupon.getEndDate());
        //neu type la product
        if (Objects.equals(PromotionTypeEnum.PRODUCT.value, coupon.getPromotionType())) {
            couponRes.setPricing(getPricingByCoupon(coupon.getId()));
        }
        couponRes.setName(coupon.getName());
        couponRes.setDiscountValue(coupon.getDiscountValue());
        couponRes.setCreatedAt(coupon.getCreatedAt());
        couponRes.setCouponCustomerTypeCode(coupon.getCouponCustomerTypeCode());
        return couponRes;
    }

    @Override
    public CouponDetailDTO getSMECouponDetail(Coupon coupon) {

        //        Lấy dữ liệu bảng CouponDraft
//        Khi cập nhật coupon hoạt động sẽ lưu thông tin vào CouponDraft
//        Khi click xác nhận : Bản ghi coponDraft đó bị xóa
        Optional<CouponDraft> couponDraft = couponDraftRepository.findByCouponId(coupon.getId());
        CouponDetailDTO couponDetailDTO = couponMapper.toDto(coupon);

        // lấy ra danh sách coupon pricing
        List<CouponPricingServiceDetailDTO> couponPricingServiceDetailDB = couponPricingApplyRepository
            .getCouponPricingServiceApplyDetail(couponDetailDTO.getId());
        List<CouponPricingDetailDTO> couponPricings = getPricingsMultiPlanOfCouponDetailDTO(couponDetailDTO.getId(),
            couponPricingServiceDetailDB, true);

        Set<Long> ids = new HashSet<>();
        ids.add(couponDetailDTO.getId());
        Set<Long> allowDelete = couponRepository.getCouponIdSubscription(ids);

        if (allowDelete.isEmpty()) {
            couponDetailDTO.setAllowDelete(YesNoEnum.YES);
        } else {
            couponDetailDTO.setAllowDelete(YesNoEnum.NO);
        }

        // lấy ra danh sách coupon combo plan
        List<CouponPricingServiceDetailDTO> couponComboDetailDB = couponComboPlanRepository
            .getCouponComboPlanDetail(couponDetailDTO.getId());
        List<CouponPricingDetailDTO> couponComboPlans = getComboOldPlanOfCouponDetailDTO(couponComboDetailDB);

        if (!CollectionUtils.isEmpty(couponComboPlans)) {
            couponPricings.addAll(couponComboPlans);
        }

        // lấy ra danh sách coupon pricing apply
        List<CouponPricingServiceDetailDTO> couponPricingServiceApplyDetailDB = couponPricingPlanRepository
            .getCouponPricingServiceDetail(couponDetailDTO.getId());
        List<CouponPricingDetailDTO> couponPricingApply = new ArrayList<>();
        List<CouponPricingDetailDTO> couponPricingPlanApplies = getPricingsMultiPlanOfCouponDetailDTO(couponDetailDTO.getId(),
            couponPricingServiceApplyDetailDB, false);
        if (CollectionUtils.isEmpty(couponPricingPlanApplies)) {
            couponPricingApply.addAll(couponPricingPlanApplies);
        }

        // lấy ra danh sách coupon combo plan apply
        List<CouponPricingServiceDetailDTO> couponComboApplyDetailDB = couponComboPlanApplyRepository
            .getCouponComboPlanApplyDetail(couponDetailDTO.getId());
        List<CouponPricingDetailDTO> couponComboPlanApplies = getComboOldPlanOfCouponDetailDTO(couponComboApplyDetailDB);

        if (!CollectionUtils.isEmpty(couponComboPlanApplies)) {
            couponPricingApply.addAll(couponComboPlanApplies);
        }

        List<CouponEnterpriseDetailDTO> enterpriseDetailDTOS = couponEnterpriseRepository
            .getCouponEnterprise(couponDetailDTO.getId());

        // lấy ra danh sách addon
        List<CouponAddonServiceDetailDTO> couponAddonServiceApplyDetailDB = couponPricingPlanRepository
            .getCouponAddonServiceDetail(couponDetailDTO.getId());
        Set<CouponAddonsDetailDTO> couponAddonsDetailDTOS = getAddonsMultiPlanOfCouponDetailDTO(couponDetailDTO.getId(),
            couponAddonServiceApplyDetailDB);

        couponDetailDTO.setCouponPricing(couponPricings);
        couponDetailDTO.setCouponPricingApply(couponPricingApply);
        couponDetailDTO.setCouponEnterprise(enterpriseDetailDTOS);
        couponDetailDTO.setCouponAddons(couponAddonsDetailDTOS);
        // Trường hợp Admin xem chi tiết thì thêm couponSupplier (Nhà cung cấp)
        if (couponDraft.isPresent()) {
            couponDetailDTO.setNameDraft(couponDraft.get().getName());
            couponDetailDTO.setMaxUsedDraft(couponDraft.get().getMaxUsed());
            couponDetailDTO.setMaximumPromotionDraft(couponDraft.get().getMaximumPromotion());
            couponDetailDTO.setStatusDraft(StatusEnum.valueOf(couponDraft.get().getStatus()));
            couponDetailDTO.setEndDateDraft(couponDraft.get().getEndDate());
            couponDetailDTO.setVisibleStatusDraft(couponDraft.get().getVisibleStatus());
        }
        getBanner(couponDetailDTO);
        CouponAdsPosition couponAdsPosition = couponAdsPositionRepository.getByCouponId(couponDetailDTO.getId());
        if (couponAdsPosition != null) {
            couponDetailDTO.setAdvertisePosition(couponAdsPosition.getJsonObject());
        }
        // Lấy thông tin condition
        CouponPromotionCondition couponCondition = couponPromotionConditionRepository.findByCouponId(couponDetailDTO.getId()).orElse(null);
        if (Objects.nonNull(couponCondition)) {
            couponDetailDTO.setCondition(couponCondition.getCondition());
        }

        // Set thông tin isServiceLevelApplicable ( để mobile app chọn layout hiển thị chi tiết CTKM tương ưng )
        // Nếu loại sản phẩm áp dụng là tất cả -> mặc định TRUE
        if (Objects.equals(couponDetailDTO.getPricingType(), TypeApplyEnum.ALL)) {
            couponDetailDTO.setIsServiceLevelApplicable(Boolean.TRUE);
        } else {
            boolean chosenAllPricing = Boolean.TRUE;
            // Check xem có chọn hết các gói cước trong dịch vụ hay không
            Set<Long> allChosenServiceIds = couponPricingPlanApplies.stream().map(CouponPricingDetailDTO::getServiceId).collect(toSet());
            Map<Long, List<ICommonPricingServiceDTO>> mapAllServicePricing = pricingRepository.getListAvailablePricingByServiceIdIn(allChosenServiceIds).stream().collect(groupingBy(
                ICommonPricingServiceDTO::getServiceId));
            Map<Long, List<CouponPricingDetailDTO>> mapChosenPricing = couponPricingPlanApplies.stream()
                .collect(groupingBy(CouponPricingDetailDTO::getServiceId));
            for (Long serviceId : allChosenServiceIds) {
                Set<Long> chosenPricingIds = Objects.requireNonNull(mapChosenPricing.get(serviceId)).stream()
                    .map(CouponPricingDetailDTO::getPricingId).collect(toSet());
                Set<Long> allPricingIds = Objects.requireNonNull(mapAllServicePricing.get(serviceId)).stream().map(ICommonPricingServiceDTO::getPricingId).collect(toSet());
                if (!Objects.equals(allPricingIds.size(), chosenPricingIds.size()) || !org.apache.commons.collections4.CollectionUtils.containsAll(allPricingIds, chosenPricingIds)) {
                    chosenAllPricing = Boolean.FALSE;
                    break;
                }
            }

            // Check xem có chọn hết các multi-plan trong gói cước hay không
            boolean chosenAllMultiPlan = Boolean.TRUE;
            Map<Long, List<CouponPricingDetailDTO>> mapCouponChosenPricingPlans = couponPricingPlanApplies.stream()
                .collect(groupingBy(CouponPricingDetailDTO::getPricingId));
            Set<Long> allChosenPricingIds = couponPricingPlanApplies.stream().map(CouponPricingDetailDTO::getPricingId).collect(toSet());
            Map<Long, List<ICommonPricingServiceDTO>> mapAllPricingMultiPlans = pricingMultiPlanRepository.getListAvailablePricingMultiPlanByPricingIdIn(
                    allChosenPricingIds).stream().collect(groupingBy(ICommonPricingServiceDTO::getPricingId));
            for (Long pricingId : allChosenPricingIds) {
                Set<Long> chosenMultiPlanIds = Objects.requireNonNull(mapCouponChosenPricingPlans.get(pricingId)).stream()
                    .map(CouponPricingDetailDTO::getPricingMultiPlanId).collect(toSet());
                Set<Long> allMultiPlanIds = Objects.requireNonNull(mapAllPricingMultiPlans.get(pricingId)).stream().map(ICommonPricingServiceDTO::getPricingMultiPlanId).collect(toSet());
                if (!Objects.equals(allMultiPlanIds.size(), chosenMultiPlanIds.size()) || !org.apache.commons.collections4.CollectionUtils.containsAll(allMultiPlanIds, chosenMultiPlanIds)) {
                    chosenAllMultiPlan = Boolean.FALSE;
                    break;
                }
            }

            // Check xem có chọn hết các gói combo plan trong combo hay không
            boolean chosenAllComboPlan = Boolean.TRUE;
            Set<Long> allChosenComboIds = couponComboPlanApplies.stream().map(CouponPricingDetailDTO::getServiceId).collect(toSet());
            Map<Long, List<ICommonPricingServiceDTO>> mapAllComboPlan = comboPlanRepository.getListAvailableComboPlanByComboIdIn(allChosenComboIds).stream().collect(groupingBy(
                ICommonPricingServiceDTO::getServiceId));
            Map<Long, List<CouponPricingDetailDTO>> mapChosenComboPlan = couponComboPlanApplies.stream()
                .collect(groupingBy(CouponPricingDetailDTO::getServiceId));
            for (Long comboId : allChosenComboIds) {
                Set<Long> chosenComboPlanIds = Objects.requireNonNull(mapChosenComboPlan.get(comboId)).stream()
                    .map(CouponPricingDetailDTO::getPricingId).collect(toSet());
                Set<Long> allComboPlanIds = Objects.requireNonNull(mapAllComboPlan.get(comboId)).stream().map(ICommonPricingServiceDTO::getPricingId).collect(toSet());
                if (!Objects.equals(allComboPlanIds.size(), chosenComboPlanIds.size()) || !org.apache.commons.collections4.CollectionUtils.containsAll(allComboPlanIds, chosenComboPlanIds)) {
                    chosenAllComboPlan = Boolean.FALSE;
                    break;
                }
            }
            couponDetailDTO.setIsServiceLevelApplicable(chosenAllPricing && chosenAllMultiPlan && chosenAllComboPlan);
        }
        return couponDetailDTO;
    }

    @Override
    public List<IGetCouponPackageDTO> getCouponPricingPackages(Set<Long> ids) {
        return couponRepository.getCouponPricingPackages(ids);
    }

    @Override
    public List<IGetCouponPackageDTO> getCouponAddonPackages(Set<Long> ids) {
        return couponRepository.getCouponAddonPackages(ids);
    }

    @Override
    public List<IGetCouponMcDTO> getCouponPricingAddonMcInPackage(Set<String> couponPricingMcIds) {
        return couponRepository.getCouponPricingAddonMcInPackage(couponPricingMcIds);
    }

    @Override
    public List<CouponPopupDTO> getAllCouponsPricingAddonPopup(Long pricingId, Long pricingMultiPlanId, List<Long> addonIds,
        List<Long> lstAddonMultiPlanId, Long companyId, String customerType, List<Long> couponIds, PortalType portalType) {
        Pageable pageable = Pageable.unpaged();
        Page<CouponPopupItfDTO> listCoupon;
        Integer checkCouponIds = 0;
        if (!couponIds.contains(0L)) {
            checkCouponIds = 1;
        }
        List<Long> ids = new ArrayList<>();
        couponIds.forEach(c -> {
            if (Objects.nonNull(c)) {
                ids.add(c);
            }
        });
        if (addonIds.isEmpty()) {
            addonIds.add(-1L);
        }
        if (lstAddonMultiPlanId.isEmpty()) {
            lstAddonMultiPlanId.add(-1L);
        }
        // lấy km của gói
        Long createdBy = pricingRepository.findByIdAndDeletedFlag(pricingId, DeletedFlag.NOT_YET_DELETED.getValue())
            .map(BaseEntity::getCreatedBy).orElse(-1L);
        // lay parnetId ng tạo pricing;
        Long parentId = userRepository.getParentId(createdBy);
        listCoupon = subscriptionRepository.getAllCouponsPricingAddonPopup(pricingId, pricingMultiPlanId, addonIds, lstAddonMultiPlanId,
            createdBy, Objects.nonNull(parentId) ? parentId : -1L,
            companyId, customerType, checkCouponIds, ids, pageable);

        List<PriceAddonDTO> priceAddonDTOList = subscriptionRepository.getPriceAddon(addonIds, lstAddonMultiPlanId);

        PriceAddonDTO addonMaxPrice = priceAddonDTOList.stream().max(Comparator.comparing(PriceAddonDTO::getPrice)).orElse(null);

        Long addonIdSelect = Objects.nonNull(addonMaxPrice) ? addonMaxPrice.getAddonId() : -1L;
        return this.checkConditionsOfUsingCouponPricingAddon(listCoupon.toList(), companyId, portalType, addonIdSelect);
    }

    /**
     * Validate coupons.
     *
     * @param coupon the coupon
     * @return the boolean
     */
    @Override
    public boolean validateCoupons(Coupon coupon, Long userId, String classify) {
        boolean isValid = true;
        Long currentUserId = Objects.nonNull(userId) ? userId : AuthUtil.getCurrentParentId();
        //Neu ap dung cho doanh nghiep can check user login
        if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)) {
            List<CouponEnterpriseDetailDTO> couponEnterprise = couponEnterpriseRepository
                .getCouponEnterprise(coupon.getId());
            long count = couponEnterprise.stream()
                .filter(cou -> Objects.equals(cou.getUserId(), currentUserId))
                .count();
            if (count == 0) {
                isValid = false;
            }
        }

        //Neu dich vu bo sung OPTION -> can phai mua ca dich vu bo sung va goi moi duoc khuyen mai
//        if (isValid && Objects.equals(coupon.getAddonsType(), EnterpriseTypeEnum.OPTION.value)) {
//            List<CouponAddon> couponAddons = couponAddonRepository.findByAddonsId(coupon.getId());
//            List<Long> addonIds = couponAddons.stream().map(x -> x.getAddonsId()).collect(Collectors.toList());
//
//            List<Long> currentAddon = subscriptionFirstStepReqDTO.getAddons().stream()
//                                                                 .filter(x -> Objects.nonNull(x.getQuantity()) && x.getQuantity() > 0)
//                                                                 .map(x -> x.getId()).collect(Collectors.toList());
//            if (!Objects.equals(addonIds, currentAddon)) {
//                isValid = false;
//            }
//        } else if (isValid && Objects.equals(coupon.getAddonsType(), EnterpriseTypeEnum.NONE.value)) {
//            isValid = false;
//        }

        //DK1: Check số lần áp dụng
        if (isValid && Objects.nonNull(coupon.getMaximumPromotion())) {
            Long companyUsedCoupon;
            if (currentUserId != null) {
                companyUsedCoupon = subscriptionRepository.countNumberOfTimeTheCompanyUsedCoupon(currentUserId, coupon.getId());
            } else {
                companyUsedCoupon = null;
            }
            if (companyUsedCoupon != null && companyUsedCoupon >= coupon.getMaximumPromotion()) {
                isValid = false;
            }
        }

        //DK: Nếu đều
        //Nếu 4 cái đều ko chọn thì false
        if (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.NO.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)) {
            isValid = false;
        }
        //Nếu là KM của pricing mà ko chọn doanh nghiệp hoạc ko chọn gói thì true
        else if ((Objects.equals(classify, CouponConst.COUPON_OF_PRICING) || Objects.equals(classify, CouponConst.COUPON_OF_COMBO_PLAN))
            && ((Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value))
            || ((Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.OPTION.value)
            || Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.ALL.value))
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.OPTION.value))
            || (Objects.equals(coupon.getEnterpriseType(), EnterpriseTypeEnum.NONE.value)
            && Objects.equals(coupon.getPricingType(), CouponPricingApplyTypeEnum.NONE.value)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)))) {
            isValid = false;
        } else if ((Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_COMBO_PLAN)
            || Objects.equals(classify, CouponConst.COUPON_OF_TOTAL_BILL_WHEN_SUBSCRIPTION_PRICING)
            || Objects.equals(classify, CouponConst.COUPON_TOTAL))
            && !(Objects.equals(coupon.getTotalBillType(), TotalBillTypeEnum.YES.value))) {
            isValid = false;
        }
        //Nếu là KM của addon  mà ko chọn doanh nghiệp hoạc ko chọn DVBS thì true
        else if (Objects.equals(classify, CouponConst.COUPON_OF_ADDON)
            && Objects.equals(coupon.getAddonsType(), AddonTypeEnum.NONE.value)) {
            isValid = false;
        }
        //DK2: check thời gian
        LocalDate startDate = Objects.isNull(coupon.getStartDate()) ? SubscriptionConstant.LOCAL_DATE_MIN_DATE
            : coupon.getStartDate();
        LocalDate endDate = Objects.isNull(coupon.getEndDate()) ? SubscriptionConstant.LOCAL_DATE_MAX_DATE
            : coupon.getEndDate();
        if (isValid && (LocalDate.now().isBefore(startDate)
            || LocalDate.now().isAfter(endDate))) {
            isValid = false;
        }

        //DK3: check số lượng tối thiểu
//        if (isValid) {
//            List<SubscriptionFirstStepReqDTO.Addon> addonList = subscriptionFirstStepReqDTO.getAddons().stream().
//                    filter(x -> Objects.nonNull(x.getQuantity()) && x.getQuantity() > 0).collect(Collectors.toList());
//            int count = 0;
//            for (SubscriptionFirstStepReqDTO.Addon addon : addonList) {
//                count += addon.getQuantity();
//            }
//            Long minimum = Objects.isNull(coupon.getMinimum()) ? 0 : coupon.getMinimum();
//            if (count > minimum) {
//                isValid = false;
//            }
//        }

        //DK4: Check số lần hưởng khuyến mại tối đa
        if (isValid && Objects.nonNull(coupon.getMaxUsed())) {
            Long subscriptionUsedCoupon = subscriptionRepository.countNumberOfTimeHasUsedCoupon(coupon.getId());
            if (subscriptionUsedCoupon >= coupon.getMaxUsed()) {
                isValid = false;
            }
        }

        //DK5: Check số tiền nhỏ nhất áp dụng
//        if (isValid) {
//            BigDecimal minimumAmount = Objects.isNull(coupon.getMinimumAmount()) ? BigDecimal.ZERO : new BigDecimal(coupon.getMinimumAmount());
//            if (totalAmount.compareTo(minimumAmount) < 0) {
//                isValid = false;
//            }
//        }

        //DK6, DK7: CHi kiem tra khi thuc hien thanh toan voi chu ky
        return isValid;
    }

    private void updateAdsPositionImgInFileAttach(McAdsPositionDTO adsPositionDTO, Long couponId) {
        if (adsPositionDTO == null) {
            return;
        }
        List<McImageDTO> lstWebImageDTO = new ArrayList<>();
        List<McImageDTO> lstMobiImageDTO = new ArrayList<>();
        McHomepageDTO homepageDTO = adsPositionDTO.getHomePage();
        if (homepageDTO != null && homepageDTO.getLstBanner() != null) {
            List<McCommonBannerDTO> lstBanner = homepageDTO.getLstBanner();
            lstBanner.forEach(bannerDTO -> {
                if (bannerDTO.getLstWebImage() != null) {
                    lstWebImageDTO.addAll(bannerDTO.getLstWebImage());
                }
                if (bannerDTO.getLstMobiImage() != null) {
                    lstMobiImageDTO.addAll(bannerDTO.getLstMobiImage());
                }
            });
        }
        McServicePageDTO servicePageDTO = adsPositionDTO.getServicePage();
        if (servicePageDTO != null) {
            if (servicePageDTO.getVoucherImageEnable() == 1) {
                if (servicePageDTO.getWebVoucherImages() != null) {
                    lstWebImageDTO.addAll(servicePageDTO.getWebVoucherImages());
                }
                if (servicePageDTO.getMobiVoucherImages() != null) {
                    lstMobiImageDTO.addAll(servicePageDTO.getMobiVoucherImages());
                }
            }
            if (servicePageDTO.getSmallLeaderBoardBannerEnable() == 1) {
                if (servicePageDTO.getWebSmallLeaderBoardBannerImages() != null) {
                    lstWebImageDTO.addAll(servicePageDTO.getWebSmallLeaderBoardBannerImages());
                }
                if (servicePageDTO.getMobiSmallLeaderBoardBannerImages() != null) {
                    lstMobiImageDTO.addAll(servicePageDTO.getMobiSmallLeaderBoardBannerImages());
                }
            }
        }
        McPopupDTO popupDTO = adsPositionDTO.getPopup();
        if (popupDTO != null && popupDTO.getPopupDisplayEnable() == 1) {
            org.apache.commons.collections4.CollectionUtils.addIgnoreNull(lstWebImageDTO, popupDTO.getWebPopupImage());
            org.apache.commons.collections4.CollectionUtils.addIgnoreNull(lstMobiImageDTO, popupDTO.getMobilePopupImage());
        }

        Set<Long> lstWebAttachFileId = lstWebImageDTO.stream().map(McImageDTO::getId).collect(Collectors.toSet());
        if (lstWebAttachFileId != null && !lstWebAttachFileId.isEmpty()) {
            fileAttachRepository.updateObjectTypeAndObjectIdAndObjectNameAndResolution(lstWebAttachFileId, FileAttachTypeEnum.COUPON_BANNER.value, couponId,
                    FileAttachConst.OBJECT_NAME_COUPON, ResolutionEnum.DESKTOP.value);
        }

        Set<Long> lstMobiAttachFileId = lstMobiImageDTO.stream().map(McImageDTO::getId).collect(Collectors.toSet());
        if (lstMobiAttachFileId != null && !lstMobiAttachFileId.isEmpty()) {
            fileAttachRepository.updateObjectTypeAndObjectIdAndObjectNameAndResolution(lstMobiAttachFileId, FileAttachTypeEnum.COUPON_BANNER.value, couponId,
                    FileAttachConst.OBJECT_NAME_COUPON, ResolutionEnum.MOBILE.value);
        }
    }

    private void setBanner(Long desktopBannerId, Long mobileBannerId, Long couponId){
        if(desktopBannerId !=null){
            fileAttachRepository.deleteAllByObjectIdAndObjectTypeAndResolutionAndIdNotAndObjectNameAndDescription(couponId, FileAttachTypeEnum.COUPON_BANNER.value,
                    ResolutionEnum.DESKTOP.value, desktopBannerId, FileAttachConst.OBJECT_NAME_COUPON, FileAttachConst.DESCRIPTION_BANNER_COUPON);
            Optional<FileAttach> optional = fileAttachRepository.findById(desktopBannerId);
            if(optional.isPresent()){
                FileAttach file = optional.get();
                file.setObjectType(FileAttachTypeEnum.COUPON_BANNER.value);
                file.setResolution(ResolutionEnum.DESKTOP.value);
                file.setObjectId(couponId);
                file.setFileType(FileTypeEnum.IMAGE.value);
                file.setObjectName(FileAttachConst.OBJECT_NAME_COUPON);
                file.setDescription(FileAttachConst.DESCRIPTION_BANNER_COUPON);
                fileAttachRepository.save(file);
            }
        } else {
            fileAttachRepository.deleteAllByObjectIdAndObjectTypeAndResolutionAndObjectNameAndDescription(couponId, FileAttachTypeEnum.COUPON_BANNER.value,
                    ResolutionEnum.DESKTOP.value, FileAttachConst.OBJECT_NAME_COUPON, FileAttachConst.DESCRIPTION_BANNER_COUPON);
        }
        if(mobileBannerId !=null){
            fileAttachRepository.deleteAllByObjectIdAndObjectTypeAndResolutionAndIdNotAndObjectNameAndDescription(couponId, FileAttachTypeEnum.COUPON_BANNER.value,
                    ResolutionEnum.MOBILE.value, mobileBannerId, FileAttachConst.OBJECT_NAME_COUPON, FileAttachConst.DESCRIPTION_BANNER_COUPON);
            Optional<FileAttach> optional = fileAttachRepository.findById(mobileBannerId);
            if(optional.isPresent()){
                FileAttach file = optional.get();
                file.setObjectType(FileAttachTypeEnum.COUPON_BANNER.value);
                file.setResolution(ResolutionEnum.MOBILE.value);
                file.setFileType(FileTypeEnum.IMAGE.value);
                file.setObjectId(couponId);
                file.setObjectName(FileAttachConst.OBJECT_NAME_COUPON);
                file.setDescription(FileAttachConst.DESCRIPTION_BANNER_COUPON);
                fileAttachRepository.save(file);
            }
        } else {
            fileAttachRepository.deleteAllByObjectIdAndObjectTypeAndResolutionAndObjectNameAndDescription(couponId, FileAttachTypeEnum.COUPON_BANNER.value,
                    ResolutionEnum.MOBILE.value, FileAttachConst.OBJECT_NAME_COUPON, FileAttachConst.DESCRIPTION_BANNER_COUPON);
        }

    }

    private void getBanner(CouponDetailDTO dto){
        List<FileAttach> fileAttaches = fileAttachRepository.findAllByObjectIdAndObjectTypeAndObjectNameAndDescription(dto.getId(),
                FileAttachTypeEnum.COUPON_BANNER.value, FileAttachConst.OBJECT_NAME_COUPON, FileAttachConst.DESCRIPTION_BANNER_COUPON);
        for(FileAttach f: fileAttaches){
            FileAttachResDTO fileDto = FileAttachResDTO.builder()
                    .id(f.getId())
                    .fileName(f.getFileName())
                    .filePath(f.getFilePath())
                    .priority(f.getPriority())
                    .fileSize(f.getFileSize())
                    .externalLink(f.getExternalLink())
                    .id(f.getId())
                    .build();
            if(f.getResolution() !=null && f.getResolution().equals(ResolutionEnum.MOBILE.value)){
                dto.setMobileBanner(fileDto);
            } else {
                dto.setDesktopBanner(fileDto);
            }
        }
    }

    private boolean isEnterpriseSme(String customerType) {
        return null == customerType || Objects.equals(customerType, CustomerTypeEnum.ENTERPRISE.getValue());
    }

    private boolean isHouseHoldSme(String customerType) {
        return Objects.equals(customerType, CustomerTypeEnum.HOUSE_HOLD.getValue());
    }

    private boolean isPersonalSme(String customerType) {
        return Objects.equals(customerType, CustomerTypeEnum.PERSONAL.getValue());
    }
}

package com.service.services.impl;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.InputMismatchException;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Description;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import com.common.CouponUtils;
import com.config.jwt.JwtUtils;
import com.constant.CreatedSourceMigration;
import com.constant.EntitiesConstant;
import com.constant.NotifyConst;
import com.constant.SeoTypeCodeConstant;
import com.constant.ServicesConstant;
import com.constant.SystemParamConstant;
import com.constant.enums.action.log.ActionLogTypeEnum;
import com.constant.enums.coupon.LimitedTypeEnum;
import com.constant.enums.file.attach.AccessTypeEnum;
import com.constant.enums.file.attach.FileTypeEnum;
import com.constant.enums.file.attach.ResolutionEnum;
import com.constant.enums.pricing.PricingDetailInputEnum;
import com.constant.enums.services.ServiceApproveStatusEnum;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.constant.enums.services.ServiceStatusDeletedEnum;
import com.constant.enums.services.ServiceStatusEnum;
import com.constant.enums.wp.AppRoleTypeEnum;
import com.dto.action.log.ActionLogTemplateDTO;
import com.dto.addons.AddonDeleteResDTO;
import com.dto.addons.AddonIdsReqDTO;
import com.dto.categories.CategoryDTODetail;
import com.dto.categories.CategoryViewDTO;
import com.dto.categories.ICategoryServicesDTO;
import com.dto.categories.IGetCategoryViewDTO;
import com.dto.categories.SeoCategoryDTO;
import com.dto.combo.detail.DetailItemCombo;
import com.dto.combo.detail.IGetDetailItemCombo;
import com.dto.common.ICommonIdCountDTO;
import com.dto.common.ICommonIdNameCreatedAt;
import com.dto.customerTicket.AttachesDTO;
import com.dto.feature.FeatureBriefInfoDTO;
import com.dto.feature.FeatureReqDTO;
import com.dto.file.attach.FileAttachLibraryDTO;
import com.dto.file.attach.FileAttachResDTO;
import com.dto.homepage.IHomepageSearchBarResDTO;
import com.dto.marketingCampaign.smePortal.ProductTagDTO;
import com.dto.mobile.PricingDetailRespDTO;
import com.dto.mobile.PricingPlanDetailRespDTO;
import com.dto.pricing.IPricingSaaSResDTO;
import com.dto.pricing.PricingApproveReqDTO;
import com.dto.pricing.PricingReqDTO;
import com.dto.pricing.PricingResSmeDTO;
import com.dto.pricing.PricingSaaSResDTO;
import com.dto.pricing.PricingTaxRes;
import com.dto.pricing.multiplePeriod.PricingMultipleResDTO;
import com.dto.product_solustions.IProductSolutionSmeDTO;
import com.dto.product_solustions.ProductSolutionSmeDTO;
import com.dto.product_solustions.ProductSolutionSmeDTO.PackageItem;
import com.dto.product_variant.AttributesCreateDTO;
import com.dto.product_variant.AttributesCreateNewDevDTO;
import com.dto.product_variant.VariantAttributesValueConvertDTO;
import com.dto.product_variant.VariantDTO;
import com.dto.product_variant.VariantPriceTypeEnum;
import com.dto.product_variant.VariantReqDTO;
import com.dto.product_variant.VariantResExtraDTO;
import com.dto.role_wp.CreateWPRoleDTO;
import com.dto.seo.SeoDTO;
import com.dto.services.ApiAppDTO;
import com.dto.services.CategoryServiceDTO;
import com.dto.services.CreateStepOneDTO;
import com.dto.services.GuideDTO;
import com.dto.services.IGetCategoriesParent;
import com.dto.services.IServiceCategoryDTO;
import com.dto.services.IVariantIdAndPricingIdDTO;
import com.dto.services.PricingAndAddonRelationDTO;
import com.dto.services.SearchSmeDTO;
import com.dto.services.SearchSmeResponseDTO;
import com.dto.services.ServiceAddonDetailDTO;
import com.dto.services.ServiceApproveDTO;
import com.dto.services.ServiceByCategoryResponseTransDTO;
import com.dto.services.ServiceCaptureDTO;
import com.dto.services.ServiceCreateDTO;
import com.dto.services.ServiceCreateResponseDTO;
import com.dto.services.ServiceDTO;
import com.dto.services.ServiceDescResDTO;
import com.dto.services.ServiceDetailFileAttachDTO;
import com.dto.services.ServiceDetailResAdminDTO;
import com.dto.services.ServiceDetailResponseDTO;
import com.dto.services.ServiceFeatureDTO;
import com.dto.services.ServiceLoginDTO;
import com.dto.services.ServiceResponseDTO;
import com.dto.services.ServiceResponseTransDTO;
import com.dto.services.ServiceSearchDTO;
import com.dto.services.ServiceSelectedRequestDTO;
import com.dto.services.ServiceSnapDTO;
import com.dto.services.ServiceSuggestionDTO;
import com.dto.services.ServiceSuggestionUpdateDTO;
import com.dto.services.ServiceSupportDTO;
import com.dto.services.ServiceTopicUpdateRequest;
import com.dto.services.ServiceUpdateAllDTO;
import com.dto.services.ServiceUpdateDTO;
import com.dto.services.ServiceUpdateDescDTO;
import com.dto.services.ServiceUserDTO;
import com.dto.services.SetupFeeInfoConvertDTO;
import com.dto.services.TaxInfoConvertDTO;
import com.dto.services.UpdateServiceDraftIdDTO;
import com.dto.services.UpdateServiceResDTO;
import com.dto.services.auth.CreateServiceIntegrateDTO;
import com.dto.services.auth.DevelopIntegrateDTO;
import com.dto.services.auth.IServiceCategoryDetailDTO;
import com.dto.services.auth.ServiceCategoryDetailDTO;
import com.dto.services.sugesstion.ServiceCommonDetailDTO;
import com.dto.users.CustomerTypeDTO;
import com.dto.users.IProviderResDTO;
import com.dto.users.SAASUserDTO;
import com.dto.users.UserAdminDTO;
import com.entity.Province.Province;
import com.entity.addons.AddonDraft;
import com.entity.api_wp.WPPermission;
import com.entity.api_wp.WorkplaceApi;
import com.entity.categories.Category;
import com.entity.categories.ServicesCategories;
import com.entity.combo.ComboPricing;
import com.entity.coupons.Coupon;
import com.entity.district.District;
import com.entity.feature.Feature;
import com.entity.file.attach.FileAttach;
import com.entity.pricing.Pricing;
import com.entity.pricing.PricingDraft;
import com.entity.product_variant.Variant;
import com.entity.product_variant.VariantDraft;
import com.entity.rating.ServiceReaction;
import com.entity.role.WPRole;
import com.entity.role.WPRolePermission;
import com.entity.seo.KeyWord;
import com.entity.seo.Seo;
import com.entity.serviceGroup.ServiceGroup;
import com.entity.serviceGroup.ServiceGroupDraft;
import com.entity.serviceGroup.ServiceGroupPricingItem;
import com.entity.services.RequestApprove;
import com.entity.services.ServiceDraft;
import com.entity.services.ServiceEntity;
import com.entity.street.Street;
import com.entity.systemParam.ApprovedRule;
import com.entity.ward.Ward;
import com.enums.ApproveStatusEnum;
import com.enums.ComponentVisible;
import com.enums.PermissionRoleDefaultEnum;
import com.enums.ProductTypeEnum;
import com.enums.RequestApproveTypeEnum;
import com.enums.SentFlagEnum;
import com.enums.ServiceEnum;
import com.enums.ServiceViewEnum.ServiceViewTypeEnum;
import com.enums.product_solutions.ObjectTypeEnum;
import com.enums.product_solutions.ServiceOperationTypeEnum;
import com.exception.ErrorKey;
import com.exception.ErrorKey.Services;
import com.exception.Resources;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mapper.ServiceMapper;
import com.mapper.UserIntegrationMapper;
import com.model.dto.EmailTemplate;
import com.model.dto.IGetPricingDefaultDTO;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.constants.enums.customFields.CustomFieldCategoryEnum;
import com.onedx.common.constants.enums.customFields.EntityTypeEnum;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.emails.ParamEmailEnum;
import com.onedx.common.constants.enums.fileAttach.FileAttachTypeEnum;
import com.onedx.common.constants.enums.pricings.PricingPlanEnum;
import com.onedx.common.constants.enums.security.roles.RoleType;
import com.onedx.common.constants.enums.services.ActionEnum;
import com.onedx.common.constants.enums.services.OnOsTypeEnum;
import com.onedx.common.constants.enums.services.ProductClassificationEnum;
import com.onedx.common.constants.enums.services.ProviderTypeEnum;
import com.onedx.common.constants.enums.services.ServiceTypeEnum;
import com.onedx.common.constants.enums.subscriptions.CalculateTypeEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.enums.systemParams.ApprovedChangeServiceEnum;
import com.onedx.common.constants.enums.systemParams.ApprovedTypeEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.EmailConst;
import com.onedx.common.constants.values.ExceptionConstants;
import com.onedx.common.constants.values.MessageConst;
import com.onedx.common.constants.values.RoleConst;
import com.onedx.common.converter.SetConverter;
import com.onedx.common.dto.base.BaseResponseDTO;
import com.onedx.common.dto.base.CommonIdNameDTO;
import com.onedx.common.dto.customFields.CustomFieldValueDTO;
import com.onedx.common.dto.mail.MailParamResDTO;
import com.onedx.common.dto.notification.NotificationDTO;
import com.onedx.common.dto.oauth2.CustomUserDetails;
import com.onedx.common.entity.customField.CustomField;
import com.onedx.common.entity.customField.CustomFieldDraftValue;
import com.onedx.common.entity.customField.CustomFieldValue;
import com.onedx.common.entity.customField.CustomLayout;
import com.onedx.common.entity.security.Role;
import com.onedx.common.entity.systemParams.SystemParam;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.exception.MessageKeyConstant;
import com.onedx.common.exception.type.BadRequestException;
import com.onedx.common.exception.type.ResourceNotFoundException;
import com.onedx.common.repository.emails.mailTemplate.ParamEmailRepository;
import com.onedx.common.repository.rolePermission.RoleRepository;
import com.onedx.common.utils.CustomPredicateUtils;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.EmailUtil;
import com.onedx.common.utils.FileUtils;
import com.onedx.common.utils.HttpRestUtil;
import com.onedx.common.utils.ObjectUtil;
import com.onedx.common.utils.SqlUtils;
import com.repository.Province.ProvinceRepository;
import com.repository.action.log.ActionLogRepository;
import com.repository.addons.AddonDraftRepository;
import com.repository.api_wp.WorkplaceApiRepository;
import com.repository.categories.CategoryRepository;
import com.repository.categories.ServicesCategoriesRepository;
import com.repository.combo.ComboPlanRepository;
import com.repository.combo.ComboPricingRepository;
import com.repository.combo.ComboRepository;
import com.repository.combo.ComboTaxRepository;
import com.repository.coupons.CouponAddonRepository;
import com.repository.coupons.CouponAdsPositionRepository;
import com.repository.coupons.CouponPricingApplyRepository;
import com.repository.coupons.CouponPricingPlanApplyRepository;
import com.repository.coupons.CouponPricingPlanRepository;
import com.repository.coupons.CouponPricingRepository;
import com.repository.coupons.CouponRepository;
import com.repository.customField.CustomFieldDraftValueRepository;
import com.repository.customField.CustomFieldRepository;
import com.repository.customField.CustomFieldValueRepository;
import com.repository.customField.CustomLayoutRepository;
import com.repository.customerType.CustomerTypeRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.district.DistrictRepository;
import com.repository.feature.FeatureRepository;
import com.repository.file.attach.FileAttachRepository;
import com.repository.manufacturer.ManufacturerRepository;
import com.repository.permission.WPPermissionRepository;
import com.repository.pricing.PricingDraftRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingRepository;
import com.repository.pricing.PricingTaxRepository;
import com.repository.pricing.PricingVariantRepository;
import com.repository.product_solutions.PackageItemRepository;
import com.repository.product_solutions.ProductSolutionRepository;
import com.repository.product_variant.MappingAttributesServiceRepository;
import com.repository.product_variant.VariantDraftRepository;
import com.repository.product_variant.VariantRepository;
import com.repository.role.WPRolePermissionRepository;
import com.repository.role.WPRoleRepository;
import com.repository.seo.KeyWordRepository;
import com.repository.seo.SeoRepository;
import com.repository.serviceGroup.ServiceGroupDraftRepository;
import com.repository.serviceGroup.ServiceGroupPricingItemRepository;
import com.repository.serviceGroup.ServiceGroupRepository;
import com.repository.services.RequestApproveRepository;
import com.repository.services.ServiceDraftRepository;
import com.repository.services.ServiceRepository;
import com.repository.shoppingCart.ShoppingCartRepository;
import com.repository.street.StreetRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.systemParam.ApprovedRuleRepository;
import com.repository.users.UserRepository;
import com.repository.ward.WardRepository;
import com.service.action.log.ActionLogService;
import com.service.combo.ComboPlanService;
import com.service.combo.ComboService;
import com.service.customField.impl.CustomFieldManager;
import com.service.email.EmailService;
import com.service.faq.TopicFaqService;
import com.service.feature.impl.FeatureServiceImpl;
import com.service.marketingCampaign.MarketingCampaignSmeService;
import com.service.multiplePeriod.SubMultiplePeriod;
import com.service.pricing.PricingService;
import com.service.product_variant.AttributesService;
import com.service.product_variant.VariantService;
import com.service.rating.ServiceReactionService;
import com.service.rating.ServiceViewService;
import com.service.seo.SeoService;
import com.service.serviceGroup.ServiceGroupService;
import com.service.serviceGroup.impl.ServiceGroupServiceImpl;
import com.service.serviceSuggestion.ServiceHandlerFactory;
import com.service.serviceSuggestion.ServiceMapperContext;
import com.service.serviceSuggestion.ServiceSuggestionService;
import com.service.services.ServicesService;
import com.service.subscriptionFormula.SubscriptionFormula;
import com.service.system.param.SystemParamService;
import com.util.AuthUtil;
import com.util.FileUtil;
import com.util.NotifyUtil;
import com.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;

@Service
@Slf4j
@PropertySource(value = "classpath:email_template.properties", encoding = "UTF-8")
public class ServicesServiceImpl implements ServicesService {

    private static final long NON_USER_ID = -1L;
    private static final long MANAGER = -1L;
    private static final String NON_SERVICE_NAME = null;
    private static final String[] LANGUAGE_TYPES = {"0", "1"};
    private static final String NULL_LINK = null;
    private static final MultipartFile MULTIPART_FILE_NULL = null;
    private static final int DELETE_FLAG = 1;
    private static final String CAU_HINH_DICH_VU = "CAU_HINH_DICH_VU";
    private static final String TOP_SELLING = "topSelling";
    private static final String TRENDING = "trending";
    private static final String TITLE_SERVICE = " | oneSME";
    private static final String NEVER_BROWSED = "NEVER_BROWSED";
    private static final String HAVE_BROWSED = "HAVE_BROWSED";
    private static final String PRODUCTVARIANT = "PRODUCT_VARIANT";
    private static final String SERVICE_GROUP = "SERVICE_GROUP";
    private static final String COMBO = "COMBO";
    private static final String[] SERVICE_MESSAGE = new String[]{"service"};
    private static final String[] DEVELOPER_MESSAGE = new String[]{"developer"};
    private static final String[] FEATURE_MESSAGE = new String[]{"feature"};
    private static final String[] SNAPSHOT_MESSAGE = new String[]{"Snapshot"};
    private static final String REGISTER = "REGISTER";
    private static final int accessTokenValiditySeconds = 60 * 60 * 10;
    private static final int refreshTokenValiditySeconds = 60 * 60 * 24 * 30;

    @Value("${send.mail.hotline}")
    private String HOTLINE;
    @Value("${kafka.topic-producer}")
    private String TOPIC_PRODUCER_NAME;
    @Value("${kafka.topic-consumer}")
    private String TOPIC_CONSUMER_NAME;
    @Value("${kafka.group-id}")
    private String GROUP_ID;
    @Value("${web.host}")
    private String webHost;
    @Value("${mail.template.service.approval}")
    private String mailApproveContent;
    @Value("${mail.template.service.approval.vnpt}")
    private String mailApproveContentVNPT;
    @Value("${mail.template.service.rejected}")
    private String mailRejectContent;
    @Value("${mail.template.service.request.updated}")
    private String mailRequestUpdatedContent;
    @Value("${mail.template.service.request.approved}")
    private String mailApproveRequestContent;
    @Value("${mail.template.service.approval.token}")
    private String mailApproveContentToken;
    @Value("${mail.subject.service.approval}")
    private String subjectServiceApproved;
    @Value("${mail.subject.service.rejected}")
    private String subjectServiceRejected;
    @Value("${mail.subject.service.request.updated}")
    private String subjectServiceRequestUpdated;
    @Value("${mail.subject.service.request.approved}")
    private String subjectServiceRequestApproved;
    @Value(value = "${dxservice.workplace}")
    private String dxServiceWp;
    @Value(value = "${workplace.clone-role-default}")
    private String cloneRoleDefault;

    @Autowired
    private HttpRestUtil httpUtil;
    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;
    @Autowired
    private PricingTaxRepository pricingTaxRepository;
    @Autowired
    private SubscriptionFormula subscriptionFormula;
    @Autowired
    private SubMultiplePeriod subMultiplePeriod;
    @Autowired
    private WorkplaceApiRepository wpApiRepository;
    @Autowired
    private WPRoleRepository wpRoleRepository;
    @Autowired
    private WPPermissionRepository wpPermissionRepository;
    @Autowired
    private WPRolePermissionRepository wpRolePermissionRepository;
    @Autowired
    private ShoppingCartRepository shoppingCartRepository;
    @Autowired
    private ServiceRepository serviceRepository;
    @Autowired
    private ManufacturerRepository manufacturerRepository;
    @Autowired
    private SeoService seoService;
    @Autowired
    private CategoryRepository categoriesRepository;
    @Autowired
    private VariantDraftRepository variantDraftRepository;
    @Autowired
    private VariantRepository variantRepository;
    @Autowired
    private MappingAttributesServiceRepository mappingAttributesServiceRepository;
    @Autowired
    private PricingService pricingService;
    @Autowired
    private ComboPlanService comboPlanService;
    @Autowired
    private DepartmentsRepository departmentsRepository;
    @Autowired
    private ServiceGroupDraftRepository serviceGroupDraftRepository;
    @Autowired
    private ServiceGroupPricingItemRepository serviceGroupPricingItemRepository;
    @Autowired
    @Lazy
    private ServiceGroupService serviceGroupService;
    @Autowired
    private ServiceReactionService serviceReactionService;
    @Autowired
    @Lazy
    private ServiceGroupServiceImpl serviceGroupServiceImpl;
    @Autowired
    private ServiceGroupRepository serviceGroupRepository;
    @Autowired
    private FileAttachRepository fileAttachRepository;
    @Autowired
    private ServiceMapper serviceMapper;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private Environment environment;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private JwtUtils jwtUtil;
    @Autowired
    private EmailService emailService;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private UserIntegrationMapper userIntegrationMapper;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ActionLogService actionLogService;
    @Autowired
    private ParamEmailRepository paramEmailRepository;
    @Autowired
    private Environment evn;
    @Autowired
    private ServiceViewService serviceViewService;
    @Autowired
    private ServiceDraftRepository serviceDraftRepository;
    @Autowired
    private CouponPricingPlanApplyRepository couponPricingPlanApplyRepository;
    @Autowired
    private CouponPricingPlanRepository couponPricingPlanRepository;
    @Autowired
    private ComboPricingRepository comboPricingRepository;
    @Autowired
    private CouponPricingRepository couponPricingRepository;
    @Autowired
    private CouponAdsPositionRepository couponAdsPositionRepository;
    @Autowired
    private PricingRepository pricingRepository;
    @Autowired
    private PricingDraftRepository pricingDraftRepository;
    @Autowired
    private CouponPricingApplyRepository couponPricingApplyRepository;
    @Autowired
    private CouponAddonRepository couponAddonRepository;
    @Autowired
    private CustomerTypeRepository customerTypeRepository;
    @Autowired
    private MarketingCampaignSmeService campaignSmeService;
    @Autowired
    private TopicFaqService topicFaqService;
    @Autowired
    private ApprovedRuleRepository approvedRuleRepository;
    @Autowired
    private AttributesService attributesService;
    @Autowired
    private VariantService variantService;
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private CustomLayoutRepository customLayoutRepository;
    @Autowired
    private CustomFieldValueRepository customFieldValueRepository;
    @Autowired
    private CustomFieldManager customFieldManager;
    @Autowired
    private RequestApproveRepository requestApproveRepository;
    @Autowired
    private ComboRepository comboRepository;
    @Autowired
    private CouponRepository couponRepository;
    @Autowired
    private ComboService comboService;
    @Autowired
    private ProductSolutionRepository productSolutionRepository;
    @Autowired
    private PackageItemRepository packageItemRepository;
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    @Autowired
    private ProvinceRepository provinceRepository;
    @Autowired
    private DistrictRepository districtRepository;
    @Autowired
    private WardRepository wardRepository;
    @Autowired
    private ComboTaxRepository comboTaxRepository;
    @Autowired
    private StreetRepository streetRepository;
    @Autowired
    private FeatureServiceImpl featureService;
    @Autowired
    private FeatureRepository featureRepository;
    @Autowired
    private SeoRepository seoRepository;
    @Autowired
    private CategoryRepository categoryRepository;
    @Autowired
    private KeyWordRepository keyWordRepository;
    @Autowired
    private ServiceSuggestionService serviceSuggestionService;
    @Autowired
    private ActionLogRepository actionLogRepository;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private AddonDraftRepository addonDraftRepository;
    @Autowired
    private ComboPlanRepository comboPlanRepository;
    @Autowired
    private PricingVariantRepository pricingVariantRepository;
    @Autowired
    private CustomFieldRepository customFieldRepository;
    @Autowired
    private CustomFieldDraftValueRepository customFieldDraftValueRepository;
    @Autowired
    private ExceptionFactory exceptionFactory;
    @Autowired
    private ServicesCategoriesRepository servicesCategoriesRepository;
    @Autowired
    @Qualifier("asyncExecutorSendRequestApproveNotif")
    private Executor executor;
    @Autowired
    ServiceHandlerFactory serviceHandlerFactory;


    @Override
    public ServiceEntity findByIdAndDeletedFlag(Long serviceId, Integer deletedFlag) {
        return serviceRepository.findByIdAndDeletedFlag(serviceId, deletedFlag)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SERVICES, ErrorKey.ID, String.valueOf(serviceId)));
    }

    @Override
    public ServiceEntity validateService(Long serviceId, Integer deletedFlag) {
        ServiceEntity serviceEntity = findByIdAndDeletedFlag(serviceId, deletedFlag);
        if (serviceEntity.getStatus() == StatusEnum.INACTIVE.value) {
            throw exceptionFactory.badRequest(MessageKeyConstant.SERVICE_NOT_ACTIVE, Resources.SERVICES, ErrorKey.ID, String.valueOf(serviceId));
        }
        return serviceEntity;
    }

    @Override
    public ServiceDescResDTO findDescById(long id, PricingDetailInputEnum type, ServiceDraft serviceDraft) {
        if (Objects.equals(PricingDetailInputEnum.PROCESSING, type) && Objects.nonNull(serviceDraft)) {
            return findServiceDraftDescById(serviceDraft);
        }

        ServiceEntity currentService = getCurrentService(id);
        validateAccessService(currentService.getUserId(), currentService.getCreatedBy());
        ServiceDescResDTO serviceDTO = getServiceOrServiceDraft(currentService, null, false); //service

        //set support information
        setSupportInfo(currentService,serviceDTO);

        serviceDTO.setApproveFirst(
            Objects.nonNull(serviceDraft)
            && Objects.isNull(serviceDraft.getUpdateFeatureReason())
            && Objects.isNull(serviceDraft.getUpdateSnapReason())
            && Objects.isNull(serviceDraft.getUpdateBaseInfoReason())
            && Objects.isNull(serviceDraft.getUpdateTechReason())
            ? YesNoEnum.YES.name() : YesNoEnum.NO.name());

        boolean hasApproved = serviceRepository.checkDraftApprovedByServiceId(currentService.getId());
        serviceDTO.setStatusBrowsing(
            (!Objects.equals(currentService.getApprove(), ServiceApproveStatusEnum.APPROVED) && !hasApproved) ? NEVER_BROWSED : HAVE_BROWSED);

        return serviceDTO;
    }

    /**
     *  Validate quyền xem chi tiết service
     */
    private void validateAccessService(Long userId, Long createdBy) {
        if (AuthUtil.getCurrentUser() == null) {
            throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_LOGIN);
        }
        if (AuthUtil.checkUserRoles(Arrays.asList(RoleType.FULL_DEV.getValue(), RoleType.DEVELOPER.getValue(),
            RoleType.DEVELOPER_BUSINESS.getValue(), RoleType.DEVELOPER_OPERATOR.getValue()))
            && !Objects.equals(AuthUtil.getCurrentParentId(), userId)
            && !Objects.equals(createdBy, AuthUtil.getCurrentUserId())) {
            throw exceptionFactory.permissionDenied(ExceptionConstants.NOT_RESOURCE_OWNER);
        }
    }

    /**
     *  getServiceOrServiceDraft
     */
    private ServiceDescResDTO getServiceOrServiceDraft (ServiceEntity serviceEntity, ServiceDraft serviceDraft, Boolean isDraft) {

        Long id = isDraft ? serviceDraft.getId() : serviceEntity.getId();
        Integer paymentMethod = isDraft ? serviceDraft.getPaymentMethod() : serviceEntity.getPaymentMethod();

        ServiceDescResDTO serviceDTO = ServiceDescResDTO.builder()
            .id(id)
            .description(isDraft ? serviceDraft.getDescription() : serviceEntity.getDescription())
            .specifications(isDraft ? serviceDraft.getSpecifications() : serviceEntity.getSpecifications())
            .sapoDescription(isDraft ? serviceDraft.getSapoDescription() : serviceEntity.getSapoDescription())
            .status(isDraft ? serviceDraft.getApprove() : serviceEntity.getApprove())
            .apiKey(isDraft ? serviceDraft.getApiKey() : serviceEntity.getApiKey())
            .secretKey(isDraft ? serviceDraft.getSecretKey() : serviceEntity.getSecretKey())
            .customerTypeCode(isDraft ? serviceDraft.getCustomerTypeCode() : serviceEntity.getCustomerTypeCode())
            .allowMultiSub(isDraft ? serviceDraft.getAllowMultiSub() : serviceEntity.getAllowMultiSub())
            .paymentMethod(PaymentMethodEnum.fromValue(paymentMethod))
            .build();

        Optional<List<FileAttach>> avatars =
            fileAttachRepository.getByObjectTypeAndServiceId(FileAttachTypeEnum.AVATAR.value, isDraft ? -1 : id, isDraft ? id : -1);
        if (avatars.isPresent()) {
            FileAttach thisFile = avatars.get().get(0);
            FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                .id(thisFile.getId())
                .fileName(thisFile.getFileName())
                .typeEnum(FileAttachTypeEnum.AVATAR)
                .filePath(thisFile.getFilePath())
                .priority(thisFile.getPriority())
                .fileSize(thisFile.getFileSize())
                .externalLink(thisFile.getExternalLink())
                .build();
            serviceDTO.setIcon(attachResDTO);
        }

        Optional<List<FileAttach>> captures =
            fileAttachRepository.getByObjectTypeAndServiceId(FileAttachTypeEnum.CAPTURE.value, isDraft ? -1 : id, isDraft ? id : -1);
        if (captures.isPresent()) {
            List<FileAttachResDTO> capUrl = new ArrayList<>();
            captures.get().forEach(capture -> {
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                    .id(capture.getId())
                    .fileName(capture.getFileName())
                    .typeEnum(FileAttachTypeEnum.CAPTURE)
                    .filePath(capture.getFilePath())
                    .priority(capture.getPriority())
                    .fileSize(capture.getFileSize())
                    .externalLink(capture.getExternalLink())
                    .build();
                capUrl.add(attachResDTO);
            });
            serviceDTO.setSnapshots(capUrl);
        }

        Optional<List<FileAttach>> banners =
            fileAttachRepository.getByObjectTypeAndServiceId(FileAttachTypeEnum.BANNER.value, isDraft ? -1 : id, isDraft ? id : -1);
        if (banners.isPresent()) {
            FileAttach thisFile = banners.get().get(0);
            FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                .id(thisFile.getId())
                .fileName(thisFile.getFileName())
                .typeEnum(FileAttachTypeEnum.AVATAR)
                .filePath(thisFile.getFilePath())
                .priority(thisFile.getPriority())
                .fileSize(thisFile.getFileSize())
                .externalLink(thisFile.getExternalLink())
                .build();
            serviceDTO.setBanner(attachResDTO);
        }

        Optional<List<FileAttach>> intros =
            fileAttachRepository.getByObjectTypeAndServiceId(FileAttachTypeEnum.INTRO.value, isDraft ? -1 : id, isDraft ? id : -1);
        if (intros.isPresent()) {
            FileAttach thisFile = intros.get().get(0);
            FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                .id(thisFile.getId())
                .fileName(thisFile.getFileName())
                .typeEnum(FileAttachTypeEnum.INTRO)
                .filePath(thisFile.getFilePath())
                .priority(thisFile.getPriority())
                .fileSize(thisFile.getFileSize())
                .externalLink(thisFile.getExternalLink())
                .build();
            serviceDTO.setVideo(attachResDTO);
        }

        Set<Integer> tutorialTypes = new HashSet<Integer>(){{
            add(FileAttachTypeEnum.VIDEO_GUIDE.value);
            add(FileAttachTypeEnum.DOCUMENT_GUIDE.value);
        }};
        Optional<List<FileAttach>> tutorials =
            fileAttachRepository.getByObjectTypeInAndServiceIdOrderByIdDesc(tutorialTypes, isDraft ? -1 : id, isDraft ? id : -1);
        if (tutorials.isPresent()) {
            List<FileAttachResDTO> videos = new ArrayList<>();
            List<FileAttachResDTO> documents = new ArrayList<>();
            for(FileAttach thisFile: tutorials.get()){
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                    .id(thisFile.getId())
                    .fileName(thisFile.getFileName())
                    .filePath(thisFile.getFilePath())
                    .priority(thisFile.getPriority())
                    .fileSize(thisFile.getFileSize())
                    .externalLink(thisFile.getExternalLink())
                    .build();
                if(thisFile.getObjectType().equals(FileAttachTypeEnum.VIDEO_GUIDE.value)){
                    attachResDTO.setTypeEnum(FileAttachTypeEnum.VIDEO_GUIDE);
                    videos.add(attachResDTO);
                } else {
                    attachResDTO.setTypeEnum(FileAttachTypeEnum.DOCUMENT_GUIDE);
                    documents.add(attachResDTO);
                }
            }
            serviceDTO.setVideoGuide(videos);
            serviceDTO.setDocGuide(documents);
            serviceDTO.setGuideVisible(
                !tutorials.get().isEmpty() ? tutorials.get().get(0).getVisible() != null ? tutorials.get().get(0)
                    .getVisible() : 1 : 1);
        }

        // Lay thong tin Seo cua dich vu
        List<SeoDTO> seoService = getServiceSeoList(isDraft ? serviceDraft.getServiceId() : id);
        serviceDTO.setSeoList(seoService);

        return serviceDTO;
    }

    private List<SeoDTO> getServiceSeoList(Long serviceId) {
        List<SeoDTO> seoService = new ArrayList<>();
        // chi lay serviceId, seo ko co serviceDraftId
        seoRepository.getAllSeoByServiceId(serviceId).forEach(s -> {
            s.setKeyWord(seoRepository.getAllKeywordBySeoId(s.getSeoId()));
            if (Objects.nonNull(s.getFileAttachId())) {
                if (fileAttachRepository.getFiledAttachBySeoId(s.getSeoId()).contains(s.getFileAttachId())) {
                    seoService.add(s);
                }
            } else {
                seoService.add(s);
            }
        });
        return seoService;
    }

    private void getDescriptionInfo(ServiceDTO serviceDTO, ServiceDraft serviceDraft, ServiceEntity service, Boolean isDraft) {
        if (isDraft) {
            serviceDTO.setApiKey(serviceDraft.getApiKey());
            serviceDTO.setSecretKey(serviceDraft.getSecretKey());
            serviceDTO.setProvider(serviceDraft.getProvider());
            serviceDTO.setAddress(serviceDraft.getAddress());
            serviceDTO.setPhoneNumber(serviceDraft.getPhoneNumber());
            serviceDTO.setEmail(serviceDraft.getEmail());
            serviceDTO.setProvinceId(serviceDraft.getProvinceId());
            serviceDTO.setProvinceCode(serviceDraft.getProvinceCode());
            serviceDTO.setDistrictId(serviceDraft.getDistrictId());
            serviceDTO.setWardId(serviceDraft.getWardId());
            serviceDTO.setStreetId(serviceDraft.getStreetId());
            if(serviceDraft.getProvinceId() !=null){
                Optional<Province> optional = provinceRepository.findById(serviceDraft.getProvinceId());
                serviceDTO.setProvinceName(optional.map(Province::getName).orElse(null));
            }
            if(serviceDraft.getProvinceCode() != null && serviceDraft.getDistrictId() !=null){
                District district = districtRepository.findDistrictByIdAndProvinceCode(serviceDraft.getDistrictId(),serviceDraft.getProvinceCode() );
                serviceDTO.setDistrictName(district!=null?district.getName():null);
            }
            if(serviceDraft.getProvinceCode() != null && serviceDraft.getWardId() !=null){
                Ward ward = wardRepository.findFirstByIdAndAndProvinceCode(serviceDraft.getWardId(),serviceDraft.getProvinceCode());
                serviceDTO.setWardName(ward!=null?ward.getName():null);
            }
            if(serviceDraft.getProvinceCode() != null && serviceDraft.getStreetId() !=null){
                Street street = streetRepository.findFirstByIdAndProvinceCode(serviceDraft.getStreetId(),
                    serviceDraft.getProvinceCode());
                serviceDTO.setStreetName(street != null ? street.getName() : null);
            }
        } else {
            serviceDTO.setApiKey(service.getApiKey());
            serviceDTO.setSecretKey(service.getSecretKey());
            serviceDTO.setProvider(service.getProvider());
            serviceDTO.setAddress(service.getAddress());
            serviceDTO.setPhoneNumber(service.getPhoneNumber());
            serviceDTO.setEmail(service.getEmail());
            serviceDTO.setProvinceId(service.getProvinceId());
            serviceDTO.setProvinceCode(service.getProvinceCode());
            serviceDTO.setDistrictId(service.getDistrictId());
            serviceDTO.setWardId(service.getWardId());
            serviceDTO.setStreetId(service.getStreetId());
            if(service.getProvinceId() !=null){
                Optional<Province> optional = provinceRepository.findById(service.getProvinceId());
                serviceDTO.setProvinceName(optional.map(Province::getName).orElse(null));
            }
            if(service.getProvinceCode() != null && service.getDistrictId() !=null){
                District district = districtRepository.findDistrictByIdAndProvinceCode(service.getDistrictId(),service.getProvinceCode() );
                serviceDTO.setDistrictName(district!=null?district.getName():null);
            }
            if(service.getProvinceCode() != null && service.getWardId() !=null){
                Ward ward = wardRepository.findFirstByIdAndAndProvinceCode(service.getWardId(),service.getProvinceCode());
                serviceDTO.setWardName(ward!=null?ward.getName():null);
            }
            if(service.getProvinceCode() != null && service.getStreetId() !=null){
                Street street = streetRepository.findFirstByIdAndProvinceCode(service.getStreetId(),
                    service.getProvinceCode());
                serviceDTO.setStreetName(street != null ? street.getName() : null);
            }

        }

        Set<Integer> tutorialTypes = new HashSet<Integer>(){{
            add(FileAttachTypeEnum.VIDEO_GUIDE.value);
            add(FileAttachTypeEnum.DOCUMENT_GUIDE.value);
        }};
        Optional<List<FileAttach>> tutorials =
            fileAttachRepository.getByObjectTypeInAndServiceIdOrderByIdDesc(tutorialTypes, isDraft ? -1 : service.getId(), isDraft ? serviceDraft.getServiceId() : -1);
        if (tutorials.isPresent()) {
            List<FileAttachResDTO> videos = new ArrayList<>();
            List<FileAttachResDTO> documents = new ArrayList<>();
            for(FileAttach thisFile: tutorials.get()){
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                    .id(thisFile.getId())
                    .fileName(thisFile.getFileName())
                    .filePath(thisFile.getFilePath())
                    .priority(thisFile.getPriority())
                    .fileSize(thisFile.getFileSize())
                    .externalLink(thisFile.getExternalLink())
                    .build();
                if(thisFile.getObjectType().equals(FileAttachTypeEnum.VIDEO_GUIDE.value)){
                    attachResDTO.setTypeEnum(FileAttachTypeEnum.VIDEO_GUIDE);
                    videos.add(attachResDTO);
                } else {
                    attachResDTO.setTypeEnum(FileAttachTypeEnum.DOCUMENT_GUIDE);
                    documents.add(attachResDTO);
                }
            }
            serviceDTO.setVideoGuide(videos);
            serviceDTO.setDocGuide(documents);
            serviceDTO.setGuideVisible(
                !tutorials.get().isEmpty() ? tutorials.get().get(0).getVisible() != null ? tutorials.get().get(0)
                    .getVisible() : 1 : 1);
        }

    }

    /**
     *
     */
    private ServiceDescResDTO findServiceDraftDescById(ServiceDraft serviceDraft) {

        //validate serviceDraft
        if (Objects.isNull(serviceDraft) || !Objects.equals(serviceDraft.getDeletedFlag(), DELETE_FLAG)) {
            throw exceptionFactory.resourceNotFound(Resources.SERVICES, ErrorKey.DRAFT_ID);
        }

        // check quyen
        handleAccessDenied(serviceDraft.getUserId());

        ServiceDescResDTO serviceDTO = getServiceOrServiceDraft(null, serviceDraft, true); //draft

        //set support information
        setSupportInfoServiceDraft(serviceDraft, serviceDTO);

        serviceDTO.setApproveFirst(
                   Objects.isNull(serviceDraft.getUpdateFeatureReason())
                && Objects.isNull(serviceDraft.getUpdateSnapReason())
                && Objects.isNull(serviceDraft.getUpdateBaseInfoReason())
                && Objects.isNull(serviceDraft.getUpdateTechReason())
                ? YesNoEnum.YES.name() : YesNoEnum.NO.name());

        serviceDTO.setStatusBrowsing(HAVE_BROWSED);

        serviceDTO.setId(serviceDraft.getServiceId());

        return serviceDTO;
    }

    @Override
    public ServiceDTO findBasicById(long id, PricingDetailInputEnum type, ServiceDraft serviceDraft, String portalType) {
        if (Objects.equals(PricingDetailInputEnum.PROCESSING, type) && Objects.nonNull(serviceDraft)) {
            return findServiceDraftBasicById(serviceDraft, portalType);
        }

        //validate
        ServiceEntity currentService = getCurrentService(id);
        this.validateServicePermissionDev(Objects.nonNull(currentService) ? Collections.singletonList(currentService) : new ArrayList<>());
        validateAccessService(currentService.getUserId(), currentService.getCreatedBy());
        ServiceDTO serviceDTO = getServiceDraftBasicOrService(currentService, null, false, portalType); //service
        serviceDTO.setApproveFirst(
            Objects.nonNull(serviceDraft)
                && Objects.isNull(serviceDraft.getUpdateFeatureReason())
                && Objects.isNull(serviceDraft.getUpdateSnapReason())
                && Objects.isNull(serviceDraft.getUpdateBaseInfoReason())
                && Objects.isNull(serviceDraft.getUpdateTechReason())
                ? YesNoEnum.YES.name() : YesNoEnum.NO.name());

        boolean hasApproved = serviceRepository.checkDraftApprovedByServiceId(currentService.getId());
        serviceDTO.setStatusBrowsing(
            (!Objects.equals(currentService.getApprove(), ServiceApproveStatusEnum.APPROVED) && !hasApproved) ? NEVER_BROWSED : HAVE_BROWSED);
        // nếu là ứng dụng -> lấy thông tin api cung cấp
        if (Objects.nonNull(currentService.getServiceTypeApplication())) {
            serviceDTO.setPermissionIds(getAppRoleIdsByApiKey(currentService.getApiKey()));
            serviceDTO.setCategoriesApp(Arrays.stream(currentService.getCategoriesApp()).collect(Collectors.toList()));
        }

        return serviceDTO;
    }

    /**
     * findServiceDraftBasicById
     */
    private ServiceDTO findServiceDraftBasicById(ServiceDraft serviceDraft, String portalType) {

        // check quyen
        validateAccessService(serviceDraft.getUserId(), serviceDraft.getCreatedBy());
        ServiceDTO serviceDTO = getServiceDraftBasicOrService(null, serviceDraft, true, portalType); //draft

        serviceDTO.setId(serviceDraft.getServiceId());
        serviceDTO.setApproveFirst(
            Objects.isNull(serviceDraft.getUpdateFeatureReason())
                && Objects.isNull(serviceDraft.getUpdateSnapReason())
                && Objects.isNull(serviceDraft.getUpdateBaseInfoReason())
                && Objects.isNull(serviceDraft.getUpdateTechReason())
                ? YesNoEnum.YES.name() : YesNoEnum.NO.name());

        serviceDTO.setStatusBrowsing(HAVE_BROWSED);
        // nếu là ứng dụng -> lấy thông tin api cung cấp
        if (Objects.nonNull(serviceDraft.getServiceTypeApplication())) {
            // TH chưa duyệt sẽ lưu theo 1 apikey khác, và apikay khác đó lưu vào trường specifications bảng service
            serviceDTO.setPermissionIds(getAppRoleIdsByApiKey(Objects.nonNull(serviceDraft.getSpecifications()) ?
                    serviceDraft.getSpecifications() :
                    serviceDraft.getApiKey()));
            serviceDTO.setCategoriesApp(Arrays.stream(serviceDraft.getCategoriesApp()).collect(Collectors.toList()));
        }

        return serviceDTO;
    }

    /**
     * getServiceDraftBasicOrService
     */
    private ServiceDTO getServiceDraftBasicOrService(ServiceEntity serviceEntity, ServiceDraft serviceDraft, Boolean isDraft, String portalType) {
        if (isDraft) {
            ServiceEntity serviceEntityNew = new ServiceEntity();
            BeanUtils.copyProperties(serviceDraft, serviceEntityNew);
            ServiceDTO serviceDTO = serviceMapper.toDto(serviceEntityNew);
            Long manufacturerId = serviceDraft.getManufacture();
            Long providerId = serviceDraft.getUserId();
            serviceDTO.setLanguage(StringUtils.stripAll(StringUtils.split(serviceDTO.getLanguageType(), ",")));
            setFileAttach(serviceDTO, true);
            setFeatures(serviceDTO, true);
            ServiceEntity currentService = getCurrentService(serviceDraft.getServiceId());
            serviceDTO.setDisplayed(Objects.requireNonNull(ServiceStatusEnum.findByStatusId(currentService.getStatus())).name());
            setManufacturer(manufacturerId, serviceDTO);
            setProvider(providerId, serviceDTO);
            serviceDTO.setVariantApply(serviceDraft.getVariantApply());
            serviceDTO.setWarrantyInfo(serviceDraft.getWarrantyPolicy());
            serviceDTO.setShippingInfo(serviceDraft.getDeliveryInfo());
            serviceDTO.setInventoryInfo(serviceDraft.getInventoryConfig());
            serviceDTO.setSku(serviceDraft.getSku());
            serviceDTO.setSuggestionType(currentService.getSuggestionType());
            if(currentService.getSuggestionType() != null && currentService.getSuggestionType() == 1){
                List<ServiceSuggestionDTO> suggestionDTOS = serviceSuggestionService.getAllServiceSuggestionsByService(currentService.getId(),1,portalType);
                serviceDTO.setServiceSuggestionList(suggestionDTOS);
            }
            serviceDTO.setServiceTopics(topicFaqService.getListTopicByService(currentService.getId(), ServiceEnum.SERVICE.getValue()));
            Long layoutId = serviceDraft.getCreationLayoutId();
            layoutId = (layoutId != null) ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.SERVICE.getValue());
            List<CustomFieldValueDTO> lstFieldValueDTO = customFieldManager
                .getListFieldDraftValue(layoutId, EntityTypeEnum.SERVICE.getValue(), serviceDraft.getId());
            if (lstFieldValueDTO.isEmpty()) {
                lstFieldValueDTO = customFieldManager.getListFieldValue(layoutId, EntityTypeEnum.SERVICE.getValue(), serviceDraft.getServiceId());
            }
            serviceDTO.setCreationLayoutId(layoutId);
            serviceDTO.setLstCustomField(lstFieldValueDTO);
            serviceDTO.setServiceTypeApplication(serviceDraft.getServiceTypeApplication());
            serviceDTO.setPaymentMethod(PaymentMethodEnum.fromValue(serviceDraft.getPaymentMethod()));
            serviceDTO.setInstallationConfiguration(serviceDraft.getInstallationConfiguration());
            // Lấy danh sách thông tin danh mục của dịch vụ
            serviceDTO.setLstCategory(categoriesRepository.getCategoryDetailByServiceDraftId(serviceDraft.getId()));

            // merge với api /api/portal/services/{id}/description/{type}
            serviceDTO.setSeoList(getServiceSeoList(serviceDraft.getServiceId()));
            getDescriptionInfo(serviceDTO, serviceDraft, null, true);
            return serviceDTO;
        } else {
            ServiceDTO serviceDTO = serviceMapper.toDto(serviceEntity);
            Long manufacturerId = serviceEntity.getManufacture();
            Long providerId = serviceEntity.getUserId();
            serviceDTO.setDisplayed(Objects.requireNonNull(ServiceStatusEnum.findByStatusId(serviceDTO.getStatus())).name());
            setManufacturer(manufacturerId, serviceDTO);
            setProvider(providerId, serviceDTO);
            serviceDTO.setVariantApply(serviceEntity.getVariantApply());
            serviceDTO.setWarrantyInfo(serviceEntity.getWarrantyPolicy());
            serviceDTO.setShippingInfo(serviceEntity.getDeliveryInfo());
            serviceDTO.setInventoryInfo(serviceEntity.getInventoryConfig());
            serviceDTO.setSku(serviceEntity.getSku());
            serviceDTO.setLanguage(StringUtils.stripAll(StringUtils.split(serviceDTO.getLanguageType(), ",")));
            setFileAttach(serviceDTO, false);
            setFeatures(serviceDTO, false);
            if (Objects.nonNull(serviceEntity.getServiceOwnerPartner())) {
                serviceDTO.setServiceOwnerPartner(serviceEntity.getServiceOwnerPartner());
            }
            if (Objects.nonNull(serviceEntity.getServiceOwnerVNPT())) {
                serviceDTO.setServiceOwnerVNPT(serviceEntity.getServiceOwnerVNPT());
            }
            serviceDTO.setSuggestionType(serviceEntity.getSuggestionType());
            if(serviceEntity.getSuggestionType() != null && serviceEntity.getSuggestionType() == 1){
                List<ServiceSuggestionDTO> suggestionDTOS = serviceSuggestionService.getAllServiceSuggestionsByService(serviceEntity.getId(),1,portalType);
                serviceDTO.setServiceSuggestionList(suggestionDTOS);
            }
            serviceDTO.setServiceTopics(topicFaqService.getListTopicByService(serviceEntity.getId(), ServiceEnum.SERVICE.getValue()));
            Long layoutId = serviceEntity.getCreationLayoutId();
            layoutId = (layoutId != null) ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.SERVICE.getValue());
            List<CustomFieldValueDTO> lstFieldValueDTO = customFieldManager
                .getListFieldValue(layoutId, EntityTypeEnum.SERVICE.getValue(), serviceEntity.getId());
            serviceDTO.setCreationLayoutId(layoutId);
            serviceDTO.setLstCustomField(lstFieldValueDTO);
            serviceDTO.setServiceTypeApplication(serviceEntity.getServiceTypeApplication());
            serviceDTO.setPaymentMethod(PaymentMethodEnum.fromValue(serviceEntity.getPaymentMethod()));
            serviceDTO.setInstallationConfiguration(serviceEntity.getInstallationConfiguration());
            // Lấy danh sách thông tin danh mục của dịch vụ
            serviceDTO.setLstCategory(categoriesRepository.getCategoryDetailByServiceId(serviceDTO.getId()));

            // merge với api /api/portal/services/{id}/description/{type}
            serviceDTO.setSeoList(getServiceSeoList(serviceEntity.getId()));
            getDescriptionInfo(serviceDTO, null, serviceEntity, false);
            return serviceDTO;
        }
    }

    private void setManufacturer(Long manufacturerId, ServiceDTO serviceDTO) {
        if (Objects.nonNull(manufacturerId)) {
            serviceDTO.setManufacturerDTO(manufacturerRepository.getManufacturerById(manufacturerId));
        }
    }

    private void setProvider(Long providerId, ServiceDTO serviceDTO) {
        if (Objects.nonNull(providerId)) {
            serviceDTO.setProviderDTO(userRepository.getUserNameAndIdById(providerId));
        }
    }

    @Override
    public Page<ServiceResponseDTO> findAllForDev(ServiceSearchDTO searchDTO, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getLoggedInUser();
        if (Objects.isNull(searchDTO.getCustomerCode()))
            searchDTO.setCustomerCode(CustomerTypeEnum.UNSET);
        if (Objects.isNull(searchDTO.getAllowMultiSub()))
            searchDTO.setAllowMultiSub(-1);
        if (Objects.isNull(searchDTO.getSearchText()))
            searchDTO.setSearchText("");
        if (Objects.isNull(searchDTO.getNameText()))
            searchDTO.setNameText("");

        boolean isApplication = Objects.equals(searchDTO.getType(), 1);

        searchDTO.setCateIds(new HashSet<>((Collections.singletonList(searchDTO.getCategoriesId()))));

        if (userLogin.getParentId() == -1) {
            if (isApplication) {
                return serviceRepository.searchApplication(searchDTO, userLogin.getId(), NON_USER_ID, pageable);
            }
            return serviceRepository.searchServices(searchDTO, userLogin.getId(), NON_USER_ID, pageable);
        }
        if (isApplication) {
            return serviceRepository.searchApplication(searchDTO, userLogin.getParentId(), NON_USER_ID, pageable);
        }
        return serviceRepository.searchServices(searchDTO, userLogin.getParentId(), NON_USER_ID, pageable);

    }

    @Override
    public List<CustomerTypeDTO> getCustomerType(String name) {
        return customerTypeRepository.getAllCustomerTypeByName(name);
    }
    @Override
    public PricingAndAddonRelationDTO checkCanRemoveCustomerType(Long serviceOrComboId , String customerType , String checkType){
        // check have service
        PricingAndAddonRelationDTO pricingAndAddonRelationDTO = new PricingAndAddonRelationDTO();

        if( checkType.equals("SERVICE") ){
            List<PricingDraft> pricingDraftList = pricingDraftRepository.findByServiceIdAndCustomerType(serviceOrComboId , customerType);
            List<AddonDraft> addonDraftList = addonDraftRepository.findAllByServiceIdAndCustomerTypeCode(serviceOrComboId , customerType);
            pricingAndAddonRelationDTO.setPricingDraftList(pricingDraftList);
            pricingAndAddonRelationDTO.setAddonDraftList(addonDraftList);
        }
        else{
            val listComboPlan = comboPlanRepository.getComboPlanByComboIdAndCustomerTypeCode(serviceOrComboId,customerType );
            pricingAndAddonRelationDTO.setComboPlanList(listComboPlan);
        }

        return pricingAndAddonRelationDTO;

    }

    @Override
    @Transactional
    public BaseResponseDTO updateBaseInfo(ServiceUpdateDTO paramDto) {
        String languageValid = validateLanguage(paramDto.getLanguage());
        ServiceEntity service = getCurrentService(paramDto.getId());
        this.validateServicePermissionDev(Objects.nonNull(service) ? Collections.singletonList(service) : new ArrayList<>());
        // neu ma khac voi ma hien tai thi kiem tra ma ton tai
        if (!StringUtils.equals(
                StringUtils.trim(paramDto.getName()),
                StringUtils.trim(service.getServiceName()))) {
            if (Objects.nonNull(paramDto.getServiceTypeApplication())) {
                validateAppName(paramDto.getName());
            } else validateServiceNameWithDeleteFlag(paramDto.getName());
        }
        if (!StringUtils.equals(
                StringUtils.trim(paramDto.getSku()),
                StringUtils.trim(service.getSku()))) {
            validateSku(paramDto.getSku());
        }
        if (Objects.nonNull(paramDto.getCategoriesId())) {
            Optional<Category> cateOpt = categoriesRepository.findById(paramDto.getCategoriesId());
            //check ton tai ma the loai
            if (!cateOpt.isPresent() && Objects.isNull(paramDto.getServiceTypeApplication())) {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
                throw new ResourceNotFoundException(message, Resources.CATEGORY, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            }
        }

        if (!paramDto.getLstCategoryId().isEmpty()) {
            List<Category> lstCategory = categoriesRepository.findAllByIdIn(paramDto.getLstCategoryId());
            //check ton tai ma the loai
            if (lstCategory.size() < paramDto.getLstCategoryId().size() && Objects.isNull(paramDto.getServiceTypeApplication())) {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
                throw new ResourceNotFoundException(message, Resources.CATEGORY, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            }
        }

        // Bắt buộc nhập lý do cập nhật
        if (Objects.equals(ServiceApproveStatusEnum.APPROVED, service.getApprove()) && Objects.isNull(paramDto.getReason())) {
            throw exceptionFactory.badRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, Resources.SERVICES, ErrorKey.UPDATE_REASON);
        }

        if (Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.SAAS) && !(Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL))) {
            if (StringUtils.isBlank(paramDto.getUrlSetup())) {
                throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.URL_SETUP, new String[]{ErrorKey.Services.URL_SETUP});
            } else if (StringUtils.isBlank(paramDto.getTokenSPDV())) {
                throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.TOKEN_SPDV, new String[]{ErrorKey.Services.TOKEN_SPDV});
            } else if (StringUtils.isBlank(paramDto.getUrl())) {
                throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.URL, new String[]{ErrorKey.Services.URL});
            } else if (Objects.isNull(paramDto.getLanguage()) || paramDto.getLanguage().length == 0) {
                throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.LANGUAGE, new String[]{ErrorKey.Services.LANGUAGE});
            }
        } else if (Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.VNPT) && !(Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL))) {
            if (StringUtils.isBlank(paramDto.getUrl())) {
                throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.URL, new String[]{ErrorKey.Services.URL});
            } else if (Objects.isNull(paramDto.getLanguage()) || paramDto.getLanguage().length == 0) {
                throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.LANGUAGE, new String[]{ErrorKey.Services.LANGUAGE});
            }
        }
        if (Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.VNPT) || Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.OTHER)) {
            if (StringUtils.isBlank(paramDto.getServiceTypeId()) && !(Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL))) {
                throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.SERVICE_TYPE_ID, new String[]{ErrorKey.Services.SERVICE_TYPE});
            }
        }
        //Nếu service là service order thỳ serviceType và serviceCode phải là kiểu số
        if (Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.OTHER) && paramDto.getServiceCode() !=null) {
            try {
                Long.valueOf(paramDto.getServiceCode());
            } catch (Exception e) {
                String msg = messageSource.getMessage(MessageKeyConstant.INVALID_DATA,
                    new Long[] { service.getId() }, LocaleContextHolder.getLocale());
                throw new BadRequestException(msg, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.Services.SERVICE_CODE, MessageKeyConstant.SERVICE_CODE_INVALID_DATA);
            }
            if (!(Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL))) {
                try {
                    Long.valueOf(paramDto.getServiceTypeId());
                } catch (Exception e) {
                    String msg = messageSource.getMessage(MessageKeyConstant.INVALID_DATA,
                            new Long[]{service.getId()}, LocaleContextHolder.getLocale());
                    throw new BadRequestException(msg, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.Services.SERVICE_TYPE, MessageKeyConstant.SERVICE_TYPE_INVALID_DATA);
                }
            }
        }

        //========================================== Update cho service sau lần đầu duyệt =======================================================
        ServiceDraft serviceDraft = findByServiceId(service.getId());
        if (Objects.equals(ServiceApproveStatusEnum.APPROVED, service.getApprove())) {
            paramDto.setDisplayed(ServiceStatusEnum.VISIBLE);
            if (Objects.nonNull(serviceDraft) && Objects.equals(ServiceApproveStatusEnum.APPROVED, serviceDraft.getApprove())) {
                ServiceDraft serviceDraftNew = cloneDataService(service, ActionEnum.UPDATE);
                updateServiceDraft(paramDto, serviceDraftNew, languageValid);
            } else if (Objects.nonNull(serviceDraft)) {
                updateServiceDraft(paramDto, serviceDraft, languageValid);
            }
            return new BaseResponseDTO(service.getId());
        }
        if (Objects.nonNull(paramDto.getServiceTypeApplication())) {
            Long[] categoriesApp = paramDto.getCategoriesApp().toArray(new Long[0]);
            service.setCategoriesApp(categoriesApp);
            service.setServiceTypeApplication(paramDto.getServiceTypeApplication());

            updateAppRoleByApiKey(service.getApiKey(), paramDto.getPermissionIds());
        }
        service.setId(paramDto.getId());
        service.setServiceName(StringUtils.trim(paramDto.getName()));
        service.setCategoriesId(paramDto.getCategoriesId());
        service.setStatus(paramDto.getDisplayed().value);
        service.setApprove(ServiceApproveStatusEnum.UNAPPROVED);
        service.setUrlService(StringUtils.trim(paramDto.getUrl()));
        service.setUrlServiceStatus(paramDto.getUrlServiceStatus());
        service.setLanguageType(languageValid);
        service.setEmail(paramDto.getEmail());
        service.setPhoneNumber(paramDto.getPhoneNumber());
        service.setUrlSetup(paramDto.getUrlSetup());
        service.setTokenSPDV(paramDto.getTokenSPDV());
        service.setServiceOwner(Objects.nonNull(paramDto.getServiceOwner()) ? paramDto.getServiceOwner().getValue() : null);
        // ứng dụng: là ON
        service.setOnOsType(Objects.nonNull(paramDto.getOnOsType()) ? paramDto.getOnOsType() : OnOsTypeEnum.ON);
        service.setProviderType(Objects.nonNull(paramDto.getProviderType()) ? paramDto.getProviderType() : ProviderTypeEnum.VNPT);
        service.setServiceCode(paramDto.getServiceCode());
        service.setUrlPreOrder(paramDto.getUrlPreOrder());
        service.setServiceType(Objects.isNull(paramDto.getServiceTypeId()) ? "" : paramDto.getServiceTypeId());
        service.setModifiedBy(AuthUtil.getCurrentUserId());
        service.setCustomerTypeCode(CollectionUtils.isEmpty(paramDto.getCustomerTypeCode()) ?
            null : paramDto.getCustomerTypeCode().stream().filter(Objects::nonNull).map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));
        service.setAllowMultiSub(paramDto.getAllowMultiSub());
        service.setRegisterEcontract(paramDto.getRegisterEcontract());
        service.setTax(paramDto.getTax());
        service.setSetupFee(paramDto.getSetupFee());
        service.setProductType(paramDto.getProductType());
        service.setPrice(paramDto.getPrice());
        service.setDescription(paramDto.getDescription());
        service.setSpecifications(paramDto.getSpecifications());
        service.setPaymentMethod(paramDto.getPaymentMethodValue());
        service.setManufacture(paramDto.getManufacturerId());
        service.setUserId(service.getPortalType().equals(PortalType.DEV) ? AuthUtil.getCurrentParentId() : paramDto.getProviderId());
        service.setSku(paramDto.getSku());
        // update thông tin bổ sung
        if (Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || paramDto.getClassification().equals(
            ProductClassificationEnum.PHYSICAL)) {
            if (Objects.nonNull(paramDto.getInventoryInfo())) {
                service.setInventoryConfig(paramDto.getInventoryInfo());
            }
            if (Objects.nonNull(paramDto.getShippingInfo())) {
                service.setDeliveryInfo(paramDto.getShippingInfo());
            }
            if (Objects.nonNull(paramDto.getWarrantyInfo())) {
                service.setWarrantyPolicy(paramDto.getWarrantyInfo());
            }
            if (Objects.nonNull(paramDto.getVariantApply())) {
                service.setVariantApply(paramDto.getVariantApply());
            }
        }
        // update danh mục
        updateServiceCategory(service.getId(), new HashSet<>(paramDto.getLstCategoryId()));
        int installationConfiguration = Objects.nonNull(paramDto.getInstallationConfiguration()) ? paramDto.getInstallationConfiguration() : 0;
        service.setInstallationConfiguration(
            Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) ? installationConfiguration : null);
        // update icon and banner
        updateIconAndBanner(paramDto);
        // Lưu thông tin vào custom_field_value
        ServiceCreateDTO requestDto = new ServiceCreateDTO();
        BeanUtils.copyProperties(paramDto,requestDto);
        // Cập nhật custom field của dịch vụ
        if(serviceDraft != null) {
            updateDraftCustomFieldValue(paramDto, EntityTypeEnum.SERVICE.getValue(), serviceDraft.getId());
        } else {
            saveCustomFieldValue(requestDto, EntityTypeEnum.SERVICE.getValue(), paramDto.getId());
        }
        return new BaseResponseDTO(service.getId());
    }

    /**
     * Cập nhật service draft
     */
    private void updateServiceDraft(ServiceUpdateDTO paramDto, ServiceDraft serviceDraft, String languageValid){
        serviceDraft.setServiceName(StringUtils.trim(paramDto.getName()));
        serviceDraft.setCategoriesId(paramDto.getCategoriesId());
        serviceDraft.setStatus(paramDto.getDisplayed().value);
        serviceDraft.setUrlService(StringUtils.trim(paramDto.getUrl()));
        serviceDraft.setUrlServiceStatus(paramDto.getUrlServiceStatus());
        serviceDraft.setLanguageType(languageValid);
        serviceDraft.setEmail(paramDto.getEmail());
        serviceDraft.setPhoneNumber(paramDto.getPhoneNumber());
        serviceDraft.setUrlSetup(paramDto.getUrlSetup());
        serviceDraft.setTokenSPDV(paramDto.getTokenSPDV());
        serviceDraft.setServiceOwner(Objects.nonNull(paramDto.getServiceOwner()) ? paramDto.getServiceOwner().getValue() : null);
        serviceDraft.setOnOsType(paramDto.getOnOsType());
        serviceDraft.setProviderType(paramDto.getProviderType());
        serviceDraft.setServiceCode(paramDto.getServiceCode());
        serviceDraft.setUrlPreOrder(paramDto.getUrlPreOrder());
        serviceDraft.setServiceType(Objects.isNull(paramDto.getServiceTypeId()) ? "" : paramDto.getServiceTypeId());
        serviceDraft.setModifiedBy(AuthUtil.getCurrentUserId());
        serviceDraft.setUpdateBaseInfoReason(paramDto.getReason());
        serviceDraft.setReason(paramDto.getReason());
        serviceDraft.setApprove(ServiceApproveStatusEnum.UNAPPROVED);
        serviceDraft.setServiceId(paramDto.getId());
        serviceDraft.setCustomerTypeCode(CollectionUtils.isEmpty(paramDto.getCustomerTypeCode()) ?
            null : paramDto.getCustomerTypeCode().stream().filter(Objects::nonNull).map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));
        serviceDraft.setAllowMultiSub(paramDto.getAllowMultiSub());
        serviceDraft.setRegisterEcontract(paramDto.getRegisterEcontract());
        serviceDraft.setTax(paramDto.getTax());
        serviceDraft.setSetupFee(paramDto.getSetupFee());
        serviceDraft.setProductType(paramDto.getProductType());
        serviceDraft.setClassification(paramDto.getClassification());
        serviceDraft.setPrice(paramDto.getPrice());
        serviceDraft.setDescription(paramDto.getDescription());
        serviceDraft.setSpecifications(paramDto.getSpecifications());
        serviceDraft.setPaymentMethod(paramDto.getPaymentMethodValue());
        serviceDraft.setManufacture(paramDto.getManufacturerId());
        serviceDraft.setUserId(serviceDraft.getPortalType().equals(PortalType.DEV) ? AuthUtil.getCurrentParentId() : paramDto.getProviderId());
        serviceDraft.setSku(paramDto.getSku());
        int installationConfiguration = Objects.nonNull(paramDto.getInstallationConfiguration()) ? paramDto.getInstallationConfiguration() : 0;
        serviceDraft.setInstallationConfiguration(
                (Objects.equals(serviceDraft.getProductType(), ServiceProductTypeEnum.DEVICE) || serviceDraft.getClassification().equals(
                    ProductClassificationEnum.PHYSICAL)) ? installationConfiguration : null);
        // TH ứng dụng mượn tạm trường setSpecifications để sử dụng lưu lstPermissionId draft
        if (Objects.nonNull(paramDto.getServiceTypeApplication())) {
            serviceDraft.setServiceTypeApplication(paramDto.getServiceTypeApplication());
            serviceDraft.setCategoriesApp(paramDto.getCategoriesApp().toArray(new Long[0]));

            String apiKey = UUID.randomUUID().toString();
            serviceDraft.setSpecifications(apiKey);
            updateAppRoleByApiKey(apiKey, paramDto.getPermissionIds());
        }
        if (Objects.equals(serviceDraft.getProductType(), ServiceProductTypeEnum.DEVICE) || serviceDraft.getClassification().equals(
            ProductClassificationEnum.PHYSICAL)) {
            if (Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || paramDto.getClassification().equals(
                ProductClassificationEnum.PHYSICAL)) {
                if (Objects.nonNull(paramDto.getInventoryInfo())) {
                    serviceDraft.setInventoryConfig(paramDto.getInventoryInfo());
                }
                if (Objects.nonNull(paramDto.getShippingInfo())) {
                    serviceDraft.setDeliveryInfo(paramDto.getShippingInfo());
                }
                if (Objects.nonNull(paramDto.getWarrantyInfo())) {
                    serviceDraft.setWarrantyPolicy(paramDto.getWarrantyInfo());
                }
                if (Objects.nonNull(paramDto.getVariantApply())) {
                    serviceDraft.setVariantApply(paramDto.getVariantApply());
                }
            }
        }

        serviceDraft = serviceDraftRepository.save(serviceDraft);

        // update icon and banner
        updateIconAndBannerServiceDraft(paramDto, serviceDraft);

        //Cập nhật danh mục
        updateServiceDraftCategory(serviceDraft.getId(), new HashSet<>(paramDto.getLstCategoryId()));

        // Cập nhật custom field của dịch vụ
        updateDraftCustomFieldValue(paramDto, EntityTypeEnum.SERVICE.getValue(), serviceDraft.getId());
    }

    private void updateServiceDraftCategory(Long serviceDraftId, Set<Long> lstCategoryId) {
        if (!lstCategoryId.isEmpty()) {
            servicesCategoriesRepository.deleteAllByServiceDraftId(serviceDraftId);
            List<ServicesCategories> lstServiceCategories = new ArrayList<>();
            for (Long categoryId : lstCategoryId) {
                lstServiceCategories.add(ServicesCategories.builder().categoryId(categoryId).serviceDraftId(serviceDraftId).build());
            }
            servicesCategoriesRepository.saveAll(lstServiceCategories);
        }
    }

    private void updateServiceCategory(Long serviceId, Set<Long> lstCategoryId) {
        if (!lstCategoryId.isEmpty()) {
            servicesCategoriesRepository.deleteAllByServiceId(serviceId);
            List<ServicesCategories> lstServiceCategories = new ArrayList<>();
            for (Long categoryId : lstCategoryId) {
                lstServiceCategories.add(ServicesCategories.builder().categoryId(categoryId).serviceId(serviceId).build());
            }
            servicesCategoriesRepository.saveAll(lstServiceCategories);
        }
    }

    /**
     * Cập nhật icon + banner cho Service Draft
     */
    private void updateIconAndBannerServiceDraft(ServiceUpdateDTO paramDto, ServiceDraft serviceDraft) {
        boolean changeIcon = fileAttachRepository.existsByIdAndServicesDraftIdIsNotNull(paramDto.getIcon());
        if (paramDto.getIcon() != null && !changeIcon) {
            fileAttachRepository.clearByServiceDraftId(serviceDraft.getId(), FileAttachTypeEnum.AVATAR.value);
            fileAttachRepository.assignToServiceDraft(paramDto.getIcon(), serviceDraft.getId(), FileAttachTypeEnum.AVATAR.value);
        }
        boolean changeBanner = fileAttachRepository.existsByIdAndServicesDraftIdIsNotNull(paramDto.getBanner());
        if (paramDto.getBanner() != null && !changeBanner) {
            fileAttachRepository.clearByServiceDraftId(serviceDraft.getId(), FileAttachTypeEnum.BANNER.value);
            fileAttachRepository.assignToServiceDraft(paramDto.getBanner(), serviceDraft.getId(), FileAttachTypeEnum.BANNER.value);
        }
        Long videoId = paramDto.getVideo();
        boolean isVideoChanged = !fileAttachRepository.existsByIdAndServicesDraftIdIsNotNull(videoId);

        if (isVideoChanged) {
            fileAttachRepository.clearByServiceDraftId(serviceDraft.getId(), FileAttachTypeEnum.INTRO.value);

            if (videoId != null) {
                fileAttachRepository.assignVideoToServiceDraft(videoId, serviceDraft.getId(), FileAttachTypeEnum.INTRO.value,
                    FileTypeEnum.VIDEO.value);
            }
        }

        if (!CollectionUtils.isEmpty(paramDto.getIconService())) {
            fileAttachRepository.clearByServiceDraftId(serviceDraft.getId(), FileAttachTypeEnum.AVATAR_DEVICE.value);
            fileAttachRepository.assignListToServiceDraft(paramDto.getIconService(), serviceDraft.getId(),
                FileAttachTypeEnum.AVATAR_DEVICE.value);
        }
    }

    public void initBaseInfo(ServiceUpdateDTO paramDto) {
        String languageValid = validateLanguage(paramDto.getLanguage());
        ServiceEntity service = getCurrentService(paramDto.getId());
        // neu ma khac voi ma hien tai thi kiem tra ma ton tai
        if (!StringUtils.equals(
                StringUtils.trim(paramDto.getName()),
                StringUtils.trim(service.getServiceName()))) {
            validateServiceName(paramDto.getName());
        }
        if (Objects.isNull(paramDto.getServiceTypeApplication())) {
            Optional<Category> cateOpt = categoriesRepository.findById(paramDto.getCategoriesId());
            //check ton tai ma the loai
            if (!cateOpt.isPresent()) {
                String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
                throw new ResourceNotFoundException(message, Resources.CATEGORY, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
            }
            if (Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.SAAS) && !(Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL))) {
                if (StringUtils.isBlank(paramDto.getUrlSetup())) {
                    throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.URL_SETUP, new String[]{ErrorKey.Services.URL_SETUP});
                } else if (StringUtils.isBlank(paramDto.getTokenSPDV())) {
                    throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.TOKEN_SPDV, new String[]{ErrorKey.Services.TOKEN_SPDV});
                } else if (StringUtils.isBlank(paramDto.getUrl())) {
                    throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.URL, new String[]{ErrorKey.Services.URL});
                } else if (Objects.isNull(paramDto.getLanguage()) || paramDto.getLanguage().length == 0) {
                    throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.LANGUAGE, new String[]{ErrorKey.Services.LANGUAGE});
                }
            } else if (Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.VNPT) && !(Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL))) {
                if (StringUtils.isBlank(paramDto.getUrl())) {
                    throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.URL, new String[]{ErrorKey.Services.URL});
                } else if (Objects.isNull(paramDto.getLanguage()) || paramDto.getLanguage().length == 0) {
                    throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.LANGUAGE, new String[]{ErrorKey.Services.LANGUAGE});
                }
            }
            if (Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.VNPT) || Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.OTHER)) {
                if (StringUtils.isBlank(paramDto.getServiceTypeId()) && !(Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL))) {
                    throw throwServiceBadRequest(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, ErrorKey.Services.SERVICE_TYPE, new String[]{ErrorKey.Services.SERVICE_TYPE});
                }
            }
            //Nếu service là service order thỳ serviceType và serviceCode phải là kiểu số
            if (Objects.equals(paramDto.getServiceOwner(), ServiceTypeEnum.OTHER) && paramDto.getServiceCode() !=null) {
                try {
                    Long.valueOf(paramDto.getServiceCode());
                } catch (Exception e) {
                    String msg = messageSource.getMessage(MessageKeyConstant.INVALID_DATA,
                        new Long[] { service.getId() }, LocaleContextHolder.getLocale());
                    throw new BadRequestException(msg, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.Services.SERVICE_CODE, MessageKeyConstant.SERVICE_CODE_INVALID_DATA);
                }
                if  (!(Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL))) {
                    try {
                        Long.valueOf(paramDto.getServiceTypeId());
                    } catch (Exception e) {
                        String msg = messageSource.getMessage(MessageKeyConstant.INVALID_DATA,
                            new Long[] { service.getId() }, LocaleContextHolder.getLocale());
                        throw new BadRequestException(msg, Resources.ORDER_SERVICE_RECEIVE, ErrorKey.Services.SERVICE_TYPE, MessageKeyConstant.SERVICE_TYPE_INVALID_DATA);
                    }
                }
            }
        }

        service.setId(paramDto.getId());
        service.setServiceName(StringUtils.trim(paramDto.getName()));
        service.setCategoriesId(paramDto.getCategoriesId());
        service.setStatus(Objects.nonNull(paramDto.getDisplayed()) ? paramDto.getDisplayed().value : ServiceStatusEnum.UNSET.value);
        service.setUrlService(StringUtils.trim(paramDto.getUrl()));
        service.setUrlServiceStatus(paramDto.getUrlServiceStatus());
        service.setLanguageType(languageValid);
        service.setEmail(paramDto.getEmail());
        service.setPhoneNumber(paramDto.getPhoneNumber());
        service.setUrlSetup(paramDto.getUrlSetup());
        service.setTokenSPDV(paramDto.getTokenSPDV());
        service.setServiceOwner(paramDto.getServiceOwner().getValue());
        if (Objects.nonNull(paramDto.getServiceOwnerPartner())) {
            service.setServiceOwnerPartner(paramDto.getServiceOwnerPartner().getValue());
        }
        // ứng dụng: là ON
        service.setOnOsType(Objects.nonNull(paramDto.getOnOsType()) ? paramDto.getOnOsType() : OnOsTypeEnum.ON);
        service.setProviderType(Objects.nonNull(paramDto.getProviderType()) ? paramDto.getProviderType() : ProviderTypeEnum.VNPT);
        service.setServiceCode(paramDto.getServiceCode());
        service.setDescription(paramDto.getDescription());
        service.setSpecifications(paramDto.getSpecifications());
        service.setUrlPreOrder(paramDto.getUrlPreOrder());
        service.setServiceType(Objects.isNull(paramDto.getServiceTypeId()) ? "" : paramDto.getServiceTypeId());
        service.setModifiedBy(AuthUtil.getCurrentUserId());
        service.setPaymentMethod(paramDto.getPaymentMethodValue());
        int installationConfiguration = Objects.nonNull(paramDto.getInstallationConfiguration()) ? paramDto.getInstallationConfiguration() : 0;
        service.setInstallationConfiguration(
            (Objects.equals(paramDto.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(paramDto.getClassification(), ProductClassificationEnum.PHYSICAL)) ? installationConfiguration : null);
        serviceMapper.toDto(serviceRepository.save(service));
        // Lưu thông tin danh mục
        updateServiceCategory(service.getId(), new HashSet<>(paramDto.getLstCategoryId()));
    }

    @Override
    public ServiceCreateResponseDTO createApplicationStepOne(CreateStepOneDTO createStepOneDTO) {
        validateAppName(createStepOneDTO.getServiceName());
        ServiceEntity service = new ServiceEntity();
        service.setServiceName(createStepOneDTO.getServiceName());
        service.setUrlService(createStepOneDTO.getUrl());
        service.setServiceTypeApplication(createStepOneDTO.getServiceTypeApplication());
        String apiKey = UUID.randomUUID().toString();
        String secretKey = UUID.randomUUID().toString();
        service.setApiKey(apiKey);
        service.setSecretKey(secretKey);
        service.setApprove(ServiceApproveStatusEnum.UNSET);
        service.setUserId(AuthUtil.getCurrentParentId());
        Date now = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        service.setModifiedBy(AuthUtil.getCurrentUserId());
        service.setCreatedBy(AuthUtil.getCurrentUserId());
        service.setCreatedAt(now);
        service.setModifiedAt(now);
        service.setStatus(ServiceStatusEnum.INVISIBLE.value);
        service.setPaymentMethod(PaymentMethodEnum.VNPTPAY.value);
        service.setAllowMultiSub(0);
        service.setServiceOwnerVNPT(2); // mặc định là 3rd
        service.setOnOsType(OnOsTypeEnum.ON); // ứng dụng là ON
        service.setProviderType(ProviderTypeEnum.THIRD_PARTY);
        String language = "0";
        String validateLanguage = validateLanguage(new String[]{language});
        service.setLanguageType(validateLanguage);
        // gán quyền
        Long[] categoriesApp = createStepOneDTO.getCategoriesApp().toArray(new Long[0]);
        service.setCategoriesApp(categoriesApp);

        updateAppRoleByApiKey(apiKey, createStepOneDTO.getPermissionIds());
        // Thêm thông tin cấu hình token ứng dụng trên WP
        String clientSecret = passwordEncoder.encode(secretKey);
        userRepository.saveAppClientDetail(apiKey, clientSecret, ObjectUtil.getOrDefault(createStepOneDTO.getAccessTokenValidity(), accessTokenValiditySeconds),
            ObjectUtil.getOrDefault(createStepOneDTO.getRefreshTokenValidity(), refreshTokenValiditySeconds));

        ServiceEntity save = serviceRepository.save(service);
        // set avatar;
        FileAttach file = fileAttachRepository.findById(createStepOneDTO.getIcon()).orElse(null);
        if (Objects.nonNull(file)) {
            file.setServiceId(save.getId());
            file.setPriority(0);
            file.setObjectType(FileAttachTypeEnum.AVATAR.value);
            fileAttachRepository.save(file);
        }

        String serviceName = StringUtil.replace(save.getServiceName());
        //lưu thông tin mặc định seo
        seoService
                .saveSeoDefault(serviceName, save.getServiceName() + TITLE_SERVICE, //service
                        Objects.nonNull(save.getDescription()) ? save.getSapoDescription() : SeoTypeCodeConstant.DESCRIPTION_CATEGORY,
                        null, save.getId(), CAU_HINH_DICH_VU, true);
        return ServiceCreateResponseDTO.builder().id(save.getId()).build();
    }

    @Override
    @Transactional
    public ServiceCreateResponseDTO create(ServiceCreateDTO serviceDTO, PortalType portalType) {
        if (Objects.nonNull(serviceDTO.getId())) { // neu da co id -> ung dung, can tao b2
            return createApplicationStepTwo(serviceDTO);
        }

        String VariantConfig = variantRepository.getVariantConfig(PRODUCTVARIANT);
        String validateLanguage = validateLanguage(serviceDTO.getLanguageType());
        if (Objects.nonNull(serviceDTO.getServiceTypeApplication())) {
            validateAppName(serviceDTO.getServiceName());
        } else validateServiceNameWithDeleteFlag(serviceDTO.getServiceName());
        validateCategoryId(serviceDTO);
        // nếu là sp vật lý thì mới kiểm tra sku
        if (Objects.equals(serviceDTO.getClassification(), ProductClassificationEnum.PHYSICAL)) {
            validateSku(serviceDTO.getSku());
        }
        // lưu thông tin chung và thông tin dich vụ (Bước 1 & 5)
        ServiceEntity currentService = ServiceEntity.builder()
            .serviceName(serviceDTO.getServiceName().trim())
            .languageType(validateLanguage)
            .categoriesId(serviceDTO.getCategoriesId())
            .approve(ServiceApproveStatusEnum.UNAPPROVED)
            .userId(portalType.equals(PortalType.DEV) ? AuthUtil.getCurrentParentId() : serviceDTO.getProviderId())
            .apiKey(UUID.randomUUID().toString())
            .secretKey(UUID.randomUUID().toString())
            .techVisible(serviceDTO.getTechVisible())
            .techLayout(serviceDTO.getTechLayout())
            .featureVisible(serviceDTO.getFeatureVisible())
            .suggestionType(serviceDTO.getSuggestionType())
            .creationLayoutId(serviceDTO.getCreationLayoutId())
            .tax(serviceDTO.getTax())
            .setupFee(serviceDTO.getSetupFee())
            .productType(serviceDTO.getProductType())
            .classification(serviceDTO.getClassification())
            .price(serviceDTO.getPrice())
            .description(serviceDTO.getDescription())
            .paymentMethod(serviceDTO.getPaymentMethodValue())
            .specifications(serviceDTO.getSpecifications())
            .attributeVariant(serviceDTO.getAttributeVariant())
            .installationConfiguration(serviceDTO.getInstallationConfiguration())
            .manufacture(serviceDTO.getManufacturerId())
            .portalType(portalType)
            .sku(serviceDTO.getSku())
            .build();

        currentService.setOnOsType(Objects.nonNull(serviceDTO.getOnOsType()) ? serviceDTO.getOnOsType() : OnOsTypeEnum.ON);
        currentService.setProviderType(Objects.nonNull(serviceDTO.getProviderType()) ? serviceDTO.getProviderType() : ProviderTypeEnum.VNPT);
        currentService.setStatus(ServiceStatusEnum.INVISIBLE.value);
        currentService.setDeletedFlag(EntitiesConstant.DeleteFlag.ACTIVE);
        currentService.setVariantConfig(VariantConfig);
        Date now = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        Long currentUserId = AuthUtil.getCurrentUserId();
        currentService.setModifiedBy(currentUserId);
        currentService.setCreatedBy(currentUserId);
        currentService.setCreatedAt(now);
        currentService.setModifiedAt(now);
        currentService.setAllowMultiSub(serviceDTO.getAllowMultiSub());
        currentService.setRegisterEcontract(serviceDTO.getRegisterEcontract());
        currentService.setCreatedSourceMigration(CreatedSourceMigration.ONE_SME);
        if (Objects.nonNull(serviceDTO.getServiceOwnerPartner())) {
            currentService.setServiceOwnerPartner(serviceDTO.getServiceOwnerPartner().getValue());
        }
        if (Objects.nonNull(serviceDTO.getServiceOwnerVNPT())) {
            currentService.setServiceOwnerVNPT(serviceDTO.getServiceOwnerVNPT().getValue());
        }
        if (currentService.getClassification().equals(ProductClassificationEnum.PHYSICAL)) {
            if (Objects.equals(serviceDTO.getProductType(), ServiceProductTypeEnum.DEVICE) || serviceDTO.getClassification().equals(
                ProductClassificationEnum.PHYSICAL)) {
                if (Objects.nonNull(serviceDTO.getInventoryInfo())) {
                    currentService.setInventoryConfig(serviceDTO.getInventoryInfo());
                }
                if (Objects.nonNull(serviceDTO.getShippingInfo())) {
                    currentService.setDeliveryInfo(serviceDTO.getShippingInfo());
                }
                if (Objects.nonNull(serviceDTO.getWarrantyInfo())) {
                    currentService.setWarrantyPolicy(serviceDTO.getWarrantyInfo());
                }
                if (Objects.nonNull(serviceDTO.getVariantApply())) {
                    currentService.setVariantApply(serviceDTO.getVariantApply());
                }
            }
        }

        initSupportInfo(currentService);
        currentService.setCustomerTypeCode(CollectionUtils.isEmpty(serviceDTO.getCustomerTypeCode()) ?
            null : serviceDTO.getCustomerTypeCode().stream().filter(Objects::nonNull).map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));
        ServiceEntity save = serviceRepository.save(currentService);
        if (!Objects.equals(serviceDTO.getClassification(), ProductClassificationEnum.PHYSICAL)) {
            save.setSapoDescription(serviceDTO.getSnapshots().get(0).getDescription());
        }

        if (Objects.nonNull(serviceDTO.getSeoReqDTO())) {
            //lưu thông tin seo
            saveSeoService(save.getId(), save.getCategoriesId(), CAU_HINH_DICH_VU, serviceDTO); //service
        } else {
            String serviceName = StringUtil.replace(save.getServiceName());
            //lưu thông tin mặc định seo
            seoService
                .saveSeoDefault(serviceName, save.getServiceName() + TITLE_SERVICE, //service
                    Objects.nonNull(save.getDescription()) ? save.getSapoDescription() : SeoTypeCodeConstant.DESCRIPTION_CATEGORY,
                    null, save.getId(), CAU_HINH_DICH_VU, true);
        }

        addServiceHistory(save, ApproveStatusEnum.UNAPPROVED, PortalType.DEV, null);

        // cập nhật thông tin cơ bản
        ServiceUpdateDTO basicDTO = new ServiceUpdateDTO();
        BeanUtils.copyProperties(serviceDTO,basicDTO);
        basicDTO.setId(currentService.getId());
        basicDTO.setName(currentService.getServiceName());
        basicDTO.setLanguage(serviceDTO.getLanguageType());
        initBaseInfo(basicDTO);

        // cập nhật thông tin mô tả
        ServiceUpdateDescDTO descDTO = ServiceUpdateDescDTO.builder()
            .id(currentService.getId())
            .icon(serviceDTO.getIcon())
            .iconService(serviceDTO.getIconService())
            .video(serviceDTO.getVideo())
            .banner(serviceDTO.getBanner())
            .snapshots(serviceDTO.getSnapshots())
            .description(serviceDTO.getDescription())
            .build();
        updateDescInfo(descDTO);

        //cập nhật thông tin công nghệ nổi bật
        updateTechInfo(serviceDTO.getTechnologies(), currentService.getId());
        //cập nhật thông tin tính năng
        updateFeatureInfo(serviceDTO.getFeatures(), currentService.getId(), null);
        currentService.setStatus(ServiceStatusEnum.INVISIBLE.value);

        //Thêm sản phẩm liên quan
        if (currentService.getSuggestionType() == 1) {
            serviceSuggestionService.createSuggestionService(currentService.getId(), serviceDTO.getServiceSuggestions());
        }

        if(serviceDTO.getServiceTopics() != null){
            topicFaqService.updateTopicService(currentService.getId(), ServiceEnum.SERVICE.getValue(), serviceDTO.getServiceTopics());
        }

        // Lưu thông tin vào custom_field_value
        saveCustomFieldValue(serviceDTO, EntityTypeEnum.SERVICE.getValue(), save.getId());

        //check cấu hình phê duyệt khi tạo dịch vụ
        //nếu check cấu hình trả về false thì duyệt luôn
        boolean isApproved = !checkConfigApproveCreateService(serviceDTO.getServiceOwner().getValue(), false);
        if(isApproved) {
            requestApprove(save.getId(), true);
            ServiceApproveDTO serviceApproveDTO = new ServiceApproveDTO(ServiceApproveStatusEnum.APPROVED, "Approved by configuration system");
            approveService(save.getId(), serviceApproveDTO, false);
        }

        // Lấy thông tin biến thể được tạo để gắn với gói
        if (Objects.equals(serviceDTO.getClassification(), ProductClassificationEnum.PHYSICAL) || Objects.equals(serviceDTO.getProductType(), ServiceProductTypeEnum.DEVICE)) {
            // lưu thông tin biến thể (Bước 2)
            updateAndCreateAttributeVariant (serviceDTO.getAttributes(), serviceDTO.getVariants(), save.getId(), portalType);
            // Lưu thông bảng giá (Bước 3)
            savePricing(serviceDTO.getPricingReqDTO(), save.getId(), portalType, serviceDTO.getCustomerTypeCode(), isApproved);
        }

        return ServiceCreateResponseDTO.builder().id(save.getId()).build();
    }

    @Description("Hàm lưu thông tin gói")
    private void savePricing (List<PricingReqDTO> lstPricing, Long serviceId, PortalType portalType, Set<CustomerTypeEnum> customerTypeCode, Boolean isApproved) {
        if (Objects.nonNull(lstPricing) && !CollectionUtils.isEmpty(lstPricing)) {
            List<PricingReqDTO> lstPricingCheckRecommend = lstPricing.stream()
                .filter(pricingReqDTO -> Objects.nonNull(pricingReqDTO.getRecommendedStatus()) && pricingReqDTO.getRecommendedStatus().equals(1))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(lstPricingCheckRecommend) && lstPricingCheckRecommend.size() > 1) {
                String message = messageSource
                        .getMessage(MessageKeyConstant.PRICING_RECOMMENDATION_LIMIT, SERVICE_MESSAGE,
                                LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.PRICING,
                        MessageKeyConstant.PRICING_RECOMMENDATION_LIMIT);
            }
            // validate và lưu thông tin pricing
            lstPricing.forEach(pricingReqDTO -> {
                try {
                    pricingReqDTO.setServiceId(serviceId);
                    pricingReqDTO.setPortalType(portalType);
                    pricingReqDTO.setCustomerTypeCode(customerTypeCode);
                    pricingService.createPricing(pricingReqDTO, true, isApproved);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            });

        }
    }

    private List<Long> getAppRoleIdsByApiKey(String apiKey) {
        List<Long> res = wpPermissionRepository.getIdsByApiKey3RD(apiKey);
        return CollectionUtils.isEmpty(res) ? new ArrayList<>() : res;
    }

    @Description("Hàm kiểm tra roleCode đã tồn tại trong bảng wp_role hay chưa")
    private boolean isExistsRoleCode(String roleCode) {
        Optional<WPRole> wpRole = wpRoleRepository
                .findByCodeAndDeletedFlag(roleCode, DeletedFlag.NOT_YET_DELETED.getValue());
        //Nếu wpRole đã tồn tại thì trả về true, ngược lại là false
        return wpRole.isPresent();
    }

    @Description("Hàm tạo role code (chỉ gồm chữ) ngẫu nhiên")
    private String generateRoleCode() {
        String roleCode;
        final int ROLE_CODE_LENGTH = 6;
        do {
            roleCode = RandomStringUtils.random(ROLE_CODE_LENGTH, true, false);
        }
        while (isExistsRoleCode(roleCode));
        return roleCode;
    }

    private void updateAppRoleByApiKey(String apiKey, List<Long> newLstPer) {
        WPRole wpRole = wpRoleRepository.findFirstByApiKeyAndType(apiKey, AppRoleTypeEnum.WORKPLACE_3RD_PARTY);
        if (Objects.isNull(wpRole)) {
            String code = generateRoleCode();
            wpRole = new WPRole();
            wpRole.setName(code);
            wpRole.setId(null);
            wpRole.setUserId(NON_USER_ID);
            wpRole.setCode(code);
            wpRole.setStatus(StatusEnum.ACTIVE.value);
            wpRole.setDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());
            wpRole.setCreatedBy(AuthUtil.getCurrentUserId());
            wpRole.setModifiedBy(AuthUtil.getCurrentUserId());
            wpRole.setCreatedAt(new Date());
            wpRole.setModifiedAt(new Date());
            wpRole.setApiKey(apiKey);
            wpRole.setType(AppRoleTypeEnum.WORKPLACE_3RD_PARTY);
            wpRole = wpRoleRepository.save(wpRole);
        }
        wpRolePermissionRepository.deleteAllByRoleCode(wpRole.getCode());
        if (!CollectionUtils.isEmpty(newLstPer)) {
            WPRole finalWpRole = wpRole;
            List<WPRolePermission> list = wpPermissionRepository.findAllByIdIn(newLstPer).stream().map(e -> {
                WPRolePermission res = new WPRolePermission();
                res.setRoleCode(finalWpRole.getCode());
                res.setPermissionCode(e.getCode());
                return res;
            }).collect(Collectors.toList());
            wpRolePermissionRepository.saveAll(list);
        }
    }

    private ServiceCreateResponseDTO createApplicationStepTwo(ServiceCreateDTO serviceDTO) {
        ServiceEntity currentService = getCurrentService(serviceDTO.getId());
        Long[] categoriesApp = serviceDTO.getCategoriesApp().toArray(new Long[0]);
        currentService.setCategoriesApp(categoriesApp);
        String validateLanguage = validateLanguage(serviceDTO.getLanguageType());
        currentService.setServiceName(serviceDTO.getServiceName().trim());
        currentService.setLanguageType(validateLanguage);
        currentService.setApprove(ServiceApproveStatusEnum.UNAPPROVED);
        currentService.setTechVisible(serviceDTO.getTechVisible());
        currentService.setTechLayout(serviceDTO.getTechLayout());
        currentService.setFeatureVisible(serviceDTO.getFeatureVisible());
        currentService.setSuggestionType(serviceDTO.getSuggestionType());
        currentService.setCreationLayoutId(serviceDTO.getCreationLayoutId());
        currentService.setDescription(serviceDTO.getDescription());
        currentService.setPaymentMethod(serviceDTO.getPaymentMethodValue());
        int installationConfiguration = ObjectUtil.getOrDefault(serviceDTO.getInstallationConfiguration(), 0);
        currentService.setInstallationConfiguration(
            Objects.equals(serviceDTO.getClassification(), ProductClassificationEnum.PHYSICAL) ? installationConfiguration : null);

        currentService.setStatus(ServiceStatusEnum.INVISIBLE.value);
        currentService.setDeletedFlag(EntitiesConstant.DeleteFlag.ACTIVE);
        currentService.setCreatedBy(AuthUtil.getCurrentUserId());
        Date now = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        currentService.setModifiedBy(AuthUtil.getCurrentUserId());
        currentService.setModifiedAt(now);
        currentService.setAllowMultiSub(serviceDTO.getAllowMultiSub());
        currentService.setRegisterEcontract(serviceDTO.getRegisterEcontract());
        if (Objects.nonNull(serviceDTO.getServiceOwnerPartner())) {
            currentService.setServiceOwnerPartner(serviceDTO.getServiceOwnerPartner().getValue());
        }
        if (Objects.nonNull(serviceDTO.getServiceOwnerVNPT())) {
            currentService.setServiceOwnerVNPT(serviceDTO.getServiceOwnerVNPT().getValue());
        }

        updateAppRoleByApiKey(currentService.getApiKey(), serviceDTO.getPermissionIds());

        initSupportInfo(currentService);
        currentService.setCustomerTypeCode(CollectionUtils.isEmpty(serviceDTO.getCustomerTypeCode()) ?
            null : serviceDTO.getCustomerTypeCode().stream().filter(Objects::nonNull).map(CustomerTypeEnum::getValue).collect(Collectors.toSet()));
        ServiceEntity save = serviceRepository.save(currentService);
        save.setSapoDescription(serviceDTO.getSnapshots().get(0).getDescription());

        addServiceHistory(save, ApproveStatusEnum.UNAPPROVED, PortalType.DEV, null);

        // cập nhật thông tin cơ bản
        ServiceUpdateDTO basicDTO = new ServiceUpdateDTO();
        BeanUtils.copyProperties(serviceDTO,basicDTO);
        basicDTO.setId(currentService.getId());
        basicDTO.setName(currentService.getServiceName());
        basicDTO.setLanguage(serviceDTO.getLanguageType());
        initBaseInfo(basicDTO);

        // cập nhật thông tin mô tả
        ServiceUpdateDescDTO descDTO = ServiceUpdateDescDTO.builder()
            .id(currentService.getId())
            .icon(serviceDTO.getIcon())
            .iconService(serviceDTO.getIconService())
            .video(serviceDTO.getVideo())
            .banner(serviceDTO.getBanner())
            .snapshots(serviceDTO.getSnapshots())
            .description(serviceDTO.getDescription())
            .build();
        updateDescInfo(descDTO);

        //cập nhật thông tin công nghệ nổi bật
        updateTechInfo(serviceDTO.getTechnologies(), currentService.getId());
        //cập nhật thông tin tính năng
        updateFeatureInfo(serviceDTO.getFeatures(), currentService.getId(), null);
        currentService.setStatus(ServiceStatusEnum.INVISIBLE.value);

        //Thêm sản phẩm liên quan
        if (currentService.getSuggestionType() == 1) {
            serviceSuggestionService.createSuggestionService(currentService.getId(), serviceDTO.getServiceSuggestions());
        }

        if(serviceDTO.getServiceTopics() != null){
            topicFaqService.updateTopicService(currentService.getId(), ServiceEnum.SERVICE.getValue(), serviceDTO.getServiceTopics());
        }
        // Cập nhật seo
        seoService.updateSeo(serviceDTO.getSeoUpdateDTO());
        return ServiceCreateResponseDTO.builder().id(save.getId()).build();
    }

    public List<ApiAppDTO> getListApiChill() {
        List<WorkplaceApi> wpApis = wpApiRepository.findAll();
        List<ApiAppDTO> lstParentApi = wpApis.stream().filter(wpApi -> wpApi.getParentId() == -1)
            .map(wpApi -> {
                ApiAppDTO apiAppDTO = new ApiAppDTO();
                BeanUtils.copyProperties(wpApi, apiAppDTO);
                return apiAppDTO;
            })
            .collect(Collectors.toList());

        List<WorkplaceApi> lstChillApi = wpApis.stream().filter(wpApi -> wpApi.getParentId() != -1)
            .collect(Collectors.toList());

        return lstParentApi.stream().peek(apiParent -> {
            List<ApiAppDTO> listChill = lstChillApi.stream()
                .filter(apiChill -> Objects.equals(apiChill.getParentId(), apiParent.getId()))
                .map(wpApi -> {
                    ApiAppDTO apiAppDTO = new ApiAppDTO();
                    apiAppDTO.setChillApi(new ArrayList<>());
                    BeanUtils.copyProperties(wpApi, apiAppDTO);
                    return apiAppDTO;
                })
                .collect(Collectors.toList());
            apiParent.setChillApi(listChill);
        }).collect(Collectors.toList());
    }

    public Boolean checkConfigApproveCreateService(Integer serviceOwner, Boolean isVariant) {
        boolean isOn = serviceOwner.equals(ServiceTypeEnum.VNPT.getValue()) || serviceOwner.equals(ServiceTypeEnum.SAAS.getValue());
        ApprovedRule approvedRule = approvedRuleRepository.findFirstByType(ApprovedTypeEnum.SERVICE.getValue());
        if (Objects.isNull(approvedRule) || Objects.isNull(approvedRule.getConditionQuery())) {
            return false;
        }
        List<Integer> infoChange =  new ArrayList<>();
        infoChange.add(0);
        if (isVariant) infoChange.add(ApprovedChangeServiceEnum.VARIANT.getValue());
        String infoChangeString = "{" + infoChange.stream().map(Objects::toString).collect(Collectors.joining(",")) + "}";

        List<Long> lstUserId = new ArrayList<>();
        lstUserId.add(-1L);
        lstUserId.add(AuthUtil.getCurrentUserId());
        String infoUser = "{" + lstUserId.stream().map(Objects::toString).collect(Collectors.joining(",")) + "}";

        String queryCheck = String.format("select %s", approvedRule.getConditionQuery());
        Query query = entityManager.createNativeQuery(queryCheck);

        if (approvedRule.getConditionQuery().contains(":userId")) {
            query.setParameter("userId", infoUser);
        }
        if(approvedRule.getConditionQuery().contains(":serviceOwner")) {
            query.setParameter("serviceOwner", isOn ? "ON" : "OS");
        }
        if(approvedRule.getConditionQuery().contains(":changeInformation")) {
            query.setParameter("changeInformation", infoChangeString);
        }
        if(approvedRule.getConditionQuery().contains(":isAll")) {
            query.setParameter("isAll", -1);
        }
        if(approvedRule.getConditionQuery().contains(":isUpdate")) {
            query.setParameter("isUpdate", isVariant);
        }

        return (Boolean) query.getSingleResult();
    }

    /**
     *
     * @param serviceId : save thông tin cấu hình seo
     * @return void
     */
    @Transactional
    public Seo saveSeoService(Long serviceId, Long categoryId, String typeCode, ServiceCreateDTO serviceDTO) {
        Seo seo = Seo.builder()
            .titlePage(serviceDTO.getSeoReqDTO().getTitlePage())
            .planUrl(serviceDTO.getSeoReqDTO().getProductUrl())
            .metaDescription(serviceDTO.getSeoReqDTO().getDescriptionMeta())
            .title(BooleanUtils.isTrue(serviceDTO.getSeoReqDTO().getIsTitle()) ? null : serviceDTO.getSeoReqDTO().getTitle())
            .description(BooleanUtils.isTrue(serviceDTO.getSeoReqDTO().getIsDescription()) ? null : serviceDTO.getSeoReqDTO().getDescription())
            .serviceId(serviceId)
            .categoriesId(categoryId)
            .seoTypeCode(typeCode)
            .isImage(serviceDTO.getSeoReqDTO().getIsImage())
            .isAvailableTitle(serviceDTO.getSeoReqDTO().getIsTitle())
            .isAvailableDescription(serviceDTO.getSeoReqDTO().getIsDescription())
            .build();
        Seo newSeo = seoRepository.save(seo);


        // save file attach of seo
        List<FileAttach> currentAttachs = fileAttachRepository
            .findByIdIn(serviceDTO.getSeoReqDTO().getAttachs().stream().map(AttachesDTO::getId).collect(Collectors.toList()));

        if (!CollectionUtils.isEmpty(currentAttachs)) {
            currentAttachs.get(0).setSeoId(newSeo.getId());
            fileAttachRepository.save(currentAttachs.get(0));
        }

        //save list keywword
        keyWordRepository.saveAll(
            serviceDTO.getSeoReqDTO().getKeywords().stream().map(k ->
             new KeyWord(null, newSeo.getId(), 1, k)
            ).collect(Collectors.toList())
        );

        return newSeo;
    }

    @Transactional
    @Override
    public void updateServiceStatus(Long serviceId, ServiceStatusEnum status) {
        if (status != ServiceStatusEnum.VISIBLE && status != ServiceStatusEnum.INVISIBLE) {
            String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }
        List<ServiceDraft> ServiceDrafts = serviceDraftRepository.findByServiceIdIn(Collections.singletonList(serviceId));
        this.validateServiceDraftPermissionDev(ServiceDrafts);

        // serviceRepository.changeStatus(status.value, serviceId);
        changeServiceStatus(serviceId, status.value);
        serviceRepository.changeStatusServiceDraft(status.value, ServiceDrafts.stream().map(ServiceDraft::getId).collect(Collectors.toSet()));
    }

    private void changeServiceStatus(Long serviceId, Integer status) {
        ServiceEntity service = serviceRepository.findById(serviceId).orElse(null);
        if (service != null) {
            service.setModifiedAt(new Date());
            service.setModifiedBy(AuthUtil.getCurrentUserId());
            service.setStatus(status);
            serviceRepository.save(service);
        }
    }

    @Override
    public void approveAndUpdateApiKey(Long serviceId) {
        ServiceEntity currentService = getCurrentService(serviceId);
        if (currentService.getApprove() != ServiceApproveStatusEnum.APPROVED) {
            currentService.setApprove(ServiceApproveStatusEnum.APPROVED);
            currentService.setApproveBy(AuthUtil.getCurrentUserId());

            addServiceHistory(currentService, ApproveStatusEnum.APPROVED, PortalType.ADMIN, null);

            // Cập nhật mview_service_detail
            serviceRepository.refreshMviewServiceDetail();
        }
        currentService.setModifiedBy(AuthUtil.getCurrentUserId());
        serviceMapper.toDto(serviceRepository.save(currentService));
    }

    @Override
    @Transactional
    public void approveService(Long serviceId, ServiceApproveDTO serviceApproveDTO, Boolean checkPricingCreatedWithService) {
        Long provinceId = departmentsRepository.getProvinceIdByUserId(AuthUtil.getCurrentUserId());
        if (Objects.nonNull(provinceId)) {
            throw new AccessDeniedException(MessageConst.ACCESS_DENIED);
        }
        ServiceEntity currentService = getCurrentService(serviceId);
        adminUpdateApproveStatus(serviceApproveDTO, currentService, serviceId);
        currentService.setModifiedBy(AuthUtil.getCurrentUserId());

        if (checkPricingCreatedWithService) {
            // khi duyệt dịch vụ sẽ duyệt luôn cho cả gói
            List<PricingDraft> listPricingDraftsCreatedWithService = pricingDraftRepository.findAllByServiceIdAndDeletedFlagAndApproveIn(serviceId, DELETE_FLAG, Arrays.asList(ServiceApproveStatusEnum.UNAPPROVED.value, ServiceApproveStatusEnum.AWAITING_APPROVAL.value));
            listPricingDraftsCreatedWithService.stream()
                .map(PricingDraft::getId)
                .forEach(pricingDraftId -> {
                    PricingApproveReqDTO pricingApproveReqDTO = new PricingApproveReqDTO(ApproveStatusEnum.APPROVED, null);
                    pricingService.approvePricing(pricingDraftId, pricingApproveReqDTO, true);
                });
        }

        serviceRepository.save(currentService);
    }

    @Override
    @Transactional
    public void updateDescInfo(ServiceUpdateDescDTO paramDto) {
        ServiceEntity currentService = getCurrentService(paramDto.getId());
        currentService.setDescription(paramDto.getDescription());
        currentService.setSapoDescription(paramDto.getShortDescription());
        this.validateServicePermissionDev(Collections.singletonList(currentService));

        // Lay thong tin Seo cua dich vu
        Long id = paramDto.getId();
        List<SeoDTO> seoService = seoRepository.getAllSeoByServiceId(id);
        seoService.forEach(s -> s.setKeyWord(seoRepository.getAllKeyword(CAU_HINH_DICH_VU)));
        paramDto.setSeoList(seoService);

        if(paramDto.getSnapshots() != null) {
            validatePriorityConsecutive(paramDto.getSnapshots());
        } else {
            paramDto.setSnapshots(new ArrayList<>());
        }
        List<Long> snapshotIds = paramDto.getSnapshots().stream().map(ServiceCaptureDTO::getId).collect(Collectors.toList());
        List<Long> iconServices = paramDto.getIconService();

        snapshotIds.add(paramDto.getIcon());
        snapshotIds.add(paramDto.getVideo());
        snapshotIds.add(paramDto.getBanner());
        if (Objects.nonNull(iconServices)) {
            snapshotIds.addAll(iconServices);
        }

        //cập nhật tiêu đề + mô tả cho các snapshot
        for(ServiceCaptureDTO snap: paramDto.getSnapshots()){
            fileAttachRepository.updateDescription(snap.getId(),snap.getTitle(),snap.getDescription(),snap.getFileType()!=null?snap.getFileType():1);
        }

        //Xoa file cua dich vu cu
        Optional<List<FileAttach>> fileAttachOptList = fileAttachRepository.findByServiceId(paramDto.getId());
        if (fileAttachOptList.isPresent()) {

            List<Long> distinct = snapshotIds.stream().distinct().collect(Collectors.toList());
            List<FileAttach> fileById = fileAttachOptList.get();

            List<FileAttach> fileNeedDeletes = new ArrayList<>();
            for (FileAttach dto : fileById) {
                if (distinct.stream().noneMatch(el -> dto.getId().equals(el))
                    && dto.getObjectType() != null
                    && !dto.getObjectType().equals(FileAttachTypeEnum.TECH.value)
                    && !dto.getObjectType().equals(FileAttachTypeEnum.AVATAR.value)
                    && !dto.getObjectType().equals(FileAttachTypeEnum.AVATAR_DEVICE.value)
                    && !dto.getObjectType().equals(FileAttachTypeEnum.CAPTURE.value)
                    && !dto.getObjectType().equals(FileAttachTypeEnum.BANNER.value)
                    && !dto.getObjectType().equals(FileAttachTypeEnum.VIDEO_GUIDE.value)
                    && !dto.getObjectType().equals(FileAttachTypeEnum.DOCUMENT_GUIDE.value)) {
                    fileNeedDeletes.add(dto);
                }
            }

            fileAttachRepository.deleteAll(fileNeedDeletes);

            //xoa file static
            fileNeedDeletes.forEach(file ->  FileUtil.deleteFileByPath(file.getFilePath()) );
        }

        // Cap nhat file moi cho dich vu
        List<FileAttach> fileNeedUpdates = fileAttachRepository.findAllById(snapshotIds);

        fileNeedUpdates.forEach(el -> {
            Optional<ServiceCaptureDTO> first = paramDto.getSnapshots().stream()
                    .filter(sel -> Objects.nonNull(sel.getId()) && sel.getId().equals(el.getId())).findFirst();
            if (first.isPresent()) {
                el.setPriority(first.get().getPriority());
                el.setObjectType(FileAttachTypeEnum.CAPTURE.value);
            } else if (el.getId().equals(paramDto.getIcon())) {
                el.setPriority(0);
                el.setObjectType(FileAttachTypeEnum.AVATAR.value);
            } else if (el.getId().equals(paramDto.getVideo())) {
                el.setPriority(0);
                el.setObjectType(FileAttachTypeEnum.INTRO.value);
            } else if (el.getId().equals(paramDto.getBanner())) {
                el.setPriority(0);
                el.setObjectType(FileAttachTypeEnum.BANNER.value);
            }
            el.setModifiedBy(AuthUtil.getCurrentUserId());
            // update avatar device
            if (Objects.nonNull(iconServices) && iconServices.contains(el.getId())) {
                el.setObjectType(FileAttachTypeEnum.AVATAR_DEVICE.value);
            }
            if (el.getServicesDraftId() == null) {
                el.setServiceId(paramDto.getId());
            }
        });
        fileAttachRepository.saveAll(fileNeedUpdates);
        //cap nhat mo ta
        serviceRepository.save(currentService);
    }

    @Override
    public List<FileAttach> uploadFile(MultipartFile[] files, Long fileSize, String link) {
        List<FileAttach> fileAttaches = new ArrayList<>();
        if (Objects.nonNull(files)) {
            for (MultipartFile file : files) {
                fileAttaches.add(saveFileAttach(file, fileSize, NULL_LINK));
            }
        }
        if (Objects.nonNull(link)) {
            fileAttaches.add(saveFileAttach(MULTIPART_FILE_NULL, fileSize, link));
        }

        return fileAttachRepository.saveAll(fileAttaches);
    }

    @Override
    public void uploadFile1(FileAttachLibraryDTO fileAttachLibraryDTO) {
        fileAttachRepository.updateFileSizeLibrary(fileAttachLibraryDTO.getId(), fileAttachLibraryDTO.getFileSizeLibrary());
    }

    @Override
    public Page<ServiceResponseTransDTO> findAllForSME(ServiceSearchDTO searchDTO, Pageable pageable) {
        Page<BigInteger> lstPageId = serviceRepository.getPageSmeServiceId(searchDTO.getServiceProductType().getValue(),
            searchDTO.getCreatedSource(), searchDTO.getStatus().value, searchDTO.getNameText(), searchDTO.getType(),
            searchDTO.getCustomerCode().getValue(), searchDTO.getSearchText(), searchDTO.getDisplayed().value, searchDTO.getOnOsType(),
            searchDTO.getAllowMultiSub(), NON_USER_ID, searchDTO.getCategoriesId(), pageable);
        List<ServiceResponseDTO> lstServiceDetail = serviceRepository.getListSmeServiceDetailByIdIn(
            lstPageId.stream().map(BigInteger::longValue).collect(Collectors.toSet()), AuthUtil.getDefaultCurrentParentId());
        return convertServiceResponseTransDTO(lstPageId.map(
            id -> lstServiceDetail.stream().filter(item -> Objects.equals(item.getId(), id.longValue())).findFirst().orElse(null)));
    }

    public Page<ServiceResponseTransDTO> convertServiceResponseTransDTO(Page<ServiceResponseDTO> dtoPage) {
        Set<Long> lstServiceId = dtoPage.stream().map(ServiceResponseDTO::getId).collect(Collectors.toSet());
        // lấy thông tin giá và Km của biến thể mặc định
        Map<Long, VariantResExtraDTO> mapVariantDefault = new HashMap<>();
        for (Long serviceId : lstServiceId) {
            List<VariantResExtraDTO> variantResponseDTOList = variantService.getAllVariantByServiceIdBOSHomepage(serviceId);
            VariantResExtraDTO defaultVariant = variantResponseDTOList.stream()
                .filter(i -> Objects.equals(i.getVariantDefault(), 1)).findFirst().orElse(null);
            if (Objects.nonNull(defaultVariant)) {
                mapVariantDefault.put(serviceId, defaultVariant);
            }
        }

        Page<ServiceResponseTransDTO> finalResponses = convertToServiceDetailRes(dtoPage);
        // lấy thông tin tương ứng của biến thế mặc định
        for (ServiceResponseTransDTO item : finalResponses) {
            if (mapVariantDefault.containsKey(item.getId())) {
                item.setVariantDefaultResponseDTO(mapVariantDefault.get(item.getId()));
            }
        }

        return finalResponses;
    }

    @Override
    public Map<Long, Integer> countByCategories(ServiceSearchDTO searchDTO) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        if (userLogin != null) {
            searchDTO.setCustomerCode(
                userLogin.getIsSME() ? CustomerTypeEnum.getValueOf(userLogin.getCustomerType()) : searchDTO.getCustomerCode());
        }
        searchDTO.setDisplayed(ServiceStatusEnum.VISIBLE); // Cấu hình tương tự method findAllForSME
        return serviceRepository.countByCategories(searchDTO, ObjectUtil.getOrDefault(AuthUtil.getCurrentParentId(), NON_USER_ID))
            .stream().collect(Collectors.toMap(ICommonIdCountDTO::getId, ICommonIdCountDTO::getNumber));
    }

    @Override
    public ServiceByCategoryResponseTransDTO getServiceByCategoryDetail(Long id, List<Long>  category, String search, CustomerTypeEnum customerType, String serviceOwnerEnum,
        Integer createdSource, ServiceProductTypeEnum serviceProductType, String order, Integer onOsType) {
        ServiceSearchDTO searchDTO = new ServiceSearchDTO(search , NON_SERVICE_NAME,
            ServiceStatusEnum.VISIBLE, ServiceApproveStatusEnum.APPROVED, customerType, -1, createdSource, serviceProductType, onOsType);
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        List<IGetCategoriesParent> listCategories = categoryRepository.findAllIdChildCategory(id);
        // filter theo category
        if (Objects.nonNull(category)) {
            listCategories = listCategories.stream().filter(cate -> category.contains(cate.getId())).collect(Collectors.toList());
        }
        Set<Long> cateIds = listCategories.stream().map(IGetCategoriesParent::getId).collect(Collectors.toSet());
        searchDTO.setCateIds(cateIds);
        List<ServiceResponseDTO>  dto;
        if (userLogin != null) {
            searchDTO.setCustomerCode(CustomerTypeEnum.getValueOf(userLogin.getCustomerType()));
            if (userLogin.getParentId() == -1) {
                dto = serviceRepository.searchServicesByCategory(searchDTO, NON_USER_ID, userLogin.getId());
            } else {
                dto = serviceRepository.searchServicesByCategory(searchDTO, NON_USER_ID, userLogin.getParentId());
            }
        } else {
            dto = serviceRepository.searchServicesByCategory(searchDTO, NON_USER_ID, NON_USER_ID);
        }
        return convertServiceByCategoryDetail(dto, id, listCategories, order);
    }

    private ServiceByCategoryResponseTransDTO convertServiceByCategoryDetail(List<ServiceResponseDTO> dto, Long categoriesId,
        List<IGetCategoriesParent> listCategories, String order) {
        Set<Long> setServiceId = dto.stream().map(ServiceResponseDTO::getId).collect(Collectors.toSet());
        Map<Long, List<CommonIdNameDTO>> mapServiceFeatureBrief = serviceRepository.getServiceCategory(setServiceId).stream()
            .collect(Collectors.toMap(IServiceCategoryDTO::getServiceId, IServiceCategoryDTO::getCategories));

        // tạo map variant default của từng SPDV
        Set<Long> setServiceIdVariant = dto.stream().filter(item ->Objects.nonNull(item.getProductType()) && Objects.equals(item.getProductType(), ServiceProductTypeEnum.DEVICE.value))
            .collect(Collectors.toList()).stream().map(ServiceResponseDTO::getId).collect(Collectors.toSet());
        List<VariantDTO> listVariant = variantRepository.getVariantDefaultByServiceIds(setServiceIdVariant);
        Map<Long, VariantDTO> mapVariant = listVariant.stream().collect(Collectors.groupingBy(VariantDTO::getServiceId, Collectors.collectingAndThen(Collectors.toList(), values -> values.get(0))));
        List<ServiceResponseDTO> listServiceParent = dto.stream().filter(i -> i.getCategoriesIds().contains(categoriesId)).collect(Collectors.toList());
        IGetCategoriesParent iGetCategoriesParent = listCategories.stream().filter(i -> Objects.equals(i.getId(), categoriesId)).findFirst().orElse(null);
        if (Objects.isNull(iGetCategoriesParent)) {
            return null;
        }

        ServiceByCategoryResponseTransDTO response = getServiceByCategoryResponseTransDTO(iGetCategoriesParent, listServiceParent, mapVariant,
            mapServiceFeatureBrief, order, true);

        // lấy dsach dich vu trong danh muc con
        dto = dto.stream().filter(i -> !i.getCategoriesIds().contains(categoriesId) || (i.getCategoriesIds().contains(categoriesId) && i.getCategoriesIds().size() > 1)).collect(
            Collectors.toList());
        List<ServiceByCategoryResponseTransDTO> listChildInCate = new ArrayList<>();
        // map dsach SPDV theo danh mục
        Map<Long, IGetCategoriesParent> mapCategory = listCategories.stream().collect(Collectors.toMap(IGetCategoriesParent::getId, Function.identity()));
        for (IGetCategoriesParent category : listCategories) {
            IGetCategoriesParent cateInfo = mapCategory.get(category.getId());
            // lấy danh dsach SPDV trong danh muc
            List<ServiceResponseDTO> listServiceChildren = dto.stream().filter(i -> i.getCategoriesIds().contains(category.getId())).collect(Collectors.toList());
            if (Objects.equals(category.getId(), categoriesId) || Objects.isNull(cateInfo) || listServiceChildren.isEmpty()) {
                continue;
            }
            ServiceByCategoryResponseTransDTO child = getServiceByCategoryResponseTransDTO(cateInfo, listServiceChildren, mapVariant,
                mapServiceFeatureBrief, order, false);
            listChildInCate.add(child);
        }
        response.setChildren(listChildInCate);
        return response;
    }

    /** hàm lấy dữ liệu danh mục con
     *
     */
    private ServiceByCategoryResponseTransDTO getServiceByCategoryResponseTransDTO(IGetCategoriesParent cateInfo,
        List<ServiceResponseDTO> listService, Map<Long, VariantDTO> mapVariant,
        Map<Long, List<CommonIdNameDTO>> mapServiceFeatureBrief, String order, boolean isParent) {
        ServiceByCategoryResponseTransDTO response = new ServiceByCategoryResponseTransDTO();
        response.setId(cateInfo.getId());
        response.setName(cateInfo.getName());
        response.setFilePath(cateInfo.getFilePath());
        switch (order) {
            case TOP_SELLING:
                listService = listService.stream().sorted(comparing(ServiceResponseDTO::getSubscriptionNumber).reversed()).collect(Collectors.toList());
                break;
            case TRENDING:
                listService = listService.stream().sorted(comparing(ServiceResponseDTO::getSubscriptionNumberTrending).reversed()).collect(Collectors.toList());
                break;
            default:
                listService = listService.stream().sorted(comparing(ServiceResponseDTO::getUpdatedTime).reversed()).collect(Collectors.toList());
                break;
        }
        int size = listService.size();
        // loại danh mục là tổng hợp các loại (productType) của SPDV
        response.setListServiceProductType(listService.stream().map(item -> ServiceProductTypeEnum.fromValue(item.getProductType())).filter(
            Objects::nonNull).collect(Collectors.toSet()));
        List<ServiceResponseDTO> listCut = listService;
        if (!isParent && size > 4) {
            listCut = listService.subList(0, 4);
        }

        List<ServiceResponseTransDTO> listServiceRes = new ArrayList<>();
        listCut.forEach(resDTO -> listServiceRes.add(convertItemService(resDTO, mapVariant, mapServiceFeatureBrief)));
        response.setSizeService(size);
        response.setListService(listServiceRes);
        return response;
    }

    @Override
    public List<ServiceByCategoryResponseTransDTO> findAllServiceByCategory(List<Long> category, String search, CustomerTypeEnum customerType,
        String serviceOwner, Integer createdSource, ServiceProductTypeEnum serviceProductType, String order, Integer onOsType) {
        ServiceSearchDTO searchDTO = new ServiceSearchDTO(search, NON_SERVICE_NAME,
            ServiceStatusEnum.VISIBLE, ServiceApproveStatusEnum.APPROVED, customerType, -1, createdSource, serviceProductType, onOsType);
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        List<ServiceResponseDTO>  dto;
        // lấy những Danh mục cấp 1
        List<IGetCategoriesParent> listCategories = categoryRepository.findAllIdChildCategory(-1L);
        // filter theo category
        Set<Long> cateIds = new HashSet<>(Collections.singletonList(-1L));
        if (Objects.nonNull(category)) {
            listCategories = listCategories.stream().filter(cate -> category.contains(cate.getId())).collect(Collectors.toList());
            cateIds = listCategories.stream().map(IGetCategoriesParent::getId).collect(Collectors.toSet());
        }
        searchDTO.setCateIds(cateIds);
        if (userLogin != null) {
            searchDTO.setCustomerCode(CustomerTypeEnum.getValueOf(userLogin.getCustomerType()));
            if (userLogin.getParentId() == -1) {
                dto = serviceRepository.searchServicesByCategory(searchDTO, NON_USER_ID, userLogin.getId());
            } else {
                dto = serviceRepository.searchServicesByCategory(searchDTO, NON_USER_ID, userLogin.getParentId());
            }
        } else {
            dto = serviceRepository.searchServicesByCategory(searchDTO, NON_USER_ID, NON_USER_ID);
        }
        return convertServiceEntityByCategory(dto, listCategories, order);
    }

    @Override
    public List<ICategoryServicesDTO> getListCategoryServices(String search, CustomerTypeEnum customerType) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        CustomerTypeEnum filteredCustomerType = Objects.nonNull(userLogin) ? CustomerTypeEnum.fromValue(userLogin.getCustomerType()) : customerType;
        return categoryRepository.getListCategoryServices(SqlUtils.optimizeSearchLike(search), filteredCustomerType.getValue());
    }

    private List<ServiceByCategoryResponseTransDTO> convertServiceEntityByCategory(List<ServiceResponseDTO> dto,
        List<IGetCategoriesParent> listCategories, String order) {
        Set<Long> setServiceId = dto.stream().map(ServiceResponseDTO::getId).collect(Collectors.toSet());
        Map<Long, List<CommonIdNameDTO>> mapServiceFeatureBrief = serviceRepository.getServiceCategory(setServiceId).stream()
            .collect(Collectors.toMap(IServiceCategoryDTO::getServiceId, IServiceCategoryDTO::getCategories));

        // map dsach SPDV theo danh mục
        // map danh sach cap duoi voi id danh muc cha
        List<IGetCategoryViewDTO> iGetCategoryViewDTOList = categoriesRepository.getAllCategory();
        List<CategoryViewDTO> categoryViewDTOList = iGetCategoryViewDTOList.stream().map(CategoryViewDTO::new).collect(Collectors.toList());
        Map<Long, Set<Long>> mapLstInteriorWithParentId = new HashMap<>();
        for (CategoryViewDTO itemDTO : categoryViewDTOList) {
            if (Objects.equals(itemDTO.getParentId(), -1L)) {
                mapParentCategoriesWithInterior(categoryViewDTOList, itemDTO, itemDTO);
                mapLstInteriorWithParentId.put(itemDTO.getId(), itemDTO.getLstInterior());
            }
        }

        Map<Long, List<ServiceResponseDTO>> mapServiceByCategory = new LinkedHashMap<>();
        for (IGetCategoriesParent category : listCategories) {
            List<ServiceResponseDTO> list = dto.stream().filter(i -> {
                Set<Long> categoryIdRetain = new HashSet<>(mapLstInteriorWithParentId.get(category.getId()));
                categoryIdRetain.retainAll(i.getCategoriesIds());
                return i.getCategoriesIds().contains(category.getId()) || !categoryIdRetain.isEmpty();
            }).collect(Collectors.toList());
            if (!list.isEmpty()) {
                mapServiceByCategory.put(category.getId(), list);
            }
        }
        Map<Long, IGetCategoriesParent> mapCategory = listCategories.stream().collect(Collectors.toMap(IGetCategoriesParent::getId, Function.identity()));
        List<ServiceByCategoryResponseTransDTO> response = new ArrayList<>();
        Set<Map.Entry<Long, List<ServiceResponseDTO>>> entrySetService = mapServiceByCategory.entrySet();
        // tạo map variant default của từng SPDV
        Set<Long> setServiceIdVariant = dto.stream().filter(item ->Objects.nonNull(item.getProductType()) && Objects.equals(item.getProductType(), ServiceProductTypeEnum.DEVICE.value))
            .collect(Collectors.toList()).stream().map(ServiceResponseDTO::getId).collect(Collectors.toSet());
        List<VariantDTO> listVariant = variantRepository.getVariantDefaultByServiceIds(setServiceIdVariant);
        Map<Long, VariantDTO> mapVariant = listVariant.stream().collect(Collectors.groupingBy(VariantDTO::getServiceId, Collectors.collectingAndThen(Collectors.toList(), values -> values.get(0))));
        for (Map.Entry<Long, List<ServiceResponseDTO>> entryService : entrySetService) {
            IGetCategoriesParent cateInfo = mapCategory.get(entryService.getKey());
            if (Objects.isNull(cateInfo)) {
                continue;
            }
            ServiceByCategoryResponseTransDTO itemCategory = getServiceByCategoryResponseTransDTO(cateInfo, entryService.getValue(), mapVariant,
                mapServiceFeatureBrief, order, false);
            response.add(itemCategory);
        }
        return response;
    }

    private void mapParentCategoriesWithInterior(List<CategoryViewDTO> categoryViewDTOList, CategoryViewDTO currentParent,
        CategoryViewDTO parent) {
        List<CategoryViewDTO> lstCateChild = categoryViewDTOList.stream().filter(e -> Objects.equals(e.getParentId(), currentParent.getId()))
            .collect(Collectors.toList());
        parent.getLstInterior().addAll(lstCateChild.stream().map(CategoryViewDTO::getId).collect(Collectors.toSet()));
        for (CategoryViewDTO currentChild : lstCateChild) {
            mapParentCategoriesWithInterior(categoryViewDTOList, currentChild, parent);
        }
    }

    private ServiceResponseTransDTO convertItemService(ServiceResponseDTO resDTO, Map<Long, VariantDTO> mapVariant,
        Map<Long, List<CommonIdNameDTO>> mapServiceFeatureBrief) {
        VariantDTO variantDTO = mapVariant.get(resDTO.getId());
        String defaultVariantAvatar = variantDTO != null ? variantDTO.getAvatar() : null;
        return ServiceResponseTransDTO.builder()
            .id(resDTO.getId())
            .name(resDTO.getName())
            .developer(resDTO.getDeveloper())
            .updatedTime(resDTO.getUpdatedTime())
            .categoryName(resDTO.getCategoryName())
            .status(resDTO.getStatus())
            .displayed(resDTO.getDisplayed())
            .icon(ObjectUtil.getOrDefault(defaultVariantAvatar, resDTO.getIcon()))
            .externalLink(resDTO.getExternalLink())
            .banner(resDTO.getBanner())
            .externalLinkBanner(resDTO.getExternalLinkBanner())
            .sapoDescription(resDTO.getSapoDescription())
            .numSub(resDTO.getSubscriptionNumber())
            .avgRating(resDTO.getAvgRating())
            .ratingQuantity(resDTO.getRatingQuantity())
            .serviceOwner(resDTO.getServiceOwner())
            .planUrl(resDTO.getPlanUrl())
            .reaction(resDTO.getReaction())
            .numReactions(ObjectUtil.getOrDefault(resDTO.getNumReactions(), 0L))
            .customerType(convertToEntityAttribute(resDTO.getCustomerType()))
            .paymentMethod(PaymentMethodEnum.fromValue(resDTO.getPaymentMethod()))
            .categoryNameApp(resDTO.getCategoryNameApp())
            .productType(ServiceProductTypeEnum.fromValue(
                Objects.nonNull(resDTO.getProductType()) ? resDTO.getProductType() : ServiceProductTypeEnum.SAAS.value))
            .defaultPricing(Objects.nonNull(resDTO.getDefaultPricingDraftId()) ? getPricingDefault(resDTO.getDefaultPricingDraftId()) : null)
            .variantDraftId(Objects.nonNull(variantDTO) ? variantDTO.getVariantDraftId() : null)
            .variantId(Objects.nonNull(variantDTO) ? variantDTO.getVariantId() : null)
            .variantName(Objects.nonNull(variantDTO) ? variantDTO.getFullName() : null)
            .isService(Boolean.TRUE)
            .allowMultiSub(resDTO.getAllowMultiSub())
            .categories(mapServiceFeatureBrief.get(resDTO.getId()))
            .previewPrice(resDTO.getPreviewPrice())
            .calculateTypeEnum(CalculateTypeEnum.fromValue(resDTO.getCalculateType()))
            .build();
    }

    @Override
    public Page<SearchSmeResponseDTO> searchAllServiceForSME(String search, String type, CustomerTypeEnum customerType, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeQuery = Objects.isNull(userLogin) || type.equals("ADMIN") ? customerType.getValue() : userLogin.getCustomerType();
        Page<SearchSmeDTO> searchList;
        List<SearchSmeResponseDTO> searchResList = new ArrayList<>();
        SystemParam systemParam = systemParamService.findByParamType(SystemParamConstant.PARAM_TYPE_SEARCH_EXACTLY);
        if (systemParam.getParamSearchExactly().equals(1)) {
            if (userLogin != null) {
                if (userLogin.getParentId() == -1) {
                    searchList = serviceRepository.searchExactlyAllServicesSME(search, NON_USER_ID, userLogin.getId(), customerTypeQuery, pageable);
                } else {
                    searchList = serviceRepository.searchExactlyAllServicesSME(search, NON_USER_ID, userLogin.getParentId(), customerTypeQuery, pageable);
                }
            } else {
                searchList = serviceRepository.searchExactlyAllServicesSME(search, NON_USER_ID, NON_USER_ID, customerTypeQuery, pageable);
            }
        } else {
            if (userLogin != null) {
                if (userLogin.getParentId() == -1) {
                    searchList = serviceRepository.searchAllServicesSME(search, NON_USER_ID, userLogin.getId(), customerTypeQuery, pageable);
                } else {
                    searchList = serviceRepository.searchAllServicesSME(search, NON_USER_ID, userLogin.getParentId(), customerTypeQuery, pageable);
                }
            } else {
                searchList = serviceRepository.searchAllServicesSME(search, NON_USER_ID, NON_USER_ID, customerTypeQuery, pageable);
            }
        }

        searchList.getContent().forEach(ser -> {
            SearchSmeResponseDTO serviceDTO = new SearchSmeResponseDTO();
            BeanUtils.copyProperties(ser, serviceDTO);
            // set goi dịch vụ vào dịch vụ
            ServiceReaction reaction = null;
            if (ser.getType().equals("COMBO")) {
                serviceDTO.setShortDescription(comboRepository.getDescriptionCombo(ser.getId()));
                serviceDTO.setReactionQuantity(serviceReactionService.countReactionByService(ser.getId(), 2));
                reaction = serviceReactionService.getByServiceIdAndType(ser.getId(), 2);
                serviceDTO.setPricing(comboPlanService.findAllPricingShortByCombo(ser.getId(), customerTypeQuery));
            } else if (ser.getType().equals("SERVICE_GROUP")) {
                ServiceGroup serviceGroup = serviceGroupRepository.findByIdAndDeletedFlag(ser.getId(), DeletedFlag.NOT_YET_DELETED.getValue())
                        .orElse(null);
                if (Objects.nonNull(serviceGroup)) {
                    serviceDTO.setLstGroupPricing(serviceGroupServiceImpl.getGroupDraftPricingDetailSME(
                            serviceGroup.getGroupServiceDraftId(), ser.getId()));
                    reaction = serviceReactionService.getByServiceIdAndType(ser.getId(), 6);
                    serviceDTO.setReactionQuantity(serviceReactionService.countReactionByService(ser.getId(), 6));
                    serviceDTO.setShortDescription(serviceGroup.getDescription());
                }
            } else {
                serviceDTO.setShortDescription(serviceRepository.getDescriptionService(ser.getId()));
                serviceDTO.setReactionQuantity(serviceReactionService.countReactionByService(ser.getId(), 1));
                reaction = serviceReactionService.getByServiceIdAndType(ser.getId(), 1);
                serviceDTO.setPricing(pricingService.findAllPricingShortByService(ser.getId(), customerTypeQuery));
            }
            serviceDTO.setCustomerType(new SetConverter().convertToEntityAttribute(ser.getCustomerType())
                    .stream().map(CustomerTypeEnum::getValueOf).collect(Collectors.toSet()));
            if (reaction != null) {
                serviceDTO.setReaction(true);
            }
            // setCategories
            List<SearchSmeResponseDTO.Category> categoryList = new ArrayList<>();
            if (!StringUtils.isEmpty(ser.getCategoryId())) {
                List<Long> idCategories = Arrays.stream(ser.getCategoryId().split(","))
                        .map(Long::parseLong).collect(Collectors.toList());
                List<CategoryDTODetail> categories = comboRepository.getListCategory(idCategories);
                categories.forEach(c -> {
                    SearchSmeResponseDTO.Category category = new SearchSmeResponseDTO.Category();
                    category.setId(c.getId());
                    category.setName(c.getName());
                    categoryList.add(category);
                });
            }
            // lọc kết quả
            if (Objects.equals(customerTypeQuery, CustomerTypeEnum.ENTERPRISE.getValue()) || !CollectionUtils.isEmpty(categoryList)) {
                serviceDTO.setCategories(categoryList);
                searchResList.add(serviceDTO);
            }
        });
        return new PageImpl<>(searchResList, searchList.getPageable(), searchList.getTotalElements());
    }

    @Override
    public Page<ServiceResponseDTO> findAllForAdmin(ServiceSearchDTO searchDTO, Pageable pageable) {
        if (Objects.isNull(searchDTO.getCustomerCode()))
            searchDTO.setCustomerCode(CustomerTypeEnum.UNSET);
        if (Objects.isNull(searchDTO.getAllowMultiSub()))
            searchDTO.setAllowMultiSub(-1);
        if (Objects.isNull(searchDTO.getDisplayed()))
            searchDTO.setDisplayed(ServiceStatusEnum.UNSET);
        if (Objects.isNull(searchDTO.getStatus()))
            searchDTO.setStatus(ServiceApproveStatusEnum.UNSET);
        if (Objects.isNull(searchDTO.getValue()))
            searchDTO.setValue("");
        if (Objects.isNull(searchDTO.getSearchText()))
            searchDTO.setSearchText("");
        if (Objects.isNull(searchDTO.getNameText()))
            searchDTO.setNameText("");

        boolean isApplication = Objects.equals(searchDTO.getType(), 1);

        if (isApplication) {
            return serviceRepository.searchApplicationAdmin(searchDTO, NON_USER_ID, NON_USER_ID, searchDTO.getProviderId(), searchDTO.getValue(), pageable);
        }
        return serviceRepository.searchServicesAdmin(searchDTO, NON_USER_ID, NON_USER_ID, searchDTO.getServiceOwner(), searchDTO.getProviderId(), searchDTO.getValue(), pageable);
    }

    @Override
    public ServiceDetailResponseDTO getDetailService(Long serviceId, String customerType) {
        if (serviceId <= 0) {
            throw exceptionFactory.badRequest(MessageKeyConstant.MUST_BE_GREATER_THAN_0, Resources.SERVICES, ErrorKey.ID,
                String.valueOf(serviceId));
        }
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        Long currentParentId = AuthUtil.getCurrentParentId();
        customerType = userLogin != null ? userLogin.getCustomerType() : customerType;
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.fromValue(customerType);
        ServiceEntity serviceEntity = findServiceById(serviceId);
        if (userLogin != null && !Objects.equals(userLogin.getCustomerType(), CustomerTypeEnum.ENTERPRISE.getValue()) &&
            Objects.nonNull(serviceEntity.getServiceTypeApplication())) {
            throw exceptionFactory.badRequest(MessageKeyConstant.INVALID_CUSTOMER_TYPE, Resources.USER, ErrorKey.CUSTOMER_TYPE,
                userLogin.getCustomerType(), CustomerTypeEnum.ENTERPRISE.getValue());
        }
        List<String> collect = userLogin != null ?
            userLogin.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()) : new ArrayList<>();
        if (collect.isEmpty() || ((collect.contains(RoleType.SME.getValue())
            || collect.contains(RoleType.FULL_SME.getValue())) &&
            !collect.contains(RoleType.DEVELOPER.getValue()))) {
            if (!Objects.equals(ServiceStatusEnum.VISIBLE.value, serviceEntity.getStatus())) {
                throw exceptionFactory.badRequest(MessageKeyConstant.SERVICE_INVISIBLE, Resources.SERVICES, ErrorKey.ID,
                    String.valueOf(serviceId));
            }
        }
        if (userLogin == null) {
            if (customerTypeEnum != null && customerTypeEnum != CustomerTypeEnum.UNSET &&
                !serviceEntity.getCustomerTypeCode().contains(customerType)) {
                throw exceptionFactory.badRequest(MessageKeyConstant.INVALID_CUSTOMER_TYPE, Resources.SERVICES, ErrorKey.CUSTOMER_TYPE,
                    customerType, String.valueOf(serviceEntity.getCustomerTypeCode()));
            }
        } else if (!AuthUtil.checkUserRoles(Arrays.asList(RoleType.ADMIN.getValue(), RoleType.DEVELOPER.getValue())) &&
            !CollectionUtils.isEmpty(serviceEntity.getCustomerTypeCode()) &&
            customerTypeEnum != null && customerTypeEnum != CustomerTypeEnum.UNSET &&
            !serviceEntity.getCustomerTypeCode().contains(customerTypeEnum.getValue())) {
            throw exceptionFactory.badRequest(MessageKeyConstant.INVALID_CUSTOMER_TYPE, Resources.USER, ErrorKey.CUSTOMER_TYPE,
                customerTypeEnum.getValue(), String.valueOf(serviceEntity.getCustomerTypeCode()));
        }

        //lay cau hinh seo
        SeoDTO seoDTO = seoRepository.getSeoDetailService(serviceId, SeoTypeCodeConstant.CAU_HINH_DICH_VU);

        Long currentUserId = Objects.nonNull(userLogin) ? userLogin.getId() : -1L;
        ServiceDetailResponseDTO responseDTO = serviceRepository.getDetailService(serviceId, currentUserId);
        responseDTO.setNumOfAllSub(serviceRepository.countSubByServiceId(serviceEntity.getId()));
        responseDTO.setServiceDraftId(serviceEntity.getServiceDraftId());
        responseDTO.setSnapshots(fileAttachRepository.getLstServiceFileAttach(serviceId));
        responseDTO.setPlans(serviceRepository.getLstSubPlanOfServiceAdmin(serviceId));
        List<ServiceDetailFileAttachDTO> listIconDevice = fileAttachRepository.getLstServiceFileAttachAvatarDevice(serviceId,
            FileAttachTypeEnum.AVATAR_DEVICE.value);
        responseDTO.setIconService(listIconDevice);
        if (Objects.isNull(responseDTO.getIcon()) && !CollectionUtils.isEmpty(listIconDevice)) {
            ServiceDetailFileAttachDTO itemDefault = listIconDevice.get(0);
            responseDTO.setIcon(itemDefault.getFilePath());
            responseDTO.setExternalLinkIcon(itemDefault.getExternalLink());
        }
        ComponentVisible techVisible = ComponentVisible.of(serviceEntity.getTechVisible());
        ComponentVisible featureVisible = ComponentVisible.of(serviceEntity.getFeatureVisible());
        ComponentVisible techLayout = ComponentVisible.of(serviceEntity.getTechLayout());
        responseDTO.setTechVisible(techVisible.getValue());
        responseDTO.setFeatureVisible(featureVisible.getValue());
        responseDTO.setTechLayout(techLayout.getValue());
        if (Objects.isNull(serviceEntity.getServiceTypeApplication())) {
            var lstSeoCategory = categoryRepository.getSeoCategoryByService(responseDTO.getIdCategory());
            if (!lstSeoCategory.isEmpty()) {
                SeoCategoryDTO seoCategoryDTO = lstSeoCategory.get(0);
                responseDTO.setPlanUrlCategory(seoCategoryDTO.getPlanUrl());
                responseDTO.setSeoCategoryDTOList(seoCategoryDTO);
            }
        }

        if(techVisible == ComponentVisible.VISIBLE) {
            responseDTO.setTechnologies(fileAttachRepository.getLstServiceTech(serviceId));
        }

        if (featureVisible == ComponentVisible.VISIBLE) {
            responseDTO.setFeatures(featureRepository.getListFeatureWithFile(serviceId));
        }

        // nếu là ứng dụng -> lấy thông tin api cung cấp
        if (Objects.nonNull(serviceEntity.getServiceTypeApplication())) {
            responseDTO.setPermissionIds(getAppRoleIdsByApiKey(serviceEntity.getApiKey()));
            responseDTO.setCategoriesApp(Arrays.stream(serviceEntity.getCategoriesApp()).collect(Collectors.toList()));
        }

        // Lưu lại lịch sử xem gói của người dùng
        if (userLogin != null) {
            serviceViewService.create(serviceId, userLogin.getId(), ServiceViewTypeEnum.SERVICE.getValue());
        }

        setGuide(responseDTO, serviceId);
        setSupportInfo(responseDTO, serviceId);
        setCouponInfo(responseDTO, serviceId, customerTypeEnum);
        setCampaignInfo(responseDTO, serviceId);

        if (Objects.equals(serviceEntity.getClassification(), ProductClassificationEnum.PHYSICAL)) {
            // trả ra thông tin mapping giữa pricing và variant
            List<IVariantIdAndPricingIdDTO> lstPricingVariant = pricingVariantRepository.getVariantIdAndPricingIdMapByServiceId(serviceId);
            responseDTO.setVariantIdToListPricingId(lstPricingVariant.stream()
                .collect(Collectors.groupingBy(
                    IVariantIdAndPricingIdDTO::getVariantId,
                    Collectors.mapping(IVariantIdAndPricingIdDTO::getPricingId, Collectors.toList())
                )));
        }

        responseDTO.setSeoDTO(seoDTO);
        if (!CollectionUtils.isEmpty(serviceEntity.getCustomerTypeCode())) {
            responseDTO.setCustomerType(serviceEntity.getCustomerTypeCode().stream().map(CustomerTypeEnum::getValueOf)
                .collect(Collectors.toSet()));
        } else {
            responseDTO.setCustomerType(new HashSet<>());
        }
        //lấy thông tin avg rating và tên người đánh giá mới nhất
        responseDTO.setAvgRating(serviceRepository.getRatingAvgService(serviceId));
        responseDTO.setUserLatestRating(serviceRepository.getUserRatingLatestOfService(serviceId));

        if ((Objects.nonNull(responseDTO.getServiceTypeApplication()) || responseDTO.getAllowMultiSub() == 0) && currentParentId != null) {
            // Lấy số sub của user và dịch vụ khi multi sub mode bị tắt
            responseDTO.setNumSub(shoppingCartRepository.getNumSubOfServiceUser(true,
                responseDTO.getId(), currentParentId));
        }

        responseDTO.setServiceTopics(topicFaqService.getListTopicByServiceForSME(serviceId, ServiceEnum.SERVICE.getValue()));

        // Bổ sung thông tin custom field
        Long layoutId = Objects.nonNull(serviceEntity.getCreationLayoutId()) ? serviceEntity.getCreationLayoutId() : -1L;

        layoutId = customLayoutRepository.findCustomByByIsDefaultAndVersion(layoutId);

        List<CustomFieldValueDTO> lstFieldValueDTO = customFieldManager.getListFieldValue(layoutId, EntityTypeEnum.SERVICE.getValue(),
            serviceId);
        responseDTO.setCreationLayoutId(layoutId);
        responseDTO.setLstCustomField(lstFieldValueDTO);

        if (Objects.nonNull(serviceEntity.getProductType())) {
            responseDTO.setProductType(serviceEntity.getProductType().value);
        }
        // Cập nhật productType cho SPDV hang hoa vat ly
        if (ProductClassificationEnum.PHYSICAL.equals(serviceEntity.getClassification())) {
            responseDTO.setProductType(ServiceProductTypeEnum.DEVICE.getValue());
        }
        //Lấy danh sách all tính năng của spdv
        if (featureVisible == ComponentVisible.VISIBLE) {
            responseDTO.setFeatureList(featureRepository.findAllFeatureByServiceId(serviceId));
        }
        responseDTO.setPaymentMethod(PaymentMethodEnum.fromValue(serviceEntity.getPaymentMethod()));
        if (Objects.isNull(responseDTO.getAttributeVariant()) || Objects.equals(responseDTO.getAttributeVariant(), Boolean.FALSE)) {
            responseDTO.setAttributeVariant(mappingAttributesServiceRepository.existsByServiceId(responseDTO.getId()));
        }
        responseDTO.setIsCancel(userLogin != null ? subscriptionRepository.checkExistsAppByStatus(serviceId, userLogin.getId()) : Boolean.FALSE);
        // Lấy thông tin danh mục
        responseDTO.setLstCategory(categoriesRepository.getCategoryDetailByServiceId(serviceId));
        // Lấy thông tin flash sale
        responseDTO.setCouponFlashSaleDetailDTO(couponRepository.findCouponFlashSaleByServiceId(serviceId));
        return responseDTO;
    }

    @Override
    public ServiceDetailResAdminDTO getDetailServiceForAdmin(Long serviceId) {
        getCurrentService(serviceId);
        ServiceDetailResAdminDTO restDto = serviceRepository.getDetailServiceForAdmin(serviceId);
        restDto.setSnapshots(fileAttachRepository.getLstServiceFileAttach(serviceId));
        restDto.setPlans(serviceRepository.getLstSubPlanOfServiceAdmin(serviceId));
        return restDto;
    }

    @Override
    @Transactional
    public void requestApprove(Long serviceId, Boolean approvedNow) {
        ServiceEntity currentService = getCurrentService(serviceId);
        ServiceDraft serviceDraft = findByServiceId(serviceId);
        this.validateServicePermissionDev(Collections.singletonList(currentService));
        boolean checkApproveStatus;
        if (Objects.nonNull(serviceDraft)) {
            checkApproveStatus = ServiceApproveStatusEnum.UNAPPROVED.equals(serviceDraft.getApprove())
                                || ServiceApproveStatusEnum.REJECTED.equals(serviceDraft.getApprove());
        } else {
            checkApproveStatus = ServiceApproveStatusEnum.UNAPPROVED.equals(currentService.getApprove())
                                || ServiceApproveStatusEnum.REJECTED.equals(currentService.getApprove());
        }

        if (checkApproveStatus) {
            if (Objects.nonNull(serviceDraft)) {
                serviceDraft.setApprove(ServiceApproveStatusEnum.AWAITING_APPROVAL);
                serviceDraft.setModifiedBy(AuthUtil.getCurrentUserId());
            } else {
                currentService.setApprove(ServiceApproveStatusEnum.AWAITING_APPROVAL);
                currentService.setModifiedBy(AuthUtil.getCurrentUserId());
            }
        } else {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.STATUS,
                    MessageKeyConstant.SERVICE_APPROVE_STATUS_CAN_NOT_CHANGE);
        }
        // Kiểm tra customerType của các Addon , pricing có liên quan
        val listAddon = addonDraftRepository.findAllByServiceIdAndDeletedFlagAndApprove(currentService.getId() , 1 , ApproveStatusEnum.APPROVED.value);
        val listPricing = pricingDraftRepository.findByServiceIdAndDeletedFlagAndApprove(currentService.getId() , 1 , ApproveStatusEnum.APPROVED.value);
        Set<String> serviceCustomerTypeCode = Objects.nonNull(serviceDraft) ? serviceDraft.getCustomerTypeCode() : currentService.getCustomerTypeCode();
        for(AddonDraft addon : listAddon){
            if(!serviceCustomerTypeCode.containsAll(addon.getCustomerTypeCode())){
                throw new BadRequestException("CustomerTypeCode của SPDV phê duyệt không thỏa mãn", Resources.SERVICES, ErrorKey.Services.CUSTOMER_TYPE,
                        MessageKeyConstant.PRICING_CUSTOMER_TYPE_NOT_AVAILABLE);
            }
        }
        for(PricingDraft pricing : listPricing){
            if(!serviceCustomerTypeCode.containsAll(pricing.getCustomerTypeCode())){
                throw new BadRequestException("CustomerTypeCode của SPDV phê duyệt không thỏa mãn", Resources.SERVICES, ErrorKey.Services.CUSTOMER_TYPE,
                        MessageKeyConstant.PRICING_CUSTOMER_TYPE_NOT_AVAILABLE);
            }
        }
        addServiceHistory(currentService, ApproveStatusEnum.AWAITING_APPROVAL, PortalType.DEV, serviceDraft);
        serviceRepository.save(currentService);
        if(!approvedNow) {
            // send mail in separate thread
            executor.execute(() -> sendRequestApproveMailAndNotification(currentService));
        }
    }

    private void sendRequestApproveMailAndNotification(ServiceEntity currentService) {
        log.info("================== start thread sendRequestApproveMailAndNotification ==================");
        // Kiểm tra email có tồn tại
        String emailTo = findUserById(currentService.getCreatedBy()).getEmail();
        sendMailApproveService(EmailCodeEnum.SV01, currentService, emailTo);
        sendMailApproveRequestForAdmin(currentService);
        // Thông báo firebase
        notifyUtil(evn.getProperty(NotifyConst.SV01_CONTENT), currentService, evn.getProperty(NotifyConst.SV01_TITLE), EmailCodeEnum.SV01.getValue());
        notifyRequestApproveForAdmin(currentService);
        log.info("================== end thread sendRequestApproveMailAndNotification ==================");
    }

    @Override
    @Transactional
    public void requestMultiApprove(List<Long> lstServiceId) {
        List<ServiceEntity> lstService = new ArrayList<>();
        lstServiceId.forEach(serviceId -> {
            ServiceEntity currentService = getCurrentService(serviceId);
            ServiceDraft serviceDraft = findByServiceId(serviceId);
            this.validateServicePermissionDev(Collections.singletonList(currentService));
            boolean checkApproveStatus;
            if (Objects.nonNull(serviceDraft)) {
                checkApproveStatus = ServiceApproveStatusEnum.UNAPPROVED.equals(serviceDraft.getApprove())
                        || ServiceApproveStatusEnum.REJECTED.equals(serviceDraft.getApprove());
            } else {
                checkApproveStatus = ServiceApproveStatusEnum.UNAPPROVED.equals(currentService.getApprove())
                        || ServiceApproveStatusEnum.REJECTED.equals(currentService.getApprove());
            }

            if (checkApproveStatus) {
                if (Objects.nonNull(serviceDraft)) {
                    serviceDraft.setApprove(ServiceApproveStatusEnum.AWAITING_APPROVAL);
                    serviceDraft.setModifiedBy(AuthUtil.getCurrentUserId());
                } else {
                    currentService.setApprove(ServiceApproveStatusEnum.AWAITING_APPROVAL);
                    currentService.setModifiedBy(AuthUtil.getCurrentUserId());
                }
            } else {
                String message = messageSource
                        .getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.STATUS,
                        MessageKeyConstant.SERVICE_APPROVE_STATUS_CAN_NOT_CHANGE);
            }
            // Kiểm tra customerType của các Addon , pricing có liên quan
            val listAddon = addonDraftRepository.findAllByServiceIdAndDeletedFlagAndApprove(currentService.getId() , 1 , ApproveStatusEnum.APPROVED.value);
            val listPricing = pricingDraftRepository.findByServiceIdAndDeletedFlagAndApprove(currentService.getId() , 1 , ApproveStatusEnum.APPROVED.value);
            for(AddonDraft addon : listAddon){
                if(!serviceDraft.getCustomerTypeCode().containsAll(addon.getCustomerTypeCode())){
                    throw new BadRequestException("CustomerTypeCode của SPDV phê duyệt không thỏa mãn", Resources.SERVICES, ErrorKey.Services.CUSTOMER_TYPE,
                            MessageKeyConstant.PRICING_CUSTOMER_TYPE_NOT_AVAILABLE);
                }
            }
            for(PricingDraft pricing : listPricing){
                if(!serviceDraft.getCustomerTypeCode().containsAll(pricing.getCustomerTypeCode())){
                    throw new BadRequestException("CustomerTypeCode của SPDV phê duyệt không thỏa mãn", Resources.SERVICES, ErrorKey.Services.CUSTOMER_TYPE,
                            MessageKeyConstant.PRICING_CUSTOMER_TYPE_NOT_AVAILABLE);
                }
            }
            // Kiểm tra email có tồn tại
            addServiceHistory(currentService, ApproveStatusEnum.AWAITING_APPROVAL, PortalType.DEV, serviceDraft);

            //lưu thông tin dịch vụ để gửi mail, thông báo qua bath
            lstService.add(currentService);
            serviceRepository.save(currentService);
        });
        //lưu thông tin dịch vụ để gửi mail, thông báo qua bath
        saveRequestApprove(lstService);
    }

    private void saveRequestApprove (List<ServiceEntity> lstService) {
        List<RequestApprove> approveList = new ArrayList<>();
        if(!lstService.isEmpty()) {
            lstService.forEach(s -> approveList.add(
                new RequestApprove(null, RequestApproveTypeEnum.SERVICE.getValue(), s.getId(), SentFlagEnum.UNSENT.getValue(), new Date(),
                    null)));
            requestApproveRepository.saveAll(approveList);
        }
    }

    /**
     * Thông báo có service yêu cầu phê duyệt trên màn hình Admin
     *
     */
    private void notifyRequestApproveForAdmin(ServiceEntity service) {
        log.info("================== start notifyRequestApproveForAdmin - start sending notif to admins ==================");
        // lay ra danh sach user co quyen FULL_ADMIN
        List<UserAdminDTO> userAdmins = userRepository.getLstFullAdminDetail();
        User developer = findUserById(service.getCreatedBy());
        String content = ObjectUtil.getOrDefault(evn.getProperty(NotifyConst.SV02_CONTENT), CharacterConstant.BLANK);
        List<NotificationDTO> notifyDTOs = new ArrayList<>();
        userAdmins.forEach(user -> {
            val notifyModel = NotificationDTO.builder()
                    .title(evn.getProperty(NotifyConst.SV02_TITLE))
                .body(String.format(content, service.getServiceName(), developer.getName()))
                    .screenId(EmailCodeEnum.SV02.getValue())
                    .userId(user.getId())
                    .portalType(PortalType.ADMIN.getType())
                    .status(StatusEnum.INACTIVE.value)
                    .createdAt(LocalDateTime.now())
                    .objectId(service.getId())
                    .build();
            notifyDTOs.add(notifyModel);
        });
        if (!CollectionUtils.isEmpty(notifyDTOs)) {
            NotifyUtil.sendNotify(notifyDTOs, EmailCodeEnum.SV02.getValue());
        }
        log.info("================== end notifyRequestApproveForAdmin - end sending notif to admins ==================");
    }

    // Gửi email cho Admin
    private void sendMailApproveRequestForAdmin(ServiceEntity currentService) {
        log.info("================== start sendMailApproveRequestForAdmin - start sending mail to admins ==================");
        String nameDeveloper = findUserById(currentService.getUserId()).getName();
        // lay ra danh sach user co quyen FULL_ADMIN
        List<UserAdminDTO> userAdmin = userRepository.getLstFullAdminDetail();
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(EmailCodeEnum.SV02.getValue());
        // hotline nền tảng
        String hotlineParam = systemParamService.getHotlineParam();
        userAdmin.forEach(admin -> {
            paramNameByCode.forEach(mailParamResDTO -> {
                if (ParamEmailEnum.USER.getValue().equals(mailParamResDTO.getParamName())) {
                    mailParamResDTO.setValue(admin.getLastName() + " " + admin.getFirstName());
                } else if (ParamEmailEnum.NAME_SERVICE.getValue().equals(mailParamResDTO.getParamName())) {
                    mailParamResDTO.setValue(Objects.isNull(currentService.getServiceName()) ? "" : currentService.getServiceName());
                } else if (ParamEmailEnum.NAME_DEVELOPER.getValue().equals(mailParamResDTO.getParamName())) {
                    mailParamResDTO.setValue(Objects.isNull(nameDeveloper) ? "" : nameDeveloper);
                } else if (ParamEmailEnum.HOTLINE.getValue().equals(mailParamResDTO.getParamName())) {
                    mailParamResDTO.setValue(hotlineParam);
                } else {
                    mailParamResDTO.setValue("");
                }
            });
            emailService.sendMail(admin.getEmail(), EmailCodeEnum.SV02, paramNameByCode);
        });
        log.info("================== end sendMailApproveRequestForAdmin - end sending mail to admins ==================");
    }

    /**
     * Luu file
     *
     * @param multipartFile File
     * @return File da luu
     */
    private FileAttach saveFileAttach(MultipartFile multipartFile, Long fileSize, String link) {
        FileAttach file = FileAttach.builder()
                .accessType(AccessTypeEnum.PUBLIC.value)
                .modifiedBy(AuthUtil.getCurrentUserId())
                .build();

        // Thuc hien gan gia tri khi file ton tai
        if (Objects.nonNull(multipartFile)) {
            int maxLength;
            if (multipartFile.getOriginalFilename() != null && multipartFile.getOriginalFilename().length() < 199) {
                maxLength = multipartFile.getOriginalFilename().length();
            } else {
                maxLength = 199;
            }
            file.setFileName(multipartFile.getOriginalFilename().substring(0,maxLength));//substring(0,199)) lay filename max
            // 200 ky tu
            file.setFilePath(FileUtil.uploadFile(multipartFile, Resources.SERVICES, environment));
            file.setFileSize(fileSize);
            String fileExtension = FileUtils.getFileExtension(Objects.requireNonNull(multipartFile.getOriginalFilename())).toUpperCase();
            switch (fileExtension) {
                case FileUtil.Extension.Image.GIF:
                case FileUtil.Extension.Image.JPG:
                case FileUtil.Extension.Image.JPEG:
                case FileUtil.Extension.Image.PNG:
                case FileUtil.Extension.Image.WEBP:
                case FileUtil.Extension.Image.TIFF:
                case FileUtil.Extension.Image.JFIF:
                    file.setFileType(1);
                    break;
                case FileUtil.Extension.Video.AVI:
                case FileUtil.Extension.Video.MKV:
                case FileUtil.Extension.Video.FLV:
                case FileUtil.Extension.Video.MP4:
                case FileUtil.Extension.Video.MOV:
                    file.setFileType(2);
                    break;
                default:
                    break;

            }
        } else if (Objects.nonNull(link)) {
            // thuc hien trim link khi vuot qua 200 ky tu
            if (link.length() <= 200) {
                file.setFileName(link);
            } else {
                String start = StringUtils.substring(link, 0, 100);
                String end = StringUtils.substring(link, link.length() - 101, link.length() - 1);
                file.setFileName(start + end);
            }
            String fileExtension = FileUtils.getFileExtension(file.getFileName()).toUpperCase();
            switch (fileExtension) {
                case FileUtil.Extension.Image.GIF:
                case FileUtil.Extension.Image.JPG:
                case FileUtil.Extension.Image.JPEG:
                case FileUtil.Extension.Image.PNG:
                case FileUtil.Extension.Image.WEBP:
                case FileUtil.Extension.Image.TIFF:
                case FileUtil.Extension.Image.JFIF:
                    file.setFileType(1);
                    break;
                case FileUtil.Extension.Video.AVI:
                case FileUtil.Extension.Video.MKV:
                case FileUtil.Extension.Video.FLV:
                case FileUtil.Extension.Video.MP4:
                case FileUtil.Extension.Video.MOV:
                    file.setFileType(2);
                    break;
                default:
                    break;

            }
            if(file.getFileName().contains("youtube.com")){
                file.setFileType(2);
            }
            file.setExternalLink(link);
            file.setFileSize(fileSize);
        }
        return file;
    }

    @Override
    public ServiceEntity getCurrentService(Long serviceId) {
        return findByIdAndDeletedFlag(serviceId, DeletedFlag.NOT_YET_DELETED.getValue());
    }

    /**
     * validate service draft permission
     */
    private void validateServiceDraftPermissionDev(List<ServiceDraft> serviceDraft) {
        if (serviceDraft.isEmpty()) {
            return;
        }
        serviceDraft.forEach(s -> {
            if (AuthUtil.checkUserRoles(
                Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_OPERATOR.getValue(), RoleType.DEVELOPER_BUSINESS.getValue()))
                && !Objects.equals(s.getUserId(), AuthUtil.getCurrentParentId())) {
                String message = messageSource
                    .getMessage(MessageKeyConstant.USER_NOT_HAVE_PERMISSION, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
                throw new ResourceNotFoundException(message, Resources.SERVICES, ErrorKey.ID, MessageKeyConstant.USER_NOT_HAVE_PERMISSION);
            }
        });
    }

    /**
     * validate service permission
     */
    private void validateServicePermissionDev(List<ServiceEntity> services) {
        if (services.isEmpty()) {
            return;
        }
        services.forEach(s -> {
            if (AuthUtil.checkUserRoles(
                Arrays.asList(RoleType.DEVELOPER.getValue(), RoleType.DEVELOPER_OPERATOR.getValue(), RoleType.DEVELOPER_BUSINESS.getValue()))
                && !Objects.equals(s.getUserId(), AuthUtil.getCurrentParentId())) {
                String message = messageSource
                    .getMessage(MessageKeyConstant.USER_NOT_HAVE_PERMISSION, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
                throw new ResourceNotFoundException(message, Resources.SERVICES, ErrorKey.ID, MessageKeyConstant.USER_NOT_HAVE_PERMISSION);
            }
        });
    }

    @Override
    public Page<ServiceResponseTransDTO> getListServiceNew(String search, Pageable pageable, Long category, CustomerTypeEnum customerType) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeValue = userLogin != null && AuthUtil.getPortalOfUserRoles().equals(PortalType.SME) ? userLogin.getCustomerType() : customerType.getValue();
        return convertServiceEntityPage(serviceRepository.getPageServiceNewestId(search, category, customerTypeValue,
            Collections.singleton(ServiceProductTypeEnum.UNSET.value), pageable));
    }

    @Override
    public Page<ServiceResponseTransDTO> getListServiceTrending(String search, String date, Pageable pageable, Long category,
        CustomerTypeEnum customerType) {
        String dateInput = StringUtils.equals(date, "today") ? DateFormat.getDateInstance().format(new Date())
            : changePatternOfDateString(date);
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeValue = userLogin != null && AuthUtil.getPortalOfUserRoles().equals(PortalType.SME) ? userLogin.getCustomerType() : customerType.getValue();
        Page<BigInteger> lstPageId = serviceRepository.getPageServiceTrendingId(search, dateInput, category,
            customerTypeValue, pageable);
        List<ServiceResponseDTO> lstServiceDetail = serviceRepository.getListServiceTrendingDetailByIdIn(dateInput, lstPageId.stream()
            .map(BigInteger::longValue).collect(Collectors.toSet()), AuthUtil.getDefaultCurrentParentId());
        return convertServiceDetailPage(lstPageId.map(
            id -> lstServiceDetail.stream().filter(item -> Objects.equals(item.getId(), id.longValue())).findFirst().orElse(null)));
    }

    @Override
    public Page<ServiceResponseTransDTO> getListComboTrending(String search, String date, Pageable pageable, Long category,
                                                                CustomerTypeEnum customerType) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeQuery = Objects.isNull(userLogin) ? customerType.getValue() : userLogin.getCustomerType();
        String dateInput = StringUtils.equals(date, "today")
                ? DateFormat.getDateInstance().format(new Date())
            : changePatternOfDateString(date);
        Page<ServiceResponseDTO> dtoPage = serviceRepository.
                getListComboTrending(search, dateInput, 30, pageable, customerTypeQuery);
        return convertComboPage(dtoPage);
    }

    @Override
    @Transactional
    public Long createServiceIntegrate(CreateServiceIntegrateDTO paramDTO) {
        String languageValid = validateLanguage(paramDTO.getLanguage());
        ServiceEntity service = ServiceEntity.builder()
                .serviceName(paramDTO.getName())
                .languageType(languageValid)
                .approve(ServiceApproveStatusEnum.APPROVED)
                .userId(AuthUtil.getCurrentParentId())
                .apiKey(UUID.randomUUID().toString())
                .secretKey(UUID.randomUUID().toString())
                .description(!StringUtils.isEmpty(paramDTO.getDescription()) ? paramDTO.getDescription() : null)
                .sapoDescription(!StringUtils.isEmpty(paramDTO.getShortDescription()) ? paramDTO.getShortDescription() : null)
                .smeServiceId(paramDTO.getId())
                .build();
        service.setStatus(ServiceStatusEnum.VISIBLE.value);
        service.setDeletedFlag(EntitiesConstant.DeleteFlag.ACTIVE);
        service.setCreatedSourceMigration(CreatedSourceMigration.ONE_SME);
        service.setCreatedBy(AuthUtil.getCurrentUserId());

        validateCategoryForIntegrateService(paramDTO, service);

        DevelopIntegrateDTO createDevDTO = paramDTO.getDeveloper();
        //neu dev chua ton tai thi tao moi
        if (checkExistDev(createDevDTO) == null) {
            String passRandom = RandomStringUtils.random(10, true, true);
            User user = userIntegrationMapper.toEntity(createDevDTO);
            user.setParentId(-1L);
            user.setPassword(passwordEncoder.encode(passRandom));
            Set<Role> roles = new HashSet<>();
            roleRepository.findById(RoleConst.ROLE_DEVELOPER).ifPresent(roles::add);
            user.setRoles(roles);
            user.setUserCode(generateUserCode());
            user.setModifiedAt(LocalDateTime.now());
            if (Objects.equals(user.getPhoneNumber(), "")) {
                user.setPhoneNumber(null);
            }
            user = userRepository.save(user);
            //gui email thong bao mat khau cho developer
            EmailTemplate template = EmailTemplate.builder()
                    .subject(EmailConst.SUBJECT_MAIL_ACTIVE)
                    .content(EmailUtil.createTemplateCreateAccount(user.getFirstName(), user.getLastName(), passRandom))
                    .build();
            emailService.save(user.getEmail(), template);
            service.setUserId(user.getId());
        }
        //neu dev ton tai r thi luu id vao service moi
        else {
            service.setUserId(checkExistDev(createDevDTO));
        }
        service = serviceRepository.save(service);
        saveFileAttachOfService(paramDTO, service, false);
        return service.getId();
    }

    /**
     * kiểm tra category của service đồng bộ
     *
     */
    private void validateCategoryForIntegrateService(CreateServiceIntegrateDTO paramDTO, ServiceEntity service) {
        Optional<Category> categoryOptional = categoriesRepository.findBySmeCategoryId(paramDTO.getCategoryId());
        if (!categoryOptional.isPresent()) {
            Category category = Category.builder()
                    .name(paramDTO.getCategoryName())
                    .smeCategoryId(paramDTO.getCategoryId())
                    .build();
            category = categoriesRepository.save(category);
            service.setCategoriesId(category.getId());
        } else {
            service.setCategoriesId(categoryOptional.get().getId());
        }
    }

    @Override
    @Transactional
    public void updateServiceIntegrate(CreateServiceIntegrateDTO paramDTO, Long id) {
        ServiceEntity serviceEntity = serviceRepository.findBySmeServiceId(id).orElseThrow(() -> {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            return new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.ID,
                    MessageKeyConstant.NOT_FOUND);
        });
        if (paramDTO.getLanguage() != null) {
            serviceEntity.setLanguageType(validateLanguage(paramDTO.getLanguage()));
        }
        if (paramDTO.getCategoryId() != null && !StringUtils.isEmpty(paramDTO.getCategoryName())) {
            validateCategoryForIntegrateService(paramDTO, serviceEntity);
        }
        serviceEntity.setServiceName(
                !StringUtils.isEmpty(paramDTO.getName()) ? paramDTO.getName() : serviceEntity.getServiceName());
        serviceEntity.setDescription(!StringUtils.isEmpty(paramDTO.getDescription()) ?
                paramDTO.getDescription() :
                serviceEntity.getDescription());
        serviceEntity.setSapoDescription(!StringUtils.isEmpty(paramDTO.getShortDescription()) ?
                paramDTO.getShortDescription() :
                serviceEntity.getSapoDescription());
        Date now = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        serviceEntity.setModifiedAt(now);
        serviceEntity.setModifiedBy(AuthUtil.getCurrentUserId());
        serviceEntity = serviceRepository.save(serviceEntity);
        saveFileAttachOfService(paramDTO, serviceEntity, true);
    }


    /**
     * Lưu bản ghi fileAttach cho dịch vụ vừa được đồng bộ từ ĐHSXKD
     *
     */
    private void saveFileAttachOfService(CreateServiceIntegrateDTO paramDTO, ServiceEntity serviceEntity, Boolean isUpdate) {
        Long serviceId = serviceEntity.getId();
        List<FileAttach> fileAttachNeedToCreate = new ArrayList<>();
        Date now = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        // tao ban ghi fileAttach luu avatar cua service vua tao
        if (!StringUtils.isEmpty(paramDTO.getIcon())) {
            //neu la truong hop update thi xoa cac ban ghi file avatar cu roi moi luu lai ban ghi moi
            if (BooleanUtils.isTrue(isUpdate)) {
                fileAttachRepository.deleteAllByServiceIdAndObjectType(serviceId, FileAttachTypeEnum.AVATAR.value);
            }
            FileAttach icon = FileAttach.builder()
                    .serviceId(serviceId)
                    .externalLink(paramDTO.getIcon())
                    .objectType(FileAttachTypeEnum.AVATAR.value)
                    .modifiedAt(now)
                    .modifiedBy(AuthUtil.getCurrentUserId())
                    .build();
            fileAttachNeedToCreate.add(icon);
        }
        // tao ban ghi fileAttach luu intro cua service vua tao
        if (!StringUtils.isEmpty(paramDTO.getVideo())) {
            //neu la truong hop update thi xoa cac ban ghi file intro cu roi moi luu lai ban ghi moi
            if (BooleanUtils.isTrue(isUpdate)) {
                fileAttachRepository.deleteAllByServiceIdAndObjectType(serviceId, FileAttachTypeEnum.INTRO.value);
            }
            FileAttach intro = FileAttach.builder()
                    .serviceId(serviceId)
                    .externalLink(paramDTO.getVideo())
                    .objectType(FileAttachTypeEnum.INTRO.value)
                    .modifiedAt(now)
                    .modifiedBy(AuthUtil.getCurrentUserId())
                    .build();
            fileAttachNeedToCreate.add(intro);
        }
        // tao cac ban ghi fileAttach luu anh chup man hinh cua service vua tao
        if (!CollectionUtils.isEmpty(paramDTO.getSnapshots())) {
            //neu la truong hop update thi xoa cac ban ghi file capture cu roi moi luu lai ban ghi moi
            if (BooleanUtils.isTrue(isUpdate)) {
                fileAttachRepository.deleteAllByServiceIdAndObjectType(serviceId, FileAttachTypeEnum.CAPTURE.value);
            }
            paramDTO.getSnapshots().forEach(snapShot -> {
                FileAttach capture = FileAttach.builder()
                        .serviceId(serviceId)
                        .externalLink(snapShot.getUrl())
                        .priority(snapShot.getPriority())
                        .objectType(FileAttachTypeEnum.CAPTURE.value)
                        .modifiedAt(now)
                        .modifiedBy(AuthUtil.getCurrentUserId())
                        .build();
                fileAttachNeedToCreate.add(capture);
            });
        }
        if (!CollectionUtils.isEmpty(fileAttachNeedToCreate)) {
            fileAttachRepository.saveAll(fileAttachNeedToCreate);
        }
    }

    /**
     * kiem tra developer da ton tai chua
     *
     */
    private Long checkExistDev(DevelopIntegrateDTO paramDTO) {
        Optional<User> user = userRepository
                .findByEmailAndPhoneNumberAndName(paramDTO.getEmail(), paramDTO.getPhoneNo(), paramDTO.getName());
        if (!user.isPresent()) {
            if (userRepository.countByEmail(paramDTO.getEmail()) > 0
             || userRepository.countByPhoneNumber(paramDTO.getPhoneNo()) > 0
             || userRepository.countByName(paramDTO.getName()) > 0) {
                String message = messageSource.getMessage(MessageKeyConstant.DATA_EXISTS, DEVELOPER_MESSAGE,
                        LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICES, ErrorKey.USER_ID, MessageKeyConstant.DATA_EXISTS);
            }
            return null;
        }
        return user.get().getId();
    }

    /**
     * tao userCode cho developer
     *
     */
    private String generateUserCode() {
        long now = new Date().getTime();
        String time = Long.toString(now);
        int size = time.length();
        return String.format("%s-%s-%s-%s",
                time.substring(size - 13, size - 9),
                time.substring(size - 9, size - 5),
                time.substring(size - 5, size - 2),
                time.substring(size - 2, size));
    }

    /**
     *
     */
    @Transactional
    public void adminUpdateApproveStatus(ServiceApproveDTO serviceApproveDTO, ServiceEntity currentService, Long serviceId) {
        ServiceDraft serviceDraft = findByServiceId(currentService.getId());
        boolean checkApproveStatus = ServiceApproveStatusEnum.AWAITING_APPROVAL.equals(currentService.getApprove());
        if (Objects.nonNull(serviceDraft)) {
            checkApproveStatus = ServiceApproveStatusEnum.AWAITING_APPROVAL.equals(serviceDraft.getApprove());
            serviceDraft.setApprove(serviceApproveDTO.getApproveStatus());
            serviceDraft.setComment(serviceApproveDTO.getComment());
            serviceDraftRepository.save(serviceDraft);
        } else {
            currentService.setApprove(serviceApproveDTO.getApproveStatus());
            currentService.setComment(serviceApproveDTO.getComment());
            serviceRepository.save(currentService);
        }
        if (ServiceTypeEnum.VNPT.getValue().equals(currentService.getServiceOwner())) {
            currentService.setTopicName(TOPIC_PRODUCER_NAME.concat("-").concat(serviceId.toString()));
        }
        String emailTo = findUserById(currentService.getCreatedBy()).getEmail();
        ApproveStatusEnum statusEnum;
        // Kiểm tra mail tồn tại
        if (Objects.isNull(emailTo)) {
            throw throwServiceBadRequest(MessageKeyConstant.SEND_MAIL_ERROR, ErrorKey.User.EMAIL, new String[]{ErrorKey.User.EMAIL});
        }
        if (checkApproveStatus) {
            switch (serviceApproveDTO.getApproveStatus()) {
                case APPROVED:
                    //
                    boolean isChangeVariant;
                    currentService.setApproveAt(new Date());
                    currentService.setApproveBy(AuthUtil.getCurrentUserId());
                    if (Objects.nonNull(serviceDraft)) {
                        isChangeVariant = Objects.equals(serviceDraft.getChangeVariant(), ServiceApproveStatusEnum.UNAPPROVED.value);
                        if (Objects.nonNull(serviceDraft.getPrice()) && (Objects.nonNull(currentService.getPrice()) && serviceDraft.getPrice().compareTo(currentService.getPrice()) != 0)) {
                            updateVariantTotalPrice(serviceDraft);
                        }
                        approveDataService(serviceDraft, currentService);
                        // Khi phê duyệt lần thứ 2 trở đi, dữ liệu nằm trong bảng draft, cần chuyển sang bảng value
                        customFieldManager.saveFieldDraftToFieldValue(EntityTypeEnum.SERVICE.getValue(), serviceDraft.getId(),
                            currentService.getId());
                    } else {
                        // tạo permission mặc định cho ứng dụng
                        createPermissionDefault(currentService);
                        isChangeVariant = Objects.equals(currentService.getChangeVariant(), ServiceApproveStatusEnum.UNAPPROVED.value);
                        serviceDraft = cloneDataService(currentService, ActionEnum.APPROVED);
                        customFieldManager.saveFieldValueToFieldDraft(EntityTypeEnum.SERVICE.getValue(), serviceDraft.getId(),
                            currentService.getId());
                        // Khi phê duyệt lần đầu, dữ liệu custom field value đã có sẵn, nên ko cần lưu lại
                    }
                    currentService.setServiceDraftId(serviceDraft.getId()); // lưu phiên bản mới nhất
                    currentService = serviceRepository.save(currentService);
                    // cập nhật variantDraft
                    if (isChangeVariant) {
                        List<Long> ids = variantDraftRepository.getListVariantIdByServiceId(serviceId);
                        List<Long> idsUnApproved = variantDraftRepository.getListVariantIdApproved(serviceId);
                        if (!CollectionUtils.isEmpty(idsUnApproved)) {
                            variantRepository.updateVariantUnApprovedByIds(idsUnApproved, 0);
                        }
                        if (!CollectionUtils.isEmpty(ids)) {
                            variantRepository.updateVariantApprovedByIds(ids, 1);
                        }
                    }

                    // sau khi approve dịch vụ/thiết bị:
                    // scan update nhóm sản phẩm:
                    // update thiết bị/dịch vụ thì phải update thiết bị/dịch vụ bị ảnh hưởng trong nhóm
                    // 5/8/2024: sau đó chuyển nhóm bị ảnh hưởng về trạng thái chưa duyệt (nếu nhóm đc tạo bởi DEV)
                    List<ServiceGroupDraft> lstGroupDraft = serviceGroupDraftRepository.getLstGroupDraftByServiceId(serviceId);
                    serviceGroupService.saveGroupDraftAsUnapproved(lstGroupDraft, Boolean.TRUE);

                    // update item variant trong group
                    List<ServiceGroupPricingItem> lstGroupPricingItem = serviceGroupPricingItemRepository.getLstGroupDraftVariantItemByServiceId(serviceId);
                    variantService.updateGroupVariantItemAfterUpdateVariant(serviceId, lstGroupPricingItem);

                    sendMailApproveService(EmailCodeEnum.SV03, currentService, emailTo);
                    notifyUtil(evn.getProperty(NotifyConst.SV03_CONTENT), currentService, evn.getProperty(NotifyConst.SV03_TITLE), EmailCodeEnum.SV03.getValue());
                    statusEnum = ApproveStatusEnum.APPROVED;
                    // dev => combo => chi tiết combo => update tổng giá khi update thiết bị không biến thể
                    if (Objects.equals(currentService.getProductType(), ServiceProductTypeEnum.DEVICE) || Objects.equals(currentService.getClassification(), ProductClassificationEnum.PHYSICAL)) {
                        BigDecimal price = Objects.nonNull(currentService.getPrice()) ? currentService.getPrice() : BigDecimal.ZERO;
                        price = getServicePrice(price, currentService.getTax());
                        List<ServiceEntity> entities = serviceRepository.findAllByServiceDraftId(serviceDraft.getId());
                        List<ComboPricing> comboPricings = comboPricingRepository.findAllByObjectIdInAndObjectType(
                                entities.stream().map(ServiceEntity::getId).collect(Collectors.toSet()), "DEVICE_NO_VARIANT");
                        if (!CollectionUtils.isEmpty(comboPricings)) {
                            BigDecimal finalPrice = price;
                            comboPricingRepository.saveAll(comboPricings.stream()
                                    .peek(comboPricing -> {
                                        long quantity = Objects.nonNull(comboPricing.getQuantity()) ? comboPricing.getQuantity() : 1L;
                                        long amount = quantity * finalPrice.longValue();
                                        long discountValue = Objects.nonNull(comboPricing.getDiscountValue()) ? comboPricing.getDiscountValue() : 0L;
                                        Integer discountType = Objects.nonNull(comboPricing.getDiscountType()) ? comboPricing.getDiscountType() : 0;
                                        if (discountType.equals(1)) {
                                            discountValue = amount * (discountValue / 100L);
                                        }
                                        comboPricing.setAmount(amount - discountValue);
                                    })
                                    .collect(Collectors.toList()));
                            Set<Long> comboPlanIds = comboPricings.stream().map(ComboPricing::getComboPlanId).filter(Objects::nonNull).collect(Collectors.toSet());
                            Set<Long> comboPlanDraftIds = comboPricings.stream().map(ComboPricing::getComboPlanDraftId).filter(Objects::nonNull).collect(Collectors.toSet());
                            if (!CollectionUtils.isEmpty(comboPlanIds)) {
                                comboPricingRepository.updateAmountComboPlanByIds(comboPlanIds);
                            }
                            if (!CollectionUtils.isEmpty(comboPlanDraftIds)) {
                                comboPricingRepository.updateAmountComboPlanDraftByIds(comboPlanDraftIds);
                            }
                        }
                    }
                    break;
                case REJECTED:
                    sendMailRejectedService(currentService, emailTo);
                    notifyUtil(evn.getProperty(NotifyConst.SV04_CONTENT), currentService,evn.getProperty(NotifyConst.SV04_TITLE), EmailCodeEnum.SV04.getValue());
                    statusEnum = ApproveStatusEnum.REJECTED;
                    break;
                case UNAPPROVED:
                    statusEnum = ApproveStatusEnum.UNAPPROVED;
                    break;
                default:
                    String message = messageSource
                            .getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
                    throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.STATUS,
                            MessageKeyConstant.SERVICE_APPROVE_STATUS_CAN_NOT_CHANGE);
            }
            addServiceHistory(currentService, statusEnum, PortalType.ADMIN, serviceDraft);
            // Cập nhật mview_service_detail
            if (Objects.equals(statusEnum, ApproveStatusEnum.APPROVED)) {
                serviceRepository.refreshMviewServiceDetail();
            }
        } else {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.STATUS,
                    MessageKeyConstant.SERVICE_APPROVE_STATUS_CAN_NOT_CHANGE);
        }
    }

    private BigDecimal getServicePrice(BigDecimal servicePrice, TaxInfoConvertDTO tax) {
        BigDecimal serviceAmount = Objects.nonNull(servicePrice) ? servicePrice : BigDecimal.ZERO;
        if (Objects.nonNull(tax)) { // tính tiền sau khuyến mại
            BigDecimal taxTotal = BigDecimal.ZERO;
            if (Objects.nonNull(tax.getHasTax())) {
                boolean hasTax = tax.getHasTax().equals(ServiceStatusEnum.VISIBLE);
                taxTotal = tax.getTaxList().stream()
                        .map(SetupFeeInfoConvertDTO.Tax::getValue)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(100));
                if (hasTax) { // tính giá tiền của SPDV
                    BigDecimal totalHasTax = taxTotal.add(BigDecimal.ONE);
                    serviceAmount = serviceAmount.divide(totalHasTax, 0, RoundingMode.HALF_UP);
                }
            }
        }
        return serviceAmount;
    }

    private void updateVariantTotalPrice(ServiceDraft serviceDraft) {
        List<VariantDraft> variantDraftDB = variantDraftRepository.findAllByServiceIdAndApprovedAndDeletedFlag(serviceDraft.getServiceId(), ApproveStatusEnum.APPROVED.value, DELETE_FLAG);
        List<Variant> variantDB = variantRepository.findAllByServiceIdAndDeletedFlag(serviceDraft.getServiceId(), DELETE_FLAG);
        TaxInfoConvertDTO tax = serviceDraft.getTax();
        BigDecimal amount = serviceDraft.getPrice();
        BigDecimal taxTotal;
        if (Objects.nonNull(tax) && Objects.nonNull(tax.getHasTax())) {
            boolean hasTax = tax.getHasTax().equals(ServiceStatusEnum.VISIBLE);
            taxTotal = tax.getTaxList().stream().map(SetupFeeInfoConvertDTO.Tax::getValue).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(100));
            if (hasTax) { // tính giá tiền của SPDV
                BigDecimal totalHasTax = taxTotal.add(BigDecimal.ONE);
                amount = amount.divide(totalHasTax, 0, RoundingMode.HALF_UP);
            }
        }
        BigDecimal finalAmount = amount;
        variantDraftRepository.saveAll(variantDraftDB.stream().map(e -> {
            VariantDraft res = new VariantDraft();
            BeanUtils.copyProperties(e, res);
            BigDecimal extraPrice = getExtraPrice(res.getVariantExtraPriceType(), finalAmount, res.getVariantPrice());
            res.setVariantExtraPrice(finalAmount.add(extraPrice));
            return res;
        }).collect(Collectors.toSet()));

        variantRepository.saveAll(variantDB.stream().map(e -> {
            Variant res = new Variant();
            BeanUtils.copyProperties(e, res);
            BigDecimal extraPrice = getExtraPrice(res.getVariantExtraPriceType(), finalAmount, res.getVariantPrice());
            res.setVariantExtraPrice(finalAmount.add(extraPrice));
            return res;
        }).collect(Collectors.toSet()));
    }

    private BigDecimal getExtraPrice(Integer variantExtraPriceType, BigDecimal finalAmount, BigDecimal extraPrice) {
        if (!Objects.equals(VariantPriceTypeEnum.VND.value, variantExtraPriceType)) {
            return finalAmount.multiply(extraPrice.divide(BigDecimal.valueOf(100)));
        }
        return extraPrice;
    }

    private void createPermissionDefault(ServiceEntity currentService) {
        List<WPPermission> listPermission = new ArrayList<>();
        List<PermissionRoleDefaultEnum> list = new ArrayList<>();
        list.add(PermissionRoleDefaultEnum.CREATE_ROLE);
        list.add(PermissionRoleDefaultEnum.DETAIL_ROLE);
        list.add(PermissionRoleDefaultEnum.UPDATE_ROLE);
        list.add(PermissionRoleDefaultEnum.DELETE_ROLE);
        list.add(PermissionRoleDefaultEnum.LIST_ROLE);

        Long maxCode = wpPermissionRepository.getMaxCodePermission();
        String apiKey = currentService.getApiKey();
        Long id = currentService.getId();
        WPPermission wpPermissionParent = addWPPermission(apiKey, id, maxCode, PermissionRoleDefaultEnum.ROLE_PERMISSION, -1L);
        WPPermission parent = wpPermissionRepository.save(wpPermissionParent);

        int count = 1;
        for (PermissionRoleDefaultEnum item : list) {
            WPPermission wpPermission = addWPPermission(apiKey, id, maxCode + count, item, parent.getCode());
            listPermission.add(wpPermission);
            count++;
        }
        List<WPPermission> listPermissionCreateRole = wpPermissionRepository.saveAll(listPermission);
        listPermissionCreateRole.add(parent);

        // tao role mac dinh khi dang ky ung dung
        createRoleDefault(id, apiKey, listPermissionCreateRole);
    }

    private void createRoleDefault(Long id, String apiKey, List<WPPermission> listPermissionCreateRole) {
        // gan role admin
        WPRole roleAdmin = getWpManagerRoleDefault(id, apiKey);
        WPRole save = wpRoleRepository.save(roleAdmin);

        // tao wp_role_permission mac dinh
        List<WPRolePermission> rolePermissionList = new ArrayList<>();
        for (WPPermission permission  : listPermissionCreateRole) {
            WPRolePermission wpRolePermission = new WPRolePermission();
            wpRolePermission.setRoleCode(save.getCode());
            wpRolePermission.setPermissionCode(permission.getCode());
            rolePermissionList.add(wpRolePermission);
        }
        wpRolePermissionRepository.saveAll(rolePermissionList);

    }

    // tao role mac dinh
    private static WPRole getWpManagerRoleDefault(Long id, String apiKey) {
        WPRole item = new WPRole();
        item.setStatus(StatusEnum.ACTIVE.value);
        item.setApiKey(apiKey);
        item.setCode(PermissionRoleDefaultEnum.MANAGER.getSubName().concat(String.valueOf(id)));
        item.setName(PermissionRoleDefaultEnum.MANAGER.getDisplayName());
        item.setUserId(MANAGER);
        item.setDeletedFlag(DeletedFlag.NOT_YET_DELETED.getValue());
        item.setCreatedBy(AuthUtil.getCurrentUserId());
        return item;
    }

    private WPPermission addWPPermission(String apiKey, Long id, long code, PermissionRoleDefaultEnum permissionRoleDefaultEnum, Long parentCode) {
        WPPermission wpPermission = new WPPermission();
        wpPermission.setCode(code);
        wpPermission.setApiKey(apiKey);
        wpPermission.setName(permissionRoleDefaultEnum.getSubName().concat(String.valueOf(id)));
        wpPermission.setDisplayName(permissionRoleDefaultEnum.getDisplayName());
        wpPermission.setParentCode(parentCode);
        wpPermission.setType(3);
        return wpPermission;
    }

    /**
     * Duyệt dữ liệu từ bảng service draft sang service
     */
    private void approveDataService(ServiceDraft serviceDraft, ServiceEntity serviceEntity) {

        serviceEntity = convertDataServiceToDraft(serviceDraft, serviceEntity);

        //Duyệt data bảng file attch
        fileAttachRepository.clearAllByServiceId(serviceEntity.getId());
        List<FileAttach> fileAttaches = fileAttachRepository.findAllByServicesDraftId(serviceDraft.getId());
        List<FileAttach> fileAttachesNew = new ArrayList<>();
        ServiceEntity finalServiceEntity = serviceEntity;
        fileAttaches.forEach(e -> {
            if(!e.getObjectType().equals(FileAttachTypeEnum.DOCUMENT_GUIDE.value)
                    && !e.getObjectType().equals(FileAttachTypeEnum.VIDEO_GUIDE.value)){
                fileAttachesNew.add(
                        new FileAttach(e.getCreatedBy(), e.getCreatedAt(), e.getModifiedBy(),
                                e.getModifiedAt(), null, e.getFileName(),
                                Objects.nonNull(e.getFilePath()) ? e.getFilePath() : e.getExternalLink(),
                                e.getUserId(), finalServiceEntity.getId(), e.getObjectType(), e.getPriority(),
                                e.getAccessType(), e.getFileSize(),
                                e.getExternalLink(),
                                e.getComboId(), null, e.getFileType(), e.getTitle(), e.getDescription(), e.getObjectId(),ResolutionEnum.DESKTOP.value, null
                        )
                );
            }

        });
        List<FileAttach> fileAttachSave = fileAttachesNew.stream()
                .collect(collectingAndThen(toCollection(() -> new TreeSet<>(comparing(FileAttach::getObjectType).thenComparing(FileAttach::getFilePath))),ArrayList::new));
        this.fileAttachRepository.saveAll(fileAttachSave);

        //Duyệt bảng features
        featureRepository.clearByServiceId(serviceEntity.getId());
        List<Feature> features = featureRepository.findAllByServiceDraftIdOrderByPriorityOrderAsc(serviceDraft.getId());
        Map<Long, Feature> featureMap = features.stream().map(e -> {
            Feature feature = new Feature();
            BeanUtils.copyProperties(e, feature);
            if (Objects.isNull(e.getBaseId())) feature.setBaseId(e.getId());
            return feature;
        }).collect(Collectors.toMap(Feature::getBaseId, Function.identity()));
        List<Feature> featuresByBaseIdIn = featureRepository.getListFeatureFromBaseId(features.stream()
                .map(Feature::getBaseId)
                .collect(Collectors.toList()));
        featureRepository.clearByServiceDraftId(serviceDraft.getId());
        if (!CollectionUtils.isEmpty(featuresByBaseIdIn)) {
            List<Feature> featuresService = featuresByBaseIdIn.stream().map(x -> {
                Feature feature = featureMap.get(x.getId());
                feature.setServiceDraftId(serviceDraft.getId());
                feature.setServiceId(finalServiceEntity.getId());
                feature.setId(x.getId());
                feature.setBaseId(x.getId());
                return feature;
            }).collect(Collectors.toList());
            featureRepository.saveAll(featuresService);
        }
    }

    /**
     * Lấy dữ liệu từ bảng service draft sang service
     *
     */
    private ServiceEntity convertDataServiceToDraft(ServiceDraft serviceDraft, ServiceEntity service) {
        service.setServiceName(StringUtils.trim(serviceDraft.getServiceName()));
        service.setCategoriesId(serviceDraft.getCategoriesId());
        service.setUrlService(serviceDraft.getUrlService());
        service.setUrlServiceStatus(serviceDraft.getUrlServiceStatus());
        service.setLanguageType(serviceDraft.getLanguageType());
        service.setUrlSetup(serviceDraft.getUrlSetup());
        service.setTokenSPDV(serviceDraft.getTokenSPDV());
        service.setServiceOwner(serviceDraft.getServiceOwner());
        service.setOnOsType(serviceDraft.getOnOsType());
        service.setProviderType(serviceDraft.getProviderType());
        service.setServiceCode(serviceDraft.getServiceCode());
        service.setUrlPreOrder(serviceDraft.getUrlPreOrder());
        service.setServiceType(Objects.isNull(serviceDraft.getServiceType()) ? "" : serviceDraft.getServiceType());
        service.setModifiedBy(AuthUtil.getCurrentUserId());
        service.setTechVisible(serviceDraft.getTechVisible());
        service.setCustomerTypeCode(serviceDraft.getCustomerTypeCode());
        service.setAllowMultiSub(serviceDraft.getAllowMultiSub());
        service.setFeatureVisible(serviceDraft.getFeatureVisible());
        service.setTax(serviceDraft.getTax());
        service.setDescription(serviceDraft.getDescription());
        service.setSpecifications(serviceDraft.getSpecifications());
        service.setSetupFee(serviceDraft.getSetupFee());
        service.setPrice(serviceDraft.getPrice());
        service.setRegisterEcontract(serviceDraft.getRegisterEcontract());
        service.setPaymentMethod(serviceDraft.getPaymentMethod());
        int installationConfiguration = ObjectUtil.getOrDefault(serviceDraft.getInstallationConfiguration(), 0);
        service.setInstallationConfiguration(
            Objects.equals(serviceDraft.getProductType(), ServiceProductTypeEnum.DEVICE) ? installationConfiguration : null);
        //
        if (Objects.nonNull(serviceDraft.getServiceTypeApplication())) {
            service.setServiceTypeApplication(serviceDraft.getServiceTypeApplication());
            service.setCategoriesApp(serviceDraft.getCategoriesApp());
            // TH chưa duyệt sẽ lưu theo 1 apikey khác, và apikEy khác đó lưu vào trường specifications bảng service_draft
            List<Long> newLstPer = getAppRoleIdsByApiKey(Objects.nonNull(serviceDraft.getSpecifications())
                    ? serviceDraft.getSpecifications() : serviceDraft.getApiKey());
            updateAppRoleByApiKey(service.getApiKey(), newLstPer);
        }
        //
        serviceDraft.setApprove(ServiceApproveStatusEnum.APPROVED);
        service.setApprove(ServiceApproveStatusEnum.APPROVED);
        serviceDraftRepository.save(serviceDraft);
        // Cập nhật thông tin danh mục
        List<ServicesCategories> lstServiceDraftCategory = servicesCategoriesRepository.findAllByServiceDraftId(serviceDraft.getId());
        updateServiceCategory(service.getId(),
            lstServiceDraftCategory.stream().map(ServicesCategories::getCategoryId).collect(Collectors.toSet()));
        return serviceRepository.save(service);
    }

    /**
     *  sao chép dữ liệu từ bảng service sang service draft cho lần duyệt đầu
     */
    private ServiceDraft cloneDataService(ServiceEntity serviceEntity, ActionEnum action) {
        ServiceDraft serviceDraft = new ServiceDraft();
        BeanUtils.copyProperties(serviceEntity, serviceDraft);
        serviceDraft.setId(null);
        serviceDraft.setServiceId(serviceEntity.getId());
        serviceDraft = serviceDraftRepository.save(serviceDraft);
        // Cập nhật thông tin danh mục
        List<ServicesCategories> lstServiceDraftCategory = servicesCategoriesRepository.findAllByServiceId(serviceEntity.getId());
        updateServiceDraftCategory(serviceDraft.getId(),
            lstServiceDraftCategory.stream().map(ServicesCategories::getCategoryId).collect(Collectors.toSet()));

        //Clone data bảng file attch
        List<FileAttach> fileAttaches = fileAttachRepository.findAllByServiceId(serviceEntity.getId());
        List<FileAttach> fileAttachesNew = new ArrayList<>();
        ServiceDraft finalServiceDraft = serviceDraft;
        fileAttaches.forEach(e -> {
            if (e.getObjectType() != null && !e.getObjectType().equals(FileAttachTypeEnum.DOCUMENT_GUIDE.value)) {
                fileAttachesNew.add(
                        new FileAttach(e.getCreatedBy(), e.getCreatedAt(), e.getModifiedBy(),
                                e.getModifiedAt(), null, e.getFileName(),
                                e.getFilePath(),
                                e.getUserId(), null, e.getObjectType(), e.getPriority(),
                                e.getAccessType(), e.getFileSize(),
                                e.getExternalLink(),
                                e.getComboId(), finalServiceDraft.getId(), e.getFileType(), e.getTitle(), e.getDescription(), e.getObjectId(),ResolutionEnum.DESKTOP.value, null
                        )
                );
            }

        });
        this.fileAttachRepository.saveAll(fileAttachesNew);

        List<Feature> features = featureRepository.findAllByServiceIdOrderByPriorityOrderAsc(serviceEntity.getId());
        if (!CollectionUtils.isEmpty(features)) {
            List<Feature> featuresService = features.stream().map(x -> {
                Feature feature = new Feature();
                BeanUtils.copyProperties(x, feature);
                feature.setId(action.equals(ActionEnum.APPROVED) ? x.getId() : null);
                feature.setServiceId(action.equals(ActionEnum.APPROVED) ? serviceEntity.getId() : null);
                feature.setServiceDraftId(finalServiceDraft.getId());
                if (Objects.nonNull(x.getBaseId())) {
                    feature.setBaseId(x.getId());
                }
                return feature;
            }).collect(Collectors.toList());
            featureRepository.saveAll(featuresService);
        }

        return serviceDraft;
    }

    // Gui mail khi tu choi phe duyet
    private void sendMailRejectedService(ServiceEntity currentService, String emailTo) {
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(EmailCodeEnum.SV04.getValue());
        User user = findUserById(currentService.getCreatedBy());
        // hotline nền tảng
        String hotlineParam = systemParamService.getHotlineParam();
        paramNameByCode.forEach(mailParamResDTO -> {
            if (ParamEmailEnum.NAME_SERVICE.getValue().equals(mailParamResDTO.getParamName())) {
                mailParamResDTO.setValue(Objects.isNull(currentService.getServiceName()) ? "" : currentService.getServiceName());
            } else if (ParamEmailEnum.REJECT_REASON.getValue().equals(mailParamResDTO.getParamName())) {
                mailParamResDTO.setValue(Objects.isNull(currentService.getComment()) ? "" : currentService.getComment());
            } else if (ParamEmailEnum.USER.getValue().equals(mailParamResDTO.getParamName())) {
                mailParamResDTO.setValue(user.getLastName() + " " + user.getFirstName());
            } else if (ParamEmailEnum.HOTLINE.getValue().equals(mailParamResDTO.getParamName())) {
                mailParamResDTO.setValue(hotlineParam);
            }
        });
        emailService.sendMail(emailTo, EmailCodeEnum.SV04, paramNameByCode);
    }

    // Gui mail khi phe duyet cho Dev
    private void sendMailApproveService(EmailCodeEnum code, ServiceEntity currentService, String emailTo) {
        log.info("================== start sendMailApproveService - start sending mail to service dev ==================");
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamForApproveService(code.getValue());
        User user = findUserById(currentService.getCreatedBy());
        // hotline nền tảng
        String hotlineParam = systemParamService.getHotlineParam();
        paramNameByCode.forEach(mailParamResDTO -> {
            if (ParamEmailEnum.NAME_SERVICE.getValue().equals(mailParamResDTO.getParamName())) {
                mailParamResDTO.setValue(Objects.isNull(currentService.getServiceName()) ? "" : currentService.getServiceName());
            } else if (ParamEmailEnum.USER.getValue().equals(mailParamResDTO.getParamName())) {
                mailParamResDTO.setValue(user.getLastName() + " " + user.getFirstName());
            } else if (ParamEmailEnum.HOTLINE.getValue().equals(mailParamResDTO.getParamName())) {
                mailParamResDTO.setValue(hotlineParam);
            } else if (ParamEmailEnum.API_KEY.getValue().equals(mailParamResDTO.getParamName())){
                mailParamResDTO.setValue(currentService.getApiKey());
            } else if (ParamEmailEnum.SECRET_KEY.getValue().equals(mailParamResDTO.getParamName())) {
                mailParamResDTO.setValue(currentService.getSecretKey());
            } else if (ParamEmailEnum.VNPT_SERVICE.getValue().equals(mailParamResDTO.getParamName())) {
                if (ServiceTypeEnum.VNPT.getValue().equals(currentService.getServiceOwner())) {
                    mailParamResDTO.setValue(StringUtils
                        .replace(mailParamResDTO.getValue(), ParamEmailEnum.PROD_TOPIC.getValue(),
                            TOPIC_CONSUMER_NAME));
                    mailParamResDTO.setValue(StringUtils
                        .replace(mailParamResDTO.getValue(), ParamEmailEnum.CONS_TOPIC.getValue(),
                            currentService.getTopicName()));
                    mailParamResDTO.setValue(StringUtils.replace(mailParamResDTO.getValue(), ParamEmailEnum.GROUP_ID
                        .getValue(), GROUP_ID));
                } else {
                    mailParamResDTO.setValue(CharacterConstant.BLANK);
                }
            }
        });
        emailService.sendMail(emailTo, code, paramNameByCode);
        log.info("================== end sendMailApproveService - end sending mail to service dev ==================");
    }

    /**
     * Thong bao toi man hinh khi phe duyet
     *
     */
    private void notifyUtil(String notifyContent, ServiceEntity service, String notifyTitle, String notifyCode) {
        log.info("================== start notifyUtil - start sending notif to service dev ==================");
        val notifyModel = NotificationDTO.builder()
                .title(notifyTitle)
                .body(String.format(notifyContent, service.getServiceName()))
                .screenId(notifyCode)
                .userId(service.getCreatedBy())
                .portalType(PortalType.DEV.getType())
                .status(StatusEnum.INACTIVE.value)
                .createdAt(LocalDateTime.now())
                .objectId(service.getId())
                .build();
        NotifyUtil.sendNotify(notifyModel, notifyCode);
        log.info("================== end notifyUtil - end sending notif to service dev ==================");
    }

    /**
     * Doi pattern date
     *
     */
    private String changePatternOfDateString(String input) {
        if (StringUtils.isBlank(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH) || StringUtils.isBlank(DateUtil.FORMAT_YYYY_MM_DD) ||
            StringUtils.isBlank(input)) {
            handleFormatDate();
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat();
            formatter.applyPattern(DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
            formatter.setLenient(false);
            Date date = formatter.parse(input);
            formatter.applyPattern(DateUtil.FORMAT_YYYY_MM_DD);
            return formatter.format(date);
        } catch (ParseException | IllegalArgumentException e) {
            handleFormatDate();
        }
        return "";
    }

    /**
     * Xu ly sai format date
     */
    private void handleFormatDate() {
        String message = messageSource
                .getMessage(MessageKeyConstant.INVALID_DATA, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
        throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Category.DATE,
                MessageKeyConstant.NOT_FOUND);
    }


    private void validateServiceName(String serviceName) {
        boolean existsByServiceName = serviceRepository.existsByServiceName(serviceName.trim());
        if (existsByServiceName) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.DUPLICATE_NAME, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.NAME,
                    (MessageKeyConstant.DUPLICATE_NAME));
        }
    }

    private void validateServiceNameWithDeleteFlag(String serviceName) {
        boolean existsByServiceName = serviceRepository.existsByServiceNameAndDeletedFlag(serviceName.trim(), EntitiesConstant.DeleteFlag.ACTIVE);
        if (existsByServiceName) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.DUPLICATE_NAME, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.NAME,
                    (MessageKeyConstant.DUPLICATE_NAME));
        }
    }

    private void validateAppName(String name) {
        boolean existsByServiceName = serviceRepository.existsByServiceNameAndDeletedFlagAndServiceTypeApplicationNotNull(name.trim(), EntitiesConstant.DeleteFlag.ACTIVE);
        if (existsByServiceName) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.DUPLICATE_NAME, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.NAME,
                    (MessageKeyConstant.DUPLICATE_NAME));
        }
    }

    private void validateSku(String sku) {
        if (Objects.nonNull(sku)) {
            boolean existsBySku = serviceRepository.existsBySkuAndDeletedFlagAndServiceTypeApplicationNotNull(sku.trim(),
                EntitiesConstant.DeleteFlag.ACTIVE);
            if (existsBySku) {
                throw exceptionFactory.badRequest(MessageKeyConstant.DUPLICATE_SKU, Resources.SERVICES, Services.SKU, sku);
            }
        } else {
            throw exceptionFactory.badRequest(MessageKeyConstant.SKU_IS_NULL, Resources.SERVICES, Services.SKU);
        }
    }

    private void validateCategoryId(ServiceCreateDTO serviceDTO) {
        Long categoriesId = serviceDTO.getCategoriesId();
        boolean existsByCategoryID = categoriesRepository.existsById(categoriesId);
        if (!existsByCategoryID) {
            String message = messageSource
                    .getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE , LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.CATEGORY, ErrorKey.Category.ID,
                    MessageKeyConstant.NOT_FOUND);
        }
    }

    private String validateLanguage(String[] languageType) {
        if (Objects.isNull(languageType) || languageType.length == 0) {
            return null;
        }
        boolean languageNotMatch = Arrays.stream(languageType)
                .anyMatch( e -> !Arrays.asList(LANGUAGE_TYPES).contains(e));
        if (languageNotMatch){
            String message = messageSource
                    .getMessage(MessageKeyConstant.SERVICE_LANGUAGE_NOT_MATCH, SERVICE_MESSAGE,
                            LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.LANGUAGE,
                    MessageKeyConstant.SERVICE_LANGUAGE_NOT_MATCH);
        }
        return Arrays.stream(languageType).distinct().collect(Collectors.joining(CharacterConstant.COMMA));
    }

	@Override
	public ServiceUserDTO loginService(ServiceLoginDTO request) throws JsonProcessingException {
        //Subsciption phai dang hoat dong hoac dung thu
        checkSubscription(request);

        ServiceEntity service = serviceRepository.findByApiKeyAndSecretKeyAndDeletedFlag(request.getApiKey(), request.getSecretKey(), 1)
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SERVICES, ErrorKey.Services.API_KEY, request.getApiKey()));
        CustomUserDetails userDetail = AuthUtil.getLoggedInUser();
		// kiem tra user co duoc su dung dich vu saas
        if (serviceRepository.checkUserInService(userDetail.getId(), service.getId(), request.getSubscription()) <= 0) {
			String message = messageSource.getMessage(MessageKeyConstant.USER_ACCESS_DENIED, SERVICE_MESSAGE,
					LocaleContextHolder.getLocale());
			throw new BadRequestException(message, Resources.USER, ErrorKey.ID, MessageKeyConstant.USER_ACCESS_DENIED);
		}

        User user = findUserById(userDetail.getId());

		// lay thong tin anh avatar
		FileAttach file = fileAttachRepository.findByObjectTypeAndUserId(FileAttachTypeEnum.AVATAR.value, user.getId()).orElse(new FileAttach());
		String deviceScope = String.join(",", Optional.ofNullable(AuthUtil.getOauthRequestScope()).orElse(new HashSet<>()));
		SAASUserDTO userSAAS = SAASUserDTO.builder()
								.id(user.getId())
								.username(user.getUsername())
								.email(user.getEmail())
								.apiKey(request.getApiKey())
								// set parent id bang chinh id cua user neu parent id cua no la -1
								.smeId(Objects.equals(user.getParentId(), -1L) ? user.getId() : user.getParentId())
								.smeName(user.getName())
								.accountExpired(userDetail.isAccountExpired())
								.accountLocked(userDetail.isAccountLocked())
								.credentialsExpired(userDetail.isCredentialsExpired())
								.enabled(userDetail.isEnabled())
								.authorities(userDetail.getAuthorities())
								.scope(deviceScope)
								.build();

		return ServiceUserDTO.builder()
				.id(user.getId())
				.name(user.getUsername())
				// set parent id bang chinh id cua user neu parent id cua no la -1
				.smeId(Objects.equals(user.getParentId(), -1L) ? user.getId() : user.getParentId())
				.email(user.getEmail())
				.smeName(user.getName())
				.avatar(file.getFilePath())
				.website(user.getWebSite())
				.phoneNumber(user.getPhoneNumber())
				.address(user.getAddress())
				// tao access token de truy cap master data service
				.accessToken(jwtUtil.generateJwtToken(userSAAS, deviceScope))
				.build();
	}

    /**
     *
     * Kiem tra do uu tien tang tuyen tinh
     *
     * @param captures Danh sach anh
     */
    private void validatePriorityConsecutive(List<ServiceCaptureDTO> captures) {
        int size = captures.size();
        //kiem tra kich thuoc anh chup man hinh
        if (size == 0 || size > 20) {
            String[] args = {CharacterConstant.SPACE, ServicesConstant.Captures.MAX_LENGTH,
                    ServicesConstant.Captures.MIN_LENGTH};
            String message = messageSource
                    .getMessage(MessageKeyConstant.Validation.SIZE, args, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.SNAPSHOT,
                    MessageKeyConstant.Validation.SIZE);
        }

        List<Integer> collect = captures.stream()
                .map(ServiceCaptureDTO::getPriority).sorted().collect(Collectors.toList());
        Integer[] expectedList = new Integer[size];
        if (!collect.isEmpty()) {
            for (int i = 0; i < size; i++) {
                expectedList[i] = i + 1;
            }

            // kiem tra 2 mang yeu cau va thuc te
            if (!Arrays.equals(collect.toArray(), expectedList)) {
                String message = messageSource
                        .getMessage(MessageKeyConstant.SERVICE_PRIORITY_CONSECUTIVE, SERVICE_MESSAGE,
                                LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICES, ErrorKey.FileAttach.PRIORITY,
                        MessageKeyConstant.SERVICE_PRIORITY_CONSECUTIVE);
            }
        }
    }

    /**
     * Xu ly truy cap trai phep
     *
     * @param userId ma nguoi dung
     */
    private void handleAccessDenied(Long userId){
        CustomUserDetails userDetails = AuthUtil.getLoggedInUser();
        Long parentId = userDetails.getParentId();
        if (Objects.equals(parentId, -1L) || Objects.isNull(parentId)) {
            parentId = userDetails.getId();
        }
        List<String> collect = userDetails.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        if (!(collect.contains(RoleType.FULL_ADMIN.getValue())
                || collect.contains(RoleType.ADMIN.getValue())
                || collect.contains(RoleType.CUSTOMER_SUPPORT.getValue())
                || collect.contains(RoleType.ROLE_DEV_VIEW_DETAIL_SERVICE.getValue())
                || collect.contains(RoleType.ROLE_DEV_VIEW_SERVICE.getValue()))
            || (collect.contains(RoleType.DEVELOPER.getValue()) && !Objects.equals(parentId, userId))) {
            throw exceptionFactory.permissionDenied(ExceptionConstants.NEED_PERMISSION);
        }
    }

    @Transactional
    @Override
    public void deletedService(Long serviceId, ServiceStatusDeletedEnum status) {
        if (status != ServiceStatusDeletedEnum.DELETED && status != ServiceStatusDeletedEnum.DISABLED) {
            String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new BadRequestException(message, Resources.SERVICES, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }
        // serviceRepository.changeStatus(status.value, serviceId);
        changeServiceStatus(serviceId, status.value);
    }

    @Override
    public Page<ServiceResponseTransDTO> getTopSelling(String search, Pageable pageable, Long category, CustomerTypeEnum customerType) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeValue = Objects.isNull(userLogin) ? customerType.getValue() : userLogin.getCustomerType();
        return convertServiceEntityPage(serviceRepository.getPageServiceSellingId(search, category, customerTypeValue,
            Collections.singleton(ServiceProductTypeEnum.UNSET.value), pageable));
    }

    /**
     * Thêm lịch sử hoạt động dịch vụ
     *
     * @param serviceEntity the service entity
     */
    private void addServiceHistory(ServiceEntity serviceEntity, ApproveStatusEnum statusEnum, PortalType type, ServiceDraft serviceDraft) {

        String reason = Objects.equals(statusEnum, ApproveStatusEnum.REJECTED)
                || Objects.equals(statusEnum, ApproveStatusEnum.UNAPPROVED)
                ? Objects.nonNull(serviceDraft) ? serviceDraft.getComment() : serviceEntity.getComment()
                : null;
        Integer version = actionLogRepository.getVersionMaxByServiceId(serviceEntity.getId());
        ActionLogTemplateDTO log = ActionLogTemplateDTO.builder()
                                                       .serviceId(serviceEntity.getId())
                                                       .status(statusEnum)
                                                       .object(Objects.nonNull(serviceDraft) ? serviceDraft.getServiceName() : serviceEntity.getServiceName())
                                                       .content(reason)
                                                       .portal(type)
                                                       .type(ActionLogTypeEnum.SERVICE)
                                                       .serviceDraftId(Objects.nonNull(serviceDraft) ? serviceDraft.getId() : null)
                                                       .version(Objects.equals(statusEnum, ApproveStatusEnum.APPROVED) ? version + 1 : null)
                                                       .build();
        actionLogService.addLog(log);
    }

    /**
     *  Trả ngoại lệ dịch vụ
     *
     * @param messageKeyConstant the message key constant
     * @param errorKey           the error key
     *
     * @return the bad request exception
     */
    private BadRequestException throwServiceBadRequest(String messageKeyConstant, String errorKey, String[] param) {
        String message = messageSource
                .getMessage(messageKeyConstant, param, LocaleContextHolder.getLocale());
        return new BadRequestException(message, Resources.SERVICES, errorKey, messageKeyConstant);
    }

    /**
     * Check subsciption truoc khi dang ky
     */
    private void checkSubscription(ServiceLoginDTO request) {
        // Kiểm tra
        if (!serviceRepository.existsByLoginDTO(request.getSubscription(), AuthUtil.getCurrentParentId(), request.getApiKey(),
            request.getSecretKey())) {
            throw throwServiceBadRequest(MessageKeyConstant.SUBSCRIPTION_IS_CANCEL, ErrorKey.Services.CANCEL,
                new String[]{ErrorKey.Services.CANCEL});
        }
    }

    /**
     * Lưu thông tin tài liệu hướng dẫn sử dụng
     */
    @Override
    public void saveGuide(GuideDTO guide){
        ServiceEntity service = findServiceById(guide.getServiceId());
        this.validateServicePermissionDev(Collections.singletonList(service));
        List<Long> removeIds = new LinkedList<>();
        removeIds.addAll(guide.getRemoveVideoIds());
        removeIds.addAll(guide.getRemoveDocIds());
        Long comboId = null;
        if(guide.getComboDraftId() !=null){
            comboId = comboRepository.getComboIdByComboDraftId(guide.getComboDraftId());
            guide.setComboId(comboId);

        }
        Map<Long, GuideDTO.Attach> attachMap = new HashMap<>();
        List<GuideDTO.Attach> attaches = new LinkedList<>();
        List<Long> attachIds = new LinkedList<>();
        List<Long> videoIds = new LinkedList<>();
        for(GuideDTO.Attach a: guide.getVideos()){
            attaches.add(a);
            videoIds.add(a.getId());
            attachIds.add(a.getId());
            attachMap.put(a.getId(),a);
        }
        for(GuideDTO.Attach a: guide.getDocs()){
            attaches.add(a);
            attachIds.add(a.getId());
            attachMap.put(a.getId(),a);
        }

        // gán file vào dịch vụ hoặc combo + set lại tên file
        if(!attaches.isEmpty()){
            for(GuideDTO.Attach attach: attaches){
                attachIds.add(attach.getId());
            }
            List<FileAttach> fileAttaches = fileAttachRepository.findByIdIn(attachIds);
            for(FileAttach file: fileAttaches){
                file.setFileName(attachMap.get(file.getId()).getName());
                if(guide.getServiceId() !=null){
                    file.setServiceId(guide.getServiceId());
                } else if(comboId !=null){
                    file.setComboId(comboId);
                } else {
                    file.setComboDraftId(guide.getComboDraftId());
                }

                if (videoIds.contains(file.getId())){
                    file.setObjectType(FileAttachTypeEnum.VIDEO_GUIDE.value);
                } else {
                    file.setObjectType(FileAttachTypeEnum.DOCUMENT_GUIDE.value);
                }
            }
            if(!fileAttaches.isEmpty()){
                fileAttachRepository.saveAll(fileAttaches);
            }
        }

        // set visible
        Integer visible = guide.getGuideVisible()!=null?guide.getGuideVisible():1;
        if(guide.getServiceId() !=null){
            fileAttachRepository.updateVisible(guide.getServiceId(),visible);
        }

        // xóa file
        if(!removeIds.isEmpty()){
            fileAttachRepository.deleteAllByIdIn(removeIds);
        }
    }

    /**
     * Lấy danh sách tài liệu HDSD của dịch vụ
     */
    private void setGuide(ServiceDetailResponseDTO serviceDetailResponseDTO,Long serviceId){
        try {
            Long userId = AuthUtil.getCurrentUser() != null ? AuthUtil.getCurrentUserId() : null;
            boolean hadService = false;
            if(Objects.nonNull(userId)){
                hadService = subscriptionRepository.existsByUserIdAndServiceId(userId, serviceId);
            }
            serviceDetailResponseDTO.setDocGuide(new LinkedList<>());
            serviceDetailResponseDTO.setVideoGuide(new LinkedList<>());
            List<ServiceDetailFileAttachDTO> docs = fileAttachRepository.getLstServiceDocGuideOrderByIdDesc(serviceId);
            List<ServiceDetailFileAttachDTO> videos = fileAttachRepository.getLstServiceVideoGuideOrderByIdDesc(serviceId);
            for(ServiceDetailFileAttachDTO dto: docs){
                if(Objects.equals(dto.getVisible(), 2) && !hadService) continue;     // nếu file chỉ cho người đã mua sản phẩm xem
                serviceDetailResponseDTO.getDocGuide().add(dto);
            }
            for(ServiceDetailFileAttachDTO dto: videos){
                if(Objects.equals(dto.getVisible(), 2) && !hadService) continue;          // nếu file chỉ cho người đã mua sản phẩm xem
                serviceDetailResponseDTO.getVideoGuide().add(dto);
            }
        } catch (Exception e){
            log.error("setGuide: {}", e.getMessage(), e);
        }
    }

    /**
     * Cập nhật thông tin hỗ trợ
     */
    @Override
    @Transactional
    public void updateSupportInfo(ServiceSupportDTO paramDto) {
        Optional<ServiceEntity> optional = serviceRepository.findById(paramDto.getId());
        if(optional.isPresent()){
            ServiceEntity serviceEntity = optional.get();
            this.validateServicePermissionDev(Collections.singletonList(serviceEntity));
            serviceEntity.setProvider(paramDto.getProvider());
            serviceEntity.setPhoneNumber(paramDto.getPhoneNumber());
            serviceEntity.setEmail(paramDto.getEmail());
            serviceEntity.setProvinceId(paramDto.getProvinceId());
            serviceEntity.setProvinceCode(paramDto.getProvinceCode());
            serviceEntity.setDistrictId(paramDto.getDistrictId());
            serviceEntity.setWardId(paramDto.getWardId());
            serviceEntity.setStreetId(paramDto.getStreetId());
            serviceEntity.setAddress(paramDto.getAddress());
            serviceRepository.save(serviceEntity);
        }
    }

    @Override
    public Page<ServiceAddonDetailDTO> findFullServiceForDev(String search, Pageable pageable) {
        return serviceRepository.searchServicesAndCombo(Objects.nonNull(search) ? search.toLowerCase(Locale.ROOT) : "", AuthUtil.getCurrentParentId(), pageable);
    }

    /**
     * Khởi tạo thông tin hỗ trợ mặc định
     */
    public void initSupportInfo(ServiceEntity currentService) {
       if(currentService.getUserId() !=null){
            Optional<User> optional = userRepository.findById(currentService.getUserId());
            if(optional.isPresent()){
                User user = optional.get();
                currentService.setProvider(user.getName());
                currentService.setProvinceId(user.getProvinceId());
                currentService.setProvinceCode(user.getProvinceCode());
                currentService.setDistrictId(user.getDistrictId());
                currentService.setWardId(user.getWardId());
                currentService.setStreetId(user.getStreetId());
                currentService.setAddress(user.getAddress());
            }
       }
    }

    /**
     * set thông tin hỗ trợ
     */
    public void setSupportInfo(ServiceEntity currentService, ServiceDescResDTO serviceDTO){
        serviceDTO.setProvider(currentService.getProvider());
        serviceDTO.setAddress(currentService.getAddress());
        serviceDTO.setPhoneNumber(currentService.getPhoneNumber());
        serviceDTO.setEmail(currentService.getEmail());
        serviceDTO.setProvinceId(currentService.getProvinceId());
        serviceDTO.setProvinceCode(currentService.getProvinceCode());
        serviceDTO.setDistrictId(currentService.getDistrictId());
        serviceDTO.setWardId(currentService.getWardId());
        serviceDTO.setStreetId(currentService.getStreetId());
        if(currentService.getProvinceId() !=null){
            Optional<Province> optional = provinceRepository.findById(currentService.getProvinceId());
            serviceDTO.setProvinceName(optional.map(Province::getName).orElse(null));
        }
        if(currentService.getProvinceCode() != null && currentService.getDistrictId() !=null){
            District district = districtRepository.findDistrictByIdAndProvinceCode(currentService.getDistrictId(),currentService.getProvinceCode() );
            serviceDTO.setDistrictName(district!=null?district.getName():null);
        }
        if(currentService.getProvinceCode() != null && currentService.getWardId() !=null){
            Ward ward = wardRepository.findFirstByIdAndAndProvinceCode(currentService.getWardId(),currentService.getProvinceCode());
            serviceDTO.setWardName(ward!=null?ward.getName():null);
        }
        if(currentService.getProvinceCode() != null && currentService.getStreetId() !=null){
            Street street = streetRepository.findFirstByIdAndProvinceCode(currentService.getStreetId(),
                currentService.getProvinceCode());
            serviceDTO.setStreetName(street != null ? street.getName() : null);
        }

    }

    /**
     * set thông tin hỗ trợ
     */
    public void setSupportInfoServiceDraft(ServiceDraft currentService, ServiceDescResDTO serviceDTO){
        serviceDTO.setProvider(currentService.getProvider());
        serviceDTO.setAddress(currentService.getAddress());
        serviceDTO.setPhoneNumber(currentService.getPhoneNumber());
        serviceDTO.setEmail(currentService.getEmail());
        serviceDTO.setProvinceId(currentService.getProvinceId());
        serviceDTO.setProvinceCode(currentService.getProvinceCode());
        serviceDTO.setDistrictId(currentService.getDistrictId());
        serviceDTO.setWardId(currentService.getWardId());
        serviceDTO.setStreetId(currentService.getStreetId());
        if(currentService.getProvinceId() !=null){
            Optional<Province> optional = provinceRepository.findById(currentService.getProvinceId());
            serviceDTO.setProvinceName(optional.map(Province::getName).orElse(null));
        }
        if(currentService.getProvinceCode() != null && currentService.getDistrictId() !=null){
            District district = districtRepository.findDistrictByIdAndProvinceCode(currentService.getDistrictId(),currentService.getProvinceCode() );
            serviceDTO.setDistrictName(district!=null?district.getName():null);
        }
        if(currentService.getProvinceCode() != null && currentService.getWardId() !=null){
            Ward ward = wardRepository.findFirstByIdAndAndProvinceCode(currentService.getWardId(),currentService.getProvinceCode());
            serviceDTO.setWardName(ward!=null?ward.getName():null);
        }
        if(currentService.getProvinceCode() != null && currentService.getStreetId() !=null){
            Street street = streetRepository.findFirstByIdAndProvinceCode(currentService.getStreetId(),
                currentService.getProvinceCode());
            serviceDTO.setStreetName(street != null ? street.getName() : null);
        }

    }

    /**
     * get thông tin hỗ trợ
     */
    public void setSupportInfo(ServiceDetailResponseDTO serviceDTO, Long serviceId){
        ServiceEntity currentService = findServiceById(serviceId);
        Optional<User> userOptional = userRepository.findById(currentService.getUserId());
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            serviceDTO.setProvider(user.getName());
            serviceDTO.setAddress(user.getAddress());
            serviceDTO.setPhoneNumber(user.getPhoneNumber());
            serviceDTO.setEmail(user.getEmail());
            if (user.getProvinceId() != null) {
                Optional<Province> optional = provinceRepository.findById(user.getProvinceId());
                serviceDTO.setProvinceName(optional.map(Province::getName).orElse(null));
            }
            if (user.getProvinceCode() != null && user.getDistrictId() != null) {
                District district = districtRepository.findDistrictByIdAndProvinceCode(user.getDistrictId(), user.getProvinceCode());
                serviceDTO.setDistrictName(district != null ? district.getName() : null);
            }
            if (user.getProvinceCode() != null && user.getWardId() != null) {
                Ward ward = wardRepository.findFirstByIdAndAndProvinceCode(user.getWardId(), user.getProvinceCode());
                serviceDTO.setWardName(ward != null ? ward.getName() : null);
            }
            if (user.getProvinceCode() != null && user.getStreetId() != null) {
                Street street = streetRepository.findFirstByIdAndProvinceCode(user.getStreetId(), user.getProvinceCode());
                serviceDTO.setStreetName(street != null ? street.getName() : null);
            }
        }
    }

    private void updateTechInfo(List<ServiceCaptureDTO> technologies, Long serviceId){
        if(technologies!=null){
            for(ServiceCaptureDTO snap: technologies){
                fileAttachRepository.updateDescription(snap.getId(),snap.getTitle(),snap.getDescription(),serviceId,FileAttachTypeEnum.TECH.value ,1);
            }
        }
    }

    private void updateFeatureInfo(List<FeatureReqDTO> features, Long serviceId, ServiceEntity service){
        if(features !=null){
            int index = 1;
            for(FeatureReqDTO dto: features){
                dto.setPriorityOrder(index ++);
                dto.setServiceId(serviceId);
                dto.setId(null);
                if (Objects.nonNull(service) && Objects.equals(service.getFeatureVisible(), ComponentVisible.VISIBLE.getValue())) {
                    if (Objects.isNull(dto.getCode()) || Objects.isNull(dto.getName())) {
                        String message = messageSource.getMessage(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, FEATURE_MESSAGE, Locale.US);
                        throw new BadRequestException(message, Resources.FEATURE, ErrorKey.ID,
                            MessageKeyConstant.FIELD_MUST_BE_NOT_NULL);
                    }
                }
                Feature feature = featureService.createFeature(dto, true);
                if (Objects.isNull(feature.getBaseId())) {
                    feature.setBaseId(feature.getId());
                    featureRepository.save(feature);
                }
            }
        }
    }

    private void setCouponInfo(ServiceDetailResponseDTO serviceDTO, Long serviceId, CustomerTypeEnum customerTypeEnum) {
        List<PricingSaaSResDTO> pricing = pricingService.findAllPricingByService(serviceId, null, customerTypeEnum, -1L);
        CouponUtils.setNewestCoupon(serviceDTO,pricing, couponAdsPositionRepository);
    }

    private void setCampaignInfo(ServiceDetailResponseDTO serviceDTO, Long serviceId) {
        ProductTagDTO productTagDTO = campaignSmeService.getProductTag(serviceId, ProductTypeEnum.SERVICE);
        serviceDTO.setDiscountInfo(productTagDTO.getDiscountInfo());
        serviceDTO.setGiftInfo(productTagDTO.getGiftInfo());
    }

    /**
     * Cập nhật thông tin bố cục/công nghệ nội bật
     */
    @Override
    @Transactional
    public void updateSnaps(ServiceSnapDTO paramDto) {
        Optional<ServiceEntity> optional = serviceRepository.findById(paramDto.getId());
        if (optional.isPresent()) {
            ServiceEntity service = optional.get();
            // Phan quyen
            handleAccessDenied(service.getUserId());
            //Update cho service sau lần đầu duyệt
            if (Objects.equals(ServiceApproveStatusEnum.APPROVED, service.getApprove())) {
                ServiceDraft serviceDraft = findByServiceId(service.getId());
                if (Objects.nonNull(serviceDraft) && Objects.equals(ServiceApproveStatusEnum.APPROVED, serviceDraft.getApprove())) {
                    ServiceDraft serviceDraftNew = cloneDataService(service, ActionEnum.UPDATE);
                    updateSnapServiceDraft(paramDto, serviceDraftNew);
                } else if (Objects.nonNull(serviceDraft)) {
                    updateSnapServiceDraft(paramDto, serviceDraft);
                }
            } else {
                //update cho service chưa duyệt lần nào
                ServiceEntity serviceEntity = optional.get();
                fileAttachRepository.clearByServiceId(serviceEntity.getId(), paramDto.getType());
                if (!CollectionUtils.isEmpty(paramDto.getSnapshots())) {
                    for (ServiceCaptureDTO snap : paramDto.getSnapshots()) {
                        if (Objects.equals(paramDto.getTechVisible(), ComponentVisible.VISIBLE.getValue())) {
                            if (Objects.isNull(snap.getId()) || Objects.isNull(snap.getPriority())) {
                                String message = messageSource.getMessage(MessageKeyConstant.FIELD_MUST_BE_NOT_NULL, SNAPSHOT_MESSAGE, Locale.US);
                                throw new BadRequestException(message, Resources.FILE_ATTACH, ErrorKey.ID,
                                        MessageKeyConstant.FIELD_MUST_BE_NOT_NULL);
                            }
                        }
                        fileAttachRepository
                                .updateDescription(snap.getId(), snap.getTitle(), snap.getDescription(), serviceEntity.getId(), paramDto.getType(),
                                        snap.getFileType() != null ? snap.getFileType() : 1);
                    }
                }
                if (paramDto.getTechLayout() != null) {
                    serviceEntity.setTechLayout(paramDto.getTechLayout());
                }
                if (paramDto.getTechVisible() != null) {
                    serviceEntity.setTechVisible(paramDto.getTechVisible());
                }
                serviceRepository.save(serviceEntity);
            }
        }
    }

    /**
     * Cập nhật thông tin snap cho serviceDraft
     */
    private void updateSnapServiceDraft(ServiceSnapDTO paramDto, ServiceDraft serviceDraft) {
        fileAttachRepository.clearByServiceDraftId(serviceDraft.getId(), paramDto.getType());
        if (Objects.nonNull(paramDto.getSnapshots())) {
            List<Long> ids = paramDto.getSnapshots().stream().map(ServiceCaptureDTO::getId).collect(Collectors.toList());
            List<FileAttach> fileAttachList = fileAttachRepository.findAllByIdIn(ids);
            List<FileAttach> fileAttachesNew = new ArrayList<>();
            fileAttachList.forEach(e -> paramDto.getSnapshots().forEach(snap -> {
                if (Objects.equals(snap.getId(), e.getId())) {
                    fileAttachesNew.add(
                        new FileAttach(e.getCreatedBy(), e.getCreatedAt(), e.getModifiedBy(),
                            e.getModifiedAt(), null, e.getFileName(),
                            e.getFilePath(),
                            e.getUserId(), null, Objects.nonNull(paramDto.getType()) ? paramDto.getType() : e.getObjectType(),
                            Objects.nonNull(e.getPriority()) ? e.getPriority() : snap.getPriority(),
                            e.getAccessType(), e.getFileSize(), e.getExternalLink(),
                            e.getComboId(), serviceDraft.getId(), Objects.nonNull(snap.getFileType()) ? snap.getFileType() : 1,
                            Objects.nonNull(snap.getTitle()) ? snap.getTitle() : e.getTitle(),
                            Objects.nonNull(snap.getDescription()) ? snap.getDescription() : e.getDescription(), e.getObjectId(),
                            ResolutionEnum.DESKTOP.value, null
                        )
                    );
                }
            }));
            this.fileAttachRepository.saveAll(fileAttachesNew);
        }

        if (paramDto.getTechLayout() != null) {
            serviceDraft.setTechLayout(paramDto.getTechLayout());
        }
        if (paramDto.getTechVisible() != null) {
            serviceDraft.setTechVisible(paramDto.getTechVisible());
        }
        if (Objects.equals(FileAttachTypeEnum.CAPTURE.value, paramDto.getType())) {
            serviceDraft.setUpdateSnapReason(paramDto.getReason());
        } else if (Objects.equals(FileAttachTypeEnum.TECH.value, paramDto.getType())) {
            serviceDraft.setUpdateTechReason(paramDto.getReason());
        }
        serviceDraft.setApprove(ServiceApproveStatusEnum.UNAPPROVED);
        serviceDraftRepository.save(serviceDraft);
    }



    /**
     * Cập nhật thông tin tính năng
     */
    @Override
    @Transactional
    public void updateFeatures(ServiceFeatureDTO paramDto) {
        Optional<ServiceEntity> optional = serviceRepository.findById(paramDto.getId());
        if (optional.isPresent()) {
            ServiceEntity serviceEntity = optional.get();
            //Update cho service sau lần đầu duyệt
            if (Objects.equals(ServiceApproveStatusEnum.APPROVED, serviceEntity.getApprove())) {
                if (!(serviceEntity.getUserId().equals(AuthUtil.getCurrentParentId()) || AuthUtil.isAdmin())) {
                    String message = messageSource.getMessage(MessageKeyConstant.SERVICE_IS_NOT_OWNED, SERVICE_MESSAGE, Locale.US);
                    throw new BadRequestException(message, Resources.SERVICES, ErrorKey.ID,
                        MessageKeyConstant.SERVICE_IS_NOT_OWNED);
                }
                ServiceDraft serviceDraft = findByServiceId(serviceEntity.getId());
                if (Objects.nonNull(serviceDraft) && Objects.equals(ServiceApproveStatusEnum.APPROVED, serviceDraft.getApprove())) {
                    ServiceDraft serviceDraftNew = cloneDataService(serviceEntity, ActionEnum.UPDATE);
                    updateFeaturesServiceDraft(paramDto, serviceDraftNew);
                } else if (Objects.nonNull(serviceDraft)) {
                    updateFeaturesServiceDraft(paramDto, serviceDraft);
                }
            } else {
                featureRepository.clearByServiceId(serviceEntity.getId());
                updateFeatureInfo(paramDto.getFeatures(),paramDto.getId(), serviceEntity);
                if(paramDto.getFeatureVisible() !=null) serviceEntity.setFeatureVisible(paramDto.getFeatureVisible());
                serviceRepository.save(serviceEntity);
            }
        }
    }

    /**
     * Cập nhật thông tin tính năng
     */
    private void updateFeaturesServiceDraft(ServiceFeatureDTO paramDto, ServiceDraft serviceDraft) {
        featureRepository.clearByServiceDraftId(serviceDraft.getId());
        updateFeatureInfoServiceDraft(paramDto.getFeatures(), serviceDraft);
        if (paramDto.getFeatureVisible() != null) {
            serviceDraft.setFeatureVisible(paramDto.getFeatureVisible());
        }
        serviceDraft.setApprove(ServiceApproveStatusEnum.UNAPPROVED);
        serviceDraft.setUpdateFeatureReason(paramDto.getReason());
        serviceDraftRepository.save(serviceDraft);
    }

    /**
     * Cập nhật Feature cho service draft
     *
     */
    private void updateFeatureInfoServiceDraft(List<FeatureReqDTO> features, ServiceDraft serviceDraft) {
        if (features != null) {
//            List<Feature> featuresList = new ArrayList<>();
            int index = 1;
            for (FeatureReqDTO featureReqDTO : features) {
                featureReqDTO.setPriorityOrder(index++);
                Feature feature = new Feature();
                BeanUtils.copyProperties(featureReqDTO, feature);
                feature.setStatus(StatusEnum.ACTIVE.value);
                feature.setBaseId(Objects.nonNull(featureReqDTO.getBaseId()) ? featureReqDTO.getBaseId() : featureReqDTO.getId());
                feature.setServiceDraftId(serviceDraft.getId());
                feature.setId(null);
                feature.setCreatedBy(AuthUtil.getCurrentUserId());
//                featuresList.add(feature);
//                Feature feature = new Feature(null, featureReqDTO.getName(), null, featureReqDTO.getCode(), null,
//                        featureReqDTO.getDescription(), featureReqDTO.getType(), featureReqDTO.getPriorityOrder(), featureReqDTO.getFileId(), serviceDraft.getId());
//                feature.setStatus(StatusEnum.ACTIVE.value);

                feature = featureRepository.save(feature);
                if (Objects.isNull(feature.getBaseId())) {
                    feature.setBaseId(feature.getId());
                    featureRepository.save(feature);
                }
            }
//            featureRepository.saveAll(featuresList);
        }
    }

    /**
     * Cập nhật icon + banner
     */
    @Transactional
    public void updateIconAndBanner(ServiceUpdateDTO paramDto) {
        if(paramDto.getIcon() !=null){
            fileAttachRepository.clearByServiceId(paramDto.getId(),FileAttachTypeEnum.AVATAR.value);
            fileAttachRepository.assignToService(paramDto.getIcon(),paramDto.getId(),FileAttachTypeEnum.AVATAR.value);
        }
        if(paramDto.getBanner() !=null){
            fileAttachRepository.clearByServiceId(paramDto.getId(),FileAttachTypeEnum.BANNER.value);
            fileAttachRepository.assignToService(paramDto.getBanner(),paramDto.getId(),FileAttachTypeEnum.BANNER.value);
        }

        if (!CollectionUtils.isEmpty(paramDto.getIconService())) {
            fileAttachRepository.clearByServiceId(paramDto.getId(), FileAttachTypeEnum.AVATAR_DEVICE.value);
            fileAttachRepository.assignListToService(paramDto.getIconService(), paramDto.getId(), FileAttachTypeEnum.AVATAR_DEVICE.value);
        }
    }

    private void setFileAttach(ServiceDTO serviceDTO, Boolean isDraft){
        Set<Integer> types = new HashSet<>();
        types.add(FileAttachTypeEnum.AVATAR.value);
        types.add(FileAttachTypeEnum.CAPTURE.value);
        types.add(FileAttachTypeEnum.BANNER.value);
        types.add(FileAttachTypeEnum.TECH.value);
        types.add(FileAttachTypeEnum.AVATAR_DEVICE.value);
        types.add(FileAttachTypeEnum.INTRO.value);
        serviceDTO.setSnapshots(new ArrayList<>());
        serviceDTO.setTechnologies(new ArrayList<>());
        serviceDTO.setIconService(new ArrayList<>());
        List<FileAttach> fileAttaches = isDraft
            ? fileAttachRepository.findAllByServicesDraftIdAndObjectTypeInOrderByPriority(serviceDTO.getId(),types)
            : fileAttachRepository.findAllByServiceIdAndObjectTypeIn(serviceDTO.getId(),types) ;
        for(FileAttach file: fileAttaches){
            if(file.getObjectType().equals(FileAttachTypeEnum.AVATAR.value)){
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                        .id(file.getId())
                        .fileName(file.getFileName())
                        .typeEnum(FileAttachTypeEnum.AVATAR)
                        .filePath(file.getFilePath())
                        .priority(file.getPriority())
                        .fileSize(file.getFileSize())
                        .externalLink(file.getExternalLink())
                        .build();
                serviceDTO.setIcon(attachResDTO);
            } else if (file.getObjectType().equals(FileAttachTypeEnum.AVATAR_DEVICE.value)) {
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                    .id(file.getId())
                    .fileName(file.getFileName())
                    .typeEnum(FileAttachTypeEnum.AVATAR)
                    .filePath(file.getFilePath())
                    .priority(file.getPriority())
                    .fileSize(file.getFileSize())
                    .externalLink(file.getExternalLink())
                    .build();
                serviceDTO.getIconService().add(attachResDTO);
            } else if (file.getObjectType().equals(FileAttachTypeEnum.CAPTURE.value)) {
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                        .id(file.getId())
                        .fileName(file.getFileName())
                        .typeEnum(FileAttachTypeEnum.CAPTURE)
                        .filePath(file.getFilePath())
                        .priority(file.getPriority())
                        .fileSize(file.getFileSize())
                        .externalLink(file.getExternalLink())
                        .title(file.getTitle())
                        .description(file.getDescription())
                        .fileType(file.getFileType())
                        .build();
                serviceDTO.getSnapshots().add(attachResDTO);
            } else if(file.getObjectType().equals(FileAttachTypeEnum.TECH.value)){
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                        .id(file.getId())
                        .fileName(file.getFileName())
                        .typeEnum(FileAttachTypeEnum.CAPTURE)
                        .filePath(file.getFilePath())
                        .priority(file.getPriority())
                        .fileSize(file.getFileSize())
                        .externalLink(file.getExternalLink())
                        .title(file.getTitle())
                        .description(file.getDescription())
                        .fileType(file.getFileType())
                        .build();
                serviceDTO.getTechnologies().add(attachResDTO);
            }  else if(file.getObjectType().equals(FileAttachTypeEnum.BANNER.value)){
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                        .id(file.getId())
                        .fileName(file.getFileName())
                        .typeEnum(FileAttachTypeEnum.BANNER)
                        .filePath(file.getFilePath())
                        .priority(file.getPriority())
                        .fileSize(file.getFileSize())
                        .externalLink(file.getExternalLink())
                        .build();
                serviceDTO.setBanner(attachResDTO);
            } else if (file.getObjectType().equals(FileAttachTypeEnum.INTRO.value) && file.getFileType().equals(FileTypeEnum.VIDEO.value)) {
                // Lấy videos sản phẩm
                FileAttachResDTO attachResDTO = FileAttachResDTO.builder()
                    .id(file.getId())
                    .fileName(file.getFileName())
                    .typeEnum(FileAttachTypeEnum.INTRO)
                    .filePath(file.getFilePath())
                    .priority(file.getPriority())
                    .fileSize(file.getFileSize())
                    .externalLink(file.getExternalLink())
                    .title(file.getTitle())
                    .description(file.getDescription())
                    .fileType(file.getFileType())
                    .build();
                serviceDTO.getVideos().add(attachResDTO);
            }
            if(serviceDTO.getCategoriesId() !=null){
                categoriesRepository.findById(serviceDTO.getCategoriesId())
                    .ifPresent(value -> serviceDTO.setCategoriesName(value.getName()));
            }

        }
    }

    private void setFeatures(ServiceDTO serviceDTO, Boolean isDraft){
        List<Feature> features = isDraft
            ? featureRepository.findAllByServiceDraftIdOrderByPriorityOrderAsc((serviceDTO.getId()))
            : featureRepository.findAllByServiceIdOrderByPriorityOrderAsc(serviceDTO.getId());
        serviceDTO.setFeatures(new ArrayList<>());
        for(Feature feature: features){
            FeatureReqDTO dto = FeatureReqDTO.builder()
                    .id(feature.getId())
                    .code(feature.getCode())
                    .name(feature.getName())
                    .serviceId(feature.getServiceId())
                    .description(feature.getDescription())
                    .type(feature.getType())
                    .fileId(feature.getFileId())
                    .baseId(feature.getBaseId())
                    .priorityOrder(feature.getPriorityOrder())
                .icon(feature.getIcon())
                    .build();
            if(dto.getFileId() !=null){
                Optional<FileAttach> fileAttach = fileAttachRepository.findById(dto.getFileId());
                if(fileAttach.isPresent()){
                    FileAttach f = fileAttach.get();
                    dto.setFilePath(f.getFilePath());
                    dto.setExternalLink(f.getExternalLink());
                    dto.setFileName(f.getFileName());
                }
            }
            serviceDTO.getFeatures().add(dto);
        }
    }

    @Override
    public ServiceEntity updateSeggestionService(Long id, ServiceSuggestionUpdateDTO req){
        ServiceEntity service = getCurrentService(id);
        service.setSuggestionType(req.getSuggestionType());
        serviceRepository.save(service);
        serviceSuggestionService.updateSeggestionService(id, 1, req);
        return service;
    }

    @Override
    public void updateTopic(Long id, List<ServiceTopicUpdateRequest> reqs){
//        ServiceEntity service = getCurrentService(id);
        topicFaqService.updateTopicService(id, ServiceEnum.SERVICE.getValue(), reqs);
    }

    @Transactional
    public AddonDeleteResDTO deleteServices(AddonIdsReqDTO idsReqDTO) {
        List<Long> listServiceDraftId = idsReqDTO.getIds();
        //lay list service theo list service_draft
        List<ServiceEntity> listService = serviceRepository.findAllByIdInAndDeletedFlag(listServiceDraftId, DELETE_FLAG);
        if (listService.isEmpty()) {
            String message = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.SERVICES, ErrorKey.ID, MessageKeyConstant.NOT_FOUND);
        }
        this.validateServicePermissionDev(listService);

        List<ServiceDraft> listServiceDraft = serviceDraftRepository.findByServiceIdInAndDeletedFlag(idsReqDTO.getIds(), DELETE_FLAG);
        Set<Long> idDrafts = listServiceDraft.stream().map(ServiceDraft::getId).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(idDrafts)) {
            serviceDraftRepository.updateDeleteFlagByServiceDraftIds(idDrafts);
        }

        List<Long> listServiceId = listService.stream().map(ServiceEntity::getId).collect(Collectors.toList());
        List<Long> listServiceIdSubscription = serviceRepository.findListServiceIdSubscription(listServiceId);
        //Lấy ra danh sách service ko có đăng ký với subscription nào
        List<ServiceEntity> listServiceCanDelete = listService.stream()
            .filter(x -> !listServiceIdSubscription.contains(x.getId())).collect(Collectors.toList());
        //Nếu tất cả Service đều đã đăng ký subscription thì báo lỗi
        if (listServiceCanDelete.isEmpty()) {
            String message = messageSource
                .getMessage(MessageKeyConstant.SERVICE_STILL_USED, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            throw new ResourceNotFoundException(message, Resources.SERVICES, ErrorKey.ID,
                MessageKeyConstant.SERVICE_STILL_USED);
        }
        List<Long> pricingIdsDeleted = new ArrayList<>();
        List<Long> pricingMultiIdsDeleted = new ArrayList<>();
        List<Long> serviceIdsDeleted = new ArrayList<>();
        //Lay thong tin pricing, pricing_multi_plan của service
        listServiceCanDelete.forEach(s -> {
            List<Long> pricingIds = serviceRepository.findListPricingByServiceId(s.getId());
            List<Long> pricingMultiIds = pricingMultiPlanRepository.findListPricingMultiPlanByPricingId(pricingIds);
            pricingIdsDeleted.addAll(pricingIds);
            pricingMultiIdsDeleted.addAll(pricingMultiIds);
            serviceIdsDeleted.add(s.getId());
            s.setDeletedFlag(DeletedFlag.DELETED.getValue());
        });

        // xóa pricing_draft, pricing, và các bảng liên quan
        serviceDraftRepository.updateDeleteFlagByServiceIds(serviceIdsDeleted);
        couponPricingRepository.deleteByPricingIdIn(pricingIdsDeleted);
        couponPricingApplyRepository.deleteByPricingIdIn(pricingIdsDeleted);
        couponPricingPlanApplyRepository.deleteByPricingMultiPlanIdIn(pricingMultiIdsDeleted);
        couponPricingPlanRepository.deleteByPricingMultiPlanIdInAndType(pricingMultiIdsDeleted, 0);
        pricingMultiPlanRepository.updateDeleteFlagById(pricingMultiIdsDeleted);
        comboPricingRepository.deleteByPricingMultiPlanIdIn(pricingMultiIdsDeleted);
        comboPricingRepository.deleteByPricingIdIn(pricingIdsDeleted);
        couponAddonRepository.changeOffStatusCouponPricingType();
        pricingRepository.deleteAllPricingByServiceId(serviceIdsDeleted);
        pricingRepository.deleteAllPricingDraftByServiceId(serviceIdsDeleted);
        serviceRepository.saveAll(listServiceCanDelete);

        // update nhóm sp khi xóa dịch vụ / thiết bị
        // chuyển các nhóm có chứa xóa dịch vụ / thiết bị bị xóa thành trạng thái Chưa duyệt
        List<ServiceGroupDraft> lstServiceGroupDraft = serviceGroupDraftRepository.getLstGroupDraftByServiceIdIn(serviceIdsDeleted);
        serviceGroupService.saveGroupDraftAsUnapproved(lstServiceGroupDraft, Boolean.TRUE);

        if (listServiceCanDelete.size() == idsReqDTO.getIds().size()) {
            String message = messageSource
                .getMessage(MessageKeyConstant.SUCCESS_DELETED_ALL_SERVICE, SERVICE_MESSAGE, LocaleContextHolder.getLocale());
            return new AddonDeleteResDTO(MessageKeyConstant.SUCCESS_DELETED_ALL_SERVICE, message);
        } else {
            String message = messageSource
                .getMessage(MessageKeyConstant.SUCCESS_DELETED_SERVICE_NON_USED, SERVICE_MESSAGE,
                    LocaleContextHolder.getLocale());
            return new AddonDeleteResDTO(MessageKeyConstant.SUCCESS_DELETED_SERVICE_NON_USED, message);
        }
    }

    @Override
    public ServiceDraft findByServiceId(Long id){
        // tim serviceDraft moi nhat
        return serviceDraftRepository.findByServiceId(id).orElse(null);
    }

    @Override
    public ServiceDraft findById(Long id) {
        return serviceDraftRepository.findById(id).orElse(null);
    }

    /**
     * Convert customerType String sang Set<CustomerTypeEnum>
     * @return Set<CustomerTypeEnum>
     */
    private Set<CustomerTypeEnum> convertToEntityAttribute(String customerTypeStr) {
        ObjectMapper mapper = new ObjectMapper();
        Set<CustomerTypeEnum> customerTypes = new HashSet<>();
        if(StringUtils.isNotEmpty(customerTypeStr)) {
            try {
                TypeReference<Set<String>> typeRef = new TypeReference<Set<String>>() {};
                customerTypes = mapper.readValue(customerTypeStr, typeRef)
                        .stream().map(CustomerTypeEnum::getValueOf)
                        .collect(Collectors.toSet());
            } catch (IOException e) {
                customerTypes = new HashSet<>();
            }
        }
        return customerTypes;
    }

    /**
     * Đổi đối tượng trả về cho màn danh sách
     * @return Page<ServiceResponseTransDTO>
     */
    private Page<ServiceResponseTransDTO> convertServiceEntityPage(Page<BigInteger> lstPageId) {
        List<ServiceResponseDTO> lstServiceDetail = serviceRepository.getListServiceDetailByIdIn(
            lstPageId.stream().map(BigInteger::longValue).collect(Collectors.toSet()), AuthUtil.getDefaultCurrentParentId());
        return convertServiceDetailPage(lstPageId.map(
            id -> lstServiceDetail.stream().filter(item -> Objects.equals(item.getId(), id.longValue())).findFirst().orElse(null)));
    }

    private Page<ServiceResponseTransDTO> convertServiceDetailPage(Page<ServiceResponseDTO> dtoPage) {
        Set<Long> setServiceId = dtoPage.map(ServiceResponseDTO::getId).toSet();
        Map<Long, List<CommonIdNameDTO>> mapServiceFeatureBrief = serviceRepository.getServiceCategory(setServiceId).stream()
            .collect(Collectors.toMap(IServiceCategoryDTO::getServiceId, IServiceCategoryDTO::getCategories));

        List<VariantDTO> listVariant = variantRepository.getVariantDefaultByServiceIds(setServiceId);
        Map<Long, VariantDTO> mapVariant = listVariant.stream().collect(
            Collectors.groupingBy(VariantDTO::getServiceId, Collectors.collectingAndThen(Collectors.toList(), values -> values.get(0))));
        return dtoPage.map(resDTO -> convertItemService(resDTO, mapVariant, mapServiceFeatureBrief));
    }

    // convert service detail interface to entity page response
    // note 26/12/2023 copy từ method convertServiceEntityPage trên, method này ko lấy price variant trong loop nữa
    private Page<ServiceResponseTransDTO> convertToServiceDetailRes(Page<ServiceResponseDTO> dtoPage) {
        // lấy thông tin giá và Km của biến thể mặc định
        Map<Long, VariantResExtraDTO> mapVariantDefault = new HashMap<>();
        dtoPage.stream()
            .filter(item -> Objects.equals(item.getProductType(), ServiceProductTypeEnum.DEVICE.value))
            .map(ServiceResponseDTO::getId).forEach(serviceId -> {
                VariantResExtraDTO defaultVariant = variantService.getDefaultVariantByServiceId(serviceId);
                if (Objects.nonNull(defaultVariant)) {
                    mapVariantDefault.put(serviceId, defaultVariant);
                }
            });
        Long currentUserId = AuthUtil.getCurrentUserId();
        return dtoPage.map(resDTO -> ServiceResponseTransDTO.builder()
            .id(resDTO.getId())
            .name(resDTO.getName())
            .developer(resDTO.getDeveloper())
            .updatedTime(resDTO.getUpdatedTime())
            .createdTime(resDTO.getCreatedTime())
            .categoryId(resDTO.getCategoryId())
            .categoryName(resDTO.getCategoryName())
            .status(resDTO.getStatus())
            .displayed(resDTO.getDisplayed())
            .icon(resDTO.getIcon())
            .externalLink(resDTO.getExternalLink())
            .banner(resDTO.getBanner())
            .externalLinkBanner(resDTO.getExternalLinkBanner())
            .sapoDescription(resDTO.getSapoDescription())
            .subscriptionNumber(resDTO.getSubscriptionNumber())
            .avgRating(resDTO.getAvgRating())
            .ratingQuantity(resDTO.getRatingQuantity())
            .serviceOwner(resDTO.getServiceOwner())
            .planUrl(resDTO.getPlanUrl())
            .reaction(resDTO.getReaction())
            .customerType(convertToEntityAttribute(resDTO.getCustomerType()))
            .paymentMethod(PaymentMethodEnum.fromValue(resDTO.getPaymentMethod()))
            .categoryNameApp(resDTO.getCategoryNameApp())
            .productType(ServiceProductTypeEnum.fromValue(
                Objects.nonNull(resDTO.getProductType()) ? resDTO.getProductType() : ServiceProductTypeEnum.SAAS.value))
            .defaultPricing(Objects.nonNull(resDTO.getDefaultPricingDraftId()) ? getPricingDefault(resDTO.getDefaultPricingDraftId()) : null)
            .variantDraftId(resDTO.getVariantDraftId())
            .isService(Boolean.TRUE)
            .allowMultiSub(resDTO.getAllowMultiSub())
            .numSub(Objects.nonNull(currentUserId) ?
                shoppingCartRepository.getNumSubOfServiceUser(true, resDTO.getId(), currentUserId) : null)
            .soldNumber(resDTO.getSoldNumber())
            .approveAt(resDTO.getApproveAt())
            .variantDefaultResponseDTO(mapVariantDefault.getOrDefault(resDTO.getId(), null))
            .build());
    }

    private Page<ServiceResponseTransDTO> convertComboPage(Page<ServiceResponseDTO> dtoPage) {
        Long smeId = ObjectUtil.getOrDefault(AuthUtil.getCurrentParentId(), -1L);
        List<Long> comboPlanIds = dtoPage.stream().map(ServiceResponseDTO::getComboPlanIdDefault).filter(Objects::nonNull).collect(Collectors.toList());
        List<PricingTaxRes> planTaxList = comboTaxRepository.getListComboTax(comboPlanIds);
        Map<Long, List<PricingTaxRes>> mapTax = planTaxList.stream().collect(Collectors.groupingBy(PricingTaxRes::getComboPlanId));

        List<IGetDetailItemCombo> listItemCombo = comboRepository.getPricingDetailCombo(comboPlanIds);
        Map<Long, List<IGetDetailItemCombo>> mapItemCombo = listItemCombo.stream().collect(Collectors.groupingBy(IGetDetailItemCombo::getComboPlanId));
        String customerType =  Objects.nonNull(AuthUtil.getCurrentUser()) ? AuthUtil.getCurrentUser().getCustomerType() : "";
        return dtoPage.map(resDTO -> {
            List<CategoryDTODetail> categories = comboRepository.getListCategory(resDTO.getCategoriesIds());
            List<CommonIdNameDTO> categoryList = new ArrayList<>();
            categories.forEach(c -> {
                CommonIdNameDTO category = new CommonIdNameDTO();
                category.setId(c.getId());
                category.setName(c.getName());
                categoryList.add(category);
            });
            BigDecimal pricePreTaxDefault = resDTO.getDefaultPrice();
            if (Objects.nonNull(resDTO.getComboPlanIdDefault())) {
                List<PricingTaxRes> listTax = mapTax.get(resDTO.getComboPlanIdDefault());
                if (!CollectionUtils.isEmpty(listTax)) {
                    pricePreTaxDefault = subscriptionFormula.priceBeforeTax(resDTO.getDefaultPrice(), listTax);
                }
            }
            ServiceResponseTransDTO serviceResponseTransDTO = ServiceResponseTransDTO.builder()
                .id(resDTO.getId())
                .type(resDTO.getType())
                .name(resDTO.getName())
                .developer(resDTO.getDeveloper())
                .updatedTime(resDTO.getUpdatedTime())
                .categoryName(resDTO.getCategoryName())
                .status(resDTO.getStatus())
                .displayed(resDTO.getDisplayed())
                .icon(resDTO.getIcon())
                .externalLink(resDTO.getExternalLink())
                .banner(resDTO.getBanner())
                .externalLinkBanner(resDTO.getExternalLinkBanner())
                .sapoDescription(resDTO.getSapoDescription())
                .numSub(resDTO.getSubscriptionNumber())
                .avgRating(resDTO.getAvgRating())
                .ratingQuantity(resDTO.getRatingQuantity())
                .serviceOwner(resDTO.getServiceOwner())
                .planUrl(resDTO.getPlanUrl())
                .reaction(resDTO.getReaction())
                .customerType(convertToEntityAttribute(resDTO.getCustomerType()))
                .isService(Boolean.FALSE)
                .allowMultiSub(resDTO.getAllowMultiSub())
                .description(resDTO.getDescription())
                .defaultPrice(pricePreTaxDefault)
                .previewPrice(resDTO.getPreviewPrice())
                .providerName(resDTO.getProviderName())
                .pricingNumber(resDTO.getPricingNumber())
                .categories(categoryList)
                .defaultPricingName(resDTO.getDefaultPricingName())
                .defaultPricingPaymentCycle(resDTO.getDefaultPricingPaymentCycle())
                .defaultNumberOfCycle(resDTO.getDefaultNumberOfCycle())
                .calculateTypeEnum(CalculateTypeEnum.fromValue(resDTO.getCalculateType()))
                .build();

            if (Objects.equals(resDTO.getType(), COMBO)) { // combo
                serviceResponseTransDTO.setComboPricing(mapItemCombo.get(resDTO.getComboPlanIdDefault()));
                Coupon coupons = couponRepository.findByComboPlanLatestId(resDTO.getComboPlanIdDefault(), smeId, resDTO.getUserId(), customerType);
                serviceResponseTransDTO.setLatestCoupon(comboService.convertCoupon(coupons));
            } else if (Objects.equals(resDTO.getType(), SERVICE_GROUP)) { // nhom
                List<IGetDetailItemCombo> listItem = comboRepository.getDetailItemServiceGroup(resDTO.getId());
                serviceResponseTransDTO.setComboPricing(listItem.stream().filter(IGetDetailItemCombo::getIsPricing).collect(Collectors.toList()));
                serviceResponseTransDTO.setComboDevice(addComboDevice(listItem));
            }

            return serviceResponseTransDTO;
        });
    }

    @Override
    public List<DetailItemCombo> addComboDevice(List<IGetDetailItemCombo> listItem) {
        Map<Long, List<IGetDetailItemCombo>> map = listItem.stream().filter(i -> !i.getIsPricing()).collect(Collectors.toList())
            .stream().collect(Collectors.groupingBy(IGetDetailItemCombo::getIdConcat, Collectors.toList()));
        List<DetailItemCombo> detailItemCombos = new ArrayList<>();
        for (Entry<Long, List<IGetDetailItemCombo>> entry : new ArrayList<>(map.entrySet())) {
            List<IGetDetailItemCombo> list = entry.getValue();
            IGetDetailItemCombo iGetDetailItemCombo = list.get(0);
            DetailItemCombo item = new DetailItemCombo();
            BeanUtils.copyProperties(iGetDetailItemCombo, item);
            item.setQuantity(list.stream().map(IGetDetailItemCombo::getQuantity).reduce(0L, Long::sum));
            detailItemCombos.add(item);
        }
        return detailItemCombos;
    }

    @Override
    public Page<ServiceResponseTransDTO> getTopSellingService(String search, Pageable pageable, Long category, CustomerTypeEnum customerType,
        Set<ServiceProductTypeEnum> serviceProductTypes) {
        Set<Integer> setServiceProductType = serviceProductTypes.stream().map(ServiceProductTypeEnum::getValue).collect(Collectors.toSet());
        return convertServiceEntityPage(serviceRepository.getPageServiceSellingId(search, category, customerType.getValue(),
            setServiceProductType, pageable));
    }

    public Integer checkDeleteFeatureInService(List<Long> featureIds, Long serviceId) {
        return featureRepository.countPricingByFeatureIdsAndServiceId(serviceId, featureIds);
    }

    @Transactional
    @Override
    public UpdateServiceResDTO updateService(ServiceUpdateAllDTO serviceDTO, Long serviceId, PortalType portalType) {
        //Validate
        findServiceById(serviceId);

        ServiceEntity currentService = getCurrentService(serviceId);

        // Kiểm tra customerType của các Addon , pricing có liên quan
        UpdateServiceResDTO resDTO = new UpdateServiceResDTO();
        if (Objects.isNull(currentService.getServiceTypeApplication())) { // nếu là ứng dụng thì k cần check
            Set<String> customerTypeUpdate = new HashSet<>();
            serviceDTO.getCustomerTypeCode().forEach(e -> customerTypeUpdate.add(e.getValue()));
            val listAddon = addonDraftRepository.findAllByServiceIdAndDeletedFlagAndApprove(currentService.getId() , 1 , ApproveStatusEnum.APPROVED.value);
            val listPricing = pricingDraftRepository.findByServiceIdAndDeletedFlagAndApprove(currentService.getId() , 1 , ApproveStatusEnum.APPROVED.value);
            for(AddonDraft addon : listAddon){
                if(!customerTypeUpdate.containsAll(addon.getCustomerTypeCode())){
                    resDTO.getLstAddon().add(new UpdateServiceResDTO.ValueDTO(addon.getId(), addon.getName()));
                }
            }
            for(PricingDraft pricing : listPricing){
                if(!customerTypeUpdate.containsAll(pricing.getCustomerTypeCode())){
                    resDTO.getLstPricing().add(new UpdateServiceResDTO.ValueDTO(pricing.getId(), pricing.getPricingName()));
                }
            }
            //nếu có bất cứ gói hay addon nào ko thỏa mãn điều kiện về đối tượng khách hàng thì ko thay đổi trạng thái dịch vụ
            if(!resDTO.getLstPricing().isEmpty() || !resDTO.getLstAddon().isEmpty()) {
                return resDTO;
            }
        }


        //lấy thông tin dịch vụ cũ để so sánh
        ServiceEntity serviceCurrent = serviceRepository.getServiceById(serviceId);
        ServiceEntity serviceInfoOld = new ServiceEntity();
        BeanUtils.copyProperties(serviceCurrent, serviceInfoOld);


        //Cập nhật thông tin cơ bản của dịch vụ
        ServiceUpdateDTO serviceUpdateDTO = new ServiceUpdateDTO();
        serviceUpdateDTO.setId(serviceId);
        BeanUtils.copyProperties(serviceDTO, serviceUpdateDTO);
        updateBaseInfo(serviceUpdateDTO);

       // cập nhật mô tả dịch vụ
        ServiceUpdateDescDTO serviceUpdateDescDTO = new ServiceUpdateDescDTO();
        serviceUpdateDescDTO.setId(serviceId);
        BeanUtils.copyProperties(serviceDTO, serviceUpdateDescDTO, "snapshots");
        updateDescInfo(serviceUpdateDescDTO);

        //Cập nhật thông tin người hỗ trợ
        /* bỏ cập nhật thông tin người hỗ trợ khi update service
        ServiceSupportDTO serviceSupportDTO = new ServiceSupportDTO();
        serviceSupportDTO.setId(serviceId);
        BeanUtils.copyProperties(serviceDTO, serviceSupportDTO);
        updateSupportInfo(serviceSupportDTO);
        */

        //Cập nhật thông tin bố cục
        ServiceSnapDTO serviceSnapDTO = new ServiceSnapDTO();
        BeanUtils.copyProperties(serviceDTO, serviceSnapDTO);
        serviceSnapDTO.setId(serviceId);
        serviceSnapDTO.setType(FileAttachTypeEnum.CAPTURE.value);
        updateSnaps(serviceSnapDTO);
        //cập nhật thông tin công nghệ nổi bật
        ServiceSnapDTO serviceTechDTO = new ServiceSnapDTO();
        BeanUtils.copyProperties(serviceDTO, serviceTechDTO);
        serviceTechDTO.setId(serviceId);
        serviceTechDTO.setType(FileAttachTypeEnum.TECH.value);
        serviceTechDTO.setSnapshots(serviceDTO.getTechnologies());
        updateSnaps(serviceTechDTO);

        //Cập nhật thông tin tính năng
        ServiceFeatureDTO serviceFeatureDTO = new ServiceFeatureDTO();
        serviceFeatureDTO.setId(serviceId);
        BeanUtils.copyProperties(serviceDTO, serviceFeatureDTO);
        updateFeatures(serviceFeatureDTO);

        //Cập nhật thông tin dịch vụ gợi y
        ServiceSuggestionUpdateDTO serviceSuggestionUpdateDTO = new ServiceSuggestionUpdateDTO();
        BeanUtils.copyProperties(serviceDTO, serviceSuggestionUpdateDTO);
        updateSeggestionService(serviceId,serviceSuggestionUpdateDTO );

        // 04/01/2023 cho cả hai dv và ứng dụng đều update topic + seo
        // Cập nhật topic
        if (Objects.nonNull(serviceDTO.getTopicReq())) {
            updateTopic(serviceId, serviceDTO.getTopicReq());
        }
        // Cập nhật seo
        if (Objects.nonNull(serviceDTO.getSeoUpdateDTO())) {
            seoService.updateSeo(serviceDTO.getSeoUpdateDTO());
        }
        if (Objects.nonNull(currentService.getServiceTypeApplication())) {
            return new UpdateServiceResDTO();
        }
        //chỉ thực hiện check cấu hình phê duyệt với dịch vụ đã từng đc phê duyệt rồi và khác ứng dụng
        //check cấu hình phê duyệt dịch vụ
        //lấy thông tin service draft nếu có
        Boolean checkApproveDraft = false;
        Boolean checkApprove = false;
        ServiceDraft serviceDraft = findByServiceId(serviceId);
        if (Objects.equals(ServiceApproveStatusEnum.APPROVED, serviceInfoOld.getApprove())) {
            if(Objects.nonNull(serviceDraft)) {
                checkApproveDraft = checkConfigApproveUpdateServiceDraft(serviceDTO, serviceDraft);
            }
        }else {
            checkApprove = checkConfigApproveUpdateService(serviceDTO, serviceInfoOld);
        }
        if (Objects.equals(ServiceApproveStatusEnum.APPROVED, serviceInfoOld.getApprove())) {
            if(Objects.nonNull(serviceDraft)) {
                if(!checkApproveDraft) {
                    requestApprove(serviceId, true);
                    ServiceApproveDTO serviceApproveDTO = new ServiceApproveDTO(ServiceApproveStatusEnum.APPROVED, "Approved by configuration system");
                    approveService(serviceId, serviceApproveDTO, false);
                }
            }else {
                if(!checkApprove) {
                    requestApprove(serviceId, true);
                    ServiceApproveDTO serviceApproveDTO = new ServiceApproveDTO(ServiceApproveStatusEnum.APPROVED, "Approved by configuration system");
                    approveService(serviceId, serviceApproveDTO, false);
                }
            }
        } else {
            if(!checkApprove) {
                requestApprove(serviceId, true);
                ServiceApproveDTO serviceApproveDTO = new ServiceApproveDTO(ServiceApproveStatusEnum.APPROVED, "Approved by configuration system");
                approveService(serviceId, serviceApproveDTO, false);
            }
        }
        // Lấy thông tin biến thể được tạo để gắn với gói
        if (Objects.equals(serviceDTO.getClassification(), ProductClassificationEnum.PHYSICAL) || Objects.equals(serviceDTO.getProductType(), ServiceProductTypeEnum.DEVICE)) {
            // update thuộc tính biến thể (Bước 2)
            updateAndCreateAttributeVariant(serviceDTO.getAttributes(), serviceDTO.getVariants(), serviceId, portalType);
            // update lại gói dịch vụ (Bước 3)
            updateAndCreatePricing(serviceDTO.getPricingReqDTO(), serviceDTO.getPricingIdsDelete(),serviceId, portalType, serviceDTO.getCustomerTypeCode(), !checkApprove);
        }


        return new UpdateServiceResDTO();
    }

    @Description("Hàm lưu chỉnh sửa thông tin gói")
    private void updateAndCreatePricing (List<PricingReqDTO> lstPricing, List<Long> pricingIdsDelete, Long serviceId, PortalType portalType, Set<CustomerTypeEnum> customerTypeCode, Boolean isApproved) {
        if (Objects.nonNull(pricingIdsDelete) && !pricingIdsDelete.isEmpty()) {
            // xóa các gói không còn nữa
            pricingService.deletePricings(pricingIdsDelete);
        }
        if (Objects.nonNull(lstPricing) && !CollectionUtils.isEmpty(lstPricing)) {
            List<PricingReqDTO> lstPricingCheckRecommend = lstPricing.stream()
                    .filter(pricingReqDTO -> Objects.nonNull(pricingReqDTO.getRecommendedStatus()) && pricingReqDTO.getRecommendedStatus().equals(1))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(lstPricingCheckRecommend) && lstPricingCheckRecommend.size() > 1) {
                String message = messageSource
                        .getMessage(MessageKeyConstant.PRICING_RECOMMENDATION_LIMIT, SERVICE_MESSAGE,
                                LocaleContextHolder.getLocale());
                throw new BadRequestException(message, Resources.SERVICES, ErrorKey.Services.PRICING,
                        MessageKeyConstant.PRICING_RECOMMENDATION_LIMIT);
            }
            // validate và lưu thông tin pricing
            lstPricing.forEach(pricingReqDTO -> {
                try {
                    pricingReqDTO.setServiceId(serviceId);
                    pricingReqDTO.setPortalType(portalType);
                    pricingReqDTO.setCustomerTypeCode(customerTypeCode);
                    if (Objects.nonNull(pricingReqDTO.getId())) {
                        pricingService.updatePricing(pricingReqDTO.getId(), pricingReqDTO, serviceId, isApproved);
                    } else {
                        pricingService.createPricing(pricingReqDTO, true, isApproved);
                    }
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            });
        }
    }


    @Description("Hàm lưu chỉnh sửa thông tin thuộc tính biến thể")
    private void updateAndCreateAttributeVariant (AttributesCreateNewDevDTO attributes, List<VariantReqDTO> variants, Long serviceId, PortalType portalType) {
        if (Objects.nonNull(attributes)) {
            List<AttributesCreateDTO> attributesCreateDTOS = attributesService.updateAttributesDetailServiceV1(serviceId, attributes, portalType);
            if (!CollectionUtils.isEmpty(variants)) {
                variantService.changeVariant(variants.stream().peek(e -> {
                    e.setServiceId(serviceId);
                    e.setAttributesValue(e.getAttributesValueExtra().stream()
                            .map(el -> {
                                VariantAttributesValueConvertDTO res = new VariantAttributesValueConvertDTO();
                                BeanUtils.copyProperties(el, res);
                                if (Objects.isNull(el.getAttributesId())) {
                                    attributesCreateDTOS.stream().filter(o -> Objects.equals(o.getKey(), el.getKey())).findFirst()
                                            .ifPresent(dto -> {
                                                res.setAttributesId(dto.getId());
                                                dto.getLstValueAttribute()
                                                        .stream().filter(o -> Objects.equals(o.getValueName(), el.getValueName()))
                                                        .findFirst().ifPresent(item -> res.setUniqueCode(item.getUniqueCode()));
                                            });
                                }
                                return res;
                            })
                            .collect(Collectors.toList()));
                }).collect(Collectors.toList()), serviceId, Boolean.TRUE);
            }
        }
    }

    private Boolean checkConfigApproveUpdateService (ServiceUpdateAllDTO serviceDTO, ServiceEntity serviceInfoOld) {
        Set<String> customerTypeUpdate = new HashSet<>();
        serviceDTO.getCustomerTypeCode().forEach(e -> customerTypeUpdate.add(e.getValue()));
        List<Integer> infoChange = new ArrayList<>();
        if (!Objects.equals(serviceInfoOld.getCustomerTypeCode(), customerTypeUpdate)) {
            infoChange.add(1);
        }
        if (!Objects.equals(serviceInfoOld.getServiceName(), serviceDTO.getName())) {
            infoChange.add(2);
        }
        if (!Objects.equals(serviceInfoOld.getServiceCode(), serviceDTO.getServiceCode())) {
            infoChange.add(3);
        }
        if (!Objects.equals(serviceInfoOld.getCategoriesId(), serviceDTO.getCategoriesId())) {
            infoChange.add(4);
        }
        if (!Objects.equals(serviceInfoOld.getAllowMultiSub(), serviceDTO.getAllowMultiSub())) {
            infoChange.add(5);
        }
        String infoChangeString = "{" + infoChange.stream().map(Objects::toString).collect(Collectors.joining(",")) + "}";

        List<Long> lstUserId = new ArrayList<>();
        lstUserId.add(-1L);
        lstUserId.add(AuthUtil.getCurrentUserId());
        String infoUser = "{" + lstUserId.stream().map(Objects::toString).collect(Collectors.joining(",")) + "}";

        boolean isOn = serviceDTO.getServiceOwner().equals(ServiceTypeEnum.VNPT) || serviceDTO.getServiceOwner().equals(ServiceTypeEnum.SAAS);
        ApprovedRule approvedRule = approvedRuleRepository.findFirstByType(ApprovedTypeEnum.SERVICE.getValue());
        if (Objects.isNull(approvedRule) || Objects.isNull(approvedRule.getConditionQuery())) {
            return false;
        }
        String queryCheck = String.format("select %s", approvedRule.getConditionQuery());
        Query query = entityManager.createNativeQuery(queryCheck);

        if (approvedRule.getConditionQuery().contains(":userId")) {
            query.setParameter("userId", infoUser);
        }
        if (approvedRule.getConditionQuery().contains(":serviceOwner")) {
            query.setParameter("serviceOwner", isOn ? "ON" : "OS");
        }
        if (approvedRule.getConditionQuery().contains(":changeInformation")) {
            query.setParameter("changeInformation", infoChangeString);
        }
        if (approvedRule.getConditionQuery().contains(":isAll")) {
            query.setParameter("isAll", -1);
        }
        if (approvedRule.getConditionQuery().contains(":isUpdate")) {
            query.setParameter("isUpdate", true);
        }
        return (Boolean) query.getSingleResult();
    }

    private Boolean checkConfigApproveUpdateServiceDraft(ServiceUpdateAllDTO serviceDTO, ServiceDraft serviceInfoOld) {
        Set<String> customerTypeUpdate = new HashSet<>();
        serviceDTO.getCustomerTypeCode().forEach(e -> customerTypeUpdate.add(e.getValue()));
        List<Integer> infoChange = new ArrayList<>();
        if (!Objects.equals(serviceInfoOld.getCustomerTypeCode(), customerTypeUpdate)) {
            infoChange.add(1);
        }
        if (!Objects.equals(serviceInfoOld.getServiceName(), serviceDTO.getName())) {
            infoChange.add(2);
        }
        if (!Objects.equals(serviceInfoOld.getServiceCode(), serviceDTO.getServiceCode())) {
            infoChange.add(3);
        }
        if (!Objects.equals(serviceInfoOld.getCategoriesId(), serviceDTO.getCategoriesId())) {
            infoChange.add(4);
        }
        if (!Objects.equals(serviceInfoOld.getAllowMultiSub(), serviceDTO.getAllowMultiSub())) {
            infoChange.add(5);
        }
        String infoChangeString = "{" + infoChange.stream().map(Objects::toString).collect(Collectors.joining(",")) + "}";

        List<Long> lstUserId = new ArrayList<>();
        lstUserId.add(-1L);
        lstUserId.add(AuthUtil.getCurrentUserId());
        String infoUser = "{" + lstUserId.stream().map(Objects::toString).collect(Collectors.joining(",")) + "}";

        boolean isOn = serviceDTO.getServiceOwner().equals(ServiceTypeEnum.VNPT) || serviceDTO.getServiceOwner().equals(ServiceTypeEnum.SAAS);
        ApprovedRule approvedRule = approvedRuleRepository.findFirstByType(ApprovedTypeEnum.SERVICE.getValue());
        if (Objects.isNull(approvedRule) || Objects.isNull(approvedRule.getConditionQuery())) {
            return false;
        }
        String queryCheck = String.format("select %s", approvedRule.getConditionQuery());
        Query query = entityManager.createNativeQuery(queryCheck);

        if (approvedRule.getConditionQuery().contains(":userId")) {
            query.setParameter("userId", infoUser);
        }
        if (approvedRule.getConditionQuery().contains(":serviceOwner")) {
            query.setParameter("serviceOwner", isOn ? "ON" : "OS");
        }
        if (approvedRule.getConditionQuery().contains(":changeInformation")) {
            query.setParameter("changeInformation", infoChangeString);
        }
        if (approvedRule.getConditionQuery().contains(":isAll")) {
            query.setParameter("isAll", -1);
        }
        if (approvedRule.getConditionQuery().contains(":isUpdate")) {
            query.setParameter("isUpdate", true);
        }

        return (Boolean) query.getSingleResult();
    }

    @Override
    public FileAttach uploadBrandingImage(MultipartFile file) {
        FileAttach fileAttach = saveFileAttach(file, 0L, null);

        // Cập nhật userId cho file
        fileAttach.setUserId(AuthUtil.getCurrentParentId());

        return fileAttachRepository.save(fileAttach);
    }

    @Override
    public PricingResSmeDTO getPricingDefault(Long pricingDraftId) {
        // check type thằng đăng nhập
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerType = Objects.isNull(userLogin) ? null : userLogin.getCustomerType();
        CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getValueOf(customerType);
        //
        Pricing pr = pricingRepository.findFirstByPricingDraftIdAndDeletedFlagAndApproveAndStatusOrderByIdDesc(
                pricingDraftId, DeletedFlag.NOT_YET_DELETED.getValue(), ApproveStatusEnum.APPROVED.value, StatusEnum.ACTIVE.value);
        // Nếu pricing null thì return
        if (Objects.isNull(pr)) return null;
        Set<CustomerTypeEnum> customerTypeEnumSet = CollectionUtils.isEmpty(pr.getCustomerTypeCode()) ? new HashSet<>() :
                pr.getCustomerTypeCode().stream().map(CustomerTypeEnum::getValueOf).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(customerTypeEnumSet) || customerTypeEnumSet.contains(customerTypeEnum) || userLogin == null) {
            PricingResSmeDTO pricingDefaultDTO = new PricingResSmeDTO();
            pricingDefaultDTO.setPricingName(pr.getPricingName());
            // Danh sách tính năng của gói cước
            if (pr.getListFeatureId() != null && !pr.getListFeatureId().isEmpty()) {
                Set<Long> setFeatureId = Arrays.stream(pr.getListFeatureId().split(",")).map(Long::parseLong).collect(Collectors.toSet());
                pricingDefaultDTO.setFeatures(featureRepository.findLstFeatureByIds(new ArrayList<>(setFeatureId)).stream().map(FeatureBriefInfoDTO::new)
                    .collect(Collectors.toList()));
            }
            //
            List<PricingMultipleResDTO> multiplePeriods = subMultiplePeriod.getMultiplePeriodByPricingId(pr.getId(), pr.getIsOneTime());
            List<PricingMultipleResDTO> multiplePeriodResponse = multiplePeriods
                    .stream()
                    .filter(mul -> (mul.getCustomerTypeCode() == null || mul.getCustomerTypeCode().contains(customerTypeEnum) || userLogin == null))
                    .collect(Collectors.toList());
            // Nếu không có thông tin về period -> data cũ, lấy theo cách cũ
            if (CollectionUtils.isEmpty(multiplePeriodResponse)) {
                // Số tiền trước thuế
                List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(pr.getId());
                BigDecimal pricingPreTax = subscriptionFormula.priceBeforeTax(pr.getPrice(), taxes);
                pricingDefaultDTO.setPrice(pricingPreTax);
                pricingDefaultDTO.setPriceValue(pr.getPrice());
                pricingDefaultDTO.setPricingPlan(PricingPlanEnum.valueOf(pr.getPricingPlan()));
                // Danh sách chu kỳ định giá
                pricingDefaultDTO.setUnitLimitedList(pricingService.getUnitLimitedByPricingId(pr.getId()));
            }

            // Nếu có period nhưng không thuộc kiểu người dùng
            if (!CollectionUtils.isEmpty(multiplePeriods) && CollectionUtils.isEmpty(multiplePeriodResponse)) {
                return null;
            } else {
                pricingDefaultDTO.setPricingMultiplePeriods(multiplePeriodResponse);
                pricingDefaultDTO.setPricingDraftId(pr.getPricingDraftId());
                pricingDefaultDTO.setPricingId(pr.getId());
                return pricingDefaultDTO;
            }
        }
        return null;
    }

    private ServiceResponseTransDTO convertServiceEntity(ServiceResponseDTO resDTO, Map<Long, List<CommonIdNameDTO>> mapServiceFeatureBrief) {
        VariantDTO variantDTO = Objects.nonNull(resDTO.getProductType()) && Objects.equals(resDTO.getProductType(), ServiceProductTypeEnum.DEVICE.value)
                ? variantRepository.getVariantDefaultByServiceId(resDTO.getId())
                : null;
        String defaultVariantAvatar = variantDTO != null ? variantDTO.getAvatar() : null;
        return ServiceResponseTransDTO.builder()
                .id(resDTO.getId())
                .name(resDTO.getName())
                .developer(resDTO.getDeveloper())
                .updatedTime(resDTO.getUpdatedTime())
                .categoryName(resDTO.getCategoryName())
                .status(resDTO.getStatus())
                .displayed(resDTO.getDisplayed())
                .icon(ObjectUtil.getOrDefault(defaultVariantAvatar, resDTO.getIcon()))
                .externalLink(resDTO.getExternalLink())
                .banner(resDTO.getBanner())
                .externalLinkBanner(resDTO.getExternalLinkBanner())
                .sapoDescription(resDTO.getSapoDescription())
                .numSub(resDTO.getSubscriptionNumber())
                .avgRating(resDTO.getAvgRating())
                .ratingQuantity(resDTO.getRatingQuantity())
                .serviceOwner(resDTO.getServiceOwner())
                .planUrl(resDTO.getPlanUrl())
                .reaction(resDTO.getReaction())
                .customerType(convertToEntityAttribute(resDTO.getCustomerType()))
                .paymentMethod(PaymentMethodEnum.fromValue(resDTO.getPaymentMethod()))
                .categoryNameApp(resDTO.getCategoryNameApp())
                .productType(ServiceProductTypeEnum.fromValue(
                    Objects.nonNull(resDTO.getProductType()) ? resDTO.getProductType() : ServiceProductTypeEnum.SAAS.value))
                .categories(mapServiceFeatureBrief.get(resDTO.getId()))
                .defaultPricing(Objects.nonNull(resDTO.getDefaultPricingDraftId()) ? getPricingDefault(resDTO.getDefaultPricingDraftId()) : null)
                .variantDraftId(Objects.nonNull(variantDTO) ? variantDTO.getId() : null)
                .variantId(Objects.nonNull(variantDTO) ? variantDTO.getVariantId() : null)
                .isService(Boolean.TRUE)
                .allowMultiSub(resDTO.getAllowMultiSub())
                .build();
    }

    private ServiceResponseTransDTO convertComboEntity(ServiceResponseDTO resDTO) {
        List<CategoryDTODetail> categories = comboRepository.getListCategory(resDTO.getCategoriesIds());
        List<CommonIdNameDTO> categoryList = new ArrayList<>();
        categories.forEach(c -> {
            CommonIdNameDTO category = new CommonIdNameDTO();
            category.setId(c.getId());
            category.setName(c.getName());
            categoryList.add(category);
        });

        BigDecimal pricePreTaxDefault = resDTO.getDefaultPrice();
        if (Objects.nonNull(resDTO.getComboPlanIdDefault())) {
            List<PricingTaxRes> listTax = comboTaxRepository.getListComboTax(Collections.singletonList(resDTO.getId()));
            if (!CollectionUtils.isEmpty(listTax)) {
                pricePreTaxDefault = subscriptionFormula.priceBeforeTax(resDTO.getDefaultPrice(), listTax);
            }
        }

        ServiceResponseTransDTO serviceResponseTransDTO = ServiceResponseTransDTO.builder()
            .id(resDTO.getId())
            .name(resDTO.getName())
            .developer(resDTO.getDeveloper())
            .updatedTime(resDTO.getUpdatedTime())
            .categoryName(resDTO.getCategoryName())
            .status(resDTO.getStatus())
            .displayed(resDTO.getDisplayed())
            .icon(resDTO.getIcon())
            .externalLink(resDTO.getExternalLink())
            .banner(resDTO.getBanner())
            .externalLinkBanner(resDTO.getExternalLinkBanner())
            .sapoDescription(resDTO.getSapoDescription())
            .numSub(resDTO.getSubscriptionNumber())
            .categories(categoryList)
            .avgRating(resDTO.getAvgRating())
            .ratingQuantity(resDTO.getRatingQuantity())
            .serviceOwner(resDTO.getServiceOwner())
            .planUrl(resDTO.getPlanUrl())
            .reaction(resDTO.getReaction())
            .customerType(convertToEntityAttribute(resDTO.getCustomerType()))
            .paymentMethod(PaymentMethodEnum.fromValue(resDTO.getPaymentMethod()))
            .isService(Boolean.FALSE)
            .allowMultiSub(resDTO.getAllowMultiSub())
            .type(resDTO.getType())
            .description(resDTO.getDescription())
            .defaultPrice(pricePreTaxDefault)
            .previewPrice(resDTO.getPreviewPrice())
            .providerName(resDTO.getProviderName())
            .pricingNumber(resDTO.getPricingNumber())
            .categories(categoryList)
            .defaultPricingName(resDTO.getDefaultPricingName())
            .defaultPricingPaymentCycle(resDTO.getDefaultPricingPaymentCycle())
            .defaultNumberOfCycle(resDTO.getDefaultNumberOfCycle())
            .comboPricing(Objects.nonNull(resDTO.getComboPlanIdDefault()) ? comboRepository.getPricingDetailCombo(
                Collections.singletonList(resDTO.getComboPlanIdDefault())) : null)
            .build();
        if (Objects.nonNull(resDTO.getComboPlanIdDefault())) {
            // Lấy danh sách khuyến mãi
            String customerType = Objects.nonNull(AuthUtil.getCurrentUser()) ? AuthUtil.getCurrentUser().getCustomerType() : "";
            Coupon coupons = couponRepository.findByComboPlanLatestId(resDTO.getComboPlanIdDefault(), AuthUtil.getCurrentParentId(),
                resDTO.getUserId(), customerType);
            serviceResponseTransDTO.setLatestCoupon(comboService.convertCoupon(coupons));
        }
        return serviceResponseTransDTO;
    }

    @Override
    public List<ServiceResponseTransDTO> getServiceSelected(List<ServiceSelectedRequestDTO> requestDTOs) {
        if (requestDTOs == null || requestDTOs.isEmpty()) return new ArrayList<>();
        // service
        Set<Long> serviceIds = requestDTOs.stream().filter((e) -> e.getType() == 1).map(ServiceSelectedRequestDTO::getId).collect(Collectors.toSet());
        Map<Long, ServiceResponseDTO> serviceDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(serviceIds)) {
            List<ServiceResponseDTO> serviceDTOList = serviceRepository.getServiceSelectedPageBuilder(serviceIds);
            serviceDTOMap.putAll(serviceDTOList.stream().collect(Collectors.toMap(ServiceResponseDTO::getId, Function.identity(), (oldValue, newValue) -> newValue)));
        }
        // combo
        Set<Long> comboIds = requestDTOs.stream().filter((e) -> e.getType() == 2).map(ServiceSelectedRequestDTO::getId).collect(Collectors.toSet());
        Map<Long, ServiceResponseDTO> comboDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(comboIds)) {
            List<ServiceResponseDTO> comboDTOList = serviceRepository.getComboSelectedPageBuilder(comboIds);
            comboDTOMap.putAll(comboDTOList.stream().collect(Collectors.toMap(ServiceResponseDTO::getId, Function.identity(), (oldValue, newValue) -> newValue)));
        }
        //
        List<ServiceSelectedRequestDTO> request = new ArrayList<>();
        requestDTOs.forEach(i -> {
            if (i.getType() == 1 && Objects.nonNull(serviceDTOMap.get(i.getId()))) {
                request.add(i);
            }
            if (i.getType() == 2 && Objects.nonNull(comboDTOMap.get(i.getId()))) {
                request.add(i);
            }
        });
        Map<Long, List<CommonIdNameDTO>> mapServiceFeatureBrief = serviceRepository.getServiceCategory(serviceIds).stream()
            .collect(Collectors.toMap(IServiceCategoryDTO::getServiceId, IServiceCategoryDTO::getCategories));
        return request.stream().map((e) -> {
            if (e.getType() == 1) {
                return convertServiceEntity(serviceDTOMap.get(e.getId()), mapServiceFeatureBrief);
            }
            return convertComboEntity(comboDTOMap.get(e.getId()));
        }).collect(Collectors.toList());
    }

    @Override
    public String getServiceName(Long serviceId) {
        ServiceEntity service = serviceRepository.findById(serviceId).orElse(null);
        if (Objects.nonNull(service)){
            return service.getServiceName();
        }
        return "";
    }

    @Override
    public Page<ServiceResponseTransDTO> getTopSellingCombo(String search, CustomerTypeEnum customerTypeEnum, Pageable pageable, Long category) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        Page<ServiceResponseDTO> dtoPage;
        String customerTypeQuery = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        if (userLogin != null) {
            if (userLogin.getParentId() == -1) {
                dtoPage = serviceRepository.getListComboTopSelling(search, customerTypeQuery, pageable, userLogin.getId());
            } else {
                dtoPage = serviceRepository.getListComboTopSelling(search, customerTypeQuery, pageable, userLogin.getParentId());
            }
        } else {
            dtoPage = serviceRepository.getListComboTopSelling(search, customerTypeQuery, pageable, NON_USER_ID);
        }
        return convertComboPage(dtoPage);
    }

    @Override
    public Page<ServiceResponseTransDTO> getTopTotalAmountService(String search, CustomerTypeEnum customerTypeEnum, Long category,
        Set<ServiceProductTypeEnum> serviceProductTypes, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeValue = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        Set<Integer> setServiceProductType = serviceProductTypes.stream().map(ServiceProductTypeEnum::getValue).collect(Collectors.toSet());
        Page<BigInteger> lstPageId = serviceRepository.getPageServiceTopAmountId(search, category, customerTypeValue, setServiceProductType,
            pageable);
        List<ServiceResponseDTO> lstServiceDetail = serviceRepository.getListServiceAmountDetailByIdIn(lstPageId.stream()
            .map(BigInteger::longValue).collect(Collectors.toSet()), AuthUtil.getDefaultCurrentParentId());
        return convertServiceDetailPage(lstPageId.map(
            id -> lstServiceDetail.stream().filter(item -> Objects.equals(item.getId(), id.longValue())).findFirst().orElse(null)));
    }

    @Override
    public Page<ServiceResponseTransDTO> getTopTotalAmountCombo(String search, CustomerTypeEnum customerTypeEnum, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        Page<ServiceResponseDTO> dtoPage;
        String customerTypeQuery = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        if (userLogin != null) {
            if (userLogin.getParentId() == -1) {
                dtoPage = serviceRepository.getTopTotalAmountCombo(search, customerTypeQuery, pageable, userLogin.getId());
            } else {
                dtoPage = serviceRepository.getTopTotalAmountCombo(search, customerTypeQuery, pageable, userLogin.getParentId());
            }
        } else {
            dtoPage = serviceRepository.getTopTotalAmountCombo(search, customerTypeQuery, pageable, NON_USER_ID);
        }
        return convertComboPage(dtoPage);
    }

    @Override
    public Page<ServiceResponseTransDTO> getListNewService(String search, CustomerTypeEnum customerTypeEnum, Long category,
        Set<ServiceProductTypeEnum> serviceProductTypes, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeValue = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        Set<Integer> setServiceProductType = serviceProductTypes.stream().map(ServiceProductTypeEnum::getValue).collect(Collectors.toSet());
        return convertServiceEntityPage(serviceRepository.getPageServiceNewestId(search, category, customerTypeValue, setServiceProductType,
            pageable));
    }

    @Override
    public Page<ServiceResponseTransDTO> getListNewCombo(String search, CustomerTypeEnum customerTypeEnum, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        Page<ServiceResponseDTO> dtoPage;
        String customerTypeQuery = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        if (userLogin != null) {
            if (userLogin.getParentId() == -1) {
                dtoPage = serviceRepository.getListNewCombo(search, customerTypeQuery, pageable, userLogin.getId());
            } else {
                dtoPage = serviceRepository.getListNewCombo(search, customerTypeQuery, pageable, userLogin.getParentId());
            }
        } else {
            dtoPage = serviceRepository.getListNewCombo(search, customerTypeQuery, pageable, NON_USER_ID);
        }
        return convertComboPage(dtoPage);
    }

    @Override
    public Page<ServiceResponseTransDTO> getListServiceView(String search, CustomerTypeEnum customerTypeEnum, Pageable pageable, Long category,
        Set<ServiceProductTypeEnum> serviceProductTypes) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeValue = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        Set<Integer> setServiceProductType = serviceProductTypes.stream().map(ServiceProductTypeEnum::getValue).collect(Collectors.toSet());
        return convertServiceEntityPage(serviceRepository.getPageServiceViewId(search, category, customerTypeValue, setServiceProductType,
            pageable));
    }

    @Override
    public Page<ServiceResponseTransDTO> getListComboView(String search, CustomerTypeEnum customerTypeEnum, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        Page<ServiceResponseDTO> dtoPage;
        String customerTypeQuery = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        if (userLogin != null) {
            if (userLogin.getParentId() == -1) {
                dtoPage = serviceRepository.getListComboView(search, customerTypeQuery, pageable, userLogin.getId());
            } else {
                dtoPage = serviceRepository.getListComboView(search, customerTypeQuery, pageable, userLogin.getParentId());
            }
        } else {
            dtoPage = serviceRepository.getListComboView(search, customerTypeQuery, pageable, NON_USER_ID);
        }
        return convertComboPage(dtoPage);
    }

    @Override
    public Page<ServiceResponseTransDTO> getListServiceRating(String search, CustomerTypeEnum customerTypeEnum, Long category,
        Set<ServiceProductTypeEnum> serviceProductTypes, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        String customerTypeValue = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        Set<Integer> setServiceProductType = serviceProductTypes.stream().map(ServiceProductTypeEnum::getValue).collect(Collectors.toSet());
        return convertServiceEntityPage(serviceRepository.getPageServiceRatingId(search, category, customerTypeValue, setServiceProductType,
            pageable));
    }

    @Override
    public Page<ServiceResponseTransDTO> getListComboRating(String search, CustomerTypeEnum customerTypeEnum, Pageable pageable) {
        CustomUserDetails userLogin = AuthUtil.getCurrentUser();
        Page<ServiceResponseDTO> dtoPage;
        String customerTypeQuery = Objects.isNull(userLogin) ? customerTypeEnum.getValue() : userLogin.getCustomerType();
        if (userLogin != null) {
            if (userLogin.getParentId() == -1) {
                dtoPage = serviceRepository.getListComboRating(search, customerTypeQuery, pageable, userLogin.getId());
            } else {
                dtoPage = serviceRepository.getListComboRating(search, customerTypeQuery, pageable, userLogin.getParentId());
            }
        } else {
            dtoPage = serviceRepository.getListComboRating(search, customerTypeQuery, pageable, NON_USER_ID);
        }
        return convertComboPage(dtoPage);
    }

    /* ******************************************************************************************************************************************
     *
     * Custom field methods
     *
     *******************************************************************************************************************************************/

    /**
     * Tìm kiếm custom_layout từ layoutId
     */
    private CustomLayout getCustomLayout(Long layoutId) {
        layoutId = (layoutId != null) ? layoutId : customFieldRepository.findDefaultLayoutId(CustomFieldCategoryEnum.SERVICE.getValue());
        if (layoutId == null) return null;
        return customLayoutRepository.findById(layoutId).orElse(null);
    }

    /**
     * Lưu thông tin vào custom_field_value
     */
    private void saveCustomFieldValue(ServiceCreateDTO requestDto, Integer entityType, Long entityId) {
        CustomLayout customLayout = getCustomLayout(requestDto.getCreationLayoutId());
        if (customLayout == null || requestDto.getLstCustomField() == null) return;

        List<CustomFieldValue> lstFieldValue = new ArrayList<>();
        Set<String> lstFieldCode = customLayout.getLstCustomField();
        for (CustomFieldValueDTO valueDTO : requestDto.getLstCustomField()) {
            if (!lstFieldCode.contains(valueDTO.getFieldCode())) continue;
            CustomField customField = customFieldRepository.findByCode(valueDTO.getFieldCode()).orElse(null);
            if (customField == null) continue;

            try {
                CustomFieldValue fieldValue = new CustomFieldValue();
                //Thay th giá trị customFieldValue nếu là services
                if(Objects.equals(EntityTypeEnum.SERVICE.getValue(), entityType)) {
                    fieldValue = customFieldValueRepository.findByEntityIdAndEntityTypeAndFieldId(entityId, entityType, customField.getId()).orElse(new CustomFieldValue());
                }
                fieldValue.setEntityType(entityType);
                fieldValue.setEntityId(entityId);
                customFieldManager.getFieldValueFromDto(customField, valueDTO, fieldValue, entityId, null);
                lstFieldValue.add(fieldValue);
            } catch (InputMismatchException | NullPointerException | JsonProcessingException e) {
                log.error("saveCustomFieldValue: {}", e.getMessage(), e);
            }
        }
        customFieldValueRepository.saveAll(lstFieldValue);
    }

    /**
     * Cập nhật giá trị trong custom_field_draft_value
     */
    private void updateDraftCustomFieldValue(ServiceUpdateDTO requestDto, Integer entityType, Long entityDraftId) {
        CustomLayout customLayout = getCustomLayout(requestDto.getCreationLayoutId());
        if (customLayout == null || requestDto.getLstCustomField() == null) return;

        List<CustomFieldDraftValue> lstDraftValue = new ArrayList<>();
        Set<String> lstFieldCode = customLayout.getLstCustomField();
        for (CustomFieldValueDTO valueDTO : requestDto.getLstCustomField()) {
            if (!lstFieldCode.contains(valueDTO.getFieldCode())) continue;
            CustomField customField = customFieldRepository.findByCode(valueDTO.getFieldCode()).orElse(null);
            if (customField == null) continue;

            try {
                CustomFieldDraftValue draftValue = customFieldDraftValueRepository.findByFieldIdAndEntityDraftId(customField.getId(), entityDraftId)
                    .orElse(new CustomFieldDraftValue());
                draftValue.setEntityType(entityType);
                draftValue.setEntityDraftId(entityDraftId);
                customFieldManager.getFieldValueFromDto(customField, valueDTO, draftValue, requestDto.getId(), entityDraftId);
                lstDraftValue.add(draftValue);
            } catch (InputMismatchException | NullPointerException | JsonProcessingException e) {
                log.error("updateDraftCustomFieldValue: {}", e.getMessage(), e);
            }
        }
        customFieldDraftValueRepository.saveAll(lstDraftValue);
    }

    // cập nhật phiên bản mới nhất services
    @Override
    public void updateServiceDraftId() {
        List<UpdateServiceDraftIdDTO> listMaxVersion = serviceRepository.getAllServiceMaxVersion();
        List<Long> ids = listMaxVersion.stream().map(UpdateServiceDraftIdDTO::getServiceId).collect(Collectors.toList());
        List<ServiceEntity> listUpdate = serviceRepository.findAllByIdIn(ids);
        List<ServiceEntity> response = new ArrayList<>();
        Map<Long, UpdateServiceDraftIdDTO> map = listMaxVersion.stream().collect(Collectors.toMap(UpdateServiceDraftIdDTO::getServiceId, Function.identity()));
        for (ServiceEntity ser : listUpdate) {
            UpdateServiceDraftIdDTO item = map.get(ser.getId());
            if (Objects.nonNull(item) && !Objects.equals(ser.getServiceDraftId(), item.getMaxId())) {
                ser.setServiceDraftId(item.getMaxId());
                response.add(ser);
            }
        }
        serviceRepository.saveAll(response);
    }

    @Override
    public void updatePricingDefault() {
        List<IGetPricingDefaultDTO> list = pricingDraftRepository.getPricingDefault();
        List<Long> ids = list.stream().map(IGetPricingDefaultDTO::getServiceId).collect(Collectors.toList());
        List<ServiceEntity> listUpdate = serviceRepository.findAllByIdIn(ids);
        Map<Long, IGetPricingDefaultDTO> map = list.stream().collect(Collectors.toMap(IGetPricingDefaultDTO::getServiceId, Function.identity()));
        List<ServiceEntity> response = new ArrayList<>();
        for (ServiceEntity ser : listUpdate) {
            IGetPricingDefaultDTO item = map.get(ser.getId());
            if (Objects.nonNull(item) && !Objects.equals(ser.getPricingDefault(), item.getPricingDefaultId())) {
                ser.setPricingDefault(item.getPricingDefaultId());
                response.add(ser);
            }
        }
        serviceRepository.saveAll(response);
    }

    @Override
    public void cloneRoleDefaultApplication(ServiceEntity serviceEntity, String bearToken) {
        CreateWPRoleDTO request = new CreateWPRoleDTO();
        request.setIsClone(true);
        request.setServiceId(serviceEntity.getId());
        request.setPermissionCode(REGISTER);
        httpUtil.callRest(dxServiceWp + cloneRoleDefault, HttpMethod.POST, bearToken, request, Void.class);
    }

    @Override
    public List<CategoryServiceDTO> findAllByCategoryForSME(CustomerTypeEnum customerType, Integer top) {
        List<CategoryServiceDTO> results = new ArrayList<>();
        String customerTypeLogin =
            Objects.nonNull(AuthUtil.getCurrentUser()) ? AuthUtil.getCurrentUser().getCustomerType() : customerType.getValue();
        List<ICommonIdNameCreatedAt> lstCategorySort = serviceRepository.findAllCategoryOrderByModifiedAt(customerTypeLogin);
        Map<Long, ICommonIdNameCreatedAt> mapCategoryById = lstCategorySort.stream()
            .collect(Collectors.toMap(ICommonIdNameCreatedAt::getId, Function.identity()));

        List<IServiceCategoryDetailDTO> lstService = serviceRepository.findServiceByCategory(customerTypeLogin, top);
        Map<Long, List<ServiceCategoryDetailDTO>> mapServiceByCategoryId = lstService.stream()
            .map(ServiceCategoryDetailDTO::new)
            .collect(Collectors.groupingBy(ServiceCategoryDetailDTO::getCategoryId));
        for (Map.Entry<Long, ICommonIdNameCreatedAt> entry : mapCategoryById.entrySet()) {
            CategoryServiceDTO categoryServiceDTO = new CategoryServiceDTO();
            categoryServiceDTO.setCategoryId(entry.getValue().getId());
            categoryServiceDTO.setCategoryName(entry.getValue().getName());
            categoryServiceDTO.setCreatedAt(entry.getValue().getCreatedAt());
            if (mapServiceByCategoryId.containsKey(entry.getKey())) {
                categoryServiceDTO.setLstService(mapServiceByCategoryId.get(entry.getKey()));
            }
            results.add(categoryServiceDTO);
        }
        return results.stream().sorted(Comparator.comparing(CategoryServiceDTO::getCreatedAt).reversed()).collect(Collectors.toList());
    }

    @Override
    public BigDecimal getServicePreTaxAmount(Long serviceId) {
        var service = findByIdAndDeletedFlag(serviceId, DeletedFlag.NOT_YET_DELETED.getValue());
        var price = service.getPrice();
        // Tính toán giá trước thuế
        var taxInfo = service.getTax();
        if (taxInfo != null && taxInfo.getHasTax() == ServiceStatusEnum.VISIBLE) {
            var totalIncludedTax = BigDecimal.valueOf(100);
            for (var tax : taxInfo.getTaxList()) {
                totalIncludedTax = totalIncludedTax.add(tax.getValue());
            }
            price = price.multiply(BigDecimal.valueOf(100)).divide(totalIncludedTax, RoundingMode.HALF_UP);
        }
        return price;
    }

    @Override
    public Page<IHomepageSearchBarResDTO> searchHomepage(String keywordSearch, Long providerId, CustomerTypeEnum customerType,
        ServiceProductTypeEnum productType, Pageable pageable) {
        // lấy thông tin customerType của user nếu đăng nhập
        CustomerTypeEnum selectedCustomerType = AuthUtil.getCurrentSmeCustomerTypeWithDefault(
            Objects.equals(customerType, CustomerTypeEnum.UNSET) ? CustomerTypeEnum.ALL : customerType);
        // query toàn bộ kết quả trùng khớp
        List<IHomepageSearchBarResDTO> res = getHomePageSearchResults(keywordSearch, providerId, selectedCustomerType, productType);
        // return page
        final int start = Math.min((int) pageable.getOffset(), res.size());
        final int end = Math.min((start + pageable.getPageSize()), res.size());
        return new PageImpl<>(res.subList(start, end), pageable, res.size());
    }

    private List<IHomepageSearchBarResDTO> getHomePageSearchResults(String keywordSearch, Long providerId, CustomerTypeEnum customerType,
        ServiceProductTypeEnum productType) {
        final String[] SERVICE_TYPES = {"SERVICE", "PRICING"};
        final String[] COMBO_TYPES = {"COMBO", "COMBO_PLAN"};
        final String[] SERVICE_GROUP_TYPES = {"SERVICE_GROUP"};
        final String[] SOLUTION_PACKAGE_TYPES = {"SOLUTION", "PACKAGE"};
        // Check cấu hình tìm kiếm sản phẩm dịch vụ (1: chính xác, 2: gần đúng) (mặc định nếu ko có cấu hình => 2)
        SystemParam searchConfig = systemParamService.findByParamType(SystemParamConstant.SEARCH_EXACTLY);
        Integer searchExactly = Objects.nonNull(searchConfig) && Objects.nonNull(searchConfig.getParamSearchExactly()) ?
            searchConfig.getParamSearchExactly() : 2;

        List<IHomepageSearchBarResDTO> iSearchHomepageResponseDTOS = serviceRepository.searchUnifiedHomePage(
            SqlUtils.optimizeSearchLike(keywordSearch), providerId, customerType.getValue(),
            Objects.equals(productType, ServiceProductTypeEnum.UNSET) ? -1 : productType.getValue(), searchExactly);

        // phân loại item
        List<IHomepageSearchBarResDTO> res = new ArrayList<>();
        res.addAll(filterAndDistinct(iSearchHomepageResponseDTOS, SERVICE_TYPES));
        res.addAll(filterAndDistinct(iSearchHomepageResponseDTOS, COMBO_TYPES));
        res.addAll(filterAndDistinct(iSearchHomepageResponseDTOS, SERVICE_GROUP_TYPES));
        res.addAll(filterAndDistinct(iSearchHomepageResponseDTOS, SOLUTION_PACKAGE_TYPES));
        res.sort(Comparator.comparing(IHomepageSearchBarResDTO::getSearchPoint).reversed()); // searchPoint desc
        return res;
    }

    // Xử lý trùng lặp và lọc theo loại
    private List<IHomepageSearchBarResDTO> filterAndDistinct(List<IHomepageSearchBarResDTO> list, String[] types) {
        return list.stream()
                .filter(item -> Arrays.asList(types).contains(item.getType()))
                .filter(CustomPredicateUtils.distinctByKeys(IHomepageSearchBarResDTO::getServiceId, IHomepageSearchBarResDTO::getPricingId))
                .collect(Collectors.toList());
    }

    @Override
    public Page<ServiceResponseTransDTO> getPageSearchedServices(String keyword, Long providerId, CustomerTypeEnum customerType,
        ServiceProductTypeEnum productType, Pageable pageable) {
        Long currentUserId = ObjectUtil.getOrDefault(AuthUtil.getCurrentUserId(), -1L);
        // lấy thông tin customerType của user nếu đăng nhập
        CustomerTypeEnum selectedCustomerType = AuthUtil.getCurrentSmeCustomerTypeWithDefault(
            Objects.equals(customerType, CustomerTypeEnum.UNSET) ? CustomerTypeEnum.ALL : customerType);
        // lấy các id của service thông qua unified search và lọc các kết quả có type là 'SERVICE'
        Set<Long> allResultServiceIds = new LinkedHashSet<>();
        getHomePageSearchResults(keyword, providerId, selectedCustomerType, productType)
            .stream().filter(item -> Objects.equals(item.getType(), "SERVICE")).forEach(item -> allResultServiceIds.add(item.getServiceId()));
        BigDecimal[] serviceIdsArr = allResultServiceIds.stream().map(BigDecimal::valueOf).toArray(BigDecimal[]::new);
        Page<ServiceResponseDTO> responses = serviceRepository.getPageSearchedServices(
            ObjectUtils.isNotEmpty(serviceIdsArr) ? serviceIdsArr : new BigDecimal[]{BigDecimal.valueOf(-1L)}, currentUserId,
            selectedCustomerType.getValue(), Collections.singleton(productType.getValue()), pageable);
        return convertServiceDetailPage(responses);
    }

    @Override
    public Page<ServiceResponseTransDTO> getPageSearchedCombo(String keyword, Long providerId, CustomerTypeEnum customerType, Pageable pageable) {
        // lấy serviceUniqueIds -> combo = id * 10000 + 1, service_group = id * 10000 + 2
        final String COMBO_TYPE = "COMBO";
        final String SERVICE_GROUP_TYPE = "SERVICE_GROUP";
        // lấy thông tin customerType của user nếu đăng nhập
        CustomerTypeEnum selectedCustomerType = AuthUtil.getCurrentSmeCustomerTypeWithDefault(
            Objects.equals(customerType, CustomerTypeEnum.UNSET) ? CustomerTypeEnum.ALL : customerType);
        Set<Long> allServiceUniqueIds = new LinkedHashSet<>();
        getHomePageSearchResults(keyword, providerId, selectedCustomerType, ServiceProductTypeEnum.UNSET)
            .stream().filter(item -> Objects.equals(item.getType(), COMBO_TYPE) || Objects.equals(item.getType(), SERVICE_GROUP_TYPE))
            .forEach(item -> allServiceUniqueIds.add(
                Objects.equals(item.getType(), COMBO_TYPE) ? (item.getServiceId() * 10000L + 1L) : item.getServiceId() * 10000L + 2L));
        // lấy page combo/service_group theo uniqueIds
        BigDecimal[] serviceUniqueIdsArr = allServiceUniqueIds.stream().map(BigDecimal::valueOf).toArray(BigDecimal[]::new);
        Page<ServiceResponseDTO> responses = serviceRepository.getPageSearchedComboAndServiceGroup(
            ObjectUtils.isNotEmpty(serviceUniqueIdsArr) ? serviceUniqueIdsArr : new BigDecimal[]{BigDecimal.valueOf(-1L)},
            ObjectUtil.getOrDefault(AuthUtil.getCurrentUserId(), -1L), selectedCustomerType.getValue(), pageable);
        return convertComboPage(responses);
    }

    @Override
    public Page<PricingSaaSResDTO> getPageSearchedPricing(String keyword, Long providerId, CustomerTypeEnum customerType,
        ServiceProductTypeEnum productType, Pageable pageable) {
        // lấy thông tin customerType của user nếu đăng nhập
        CustomerTypeEnum selectedCustomerType = AuthUtil.getCurrentSmeCustomerTypeWithDefault(
            Objects.equals(customerType, CustomerTypeEnum.UNSET) ? CustomerTypeEnum.ALL : customerType);
        // lấy các id của service thông qua unified search và lọc các kết quả có type là 'PRICING'
        Set<Long> allResultPricingIds = new LinkedHashSet<>();
        getHomePageSearchResults(keyword, providerId, selectedCustomerType, productType)
            .stream().filter(item -> Objects.equals(item.getType(), "PRICING")).forEach(item -> allResultPricingIds.add(item.getPricingId()));
        BigDecimal[] pricingIdsArr = allResultPricingIds.stream().map(BigDecimal::valueOf).toArray(BigDecimal[]::new);
        Page<IPricingSaaSResDTO> pagePricing = serviceRepository.getPageSearchedPricing(
            ObjectUtils.isNotEmpty(pricingIdsArr) ? pricingIdsArr : new BigDecimal[]{BigDecimal.valueOf(-1L)},
            ObjectUtil.getOrDefault(AuthUtil.getCurrentUserId(), -1L), selectedCustomerType.getValue(), pageable);
        // return page
        return pagePricing.map(item -> pricingService.convertPricingSaaSResponse(item));
    }

    @Override
    public Page<IProviderResDTO> getPageSearchedProviders(String keyword, Pageable pageable) {
        return serviceRepository.getPageSearchedProviders(keyword, pageable);
    }

    @Override
    public Page<ServiceCommonDetailDTO> getPageSearchedSolutionBundling(String keyword, Long providerId, CustomerTypeEnum customerType, Pageable pageable) {
        // lấy thông tin customerType của user nếu đăng nhập
        CustomerTypeEnum selectedCustomerType = AuthUtil.getCurrentSmeCustomerTypeWithDefault(
            Objects.equals(customerType, CustomerTypeEnum.UNSET) ? CustomerTypeEnum.ALL : customerType);
        // lấy các id của package thông qua unified search và lọc các kết quả có type là 'SOLUTION'/'PACKAGE'
        List<IHomepageSearchBarResDTO> searchResults = getHomePageSearchResults(keyword, providerId, selectedCustomerType,
            ServiceProductTypeEnum.UNSET);

        // Tổng hợp -> select trong query để paging và sort
        Set<Long> solutionPackageUnqIds = new LinkedHashSet<>();
        searchResults.forEach(item -> {
            // trường hợp có solution (có/không có package) -> id = solution_id + 0005
            if (Objects.equals(item.getType(), ObjectTypeEnum.SOLUTION.name()) && Objects.nonNull(item.getServiceId())) {
                // service_id = solution_id(serviceId) + 0005
                solutionPackageUnqIds.add(item.getServiceId() * 10000L + 5L);
            } else if (Objects.equals(item.getType(), ObjectTypeEnum.PACKAGE.name()) && Objects.isNull(item.getServiceId()) &&
                Objects.nonNull(item.getPricingId())) { // package không có solution id = package_id + 0006
                // service_id = package_id(pricingId) + 0006
                solutionPackageUnqIds.add(item.getPricingId() * 10000L + 6L);
            }
        });
        BigDecimal[] solutionPackageUnqIdsArr = solutionPackageUnqIds.stream().map(BigDecimal::valueOf).toArray(BigDecimal[]::new);
        Page<ServiceSuggestionDTO> pageBaseDataResponse = productSolutionRepository.getPageCombinedSolutionPackageByUniqueIds(
            solutionPackageUnqIdsArr, pageable);

        return pageBaseDataResponse.map(item -> {
            ServiceMapperContext serviceMapperContext = new ServiceMapperContext(AuthUtil.getCurrentUserId(), selectedCustomerType,
                ServiceOperationTypeEnum.ALL, item, null);
            com.service.serviceSuggestion.ServiceMapper serviceMapper = serviceHandlerFactory.getHandler(item.getSuggestionType());
            if (Objects.nonNull(serviceMapper)) {
                serviceMapperContext.setServiceSuggestionDTO(item);
                return serviceMapper.mapDetail(serviceMapperContext);
            }
            return new ServiceCommonDetailDTO(item);
        });
    }

    @Transactional(readOnly = true)
    @Override
    public Page<ServiceResponseTransDTO> getListFavoriteService(String search, Set<ServiceProductTypeEnum> serviceProductTypes, CustomerTypeEnum customerType,
        Pageable pageable) {
        Long currentUserId = ObjectUtil.getOrDefault(AuthUtil.getCurrentUserId(), -1L);
        Set<Integer> serviceProductTypeInts = serviceProductTypes.stream().map(ServiceProductTypeEnum::getValue).collect(Collectors.toSet());
        Page<ServiceResponseDTO> responses = serviceRepository.getListFavoriteService(SqlUtils.optimizeSearchLike(search), currentUserId,
            customerType.getValue(), serviceProductTypeInts, pageable);
        return convertServiceDetailPage(responses);
    }

    // ham lay default Pricing mobile
    public PricingDetailRespDTO getPricingDefaultMobile(ServiceResponseDTO serviceRes, Set<CustomerTypeEnum> lstCustomerType) {
        Long pricingDraftId = serviceRes.getDefaultPricingDraftId();
        Pricing pr = pricingRepository.findFirstByPricingDraftIdAndDeletedFlagAndApproveAndStatusOrderByIdDesc(
            pricingDraftId, DeletedFlag.NOT_YET_DELETED.getValue(), ApproveStatusEnum.APPROVED.value, StatusEnum.ACTIVE.value);
        // Nếu pricing null thì return
        if (Objects.isNull(pr)) {
            return null;
        }
        Set<CustomerTypeEnum> customerTypeEnumSet = CollectionUtils.isEmpty(pr.getCustomerTypeCode()) ? new HashSet<>() :
            pr.getCustomerTypeCode().stream().map(CustomerTypeEnum::getValueOf).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(customerTypeEnumSet) || lstCustomerType.stream().anyMatch(customerTypeEnumSet::contains) ||
            lstCustomerType.contains(CustomerTypeEnum.UNSET)) {
            PricingDetailRespDTO pricingDefaultDTO = new PricingDetailRespDTO();
            pricingDefaultDTO.setPricingName(pr.getPricingName());
            // bestDeal: KM giá tốt nhất của gói mặc định
            List<PricingPlanDetailRespDTO> multiplePeriods = subMultiplePeriod.getPlanPeriodByPricingId(pr.getId(), pr.getIsOneTime(),
                lstCustomerType, serviceRes.getBestDealPromotionType(),
                serviceRes.getBestDealDiscountType(), serviceRes.getBestDealDiscountValue());
            List<PricingPlanDetailRespDTO> multiplePeriodResponse = multiplePeriods
                .stream()
                .filter(mul -> (mul.getCustomerTypeCode() == null || lstCustomerType.stream().anyMatch(mul.getCustomerTypeCode()::contains)))
                .collect(Collectors.toList());
            // Nếu không có thông tin về period -> data cũ, lấy theo cách cũ
            if (CollectionUtils.isEmpty(multiplePeriodResponse)) {
                // Số tiền trước thuế
                List<PricingTaxRes> taxes = pricingTaxRepository.getPricingTax(pr.getId());
                BigDecimal pricingPreTax = subscriptionFormula.priceBeforeTax(pr.getPrice(), taxes);
                pricingDefaultDTO.setPrice(pricingPreTax);
                pricingDefaultDTO.setPriceValue(pr.getPrice());
                pricingDefaultDTO.setPricingPlan(PricingPlanEnum.valueOf(pr.getPricingPlan()));
                pricingDefaultDTO.setPaymentCycle(pr.getPaymentCycle());
                pricingDefaultDTO.setCycleType(LimitedTypeEnum.valueOf(pr.getCycleType()));
                // Danh sách chu kỳ định giá
                pricingDefaultDTO.setUnitLimitedList(pricingService.getUnitLimitedByPricingId(pr.getId()));
            }

            // Nếu có period nhưng không thuộc kiểu người dùng
            if (!CollectionUtils.isEmpty(multiplePeriods) && CollectionUtils.isEmpty(multiplePeriodResponse)) {
                return null;
            } else {
                pricingDefaultDTO.setPricingMultiplePeriods(multiplePeriodResponse);
                pricingDefaultDTO.setPricingDraftId(pr.getPricingDraftId());
                pricingDefaultDTO.setPricingId(pr.getId());
                return pricingDefaultDTO;
            }
        }
        return null;
    }

    @Override
    public boolean allowAddToCart(Long serviceId, Long comboId) {
        Long userId = AuthUtil.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return true;
        }
        if (Objects.nonNull(serviceId) && Objects.isNull(comboId)) {
            return !serviceRepository.existsServiceDisableMultiSubAndHasSub(serviceId, userId);
        }
        if (Objects.nonNull(comboId) && Objects.isNull(serviceId)) {
            return !serviceRepository.existsComboDisableMultiSubAndHasSub(comboId, userId);
        }
        return false;
    }

    @Override
    public Page<IProviderResDTO> getAllProviderNameAndNumService(Pageable pageable){
        return serviceRepository.getAllProviderNameAndNumService(pageable);
    }

    @Override
    public Page<ProductSolutionSmeDTO> getMostViewedSolutions(Pageable pageable, CustomerTypeEnum customerType) {
        List<IProductSolutionSmeDTO> listProductSolution = productSolutionRepository.getListLatestProductSolutionView(customerType.name(), 6)
            .stream()
            .sorted(Comparator.comparing(IProductSolutionSmeDTO::getViewCount).reversed())
            .collect(Collectors.toList());

        List<ProductSolutionSmeDTO> lstSolutionView = getProductSolutionSmeDTO(listProductSolution);

        final int start = Math.min((int) pageable.getOffset(), lstSolutionView.size());
        final int end = Math.min((start + pageable.getPageSize()), lstSolutionView.size());
        return new PageImpl<>(lstSolutionView.subList(start, end), pageable, lstSolutionView.size());
    }

    public List<ProductSolutionSmeDTO> getProductSolutionSmeDTO(List<IProductSolutionSmeDTO> listProductSolution) {
        // Lấy danh sách packageDefaultId
        List<Long> listPackageDefaultId = listProductSolution.stream()
            .map(IProductSolutionSmeDTO::getPackageDefaultId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // Lấy danh sách PackageItem theo packageId
        List<PackageItem> lstPackage = packageItemRepository.getListPackageItemByPackageIdIn(listPackageDefaultId).stream()
            .map(PackageItem::new)
            .collect(Collectors.toList());

        // Map theo packageId
        Map<Long, List<PackageItem>> packageItemMap = lstPackage.stream()
            .filter(item -> item.getPackageId() != null)
            .collect(Collectors.groupingBy(PackageItem::getPackageId));

        // Mapping kết quả
        return listProductSolution.stream()
            .map(item -> {
                ProductSolutionSmeDTO dto = new ProductSolutionSmeDTO(item);
                dto.setLstPackageItem(packageItemMap.get(dto.getPackageDefaultId()));
                return dto;
            })
            .collect(Collectors.toList());
    }


    private User findUserById(Long userId) {
        return userRepository.findByIdAndDeletedFlag(userId, DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.USER, ErrorKey.ID, String.valueOf(userId)));
    }

    private ServiceEntity findServiceById(Long serviceId) {
        return serviceRepository.findByIdAndDeletedFlag(serviceId, DeletedFlag.NOT_YET_DELETED.getValue())
            .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.SERVICES, ErrorKey.ID, String.valueOf(serviceId)));
    }

    @Override
    public Boolean validateProductSku(String skuString, Long serviceId) {
        return !serviceRepository.existsBySkuAndDeletedFlagAndIdNot(skuString,1,serviceId);
    }
}

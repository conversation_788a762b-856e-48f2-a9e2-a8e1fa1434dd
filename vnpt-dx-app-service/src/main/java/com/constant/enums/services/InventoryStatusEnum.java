package com.constant.enums.services;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InventoryStatusEnum {
    UNSET(-1), OUT_OF_STOCK(0), IN_STOCK(1), INCOMING_STOCK(2);
    private final int value;

    public static InventoryStatusEnum fromValue(Integer value) {
        if (Objects.isNull(value)) {
            return UNSET;
        }
        switch(value) {
            case 0:
                return OUT_OF_STOCK;
            case 1:
                return IN_STOCK;
            case 2:
                return INCOMING_STOCK;
            default:
                return UNSET;
        }
    }
}
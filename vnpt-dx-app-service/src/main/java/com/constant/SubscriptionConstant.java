package com.constant;

import java.time.LocalDate;
import com.onedx.common.constants.enums.theme.MailActionTypeEnum;

public class SubscriptionConstant {

    public static final String ID = "id";
    public static final Long DEFAULT_SUCCESS_ID = 0L;
    public static final String QUANTITY_UNLIMITED = "UNLIMITED";
    public static final String ACCESS_TRADE = "accesstrade";
    public static final String APINFO = "apinfo";
    public static final String AFFILIATES = "affiliates";
    public static final Integer INSTALLED = 1;
    public static final String INIT_SUCCESS = "00";
    public static final Long PARENT_ID = -1L;
    public static final Integer UNLIMITED = -1;
    public static final Long ADMIN = 0L;
    public static final String COMPANY_NAME_NULL = "";
    public static final String SHEET = "Danh sách Subscription";
    public static final String[] HEADERs = {"Thông tin gói dịch vụ", "Thông tin khách hàng", "<PERSON>r<PERSON><PERSON> thái", "<PERSON><PERSON><PERSON> tạo", "<PERSON> kì thanh toán",
        "<PERSON><PERSON> tiền", "Chu kì thanh toán tiếp theo"};
    public static final String TITLE = "Danh sách Subscription";
    public static final String SHEET_COMBO = "Danh sách Subscription Combo";
    public static final String[] HEADERs_COMBO = {"Thông tin gói dịch vụ", "Nhà cung cấp", "Thông tin khách hàng", "Trạng thái", "Ngày tạo",
        "Chu kì thanh toán", "Số tiền", "Chu kì thanh toán tiếp theo"};
    public static final String TITLE_COMBO = "Danh sách Subscription Combo";
    public static final String BATCH = "batch";
    public static final String SME = "SME";
    public static final String SME_PORTAL = "SME";
    public static final String DEV_PORTAL = "DEV";
    public static final String ADMIN_PORTAL = "ADMIN";
    public static final int CYCLE_QUANTITY_MAX = 999;
    public static final int CYCLE_QUANTITY_MIN = 1;
    public static final Integer SUBSCRIPTION_PRICING = 0;
    public static final Integer SUBSCRIPTION_COMBO_PLAN = 1;
    public static final Integer UNLIMITED_CYCLES = -1;
    public static final Long HAS_OLD_SUBSCRIPTION = -1L;
    public static final Integer NO_HAS_TAX = 0;
    public static final Long IS_ADMIN = -1L;
    public static final Long GUEST_USER = -1L;
    public static final LocalDate LOCAL_DATE_MIN_DATE = LocalDate.of(1970, 1, 1);
    public static final LocalDate LOCAL_DATE_MAX_DATE = LocalDate.of(3000, 1, 1);
    public static final Long IS_ADMIN_OR_DEV = 1L;
    public static final Long IS_SME = -1L;
    public static final String COUPON_TOTAL_BILL = "tổng hóa đơn";
    public static final String KHDN_DEPARTMENT = "KHDN";
    public static final Integer REGISTER = 1;
    public static final Integer NEW_CYCLE = 2;
    public static final Integer SWAP = 3;
    public static final Integer RE_ACTIVATE = 4;
    public static final Integer UPDATE_SUB = 5; // chỉnh sửa thuê bao
    public static final Integer DETAIL_SUB = 6; // Chi tiết thuê bao
    public static final Integer RENEW_SUB = 7; // Gia hạn thuê bao
    public static final Integer GET_CART = 99; // Giỏ hàng

    public static final Integer IS_ONE_TIME = 0;
    public static final String MODAL_RE_ACTIVE = "TYPE=REACTIVE";

    public static final String LINK_ADMIN_SUBS_SERVICE_DETAIL = "%s/admin-portal/subscription/service/%d";
    public static final String LINK_ADMIN_SUBS_COMBO_DETAIL = "%s/admin-portal/subscription/combo/%d";
    public static final String LINK_DEV_SUBS_SERVICE_DETAIL = "%s/dev-portal/subscription/service/%d";
    public static final String LINK_DEV_SUBS_COMBO_DETAIL = "%s/dev-portal/subscription/combo/%d";
    public static final String LINK_SME_SUBS_SERVICE_DETAIL = "%s/sme-portal/account/subscription/%d/detail";
    public static final String LINK_SME_RE_ACTIVE_SERVICE = "%s/mail-action?subId=%s&type=" + MailActionTypeEnum.SUB_REACTIVE.getValue();
    public static final String LINK_DEV_RE_ACTIVE_SERVICE = "%s/dev-portal/subscription/service/%d%s";
    public static final String LINK_ADMIN_RE_ACTIVE_SERVICE = "%s/admin-portal/subscription/service/%d%s";
    public static final String LINK_SME_EXTEND_SERVICE = "%s/mail-action?subId=%s&type=" + MailActionTypeEnum.SUB_EXTEND.getValue();
    public static final String LINK_ADMIN_EXTEND_SERVICE = "%s/admin-portal/subscription/service/%s?clickExtendButton=true";
    public static final String LINK_DEV_EXTEND_SERVICE = "%s/dev-portal/subscription/service/%s?clickExtendButton=true";
    public static final String LINK_SME_SUBS_COMBO_DETAIL = "%s/sme-portal/account/combo/%d/detail";
    public static final String LINK_SME_RE_ACTIVE_COMBO = "%s/sme-portal/account/combo/%d/detail%s";
    public static final String LINK_ADMIN_TRANSACTION_DETAIL = "%s/admin-portal/transaction-logs/%d/detail-transaction";
    public static final String STR_TIME_EXTEND_OR_REACTIVE = "Sau %S ngày sẽ không thể gia hạn lại thuê bao và xóa hoàn toàn dữ liệu của khách hàng.";

    public static class MailParams {
        public static final String SME_DEV_OR_ADMIN = "$SME_DEV_OR_ADMIN";
        public static final String SME_OR_DEV = "$SME_OR_DEV";
        public static final String NAME_SERVICE = "$NAME_SERVICE";
        public static final String NAME_PRICING = "$NAME_PRICING";
        public static final String NAME_SERVICE_AND_PRICING = "$NAME_SERVICE_AND_PRICING";
        public static final String NAME_COMPANY = "$NAME_COMPANY";
        public static final String TAG_NAME_COMPANY = "$TAG_NAME_COMPANY";
        public static final String CODE_TRANSACTION = "$CODE_TRANSACTION";
        public static final String SUBSCRIPTION_CODE = "$SUBSCRIPTION_CODE";
        public static final String NAME_REGISTER = "$NAME_REGISTER";
        public static final String POSITION = "$POSITION";
        public static final String DATE_SUBSCRIPTION = "$DATE_SUBSCRIPTION";
        public static final String DATE_START = "$DATE_START";
        public static final String DATE_END = "$DATE_END";
        public static final String NUM_OF_CYCLE = "$NUM_OF_CYCLE";
        public static final String CYCLE = "$CYCLE";
        public static final String AMOUNT = "$AMOUNT";
        public static final String PAYMENT_STATUS = "$PAYMENT_STATUS";
        public static final String LINK_DETAIL = "$LINK_DETAIL";
        public static final String LINK_USE_SUBS = "$LINK_USE_SUBS";
        public static final String USER = "$USER";
        public static final String HOTLINE = "$HOTLINE_TOANQUOC";
        public static final String CODE_INVOICE = "$CODE_INVOICE";
        public static final String TOTAL_AMOUNT = "$TOTAL_AMOUNT";
        public static final String REMARKS = "$REMARKS";
        public static final String RE_ACTIVE_SUB = "RE_ACTIVE_SUB";

        public static final String ADMIN = "Quản trị viên";
        public static final String DEV = "Nhà phát triển";
        public static final String PROVIDER = "Nhà cung cấp dịch vụ";
        public static final String SME = "Doanh nghiệp";
        public static final String SME_YOU = "Doanh nghiệp của bạn";
        public static final String PERSONAL = "Khách hàng";
        public static final String YOU = "Bạn";

        public static final String YEAR = "Năm";
        public static final String MONTH = "Tháng";
        public static final String WEEK = "Tuần";
        public static final String DAY = "Ngày";
        public static final String ADMIN_OR_DEV = "Dịch vụ của nhà cung cấp:";
        public static final String QUANTITY_UNLIMITED = "Không giới hạn";
        public static final String EXPIRED_TIME_UNLIMITED = "Sử dụng vĩnh viễn";
        public static final String CYCLE_ONE_TIME = "Thanh toán một lần";
        public static final String AMOUNT_CURRENCY = " VNĐ";

        public static final String BILL_STATUS_INIT = "Khởi tạo";
        public static final String BILL_STATUS_WAITING = "Chờ thanh toán";
        public static final String BILL_STATUS_PAID = "Đã thanh toán";

        public static final String ADMIN_PORTAL = "admin";
        public static final String DEV_PORTAL = "dev";

        public static final String WEB_HOST_SUBS_SERVICE_DETAIL = "web.host.subs.detail";
        public static final String WEB_HOST_SUBS_SERVICE_DETAIL_SME = "web.host.subs.service.detail.sme.portal";
        public static final String WEB_HOST_SUBS_COMBO_DETAIL_SME = "web.host.subs.combo.detail.sme.portal";
        public static final String WEB_HOST_CART_SERVICE_DETAIL = "web.host.cart.detail";

        public static final String WEB_HOST_SERVICE = "service";
        public static final String WEB_HOST_SERVICE_ORDER = "order-service";
        public static final String WEB_HOST_COMBO = "combo";
        public static final String SERVICE_ORDER_CODE = "/order";
    }

    //Mail param
    public static class MailParam {

        public static final String TIME_EXTEND_OR_REACTIVE = "$TIME_EXTEND_OR_REACTIVE";
        public static final String HOTLINE = "$HOTLINE_TOANQUOC";
        public static final String SME_DEV_OR_ADMIN = "$SME_DEV_OR_ADMIN";
        public static final String NAME_SERVICE = "$NAME_SERVICE";
        public static final String NAME_PRICING = "$NAME_PRICING";
        public static final String NAME_COMPANY = "$NAME_COMPANY";
        public static final String NAME = "$NAME";
        public static final String NAME_SME_DEVELOPER = "$NAME_SME_DEVELOPER";
        public static final String NAME_REGISTER = "$NAME_REGISTER";
        public static final String POSITION = "$POSITION";
        public static final String DATE_SUBSCRIPTION = "$DATE_SUBSCRIPTION";
        public static final String DATE_START_TRIAL = "$DATE_START_TRIAL";
        public static final String DATE_END_TRIAL = "$DATE_END_TRIAL";
        public static final String LINK_DETAIL = "$LINK_DETAIL";
        public static final String LINK_RENEWING = "$LINK_RENEWING";
        public static final String LINK_RE_ACTIVE = "$LINK_RE_ACTIVE";
        public static final String USER = "$USER";
        public static final String TAG_NAME_COMPANY = "$TAG_NAME_COMPANY";
        public static final String CUSTOMER_COMPANY_NAME = "$CUSTOMER_COMPANY_NAME";
        public static final String NAME_PERIOD = "$NAME_PERIOD";
        public static final String LINK_INCIDENT = "$LINK_INCIDENT";
        public static final String NAME_SERVICE1 = "$NAME_SERVICE1";
        public static final String NAME_PRICING1 = "$NAME_PRICING1";
        public static final String NAME_PERIOD1 = "$NAME_PERIOD1";

        // Mail param sme subscription
        public static final String DATE_MODIFIED = "$DATE_MODIFIED";
        public static final String DATE_APPLY = "$DATE_APPLY";
        public static final String LINK_USE_SUBS = "$LINK_USE_SUBS";
        public static final String CODE_TRANSACTION = "$CODE_TRANSACTION";
        public static final String SOURCE_CANCELED = "$SOURCE_CANCELED";
        public static final String SME_NAME = "$SME_NAME";
        public static final String PROVIDER = "$PROVIDER";
        public static final String IS_ADMIN = "$IS_ADMIN";
        public static final String PROVIDER_OR_NOT = "$PROVIDER_OR_NOT";
        public static final String HOTLINE_TINH = "$HOTLINE_TINH";
        public static final String NAME_MODIFIED = "$NAME_MODIFIED";
        public static final String REMAIN_DAYS = "$REMAIN_DAYS";
        public static final String DATE_CHANGED_PRICING = "$DATE_CHANGED_PRICING";
        public static final String NAME_EXTEND = "$NAME_EXTEND";
        public static final String ROLE_EXTEND = "$ROLE_EXTEND";
        public static final String DATE_EXTEND = "$DATE_EXTEND";
        public static final String NAME_CANCELED = "$NAME_CANCELED";
        public static final String DATE_CANCELED = "$DATE_CANCELED";
        public static final String DATE_APPLY_CANCELED = "$DATE_APPLY_CANCELED";

        public static final String NAME_REACTIVE = "$NAME_REACTIVE";
        public static final String DATE_REACTIVE = "$DATE_REACTIVE";
        public static final String DATE_START_REACTIVE = "$DATE_START_REACTIVE";
        public static final String DATE_END_REACTIVE = "$DATE_END_REACTIVE";
        public static final String NUMBER_OF_CYCLE = "$NUMBER_OF_CYCLE";
        public static final String CURRENT_CYCLE = "$CURRENT_CYCLE";
        public static final String AMOUNT = "$AMOUNT";
        public static final String SOURCE_REACTIVE = "$SOURCE_REACTIVE";
        public static final String SUB_CODE = "$SUB_CODE";
        public static final String TOTAL_AMOUNT = "$TOTAL_AMOUNT";
        public static final String NOTE = "$NOTE";

        public static final String CONTENT_NAME_PRICING = "$CONTENT_UPDATE";
        public static final String NAME_PRICING_NEW = "$NAME_PRICING_NEW";
        public static final String NAME_PRICING_OLD = "$NAME_PRICING_OLD";
        public static final String NAME_CHANGED_PRICING = "$NAME_CHANGED_PRICING";
        public static final String NAME_COMBO = "$NAME_COMBO";
        public static final String NAME_COMBO_PRICING = "$NAME_COMBO_PRICING";

    }

    public static class UpdateContentTemplate {
        public static final String CONTENT_NAME_PRICING = "%s. Đổi gói dịch vụ: Từ gói %s sang gói %s.";
        public static final String CONTENT_QUANTITY_PRICING = "%s. Thay đổi số lượng gói dịch vụ: Từ %s sang %s.";
        public static final String CONTENT_QUANTITY_ADDON = "%s. Thay đổi số lượng dịch vụ bổ sung %s: Từ %s sang %s.";
        public static final String CONTENT_PRICE_PRICING = "%s. Thay đổi giá của gói dịch vụ: Từ %s sang %s.";
        public static final String CONTENT_PRICE_ADDON = "%s. Thay đổi giá của dịch vụ bổ sung %s: Từ %s sang %s.";
        public static final String CONTENT_NAME_ADDON_REMOVE = "%s. Xóa dịch vụ bổ sung: %s.";
        public static final String CONTENT_NAME_ADDON_ADDED = "%s. Thêm mới dịch vụ bổ sung: %s.";
    }

    public static class VNPTPayQRResponseCode {
        public static final String SUCCESS = "00";
        public static final String FAIL = "01";
        public static final String DATA_WRONG = "02";
        public static final String NO_DATA_FOUND = "05";
        public static final String SYSTEM_ERROR = "06";
        public static final String INCORRECT_KEY = "07";
        public static final String MERCHANT_ACCOUNT_LOCKED = "08";
        public static final String MERCHANT_ACCOUNT_NOT_EXIST = "09";
        public static final String system_under_maintenance = "96";
    }
}

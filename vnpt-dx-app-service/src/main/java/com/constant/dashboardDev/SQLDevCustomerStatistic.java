package com.constant.dashboardDev;

public class SQLDevCustomerStatistic {
    
    public static final String OVER_VIEW_NEW_SUBSCRIPTION = 
            "with new_sub_cte as ( \n" + 
            "    select \n" + 
            "        sub.id as sub_id, sub.user_id as user_id \n" + 
            "    from {h-schema}subscriptions sub \n" + 
            "        left join {h-schema}combo_plan on combo_plan.id = sub.combo_plan_id and sub.combo_plan_id is not null \n" + 
            "        left join {h-schema}combo on combo_plan.combo_id = combo.id \n" + 
            "        left join {h-schema}pricing on pricing.id = sub.pricing_id and sub.combo_plan_id is null \n" + 
            "        left join {h-schema}services ser on pricing.service_id = ser.id \n" + 
            "        where \n" + 
            "            --<PERSON><PERSON> quyền dữ liệu thuộc Dev đang đăng nhập\n" + 
            "            (combo.user_id = :developerId or ser.user_id = :developerId) and \n" + 
            "            cast(:startTime as date) <= sub.started_at and \n" + 
            "            sub.started_at < cast(:endTime as date) + interval '1' day and \n" + 
            "            sub.confirm_status = 1 and \n" + 
            "            sub.reg_type = 1 and \n" + 
            "            sub.deleted_flag = 1 and \n" + 
            "            ( \n" + 
            "                'ALL' in (:serviceTypeLst) or \n" + 
            "                ( \n" + 
            "                    'ON' in (:serviceTypeLst) and \n" + 
            "                    ( \n" + 
            "                        (sub.combo_plan_id is not null and combo.combo_owner in(0, 1)) or \n" + 
            "                        ser.service_owner in(0, 1) \n" + 
            "                    ) \n" + 
            "                ) or \n" + 
            "                ( \n" + 
            "                    'OS' in (:serviceTypeLst) and \n" + 
            "                    ( \n" + 
            "                        ( \n" + 
            "                            sub.combo_plan_id is not null and \n" + 
            "                            (combo.combo_owner in(2, 3) or combo.combo_owner is null) \n" + 
            "                        ) or \n" + 
            "                        ser.service_owner in(2, 3) \n" + 
            "                    ) \n" + 
            "                ) \n" + 
            "            ) and \n" + 
            "            ( -1 in (:serviceIdLst) or ser.id in (:serviceIdLst)) \n" + 
            ") \n" + 
            "select \n" + 
            "  count(new_sub_cte.sub_id) as subscriptionQuantity, \n" + 
            "  province.name as province \n" + 
            "from new_sub_cte \n" + 
            "    inner join {h-schema}users on new_sub_cte.user_id = users.id \n" + 
            "    inner join {h-schema}province on users.province_id = province.id \n" + 
            "    inner join {h-schema}users_roles on users.id = users_roles.user_id \n" + 
            "    inner join {h-schema}role on users_roles.role_id = role.id \n" + 
            "where \n" + 
            "    users.parent_id = -1 and \n" + 
            "    role.id = 3 and \n" + 
            "    ('ALL' in (:customerTypeLst) or users.customer_type in (:customerTypeLst)) \n" + 
            "group by province.name \n" + 
            "order by count(new_sub_cte.user_id) desc \n" + 
            "limit :top";

    public static final String GET_DATA_NEW_SUBSCRIPTION_SELECT = 
            "select \n" +
            "    most_new_sub_cte.name_service as serviceName, \n" +
            "    most_new_sub_cte.name_enterprise as enterpriseName, \n" +
            "    most_new_sub_cte.tax_number as taxCode, \n" +
            "    to_char(most_new_sub_cte.next_payment_cycle, 'dd/mm/yyyy') as nextPaymentCycle, \n" +
            "    most_new_sub_cte.email as email, \n" +
            "    to_char(most_new_sub_cte.date_reg, 'dd/mm/yyyy hh24:mi:ss') as registryAt, \n" +
            "    case \n" +
            "        when most_new_sub_cte.number_of_cycles = -1 then 'Không giới hạn' \n" +
            "        else concat(most_new_sub_cte.number_of_cycles) \n" +
            "    end as numberOfCycles, \n" +
            "    to_char(most_new_sub_cte.start_date_use, 'dd/mm/yyyy') as startAt, \n" +
            "    most_new_sub_cte.province_name as province, \n" +
            "    most_new_sub_cte.sub_status as subStatus, \n" +
            "    most_new_sub_cte.ser_type as serviceType, \n" +
            "    most_new_sub_cte.service_pack_name as pricingName, \n" +
            "    to_char(most_new_sub_cte.date_create, 'dd/mm/yyyy hh24:mi:ss') as createAt \n" +
            "from \n" +
            "    ( \n" +
            "        select \n" +
            "            coalesce(ser.service_name, com.combo_name) as name_service, \n" +
            "            case \n" +
            "                when users.customer_type = 'cn' then concat(users.last_name,' ',users.first_name) \n" +
            "                else users.name \n" +
            "            end as name_enterprise, \n" +
            "            users.tin as tax_number, \n" +
            "            sub.next_payment_time as next_payment_cycle, \n" +
            "            users.email as email, \n" +
            "            sub.created_at as date_reg, \n" +
            "            sub.started_at as start_date_use, \n" +
            "            province.name as province_name, \n" +
            "            coalesce(combo_plan.combo_name, pricing.pricing_name) as service_pack_name, \n" +
            "            users.created_at as date_create, \n" +
            "            case \n" +
            "                when sub.combo_plan_id is not null then combo_plan.number_of_cycles \n" +
            "                when sub.pricing_multi_plan_id is not null then pricing_plan.number_of_cycles \n" +
            "                else pricing.number_of_cycles \n" +
            "            end as number_of_cycles, \n" +
            "            case \n" +
            "                when combo_plan.cycle_type = 0 or pricing_plan.circle_type = 0 or pricing.cycle_type = 0 then 'Ngày' \n" +
            "                when combo_plan.cycle_type = 1 or pricing_plan.circle_type = 1 or pricing.cycle_type = 1 then 'Tuần' \n" +
            "                when combo_plan.cycle_type = 2 or pricing_plan.circle_type = 2 or pricing.cycle_type = 2 then 'Tháng' \n" +
            "                when combo_plan.cycle_type = 3 or pricing_plan.circle_type = 3 or pricing.cycle_type = 3 then 'Năm' \n" +
            "            end as type_of_cycle, \n" +
            "            case\n" +
            "                when sme_progress.id = 1 and coalesce(coalesce(com.combo_owner, ser.service_owner), 3) in (2, 3) then 'Tiếp nhận đơn hàng' \n" +
            "                when sme_progress.id = 2 and coalesce(coalesce(com.combo_owner, ser.service_owner), 3) in (2, 3) then 'Đang triển khai' \n" +
            "                when sme_progress.id = 3 and coalesce(coalesce(com.combo_owner, ser.service_owner), 3) in (2, 3) then 'Hủy đơn hàng' \n" +
            "                when sme_progress.id = 4 and coalesce(coalesce(com.combo_owner, ser.service_owner), 3) in (2, 3) then 'Hoàn thành' \n" +
            "                when sme_progress.id = 5 and coalesce(coalesce(com.combo_owner, ser.service_owner), 3) in (2, 3) then 'Đã thanh toán' \n" +
            "                when sub.status = 0 and coalesce(com.combo_owner, ser.service_owner) in (0, 1) then 'Đang chờ' \n" +
            "                when sub.status = 1 and coalesce(com.combo_owner, ser.service_owner) in (0, 1) then 'Dùng thử' \n" +
            "                when sub.status = 2 and coalesce(com.combo_owner, ser.service_owner) in (0, 1) then 'Hoạt động' \n" +
            "                when sub.status = 3 and coalesce(com.combo_owner, ser.service_owner) in (0, 1) then 'Đã hủy' \n" +
            "                when sub.status = 4 and coalesce(com.combo_owner, ser.service_owner) in (0, 1) then 'Kết thúc' \n" +
            "            end as sub_status, \n" +
            "            case \n" +
            "                when coalesce(com.combo_owner, ser.service_owner) in (0, 1) then 'ON' \n" +
            "                else 'OS'\n" +
            "            end as ser_type \n" +
            "        from {h-schema}subscriptions sub \n" +
            "            join {h-schema}users on sub.user_id = users.id\n" +
            "            left join {h-schema}province on users.province_id = province.id\n" +
            "            left join {h-schema}pricing on pricing.id = sub.pricing_id and sub.combo_plan_id is null\n" +
            "            left join {h-schema}services ser on pricing.service_id = ser.id\n" +
            "            left join {h-schema}order_service_receive service_receive on service_receive.subscription_id = sub.id\n" +
            "            left join {h-schema}order_service_status service_status on service_status.id = cast (service_receive.order_status as bigint)\n" +
            "            left join {h-schema}sme_progress on sme_progress.id = service_status.sme_progress_id \n" +
            "            left join {h-schema}combo_plan on combo_plan.id = sub.combo_plan_id and sub.combo_plan_id is not null\n" +
            "            left join {h-schema}combo com on combo_plan.combo_id = com.id\n" +
            "            left join {h-schema}pricing_multi_plan pricing_plan on sub.pricing_multi_plan_id = pricing_plan.id\n" +
            "        where \n" +
            "            --Phân quyền dữ liệu thuộc Dev đang đăng nhập\n" +
            "            (com.user_id = :developerId or ser.user_id = :developerId) and \n" +
            "            ( -1 in (:serviceIdLst) or ser.id in (:serviceIdLst)) and \n" +
            "            ( \n" +
            "                'ALL' in (:serviceTypeLst) or \n" +
            "                ( \n" +
            "                    'ON' in (:serviceTypeLst) and \n" +
            "                    ( \n" +
            "                        (sub.combo_plan_id is not null and com.combo_owner in(0, 1)) or \n" +
            "                        ser.service_owner in(0, 1) \n" +
            "                    ) \n" +
            "                ) or \n" +
            "                ( \n" +
            "                    'OS' in (:serviceTypeLst) and \n" +
            "                    ( \n" +
            "                        ( \n" +
            "                            sub.combo_plan_id is not null and \n" +
            "                            (com.combo_owner in(2, 3) or com.combo_owner is null) \n" +
            "                        ) or \n" +
            "                        ser.service_owner in(2, 3) \n" +
            "                    ) \n" +
            "                ) \n" +
            "            ) and \n" +
            "            cast(:startTime as date) <= sub.started_at and \n" +
            "            sub.started_at < cast(:endTime as date) + interval '1' day and \n" +
            "            sub.user_id in ( \n" +
            "                select id \n" +
            "                from {h-schema}users\n" +
            "                where \n" +
            "                    id in (select distinct user_id from {h-schema}users_roles where role_id = 3) and \n" +
            "                    (-1 in (:provinceIdLst) or province.id in (:provinceIdLst)) \n" +
            "            ) and \n" +
            "            sub.confirm_status = 1 and \n" +
            "            sub.reg_type = 1 and \n" +
            "            ('ALL' in (:customerTypeLst) or users.customer_type in (:customerTypeLst)) \n" +
            ") as most_new_sub_cte";
}

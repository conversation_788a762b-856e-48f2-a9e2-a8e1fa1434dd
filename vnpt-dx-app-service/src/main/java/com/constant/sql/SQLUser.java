package com.constant.sql;

/**
 * * SQLUser
 *
 * <AUTHOR>
 *  4/19/2021 1:53 PM
 */
public class SQLUser {
    public static final String GET_RATIO_REPORT_BY_MONTH_AND_PROVINCE_ID = "select " +
            "  q.totalAccount, " +
            "  q.totalRegister, " +
            "  q.totalUnRegister, " +
            "  to_char( 100 * (q.totalRegister::::float) / q.totalAccount, '999D999') as ratioRegister , " +
            "  to_char( 100 * (q.totalUnRegister::::float) / q.totalAccount, '999D999') as ratioUnRegister " +
            "from " +
            "  ( " +
            "  select " +
            "    count(sub.userId) as totalAccount, " +
            "    sum(case when sub.subCount > 0 then 1 else 0 end) as totalRegister, " +
            "    sum(case when sub.subCount > 0 then 0 else 1 end) as totalUnRegister " +
            "  from " +
            "    ( " +
            "    select " +
            "      u.id as userId, " +
            "      count(s.id) as subCount " +
            "    from " +
            "      {h-schema}users u " +
            "    join {h-schema}users_roles ur on " +
            "      u.id = ur.user_id " +
            "      and u.status = 1 " +
            "      and u.created_at <= :date " +
            "    join {h-schema}role r on " +
            "      ur.role_id = r.id " +
            "      and r.name = 'ROLE_SME' " +
            "    left join {h-schema}subscriptions s on " +
            "      u.id = s.user_id " +
            "      and s.created_at <= :date " +
            "    where " +
            "      (-1 = :provinceId " +
            "      or u.province_id = :provinceId) " +
            "    group by " +
            "      u.id) as sub)as q";
    public static final String CHECK_COMPANY_ACTIVE =
            "SELECT " +
            "    count(1)>0 " +
            "FROM " +
            "    {h-schema}users u " +
            "JOIN {h-schema}users_roles ur ON " +
            "    u.id = ur.user_id " +
            "JOIN {h-schema}role r ON " +
            "    ur.role_id = r.id " +
            "WHERE " +
            "    r.name IN ('ROLE_SME', 'ROLE_DEVELOPER') " +
            "    AND u.status = 1 " +
            "    AND u.deleted_flag = 1 " +
            "    AND u.id = :companyId";

    public static final String GET_NAME_DEV_BY_ID =
            "select u.name "
                + "from {h-schema}users u "
                + "where u.id = :id ";

    public static final String GET_PARENT_NAME_DEV_BY_USER_ID =
        "select \n" +
            "    case\n" +
            "        when users.parent_id = -1 then users.name \n" +
            "        else parentUser.name\n" +
            "    end as name\n" +
            "from \n" +
            "    {h-schema}users\n" +
            "    left join {h-schema}users parentUser on users.parent_id = parentUser.id\n" +
            "where \n" +
            "    users.id = :userId";

    public static final String GET_NAME_ADMIN_BY_ID =
        "select concat(u.last_name,' ',u.first_name) "
            + "from {h-schema}users u "
            + "where u.id = :id ";

    public static final String GET_ALL_TIN_OF_USER =
        " SELECT "
            + " DISTINCT u.tin "
            + "FROM "
            + "  {h-schema}users u "
            + "JOIN  {h-schema}subscriptions s ON "
            + " s.user_id = u.id "
            + " AND s.deleted_flag = 1 "
            + " AND s.confirm_status = 1 "
            + "JOIN {h-schema}services s2 ON "
            + " s2.id = s.service_id "
            + " AND s2.deleted_flag = 1 "
            + " AND (s2.service_owner NOT IN (2, 3) "
            + " OR s2.service_owner IS NULL) "
            + "JOIN {h-schema}users u2 ON "
            + " u2.id = s2.user_id "
            + "LEFT JOIN {h-schema}departments d ON "
            + " d.id = u.department_id "
            + "WHERE "
            + " u.deleted_flag = 1 "
            + " AND (:tin = '' OR COALESCE (u.tin, '') LIKE %:tin% ) "
            + " AND (1 = :portalType OR s2.user_id = :userId) "
            + " AND (-1 = :provinceId OR u.province_id = :provinceId)"
            + "  ORDER BY u.tin ";

    public static final String GET_ALL_TIN_OF_SUB_COMBO =
        " SELECT "
            + " DISTINCT u.tin "
            + "FROM "
            + "  {h-schema}users u "
            + "JOIN {h-schema}subscriptions s ON "
            + " s.user_id = u.id "
            + " AND s.deleted_flag = 1 "
            + " AND s.confirm_status = 1 "
            + "JOIN {h-schema}combo_plan cp ON "
            + " cp.id = s.combo_plan_id  "
            + " AND cp.deleted_flag = 1 "
            + " JOIN {h-schema}combo c2 "
            + " ON cp.combo_id = c2.id AND c2.deleted_flag = 1 "
            + "JOIN {h-schema}users u2 ON "
            + " u2.id = c2.user_id  "
            + "LEFT JOIN {h-schema}departments d ON "
            + " d.id = u.department_id AND d.deleted_flag = 1 "
            + "WHERE "
            + " u.deleted_flag = 1 "
            + " AND (u.tin ILIKE ('%' || :tin || '%')) "
            + " AND (:portalType = 1 OR c2.user_id = :userId) "
            + " AND (:provinceId = -1 OR u.province_id = :provinceId)"
            + " ORDER BY u.tin ";

    public static final String FIND_USER_BY_EMAIL_DB =
        " SELECT  "
            + "  u.id,  "
            + "  u.first_name AS firstName,  "
            + "  u.last_name AS lastName,  "
            + "  u.email ,  "
            + "  u.phone_number AS phoneNumber,  "
            + "  u.parent_id AS parentId,  "
            + "  u.customer_type AS customerType  "
            + "FROM  "
            + "  {h-schema}users u  "
            + "WHERE  "
            + "  u.deleted_flag = 1  "
            + "  AND (lower(u.email) = lower(:emailOrPhone)  "
            + "  OR u.phone_number = :emailOrPhone)";

    public static final String GET_LIST_ADMIN_PROVINCE =
            "SELECT DISTINCT u.* FROM {h-schema}users u " +
                    "              JOIN {h-schema}departments d ON " +
                    "                 u.department_id = d.id " +
                    "              JOIN {h-schema}users_roles ur ON " +
                    "                 ur.user_id = u.id " +
                    "              WHERE " +
                    "                 d.province_id = :provinceId" +
                    "                 AND ur.role_id IN (1, 2, 5) and u.deleted_flag = 1 and u.status = 1";

    public static final String GET_PROVIDER_BY_SERVICE =
            "select u.* from {h-schema}users u " +
                    "left join {h-schema}services s ON s.user_id = u.id " +
                    "left join {h-schema}subscriptions sub ON sub.service_id = s.id " +
                    "where sub.id = :subId and u.deleted_flag = 1 and u.status = 1";

    public static final String GET_PROVIDER_BY_SERVICE_FOR_EMAIL =
            "select COALESCE(concat(u.last_name, ' ', u.first_name), u.name, ' ') from {h-schema}users u " +
                    "left join {h-schema}services s ON s.user_id = u.id " +
                    "left join {h-schema}subscriptions sub ON sub.service_id = s.id " +
                    "where sub.id = :subId";

    public static final String GET_ALL_SME_ADMIN_USER_ID =
            "select * from {h-schema}users u " +
                    "join (" +
                    "select DISTINCT ur.user_id from {h-schema}users_roles ur where ur.role_id = 11 " +
                    ") tbl ON tbl.user_id = u.id and (u.parent_id = :userId or u.id = :userId) where u.deleted_flag = 1 and u.status = 1";

    public static final String GET_ALL_SME_ADMIN_BY_USER_ID_IN =
        "select * from {h-schema}users u \n" +
            "join ( \n" +
            "   select DISTINCT ur.user_id from {h-schema}users_roles ur where ur.role_id = 11 \n" +
            ") tbl ON tbl.user_id = u.id and (u.parent_id in (:userIds) or u.id in (:userIds)) \n" +
            "where u.deleted_flag = 1 and u.status = 1";

    public static final String GET_ALL_DEV_ADMIN_USER_ID =
            "select * from {h-schema}users u " +
                    "join (" +
                    "select DISTINCT ur.user_id from {h-schema}users_roles ur where ur.role_id = 10 " +
                    ") tbl ON tbl.user_id = u.id and (u.parent_id = :userId or u.id = :userId) where u.deleted_flag = 1 and u.status = 1";

    public static final String GET_USER_NAME_AND_ID_BY_ID =
        "SELECT id, name FROM {h-schema}users WHERE id = :id AND deleted_flag = 1";

    public static final String REPORT_ALL_SME_EMPLOYEE_BY_IDS =
            "SELECT report.*, u.name AS smeName "
                    + "FROM "
                    + "(SELECT "
                    + "	count(u.id) AS totalAccount, "
                    + "	parent_id AS smeId "
                    + "FROM "
                    + "	{h-schema}users u "
                    + "WHERE "
                    + "	u.deleted_flag = 1 "
                    + "	AND u.status = 1 "
                    + "	AND ((:getFull = FALSE AND u.parent_id IN (:smeIds)) OR (:getFull = TRUE AND u.parent_id <> -1)) "
                    + "GROUP BY "
                    + "	u.parent_id) AS report "
                    + "	JOIN {h-schema}users u ON report.smeId = u.id";

    public static final String REPORT_RATIO_DEVELOPER_REGISTER_BY_YEAR =
            "WITH user_develop AS ( " +
                    "     SELECT " +
                    "          u.id, " +
                    "          u.created_at " +
                    "     FROM " +
                    "          {h-schema}users u " +
                    "     JOIN {h-schema}users_roles ur ON " +
                    "          u.id = ur.user_id " +
                    "          AND u.status = 1 " +
                    "          AND u.created_at BETWEEN :firstDay AND :endDay" +
                    "          AND (:provinceId = -1 " +
                    "          OR u.province_id = :provinceId) " +
                    "     JOIN {h-schema}role r ON " +
                    "          ur.role_id = r.id " +
                    "          AND r.name = 'ROLE_DEVELOPER'  " +
                    ") " +
                    "SELECT " +
                    "     totalAccount.total AS totalAccount, " +
                    "     totalUnRegister.total AS totalUnRegister, " +
                    "     totalUnRelease.total AS totalUnRelease, " +
                    "     totalRelease.total AS totalRelease, " +
                    "     CASE " +
                    "        WHEN totalAccount.total = 0 THEN 0 " +
                    "        ELSE totalUnRegister.total::::float / totalAccount.total * 100 " +
                    "     END AS ratioUnRegister, " +
                    "     CASE " +
                    "        WHEN totalAccount.total = 0 THEN 0 " +
                    "        ELSE totalUnRelease.total::::float / totalAccount.total * 100 " +
                    "     END AS ratioUnRelease, " +
                    "     CASE " +
                    "        WHEN totalAccount.total = 0 THEN 0 " +
                    "        ELSE totalRelease.total::::float / totalAccount.total * 100 " +
                    "     END AS ratioRelease " +
                    "FROM " +
                    "     ( " +
                    "     SELECT " +
                    "          count(1) total " +
                    "     FROM " +
                    "          user_develop ud " +
                    "     WHERE " +
                    "          ud.created_at BETWEEN :firstDay AND :endDay  " +
                    "     ) totalAccount " +
                    "CROSS JOIN ( " +
                    "     SELECT " +
                    "          COUNT(DISTINCT ud.id) total " +
                    "     FROM " +
                    "          user_develop ud " +
                    "     WHERE " +
                    "          ud.id NOT IN ( " +
                    "               SELECT " +
                    "                    DISTINCT s.user_id " +
                    "               FROM " +
                    "                    {h-schema}services s " +
                    "               WHERE " +
                    "                    s.approve = 1 " +
                    "               AND s.created_at BETWEEN :firstDay AND :endDay " +
                    "               ) " +
                    "          ) totalUnRegister " +
                    "CROSS JOIN ( " +
                    "     SELECT " +
                    "          COUNT(DISTINCT ud.id) total " +
                    "     FROM " +
                    "          user_develop ud " +
                    "     LEFT JOIN {h-schema}services s ON " +
                    "          ud.id = s.user_id " +
                    "     LEFT JOIN {h-schema}sub_plan_service sps ON " +
                    "          s.id = sps.service_id " +
                    "     WHERE " +
                    "          s.approve = 1 " +
                    "          AND s.created_at BETWEEN :firstDay AND :endDay " +
                    "          AND ( sps.id IS NULL " +
                    "               OR ( " +
                    "                    sps.id IS NOT NULL " +
                    "                    AND (SELECT sp.approve FROM {h-schema}subscription_plan sp WHERE sp.id = sps.subscription_plan_id) = 0  " +
                    "                    )  " +
                    "               )  " +
                    "               ) totalUnRelease " +
                    "CROSS JOIN ( " +
                    "     SELECT " +
                    "          COUNT(DISTINCT ud.id) total " +
                    "     FROM " +
                    "          user_develop ud " +
                    "     LEFT JOIN {h-schema}services s ON " +
                    "          ud.id = s.user_id " +
                    "     LEFT JOIN {h-schema}sub_plan_service sps ON " +
                    "          s.id = sps.service_id " +
                    "     WHERE " +
                    "          s.approve = 1 " +
                    "          AND s.created_at BETWEEN :firstDay AND :endDay " +
                    "          AND sps.id IS NOT NULL " +
                    "          AND ( SELECT sp.approve FROM {h-schema}subscription_plan sp WHERE sp.id = sps.subscription_plan_id) = 1  " +
                    "               ) totalRelease";

    public static final String EXIST_BY_PHONE_AFF =
            "select EXISTS ( " +
                    "     select id from {h-schema}users u LEFT JOIN {h-schema}users_roles ur ON ur.user_id = u.id " +
                    "     where u.deleted_flag = 1 and lower(u.phone_number) = lower(:phone) " +
                    "     and (-1 = :userId or u.id <> :userId) and ur.role_id IN (:roleAdminDevIds) " +
                    ")";

    public static final String EXIST_BY_TIN_AFF =
            "select EXISTS ( " +
                    "     select u.id from {h-schema}users u JOIN {h-schema}affiliate_users aff ON u.id = aff.user_id " +
                    "     where u.deleted_flag = 1 and lower(u.tin) = lower(:tin) " +
                    "     and (-1 = :userId or u.id <> :userId) " +
                    ")";

    public static final String EXIST_BY_REP_PERSONAL_CERT_NUMBER_AFF =
            "select EXISTS ( " +
                    "     select u.id from {h-schema}users u JOIN {h-schema}affiliate_users aff ON u.id = aff.user_id " +
                    "     where " +
                    "           u.deleted_flag = 1 " +
                    "           and u.rep_personal_cert_number = :repPersonalCertNumber " +
                    "           and u.rep_personal_cert_type_id = :repPersonalCertTypeId " +
                    "           and (-1 = :userId or u.id <> :userId) " +
                    ")";

    public static final String EXIST_BY_BUSINESS_LICENSE_NUMBER_AFF =
            "select EXISTS ( " +
                    "     select u.id from {h-schema}users u JOIN {h-schema}affiliate_users aff ON u.id = aff.user_id " +
                    "     where u.deleted_flag = 1 and lower(u.business_license_number) = lower(:businessLicense) " +
                    "     and (-1 = :userId or u.id <> :userId) " +
                    ")";

    public static final String COUNT_USER_BETWEEN_CREAT_AT =
            "SELECT COUNT(u.id) FROM User u " +
                    " WHERE u.createdAt BETWEEN :firstDay AND :endDay" +
                    " AND u.status = :status";

    public static final String COUNT_BY_ROLE =
            "SELECT COUNT(u.id) FROM {h-schema}users u" +
                    " WHERE u.id IN (SELECT ur.user_id FROM {h-schema}users_roles ur WHERE ur.role_id = :roleId)" +
                    " AND u.status = :status";

    public static final String CHECK_IS_ADMIN =
        "select exists ( \n" +
            "    select 1 \n" +
            "    from {h-schema}view_role_admin \n" +
            "    where user_id = :userId \n" +
            ")";

    public static final String CHECK_IS_DEV =
        "select exists ( \n" +
            "    select 1 \n" +
            "    from {h-schema}view_role_dev \n" +
            "    where user_id = :userId \n" +
            ")";

    public static final String GET_LST_EMPLOYEE =
        "select id from {h-schema}users where parent_id = :currentUserId";
    public static final String GET_PERMISSION_BY_ROLE_NAME =
        "select \n"
            + "  distinct permission.name \n"
            + "from {h-schema}role\n"
            + "join {h-schema}roles_permissions on role.id = roles_permissions.role_id\n"
            + "join {h-schema}permission on permission.id = roles_permissions.permission_id\n"
            + "where role.name in (:roleName)";


    public static final String GET_ACCOUNT_RATIO_BY_YEAR =
            "SELECT  taus.totalAccount                                                                  AS totalAccount,   " +
                    " taus.totalRegister                                                                AS totalRegister,   " +
                    " taus.totalUnRegister                                                              AS totalUnRegister,   " +
                    " (to_char(100 * (taus.totalRegister::::FLOAT) / taus.totalAccount, '999D999'))       AS ratioRegister,   " +
                    " (to_char(100 - 100 * (taus.totalRegister::::FLOAT) / taus.totalAccount, '999D999')) AS ratioUnRegister   " +
                    " FROM (   " +
                    "          SELECT count(sub.userId)                                  AS totalAccount,   " +
                    "                 sum(CASE WHEN sub.subCount != 0 THEN 1 ELSE 0 END) AS totalRegister,   " +
                    "                 sum(CASE WHEN sub.subCount != 0 THEN 0 ELSE 1 END) AS totalUnRegister   " +
                    "          FROM (   " +
                    "                   SELECT u.id        AS userId,   " +
                    "                          count(s.id) AS subCount   " +
                    "                   FROM {h-schema}users u   " +
                    "                            JOIN {h-schema}users_roles ur ON   " +
                    "                           u.id = ur.user_id   " +
                    "                           AND u.status = 1   " +
                    "                           AND date_part('year', TO_TIMESTAMP(TO_CHAR(u.created_at, 'YYYY'), 'YYYY')) =   " +
                    "                               CASE   " +
                    "                                   WHEN :year = '-1' THEN date_part('year', CURRENT_DATE)   " +
                    "                                   ELSE date_part('year', TO_TIMESTAMP(:year, 'YYYY'))   " +
                    "                                   END   " +
                    "                            JOIN {h-schema}role r  " +
                    "                             ON  ur.role_id = r.id " +
                    "                             AND r.name = 'ROLE_SME' " +
                    "                            LEFT JOIN {h-schema}subscriptions s  " +
                    "                                 ON u.id = s.user_id   " +
                    "                                 AND date_part('year', TO_TIMESTAMP(TO_CHAR(s.created_at, 'YYYY'), 'YYYY')) =   " +
                    "                               CASE   " +
                    "                                   WHEN :year = '-1' THEN date_part('year', CURRENT_DATE)   " +
                    "                                   ELSE date_part('year', TO_TIMESTAMP(:year, 'YYYY'))   " +
                    "                                   END   " +
                    "                   WHERE (-1 = :provinceId   " +
                    "                       OR u.province_id = :provinceId)   " +
                    "                   GROUP BY u.id) AS sub   " +
                    "      ) AS taus ";

    public static final String REPORT_RATIO_DEVELOPER_REGISTER_BY_QUARTER =
            "WITH user_develop AS ( " +
                    "SELECT " +
                    "     u.id, " +
                    "     u.created_at " +
                    "FROM " +
                    "     {h-schema}users u " +
                    "JOIN {h-schema}users_roles ur ON " +
                    "     u.id = ur.user_id " +
                    "     AND u.status = 1 " +
                    "     AND (:provinceId = -1 OR u.province_id = :provinceId) " +
                    "JOIN {h-schema}role r ON " +
                    "     ur.role_id = r.id " +
                    "     AND r.name = 'ROLE_DEVELOPER' ) " +
                    "SELECT " +
                    "     totalAccount.total AS totalAccount, " +
                    "     totalUnRegister.total AS totalUnRegister, " +
                    "     totalUnRelease.total AS totalUnRelease, " +
                    "     totalRelease.total AS totalRelease, " +
                    "     CASE " +
                    "        WHEN totalAccount.total = 0 THEN 0 " +
                    "        ELSE totalUnRegister.total::::float / totalAccount.total * 100 " +
                    "     END AS ratioUnRegister, " +
                    "     CASE " +
                    "        WHEN totalAccount.total = 0 THEN 0 " +
                    "        ELSE totalUnRelease.total::::float / totalAccount.total * 100 " +
                    "     END AS ratioUnRelease, " +
                    "     CASE " +
                    "        WHEN totalAccount.total = 0 THEN 0 " +
                    "        ELSE totalRelease.total::::float / totalAccount.total * 100 " +
                    "     END AS ratioRelease " +
                    "FROM " +
                    "     ( " +
                    "     SELECT " +
                    "          count(1) total " +
                    "     FROM " +
                    "          user_develop ud " +
                    "     WHERE " +
                    "          ud.created_at <= :date ) totalAccount " +
                    "CROSS JOIN ( " +
                    "     SELECT " +
                    "          COUNT(DISTINCT ud.id) total " +
                    "     FROM " +
                    "          user_develop ud " +
                    "     LEFT JOIN {h-schema}services s ON " +
                    "          ud.id = s.user_id " +
                    "          AND s.approve = 1 " +
                    "          AND s.created_at <= :date " +
                    "     WHERE " +
                    "          s.id IS NULL " +
                    ") totalUnRegister " +
                    "CROSS JOIN ( " +
                    "     SELECT " +
                    "          COUNT(DISTINCT ud.id) total " +
                    "     FROM " +
                    "          user_develop ud " +
                    "     JOIN {h-schema}services s ON " +
                    "          ud.id = s.user_id " +
                    "          AND s.approve = 1 " +
                    "          AND s.created_at <= :date " +
                    "     LEFT JOIN {h-schema}sub_plan_service sps ON " +
                    "          s.id = sps.service_id " +
                    "     LEFT JOIN {h-schema}subscription_plan sp ON " +
                    "          sps.subscription_plan_id = sp.id " +
                    "          AND sp.approve <> 1 " +
                    "     LEFT JOIN ( " +
                    "          SELECT " +
                    "               DISTINCT ud.id " +
                    "          FROM " +
                    "               user_develop ud " +
                    "          JOIN {h-schema}services s ON " +
                    "               ud.id = s.user_id " +
                    "               AND s.approve = 1 " +
                    "               AND s.created_at <= :date " +
                    "          JOIN {h-schema}sub_plan_service sps ON " +
                    "               s.id = sps.service_id " +
                    "          JOIN {h-schema}subscription_plan sp ON " +
                    "               sps.subscription_plan_id = sp.id " +
                    "               AND sp.approve = 1 ) uds ON " +
                    "          uds.id = ud.id " +
                    "     WHERE " +
                    "          uds.id IS NULL " +
                    ") totalUnRelease " +
                    "CROSS JOIN ( " +
                    "     SELECT " +
                    "          COUNT(DISTINCT ud.id) total " +
                    "     FROM " +
                    "          user_develop ud " +
                    "     JOIN {h-schema}services s ON " +
                    "          ud.id = s.user_id " +
                    "          AND s.approve = 1 " +
                    "          AND s.created_at <= :date " +
                    "     JOIN {h-schema}sub_plan_service sps ON " +
                    "          s.id = sps.service_id " +
                    "     JOIN {h-schema}subscription_plan sp ON " +
                    "          sp.id = sps.subscription_plan_id " +
                    "          AND sp.approve = 1 " +
                    ") totalRelease;";

    public static final String GET_ROLE = "SELECT r.name " +
            "FROM {h-schema}users_roles ur " +
            "JOIN {h-schema}role r " +
            "ON ur.role_id = r.id " +
            "WHERE ur.user_id = :userId";

    public static final String GET_ROLE_DISPLAY_NAME =
        "SELECT r.display_name " +
        "FROM {h-schema}users_roles ur " +
        "JOIN {h-schema}role r " +
        "ON ur.role_id = r.id " +
        "WHERE ur.user_id = :userId " +
        "AND r.display_name IS NOT NULL";

    public static final String GET_USER_IS_PARENT_BY_ID_ROLE_ID =
            "SELECT "
            + "    u.* "
            + "FROM "
            + "    {h-schema}users u "
            + "JOIN ( "
            + "    SELECT "
            + "        DISTINCT(u.id) AS id "
            + "    FROM "
            + "        {h-schema}users u "
            + "    JOIN {h-schema}users_roles ur ON "
            + "        u.id = ur.user_id "
            + "    JOIN {h-schema}\"role\" r ON "
            + "        r.id = ur.role_id "
            + "    WHERE "
            + "        u.parent_id = -1 "
            + "        AND r.id = :roleId "
            + "        AND u.id IN (:ids)) AS userIds  "
            + "ON "
            + "    u.id = userIds.id";

    public static final String GET_SME_FOR_INTEGRATION =
        "SELECT NEW com.onedx.common.dto.integration.backend.IntegrationSmeDTO(" +
            "u.id, icon.externalLink, " +
            "cover.externalLink, u.name, " +
            "u.phoneNumber, u.email, u.nationId, " +
            "n.name, u.provinceId, p.name, " +
            "u.districtId, d.name, w.id, w.name, sd.id, sd.name, u.address, " +
            "u.webSite, u.description, u.corporateTaxCode, " +
            "u.businessSize, bs.name, u.businessAreas, ba.name, u.socialInsuranceNumber, u.status as userStatus, u.parentId as parentId, u.smeUUID as smeUUID) " +
            "FROM User u " +
            "LEFT JOIN FileAttach icon " +
            "ON icon.userId = u.id " +
            "AND icon.objectType = 3 " +
            "LEFT JOIN FileAttach cover " +
            "ON cover.userId = u.id " +
            "AND cover.objectType = 4 " +
            "LEFT JOIN BusinessSize bs " +
            "ON bs.id = u.businessSize " +
            "LEFT JOIN BusinessAreas ba " +
            "ON ba.id = u.businessAreas " +
            "LEFT JOIN Nation n " +
            "ON n.id = u.nationId " +
            "LEFT JOIN District d " +
            "ON d.id = u.districtId " +
            "AND u.provinceCode = d.provinceCode " +
            "LEFT JOIN Province p " +
            "ON p.id = u.provinceId " +
            "LEFT JOIN Ward w " +
            "ON w.id = u.wardId and w.deletedFlag = 1 " +
            "AND w.provinceCode = u.provinceCode " +
            "LEFT JOIN Street sd ON sd.id = u.streetId AND sd.wardId = u.wardId and sd.districtId = u.districtId AND sd.provinceCode = u.provinceCode \n" +
            "WHERE u.id = :id AND u.deletedFlag = 1";

    public static final String GET_LIST_EMPLOYEE_FOR_INTEGRATION =
        "SELECT new com.onedx.common.dto.integration.backend.IntegrationEmployeeDTO("
            + "u.id, u.name, "
            + "u.email, su.status, fa.externalLink, u.phoneNumber, "
            + "u.firstName, u.lastName, "
            + "CASE "
            + "	WHEN u.id = :smeId THEN TRUE "
            + "	ELSE FALSE "
            + "END, u.gender, u.birthday, u.status as userStatus, u.parentId as parentId,u.smeUUID) "
            + "FROM User u "
            + "LEFT JOIN FileAttach fa "
            + "ON fa.userId = u.id "
            + "AND fa.objectType = 5 "
            + "LEFT JOIN SubscriptionUser su "
            + "ON su.userId = u.id "
            + "AND su.subscriptionId = :subscriptionId "
            + "WHERE (u.id = :smeId OR u.parentId = :smeId) "
            + "AND u.deletedFlag = 1 ";


    public static final String GET_LIST_EMPLOYEE_OF_SME =
        "SELECT new com.onedx.common.dto.integration.backend.IntegrationEmployeeDTO("
            + "u.id, u.name, "
            + "u.email, u.status, fa.externalLink, u.phoneNumber, "
            + "u.firstName, u.lastName, "
            + "CASE "
            + "	WHEN u.id = :smeId THEN TRUE "
            + "	ELSE FALSE "
            + "END, u.gender, u.birthday) "
            + "FROM User u "
            + "LEFT JOIN FileAttach fa "
            + "ON fa.userId = u.id "
            + "AND fa.objectType = 5 "
            + "WHERE (u.id = :smeId OR u.parentId = :smeId) "
            + "AND u.deletedFlag = 1 ";

    public static final String GET_ADMIN_BY_SME_USER_ID =
        "SELECT u.* "
            + "FROM {h-schema} users u "
            + "WHERE u.deleted_flag = 1 "
            + "AND u.status = 1 "
            + "AND u.id = :id "
            + "AND u.parent_id = -1";

    public static final String GET_CUSTOMER_SME =
        "SELECT u.name AS smeName, "
            + " u.tin AS taxNo, "
            + " u.phone_number AS phoneNo, "
            + " u.email, "
            + " u.address, "
            + " n.name AS countryName, "
            + " p.name AS provinceName, "
            + " d.name AS districtName, "
            + " u.rep_fullname as contactName, "
            + " :phone as contactPhone, "
            + " sp.number_of_day_export_bill as numberOfDayExportBill, "
            + " COALESCE (invoice_address.sme_name, u.name) AS invoiceSmeName, "
            + " COALESCE (invoice_address.tin, u.tin) AS invoiceTaxNo, "
            + " COALESCE (invoice_address.address, u.address) AS invoiceAddress, "
            + " setup_address.address AS setupAddress,"
            + " u.last_name AS lastName,"
            + " u.first_name AS firstName,"
            + " concat(u.last_name, ' ', u.first_name) AS fullName, "
            + " u.rep_personal_cert_number AS identityNo "
            + "FROM {h-schema}users u "
            + "LEFT join {h-schema}nation n ON u.nation_id = n.id "
            + "LEFT join {h-schema}province p ON u.province_id = p.id "
            + "AND u.province_code = p.code "
            + "LEFT join {h-schema}district d ON u.district_id = d.id "
            + "AND u.province_id = d.province_id "
            + "AND u.province_code = d.province_code "
            + "LEFT join {h-schema}system_params sp ON sp.param_type = 'EXPORT_BILLING' "
            + "LEFT JOIN {h-schema}address AS invoice_address "
            +   "ON (u.id = invoice_address.user_id AND invoice_address.type = 0 AND invoice_address.default_location = 1 ) "
            + "LEFT JOIN {h-schema}address AS setup_address "
            +   "ON (u.id = setup_address.user_id AND setup_address.type = 1 AND setup_address.default_location = 1) "
            + "WHERE "
            + "u.id = :parentId "
            + "AND u.deleted_flag = 1 ";

    public static final String GET_CUSTOMER_SME_SUBSCRIPTION =
            "    SELECT distinct \n" +
            "    u.name AS smeName, \n" +
            "    u.tin AS taxNo, \n" +
            "    u.phone_number AS phoneNo, \n" +
            "    u.email, \n" +
            "    u.address, \n" +
            "    n.name AS countryName, \n" +
            "    p.name AS provinceName, \n" +
            "    u.province_id AS provinceId, \n" +
            "    u.district_id AS districtId, \n" +
            "    u.street_id AS streetId, \n" +
            "    u.ward_id AS wardId, \n" +
            "    d.name AS districtName, \n" +
            "    sp.number_of_day_export_bill as numberOfDayExportBill, \n" +
            "    COALESCE (invoice_address.sme_name, u.name) AS invoiceSmeName, \n" +
            "    COALESCE (invoice_address.tin, u.tin) AS invoiceTaxNo, \n" +
            "    COALESCE (invoice_address.address, u.address) AS invoiceAddress, \n" +
            "    setup_address.address AS setupAddress,\n" +
            "    u.last_name AS lastName," +
            "    u.first_name AS firstName," +
            "    concat_ws(' ', u.last_name, u.first_name) AS fullName, \n" +
            "    u.employee_code as employeeCode, \n" +
            "    case \n" +
            "        when u.customer_type = 'CN' then concat_ws(' ', u.last_name, u.first_name)\n" +
            "        else u.rep_fullname\n" +
            "    end as contactName, \n" +
            "    u.phone_number as contactPhone, \n" +
            "    u.rep_personal_cert_number AS identityNo \n" +
            "from\n" +
            "    {h-schema}users u \n" +
            "    LEFT join {h-schema}nation n ON u.nation_id = n.id \n" +
            "    LEFT join {h-schema}province p ON u.province_id = p.id AND u.province_code = p.code \n" +
            "    LEFT join {h-schema}district d ON u.district_id = d.id \n" +
            "        AND u.province_id = d.province_id and u.province_code = d.province_code \n" +
            "    LEFT join {h-schema}system_params sp ON sp.param_type = 'EXPORT_BILLING' \n" +
            "    LEFT JOIN {h-schema}address AS invoice_address ON (u.id = invoice_address.user_id \n" +
            "        AND invoice_address.type = 0 AND invoice_address.default_location = 1 ) \n" +
            "    LEFT JOIN {h-schema}address AS setup_address ON (u.id = setup_address.user_id\n" +
            "        AND setup_address.type = 1 AND setup_address.default_location = 1) \n" +
            "WHERE \n" +
            "    u.id = :parentId and\n" +
            "    u.deleted_flag = 1";


    public static final String GET_EMPLOYEE_USER_SUBSCRIPTION =
        "SELECT new com.onedx.common.dto.integration.backend.IntegrationEmployeeDTO(" +
            "u.id, " +
            "u.name, " +
            "u.email, " +
            "us.status, " +
            "u.avatar, " +
            "u.phoneNumber, " +
            "u.firstName, " +
            "u.lastName, " +
            "u.status as userStatus," +
            "u.parentId as parentId," +
            "u.smeUUID as smeUUID) " +
            "FROM User u " +
            "LEFT JOIN SubscriptionUser us ON us.userId = u.id AND us.subscriptionId = :subscriptionId " +
            "   AND us.serviceId IS NOT NULL " +
            "LEFT JOIN ServiceEntity s ON us.serviceId = s.id AND s.id = :serviceId " +
            "WHERE u.id IN :uid " +
            "AND u.deletedFlag = 1";

    public static final String GET_EMPLOYEE_PARENT_USER_SUBSCRIPTION =
            "SELECT " +
                    "u.id " +
                    "FROM {h-schema}users u " +
                    "WHERE (u.parent_id = :parentId OR u.id = :parentId) " +
                    "AND u.deleted_flag = 1";

    public static final String GET_INFO_CLUE =
        "SELECT "
            + "    c.user_code AS amCode, "
            + "    c.clue_name AS amName "
            + "FROM "
            + "    {h-schema}clue c "
            + "WHERE "
            + "    c.province_code IN ( "
            + "    SELECT "
            + "        COALESCE(u.province_code, p.code) "
            + "    FROM "
            + "        {h-schema}users u "
            + "    LEFT JOIN {h-schema}province p ON "
            + "        u.id = p.id "
            + "    WHERE "
            + "        u.id = :userId)";

    public static final String GET_REPRESENT_INFO =
        "SELECT "
            + "    COALESCE(u.rep_fullname, CONCAT(u.last_name,' ',u.first_name)) AS name, "
            + "    CASE "
            + "        WHEN u.rep_gender = 0 THEN 'FEMALE' "
            + "        WHEN u.rep_gender = 1 THEN 'MALE' "
            + "        WHEN u.rep_gender = 2 THEN 'OTHER' "
            + "    END AS gender, "
            + "    to_char(u.rep_birthday, 'dd/MM/yyyy') AS birthDate, "
            + "    u.rep_title AS POSITION, "
            + "    u.rep_nation_id AS nationalityId, "
            + "    n.name AS nationalityName, "
            + "    u.rep_folk_id AS ethnicityId, "
            + "    f.name AS ethnicityName, "
            + "    u.rep_personal_cert_type_id AS identityTypeId, "
            + "    pct.name AS identityTypeName, "
            + "    to_char(u.rep_personal_cert_date, 'dd/MM/yyyy') AS dateOfIssue, "
            + "    u.rep_personal_cert_place AS placeOfIssue, "
            + "    u.rep_registered_place AS placeOfHouse, "
            + "    u.rep_address AS currentPlace, "
            + "    u.rep_personal_cert_number AS identityNo "
            + "FROM "
            + "    {h-schema}users u "
            + "LEFT JOIN {h-schema}nation n ON "
            + "    n.id = u.rep_nation_id "
            + "LEFT JOIN {h-schema}folk f ON "
            + "    f.id = u.rep_folk_id "
            + "LEFT JOIN {h-schema}personal_cert_type pct ON "
            + "    pct.id = u.rep_personal_cert_type_id "
            + "WHERE "
            + "    u.id = :userId";

    public static final String GET_PROVINCE_BY_USER_ID =
        "SELECT dp.province_id as provinceId, "
            + "       dp.id as departmentId "
            + "FROM {h-schema}users u "
            + "JOIN {h-schema}departments dp ON u.department_id = dp.id "
            + "AND dp.deleted_flag = 1 AND dp.status = 1 "
            + "AND u.deleted_flag = 1 AND u.status = 1 "
            + "AND u.id = :userId ";

    public static final String GET_PARENT_ID =
        "SELECT "
        + "    CASE "
        + "        WHEN u.parent_id = -1 THEN u.id "
        + "        WHEN u.parent_id <> -1 THEN u.parent_id "
        + "    END "
        + "FROM "
        + "    {h-schema}users u "
        + "WHERE "
        + "    u.id = :userId";

    public static final String GET_USER_ADMIN =
        "SELECT u.id, u.first_name AS firstName, u.last_name AS lastName, u.email "
            + "FROM "
            + "    {h-schema}users u "
            + "INNER JOIN {h-schema}users_roles ur ON ur.user_id = u.id AND ur.role_id = 2 "
            + "         AND u.status = 1 AND u.deleted_flag = 1";

    public static final String GET_ALL_USER_ROLE_FULL_ADMIN =
            "SELECT \n" +
                    "   u.id, \n" +
                    "   u.first_name AS firstName, \n" +
                    "   u.last_name AS lastName, \n" +
                    "   u.email \n" +
                    "FROM {h-schema}users u \n" +
                    "   INNER JOIN {h-schema}users_roles ur ON ur.user_id = u.id AND ur.role_id = 9 AND u.status = 1 AND u.deleted_flag = 1";

    public static final String GET_LST_FULL_ADMIN_DETAIL =
        "SELECT \n" +
            "    users.id, \n" +
            "    users.first_name AS firstName, \n" +
            "    users.last_name AS lastName, \n" +
            "    users.email \n" +
            "FROM \n" +
            "    {h-schema}users \n" +
            "    JOIN {h-schema}view_role_full_admin ON users.id = view_role_full_admin.user_id \n" +
            "WHERE \n" +
            "    users.deleted_flag = 1 ";

    public static final String GET_LST_ID_FULL_ADMIN =
        "SELECT \n" +
            "    users.id \n" +
            "FROM \n" +
            "    {h-schema}users \n" +
            "    JOIN {h-schema}view_role_full_admin ON users.id = view_role_full_admin.user_id \n" +
            "WHERE \n" +
            "    users.deleted_flag = 1 ";

    public static final String CHECK_CAN_SEND_EMAIL =
        "SELECT count(an.id) > 0 "
            + "FROM {h-schema}action_notification an WHERE an.action_code ILIKE ('%' || :code || '%') AND an.is_send_email = 1";

    public static final String GET_ALL_USER_BY_ROLE =
        "SELECT "
            + "    DISTINCT u.* "
            + "FROM "
            + "    {h-schema}users u "
            + "INNER JOIN {h-schema}users_roles ur ON "
            + "    u.id = ur.user_id "
            + "INNER JOIN {h-schema}role r ON "
            + "    r.id = ur.role_id "
            + "    and r.status = 1 "
            + "WHERE "
            + "    r.name IN (:names) "
            + "    AND u.status = 1 "
            + "    AND u.deleted_flag = 1";

    public static final String GET_ALL_VALID_USER_FOR_COUPON_EMAIL =
        "SELECT \n" +
            "    DISTINCT users.* \n" +
            "FROM \n" +
            "    {h-schema}users \n" +
            "    JOIN {h-schema}view_role_sme ON users.id = view_role_sme.user_id \n" +
            "WHERE \n" +
            "    users.status = 1 \n" +
            "    AND users.deleted_flag = 1\n" +
            "    and \n" +
            "    (\n" +
            "        -- nếu status_confirm_data_policy is false thì vẫn chọn \n" +
            "        coalesce(users.status_confirm_data_policy, false) is false or\n" +
            "        ( -- chỉ chọn nếu confirmed_data_policies chứa 5 && status_confirm_data_policy = true \n" +
            "            users.status_confirm_data_policy is true \n" +
            "            and 5 = any(cast(string_to_array(replace(replace(users.confirmed_data_policies, '[', ''), ']', ''), ',') as int[]))\n" +
            "        ) \n" +
            "    )";

    public static final String GET_ENTERPRISE_BY_COUPON_ID =
        "SELECT \n" +
            "    users.* \n" +
            "FROM \n" +
            "    {h-schema}users \n" +
            "    JOIN {h-schema}coupon_enterprise ON coupon_enterprise.user_id = users.id AND users.deleted_flag = 1 \n" +
            "WHERE \n" +
            "    coupon_enterprise.coupon_id = :couponId \n" +
            "    and \n" +
            "    (\n" +
            "        -- nếu status_confirm_data_policy is false thì vẫn chọn \n" +
            "        coalesce(users.status_confirm_data_policy, false) is false or\n" +
            "        ( -- chỉ chọn nếu confirmed_data_policies chứa 5 && status_confirm_data_policy = true \n" +
            "            users.status_confirm_data_policy is true \n" +
            "            and 5 = any(cast(string_to_array(replace(replace(users.confirmed_data_policies, '[', ''), ']', ''), ',') as int[]))\n" +
            "        ) \n" +
            "    )";

    public static final String GET_SUPPLIER_BY_COUPON_ID =
        "SELECT "
            + "       u.* "
            + " FROM "
            + "     {h-schema}users u "
            + " JOIN {h-schema}coupon_supplier cs "
            + "    ON u.id = cs.user_id "
            + "        AND u.deleted_flag = 1 "
            + " WHERE cs.coupon_id = :couponId";

    public static final String GET_COUPON_OWNER =
        "SELECT  "
            + "    u.*  "
            + "FROM  "
            + "    {h-schema}users u  "
            + "WHERE  "
            + "    u.id = :userId  "
            + "    AND u.deleted_flag = 1";

    public static final String GET_LIST_USER_WITH_SAME_COMPANNY_BY_USERID =
            "WITH RECURSIVE cte as ( "
            + " SELECT u.*, 0 as level "
            + "     FROM {h-schema}users u WHERE u.id IN ( "
            + "         WITH RECURSIVE cte_sup AS ( "
            + "             SELECT u.*, 0 AS level FROM {h-schema}users u WHERE u.id =:userId "
            + "             UNION ALL "
            + "             SELECT e.*, cte_sup.level + 1 FROM {h-schema}users e JOIN cte_sup ON e.id = cte_sup.parent_id ) "
            + "         SELECT id FROM cte_sup WHERE parent_id = -1   "
            + "     ) "
            + " UNION ALL "
            + " SELECT e.*, cte.level + 1 FROM {h-schema}users e JOIN cte ON e.parent_id = cte.id ) "
            + " SELECT * FROM cte  ORDER BY level , id ";

    public static final String GET_PROVINCE_ID_OF_DEPARTMENT_BY_USER_ID =
        "SELECT "
            + "    d.province_id "
            + "FROM "
            + "    {h-schema}users u "
            + "JOIN {h-schema}departments d ON "
            + "    u.department_id = d.id "
            + "    AND u.deleted_flag = 1 "
            + "    AND d.deleted_flag = 1 "
            + "    AND u.id = :userId";

    public static final String GET_ALL_USER_FOR_SEND_NOTIFY_SUB =
        "select distinct  "
            + "    u.id as id, "
            + "    u.email as email, "
            + "    concat(u.last_name, ' ' , u.first_name) as fullName, "
            + "    'admin' as portal "
            + "from "
            + "    {h-schema}users u "
            + "inner join {h-schema}users_roles ur on "
            + "    u.id = ur.user_id "
            + "inner join {h-schema}departments d on  "
            + "    u.department_id = d.id  "
            + "inner join {h-schema}users u2 on  "
            + "    u2.id = d.created_by  "
            + "inner join {h-schema}users_roles ur2 on "
            + "    u2.id = ur2.user_id "
            + "where "
            + "    ur.role_id in (1, 2, 5) "
            + "    and ur2.role_id in (1, 2, 5) "
            + "    and u.deleted_flag = 1 "
            + "    and u.status = 1 "
            + "    and (d.province_id = :provinceId "
            + "        or d.department_code = :departmentCode) "
            + "union all  "
            + "select  "
            + "    u.id as id, "
            + "    u.email as email, "
            + "    concat(u.last_name, ' ' , u.first_name) as fullName, "
            + "    'dev' as portal "
            + "from "
            + "    {h-schema}users u "
            + "where u.id = :userDevId ";

    public static final String GET_DEV_USER_FOR_SEND_NOTIFY_SUB =
        "SELECT  \n"
            + "    u.id AS id, \n"
            + "    u.email AS email, \n"
            + "    CONCAT_WS(' ', u.last_name, u.first_name) AS fullName, \n"
            + "    'dev' AS portal \n"
            + "FROM \n"
            + "    {h-schema}users u \n"
            + "WHERE u.id = :userDevId ";

    public static final String GET_DEV_EMPLOYEE_FOR_SEND_NOTIFY_SUB =
        "SELECT  \n"
            + "    u.id AS id, \n"
            + "    u.email AS email, \n"
            + "    CONCAT_WS(' ', u.last_name, u.first_name) AS fullName, \n"
            + "    'dev' AS portal \n"
            + "FROM \n"
            + "    {h-schema}users u \n"
            + "WHERE u.id = :userId AND u.parent_id = :parentId ";

    public static final String GET_ALL_PROVINCE_ADMIN_FOR_SEND_NOTIFY_SUB =
        "SELECT DISTINCT  \n"
            + "    u.id AS id, \n"
            + "    u.email AS email, \n"
            + "    CONCAT_WS(' ', u.last_name, u.first_name) AS fullName, \n"
            + "    'admin' AS portal \n"
            + "FROM  \n"
            + "    {h-schema}users u \n"
            + "INNER JOIN {h-schema}users_roles ur ON \n"
            + "    u.id = ur.user_id \n"
            + "INNER JOIN {h-schema}departments d ON  \n"
            + "    u.department_id = d.id  \n"
            + "INNER JOIN {h-schema}users u2 ON  \n"
            + "    u2.id = d.created_by  \n"
            + "INNER JOIN {h-schema}users_roles ur2 ON \n"
            + "    u2.id = ur2.user_id \n"
            + "WHERE \n"
            + "    ur.role_id in (1, 2, 5) \n"
            + "    AND ur2.role_id in (1, 2, 5) \n"
            + "    AND u.deleted_flag = 1 \n"
            + "    AND u.status = 1 \n"
            + "    AND (d.province_id = :provinceId \n"
            + "        OR d.department_code = :departmentCode) ";

    public static final String GET_USER_SME_FOR_SEND_NOTIFY_SUB =
        "select distinct\n" +
            "    users.id as id, \n" +
            "    users.email as email, \n" +
            "    case \n" +
            "        when \n" +
            "            role.name::::text = ANY (ARRAY['ROLE_DEVELOPER'::::text, 'ROLE_ADMIN'::::text])\n" +
            "            then concat_ws(' ' , users.last_name, users.first_name) -- ADMIN OR DEV\n" +
            "        when \n" +
            "            role.name::::text = 'ROLE_SME'::::text and users.customer_type = 'CN' \n" +
            "            then concat_ws(' ' , users.last_name, users.first_name) -- SME CN\n" +
            "        when \n" +
            "            role.name::::text = 'ROLE_SME'::::text and users.customer_type <> 'CN' \n" +
            "            then coalesce(users.name, concat_ws(' ' , users.last_name, users.first_name)) -- SME DN\n" +
            "    end as fullName,\n" +
            "    'sme' as portal \n" +
            "from \n" +
            "    {h-schema}users\n" +
            "    join {h-schema}users_roles on users.id = users_roles.user_id\n" +
            "    join {h-schema}role ON role.id = users_roles.role_id\n" +
            "where \n" +
            "    role.name::::text = ANY (ARRAY['ROLE_DEVELOPER'::::text, 'ROLE_ADMIN'::::text, 'ROLE_SME'::::text]) and\n" +
            "    users.id = :userId and \n" +
            "    users.deleted_flag = 1 and \n" +
            "    users.status = 1 \n" +
            "limit 1";


    public static final String GET_LIST_ADMIN_BY_PROVINCE_ID =
        "SELECT "
            + "    DISTINCT u.* "
            + "FROM "
            + "    {h-schema}users u "
            + "JOIN {h-schema}departments d ON "
            + "    u.department_id = d.id "
            + "JOIN {h-schema}users_roles ur ON "
            + "    ur.user_id = u.id "
            + "WHERE "
            + "    d.province_id = :provinceId "
            + "    AND ur.role_id IN (1, 2, 5) "
            + "UNION ALL "
            + "SELECT "
            + "    DISTINCT u.* "
            + "FROM "
            + "    {h-schema}users u "
            + "JOIN {h-schema}departments d ON "
            + "    u.department_id = d.id "
            + "JOIN {h-schema}users_roles ur ON "
            + "    ur.user_id = u.id "
            + "WHERE "
            + "    d.department_code = :departmentCode "
            + "    AND ur.role_id IN (1, 2, 5) ";

    public static final String GET_USER_SME_TO_SUB =
        "SELECT  DISTINCT "
            + "    u.id, "
            + "    CASE  "
            + "        WHEN u.name IS NOT NULL THEN u.name "
            + "        ELSE concat(u.last_name,' ' ,u.first_name) "
            + "    END AS name,  "
            + "    CASE  "
            + "        WHEN u.rep_fullname IS NOT NULL THEN u.rep_fullname "
            + "        WHEN u.customer_type = 'CN' THEN concat(u.last_name,' ' ,u.first_name) "
            + "    END AS adminName,"
            + "    case "
            + "    when REPLACE (u.tin, ' ','') ~ '^\\d+(\\.\\d+)?$' = true then CAST(CAST(REPLACE (u.tin, ' ','') AS NUMERIC) AS BIGINT) "
            + "    else 9999999999999999999 "
            + "    end as tax, "
            + "    u.tin as tin, "
            + "    u.rep_personal_cert_number as repPersonalCertNumber, "
            + "    coalesce(u.customer_type, 'KHDN') as customerType "
            + "FROM {h-schema}users u  "
            + "JOIN {h-schema}users_roles ur on u.id = ur.user_id and u.deleted_flag =1 and u.status = 1 "
            + "WHERE  "
            + "    ur.role_id = 3 and (:customerType = 'CN' OR u.parent_id = -1) and "
            + "    (:name = '' or u.\"name\" ILIKE ('%' || :name || '%') ) and  "
            + "    (:tin = '' or u.tin ILIKE ('%' || :tin || '%') ) and  "
            + "    (:adminName = '' or (u.rep_fullname ILIKE ('%' || :adminName || '%') and u.rep_fullname is not null) or \n"
            + "    (concat(u.last_name,' ' ,u.first_name) ILIKE ('%' || :adminName || '%') and u.rep_fullname is null and u.customer_type = 'CN')) and "
            + "    (:repPersonalCertNumber = '' or u.rep_personal_cert_number ILIKE ('%' || :repPersonalCertNumber || '%') ) and "
            + "    (:customerType = '' OR u.customer_type = :customerType) ";

    public static final String GET_USER_PROVINCE_SME_TO_SUB =
        "SELECT "
            + "    p.id, "
            + "    p.\"name\" as provinceName, "
            + "    p.code as provinceCode "
            + "FROM  {h-schema}province p "
            + "WHERE "
            + "    (:provinceName = '' or p.\"name\" ILIKE ('%' || :provinceName || '%')) ";

    public static final String GET_MENU_REPORT_OF_USER =
            "SELECT distinct(mr.*) FROM {h-schema}users u " +
            "JOIN {h-schema}users_roles ur ON u.id = ur.user_id " +
            "JOIN {h-schema}roles_permissions rp ON ur.role_id = rp.role_id " +
            "JOIN {h-schema}permission p ON rp.permission_id = p.id " +
            "JOIN {h-schema}menu_report mr ON p.id = mr.permission_id " +
            "WHERE u.id = :user_id AND mr.status = 1"
            ;

    public static final String GET_LIST_USER_FILTER_INFO =
        "SELECT u.id, concat(u.last_name, ' ', u.first_name ) as name, u.province_id as provinceId, u.department_id as departmentId " +
            " FROM {h-schema}users u " +
            " WHERE " +
            " (-1 IN (:userIds) OR u.id IN (:userIds)) " +
            " AND (:username = '' OR concat(u.last_name, ' ', u.first_name ) ILIKE ('%' || :username || '%'))" +
            " AND u.id NOT IN (:userIdsNotIn) " +
            " AND (-1 IN (:departmentIds) OR u.department_id IN (:departmentIds)) " +
            " AND status = 1 AND deleted_flag = 1" +
            "	and u.id in (" +
            "  	select u.id from {h-schema}users u" +
            "	LEFT JOIN {h-schema}users_roles ur ON u.id = ur.user_id" +
            "	LEFT JOIN {h-schema}role r ON r.id = ur.role_id" +
            "	LEFT JOIN {h-schema}role_portal rp ON rp.role_id = r.id" +
            "	where rp.portal_id =1" +
            "	)" +
            " ORDER BY name ";

    public static final String GET_SME_FOR_ERROR_INTEGRATION =
        "SELECT NEW com.onedx.common.dto.integration.backend.IntegrationSmeDTO(" +
            "u.id, icon.externalLink, " +
            "cover.externalLink, u.name, " +
            "u.phoneNumber, u.email, u.nationId, " +
            "n.name, u.provinceId, p.name, " +
            "u.districtId, d.name, w.id, w.name, sd.id, sd.name, u.address, " +
            "u.webSite, u.description, u.corporateTaxCode, " +
            "u.businessSize, bs.name, u.businessAreas, ba.name, u.socialInsuranceNumber, u.isEdit) " +
            "FROM User u " +
            "LEFT JOIN FileAttach icon " +
            "ON icon.userId = u.id " +
            "AND icon.objectType = 3 " +
            "LEFT JOIN FileAttach cover " +
            "ON cover.userId = u.id " +
            "AND cover.objectType = 4 " +
            "LEFT JOIN BusinessSize bs " +
            "ON bs.id = u.businessSize " +
            "LEFT JOIN BusinessAreas ba " +
            "ON ba.id = u.businessAreas " +
            "LEFT JOIN Nation n " +
            "ON n.id = u.nationId " +
            "LEFT JOIN District d " +
            "ON d.id = u.districtId " +
            "AND u.provinceCode = d.provinceCode " +
            "LEFT JOIN Province p " +
            "ON p.id = u.provinceId " +
            "LEFT JOIN Ward w " +
            "ON w.id = u.wardId " +
            "AND w.provinceCode = u.provinceCode " +
            "LEFT JOIN Street sd ON sd.id = u.streetId AND sd.wardId = u.wardId AND sd.districtId = u.districtId AND sd.provinceCode = u.provinceCode \n" +
            "WHERE u.id = :id AND u.deletedFlag = 1 ";

    public static final String GET_EMPLOYEE_STAT =
            "with time_label as \n" +
            "    ( \n" +
            "        select \n" +
            "            to_char(cast(date_trunc('month', gen_month) as date), 'yyyy-MM') as time_label, \n" +
            "            case \n" +
            "                when cast(date_trunc('month', gen_month) as date) >= cast(:fromDate as date) \n" +
            "                    then cast(date_trunc('month', gen_month) as date) \n" +
            "                else cast(:fromDate as date) \n" +
            "            end as start_time, \n" +
            "            case \n" +
            "                when cast(date_trunc('month', gen_month) as date) + interval '1 month' - interval '1 day' <= cast(:toDate as date) \n" +
            "                    then cast((date_trunc('month', gen_month) + interval '1 month' - interval '1 day') as date) \n" +
            "                else cast(:toDate as date) \n" +
            "            end as end_time \n" +
            "        from \n" +
            "            generate_series(cast(:fromDate as date), cast(:toDate as date),  '1 month') as gen_month \n" +
            "    ) \n" +
            "select \n" +
            "    vTimeLabel.time_label as month, \n" +
            "    count(distinct(mUsers.id)) as numNew, \n" +
            "    count(distinct(mUsers.id)) filter (where mUsers.status = 1) as numActive, \n" +
            "    count(distinct(mUsers.id)) filter (where mUsers.status = 0) as numInactive \n "     +
            "from \n" +
            "    time_label as vTimeLabel \n" +
            "    left join {h-schema}users as mUsers on cast(mUsers.created_at as date) >= vTimeLabel.start_time and  \n" +
            "        cast(mUsers.created_at as date) <= vTimeLabel.end_time and  \n" +
            "        mUsers.parent_id = :smeId \n" +
            "group by vTimeLabel.time_label, vTimeLabel.start_time \n" +
            "order by vTimeLabel.start_time ";

    public static final String GET_EMPLOYEE_STAT_BY_MONTH =
            "select " +
            "    mUsers.email as email, " +
            "    mUsers.tech_id as techId, " +
            "    concat(mUsers.last_name, ' ', mUsers.first_name) as name, " +
            "    mUsers.phone_number as phone, " +
            "    mDepartments.department_name as department, " +
            "    case " +
            "        when mUsers.status = 0 " +
            "            then 'Không hoạt động' " +
            "        else 'Hoạt động' " +
            "    end as status, " +
            "    cast(mUsers.created_at as date) as createdDate, " +
            "    cast(mUsers.modified_at as date) as updatedDate, " +
            "    null as closedDate " +
            "from " +
            "    {h-schema}users as mUsers" +
            "    left join {h-schema}departments as mDepartments on mUsers.department_id = mDepartments.id " +
            "where  " +
            "    mUsers.parent_id = :smeId and " +
            "    to_char(cast(mUsers.created_at as date), 'yyyy-MM') = to_char(date(:month), 'yyyy-MM') " +
            "order by mUsers.created_at asc ";

    public static final String GET_LIST_ADMIN_BY_PROVINCE_IDS =
        " SELECT distinct u FROM User u "
            + " JOIN Department d ON "
            + "    u.departmentId = d.id "
            + " JOIN UserRoles ur ON "
            + "    ur.userId = u.id "
            + " WHERE "
            + "    d.provinceId IN :provinceIds "
            + "    AND ur.roleId IN (1, 2, 5) ";

    public static final String GET_SME_ADMIN_BY_ID =
            "    select\n"
                + "\t distinct u.*\n"
                + "from\n"
                + "\t{h-schema}users u\n"
                + "join {h-schema}users_roles ur on\n"
                + "\tur.user_id = u.id\n"
                + "where\n"
                + "\t(u.id = :userId\n"
                + "\t\tor u.parent_id = :userId)\n"
                + "\tand ur.role_id not in (1, 2, 5)";

    public static final String GET_LIST_USER_NOT_ACTIVATED_AND_BEFORE_DATE =
            " SELECT " +
                    " u.*  " +
                    "FROM " +
                    " {h-schema}users AS u  " +
                    "WHERE " +
                    " u.status = 0  " +
                    " AND (u.activation_key is not null OR u.activation_key <> '') " +
                    " AND to_char(u.created_at,'YYYY-MM-DD') <= :date";

    public static final String GET_LIST_USER_NOT_ACTIVATED_BY_TINS =
            "SELECT * FROM {h-schema}users AS u WHERE u.activation_key <> '' AND u.email IS NOT NULL and u.id in :userIds";

    public static final String GET_LIST_USER_SME_INACTIVE =
            "WITH list_id_in_user_roles AS ( " +
                    "  SELECT DISTINCT ur.user_id  " +
                    " FROM  " +
                    "  {h-schema}users_roles ur  " +
                    " WHERE  " +
                    "  ur.role_id IN (3) " +
                    ") " +
                    "SELECT v1.*    " +
                    "FROM (SELECT u.* FROM {h-schema}users as u    " +
                    "   WHERE to_char(u.last_login,'YYYY-MM-DD') <= :date   " +
                    "     AND u.status = 1  " +
                    "     AND u.id IN ( SELECT * FROM list_id_in_user_roles)) as v1   " +
                    "INNER JOIN (SELECT * FROM (SELECT  u.id FROM {h-schema}users as u   " +
                    "   LEFT JOIN (select count(1) as count, user_id from {h-schema}subscriptions group by user_id) as s on s.user_id = u.id    " +
                    "   WHERE s.count is null   " +
                    "      AND u.status = 1  " +
                    "   UNION    " +
                    "   SELECT s.user_id as id   " +
                    "    FROM {h-schema}subscriptions as s    " +
                    "    WHERE s.user_id NOT IN (SELECT s.user_id FROM {h-schema}subscriptions as s WHERE  s.confirm_status = 1 AND s.deleted_flag = 1 AND (s.status = 0 or s.status = 1 or s.status = 2) GROUP BY s.user_id) GROUP BY s.user_id) as a GROUP BY id ) as v2 on v1.id = v2.id  " ;

    public static final String GET_LIST_USER_DEV_INACTIVE =
            "WITH list_id_in_user_roles AS ( " +
                    "  SELECT DISTINCT ur.user_id  " +
                    " FROM  " +
                    "  {h-schema}users_roles ur  " +
                    " WHERE  " +
                    "  ur.role_id IN (4) " +
                    ") " +
                    " SELECT u.* " +
                    "   FROM {h-schema}users as u   " +
                    "   WHERE to_char(u.last_login,'YYYY-MM-DD') <= :date   " +
                    "     AND u.status = 1  " +
                    "     AND u.id IN ( SELECT * FROM list_id_in_user_roles)   " +
                    "     AND u.id not in (SELECT DISTINCT s.user_id   " +
                    "         FROM {h-schema}services as s   " +
                    "         WHERE s.id in (SELECT DISTINCT s.service_id   " +
                    "             FROM {h-schema}subscriptions as s   " +
                    "             WHERE  s.confirm_status = 1 AND s.deleted_flag = 1 AND (s.status = 0 or s.status = 1 or s.status = 2) )) " ;

    public static final String CHANGE_STATUS_USER =
            "UPDATE {h-schema}users SET status = :status WHERE id in :ids";

    public static final String GET_ACTION_ACCOUNT_HISTORY =
        "SELECT \n" +
            "      history.id AS id, \n" +
            "      history.content AS content, \n" +
            "      history.userId AS userId, \n" +
            "      history.actionName AS actionName, \n" +
            "      history.createdAt AS createdAt, \n" +
            "      CASE \n" +
            "           WHEN history.actorId = 0 THEN 'Hệ thống tự động' \n" +
            "           ELSE CONCAT_WS(' ', users.last_name, users.first_name) \n" +
            "      END AS name, \n" +
            "      CASE \n" +
            "           WHEN history.actorId = 0 THEN 'Hệ thống tự động' \n" +
            "           ELSE users.email \n" +
            "      END AS email\n," +
            "      CASE \n"+
            "            WHEN vAdminId.user_id IS NOT NULL THEN 'Admin' \n"+
            "            WHEN vDevId.user_id IS NOT NULL THEN 'Dev' \n"+
            "            WHEN vSMEId.user_id IS NOT NULL THEN 'SME' \n"+
            "            ELSE '' \n"+
            "      END AS actorRole, \n" +
            "      history.actionType AS actionType, \n" +
            "      history.isLogSub AS isLogSub, \n" +
            "      history.pricingName AS pricingName, \n" +
            "      history.serviceName AS serviceName \n "+
            "FROM \n" +
            "     (" +
            // Lấy lịch sử cập nhật tài khoản từ bảng account_update_history
            "           SELECT  \n" +
            "                    -1 AS id, \n" +
            "                    accHistory.user_id AS userId, \n" +
            "                    accHistory.created_by AS actorId, \n" +
            "                    accHistory.created_at AS createdAt, \n" +
            "                    CASE \n" +
            "                          WHEN accHistory.action = 1 THEN 'Tạo tài khoản' \n" +
            "                          WHEN accHistory.action = 2 THEN 'Sửa thông tin tài khoản' \n" +
            "                          WHEN accHistory.action = 3 THEN 'Bật tài khoản' \n" +
            "                          WHEN accHistory.action = 4 THEN 'Tắt tài khoản' \n" +
            "                          WHEN accHistory.action = 5 THEN 'Xóa tài khoản' \n" +
            "                          WHEN accHistory.action = 6 THEN 'Lưu trữ tài khoản' \n" +
            "                          WHEN accHistory.action = 7 THEN 'Bỏ lưu trữ tài khoản' \n" +
            "                          WHEN accHistory.action = 8 THEN 'Kích hoạt tài khoản' \n" +
            "                    END AS actionName, \n" +
            "                    accHistory.content AS content, \n" +
            "                    accHistory.action AS actionType, \n" +
            "                    false AS isLogSub, \n" +
            "                    '' AS pricingName, \n" +
            "                    '' AS serviceName \n" +
            "           FROM {h-schema}account_update_history AS accHistory \n" +
            "           WHERE (:isCurrentUserLog = true AND (accHistory.user_id = :id OR accHistory.created_by = :id)) OR" +
            "                     accHistory.user_id = :id \n" +
            // Lấy nhật ký hoạt động loại mới (Field Force) từ bảng action_history
            "      UNION ALL \n" +
            "           SELECT \n" +
            "                    actHistory.id AS id, \n" +
            "                    actHistory.object_id AS userId, \n" +
            "                    actHistory.created_by AS actorId, \n" +
            "                    actHistory.created_at AS createdAt, \n" +
            "                    actHistory.action_name AS actionName, \n" +
            "                    actHistory.content AS content, \n" +
            "                    -1 AS actionType, " +
            "                    false AS isLogSub, \n" +
            "                    '' AS pricingName, \n" +
            "                    '' AS serviceName \n" +
            "           FROM {h-schema}action_history AS actHistory \n" +
            "           WHERE \n" +
            "                     (:isCurrentUserLog = true AND ((actHistory.object_type IN (0,1) AND actHistory.object_id = :id) OR actHistory.created_by = :id)) OR \n" +
            "                     (actHistory.object_type IN (0,1) AND actHistory.object_id = :id) \n" +
            // Lấy lịch sử thao tác với thuê bao từ bảng subscription_history
            "       UNION ALL \n" +
            "            SELECT \n" +
            "                      -1 as id, \n" +
            "                      sub.user_id AS userId, \n" +
            "                      subHist.created_by AS actorId, \n" +
            "                      subHist.created_at AS createdAt, \n" +
            "                      '' AS actionName, \n" +
            "                      subHist.content AS content, \n" +
            "                      subHist.content_type AS actionType, \n" +
            "                      true AS isLogSub, \n" +
            "                      COALESCE(pricing.pricing_name, cbPlan.combo_name, '') AS pricingName, \n" +
            "                      COALESCE(services.service_name, combo.combo_name, '') AS serviceName \n" +
            "            FROM {h-schema}subscription_history subHist \n" +
            "                     LEFT JOIN {h-schema}subscriptions AS sub ON subHist.subscription_id = sub.id\n" +
            "                     LEFT JOIN {h-schema}pricing ON sub.pricing_id = pricing.id \n" +
            "                     LEFT JOIN {h-schema}services ON pricing.service_id = services.id \n" +
            "                     LEFT JOIN {h-schema}combo_plan AS cbPlan ON sub.combo_plan_id = cbPlan.id \n" +
            "                     LEFT JOIN {h-schema}combo ON cbPlan.combo_id = combo.id \n" +
            "            WHERE (:isCurrentUserLog = true AND subHist.created_by = :id) \n" +
            "    ) AS history \n" +
            "    LEFT JOIN {h-schema}users ON history.actorId = users.id \n" +
            "    LEFT JOIN {h-schema}view_role_admin vAdminId ON vAdminId.user_id = history.actorId \n"+
            "    LEFT JOIN {h-schema}view_role_dev vDevId ON vDevId.user_id = history.actorId \n"+
            "    LEFT JOIN {h-schema}view_role_sme vSMEId ON vSMEId.user_id = history.actorId \n"+
            "WHERE  \n"+
            "    (date('1970-01-01') = date(:startDate) OR date(history.createdAt) >= date(:startDate)) AND \n" +
            "    (date('1970-01-01') = date(:endDate) OR date(history.createdAt) <= date(:endDate))";

    public static final String GET_LIST_USER_ID_BY_ENTERPRISE_ID =
        "SELECT DISTINCT user_id FROM {h-schema}enterprise WHERE user_id is not null AND id IN (:enterpriseIds)";

    public static final String GET_LIST_USER_ACTIVE_BY_IDS =
            "SELECT * FROM {h-schema}users AS u WHERE u.activation_key is null AND u.id in :userIds";

    public static final String GET_USER_PERSONAL_BY_EMAIL =
        "SELECT * FROM {h-schema}users WHERE email = :email AND customer_type = 'CN' AND deleted_flag = 1 LIMIT 1";

    public static final String FIND_USER_BY_EMAIL_OR_PHONE =
        "SELECT u.* "
            + "FROM {h-schema}users u "
            + "WHERE "
            + "   u.deleted_flag = 1 "
            + "   AND u.customer_type = :customerType "
            + "   AND ((provider_type = 3 "
            + "      AND u.phone_number = :phone) "
            + "   OR u.email = :email) order by u.id desc limit 1";

    public static final String FIND_USER_BY_EMAIL_AND_CUSTOMER_TYPE =
        "SELECT u.* "
            + "FROM {h-schema}users u "
            + "WHERE "
            + "   u.deleted_flag = 1 "
            + "   AND coalesce(u.customer_type, 'KHDN') IN :customerTypes "
            + "   AND (u.email = :email) order by u.id desc limit 1";

    public static final String FIND_USER_BY_PHONE_AND_CUSTOMER_TYPE =
        "SELECT u.* "
            + "FROM {h-schema}users u "
            + "WHERE "
            + "   u.deleted_flag = 1 "
            + "   AND coalesce(u.customer_type, 'KHDN') IN :customerTypes "
            + "   AND (u.provider_type = 3 AND u.phone_number = :phone) order by u.id desc limit 1";

    public static final String UPDATE_USER_SUBSCRIPTION_STATUS =
        "with stats as ( \n" +
            "        -- Đếm số lượng thuê bao dùng thử/đang hoạt động \n" +
            "        select \n" +
            "            user_id, \n" +
            "            count(id) filter (where status = 1 and reg_type = 0) as num_trial, \n" +
            "            count(id) filter (where status = 2 and reg_type = 1) as num_active \n" +
            "        from \n" +
            "            {h-schema}subscriptions \n" +
            "        where \n" +
            "            deleted_flag = 1 and \n" +
            "            confirm_status = 1 \n" +
            "        group by user_id \n" +
            "    ), \n" +
            "    classification as ( \n" +
            "        -- Phân loại trạng thái thuê bao (dùng thử/hoạt động) của từng user \n" +
            "        select \n" +
            "            user_id, \n" +
            "            case \n" +
            "                when num_active > 0 then 1 \n" +
            "                when num_trial > 0 then 0 \n" +
            "                else -1 \n" +
            "            end as subscription_status \n" +
            "        from stats \n" +
            "    ) \n" +
            " \n" +
            "-- Cập nhật trạng thái thuê bao của user \n" +
            "update {h-schema}users \n" +
            "    set subscription_status = mClassification.subscription_status \n" +
            "from classification as mClassification \n" +
            "where \n" +
            "    users.id = mClassification.user_id and \n" +
            "    users.subscription_status is distinct from mClassification.subscription_status ";

    public static final String REFRESH_ENTERPRISE_USER_CLASSIFICATION =
        "DROP TABLE IF EXISTS {h-schema}mview_enterprise_user_classification; \n" +
            "CREATE TABLE {h-schema}mview_enterprise_user_classification AS \n" +
            "    SELECT * FROM {h-schema}view_enterprise_user_classification_202310; ";

    public static final String REFRESH_ENTERPRISE_USER_CLASSIFICATION_CHANGE =
        "DROP TABLE IF EXISTS {h-schema}mview_enterprise_user_classification_change; \n" +
            "CREATE TABLE {h-schema}mview_enterprise_user_classification_change AS \n" +
            "    SELECT \n" +
            "        enterprise.id as enterprise_id, \n" +
            "        enterprise.user_id as e_user_id, \n" +
            "        enterprise.num_subs as e_num_subs, \n" +
            "        enterprise.customer_state as e_customer_state, \n" +
            "        mClassification.user_id, \n" +
            "        mClassification.num_subs, \n" +
            "        mClassification.customer_state \n" +
            "    FROM {h-schema}enterprise \n" +
            "        LEFT JOIN {h-schema}mview_enterprise_user_classification AS mClassification ON mClassification.enterprise_id = enterprise.id \n" +
            "    WHERE \n" +
            "        mClassification.user_id IS DISTINCT FROM enterprise.user_id OR \n" +
            "        COALESCE(mClassification.num_subs, 0) IS DISTINCT FROM COALESCE(enterprise.num_subs, 0) OR \n" +
            "        mClassification.customer_state IS DISTINCT FROM enterprise.customer_state; ";

    public static final String UPDATE_ENTERPRISE_USER_MAPPING_AND_STATE =
        "UPDATE {h-schema}enterprise \n" +
            "    SET customer_state = mChange.customer_state, \n" +
            "    user_id = mChange.user_id, \n" +
            "    num_subs = mChange.num_subs \n" +
            "FROM {h-schema}mview_enterprise_user_classification_change AS mChange \n" +
            "WHERE enterprise.id = mChange.enterprise_id \n";

    public static final String GET_LIST_USER_ADMIN_SUB =
            "SELECT DISTINCT  u.id AS id, "
            + "       u.last_name AS lastName, "
            + "       u.first_name AS firstName, "
            + "       u.\"name\" AS name, "
            + "       u.rep_fullname AS fullName , "
            + "       u.customer_type AS customerType, "
            + "       u.email AS email,  "
            + "       u.province_Id AS provinceId  "
            + "FROM {h-schema}users u "
            + "LEFT JOIN {h-schema}departments d ON u.department_id = d.id "
            + "LEFT JOIN {h-schema}users_roles ur ON u.id = ur.user_id "
            + "WHERE d.province_id IS NULL "
            + "  AND ur.role_id in (:lstRoleAdmin)";

    public static final String FIND_BY_PORTAL_ID =
            "SELECT rp.role_id "
            + "FROM  {h-schema} role_portal rp "
            + "WHERE rp.portal_id = :portalId ";

    public static final String GET_LIST_ADMIN_BY_USER_ID =
            "SELECT DISTINCT  u.id AS id, "
                    + "       u.last_name AS lastName, "
                    + "       u.first_name AS firstName, "
                    + "       u.\"name\" AS name, "
                    + "       u.rep_fullname AS fullName , "
                    + "       u.customer_type AS customerType, "
                    + "       u.email AS email,  "
                    + "       u.province_Id AS provinceId  "
                    + "FROM {h-schema}users u "
                    + "LEFT JOIN {h-schema}departments d ON u.department_id = d.id "
                    + "LEFT JOIN {h-schema}users_roles ur ON u.id = ur.user_id "
                    + "WHERE d.province_id = :provinceId "
                    + "  AND ur.role_id in (:lstRoleAdmin)";

    public static final String GET_LIST_USER_IN_BAN_KHDN =
            "SELECT u.last_name AS lastName, "
                    + "       u.id AS id, "
                    + "       u.first_name AS firstName, "
                    + "       u.\"name\" AS name, "
                    + "       u.rep_fullname AS fullName , "
                    + "       u.customer_type AS customerType, "
                    + "       u.email AS email,  "
                    + "       u.province_Id AS provinceId  "
                    + "FROM {h-schema}users u  "
                    + "INNER JOIN {h-schema}departments d ON u.department_id = d.id  "
                    + "WHERE  LOWER(d.department_name) = LOWER(:departmentName)";


    public static final String FIND_USER_BY_DELETE_FLAG =
            "SELECT u.last_name AS lastName, "
            + "       u.id AS id, "
            + "       u.first_name AS firstName, "
            + "       u.\"name\" AS name, "
            + "       u.rep_fullname AS fullName , "
            + "       u.customer_type AS customerType, "
            + "       u.email AS email,  "
            + "       u.province_Id AS provinceId  "
            + "FROM {h-schema}users u "
            + "WHERE u.id = :userId "
            + "  AND u.deleted_flag = :deletedFlag";

    public static final String DELETE_USER_ROLE_BY_USER_ID =
            "DELETE "
                    + "FROM "
                    + "    {h-schema}users_roles ur "
                    + "WHERE "
                    + "  ur.user_id = :id ";

    public static final String GET_USER_ADMIN_BY_USER_ID =
            "SELECT * from {h-schema}users WHERE id in (\n" +
                    "SELECT DISTINCT ur.user_id  \n" +
                    " FROM {h-schema}users_roles ur \n" +
                    " JOIN {h-schema}role as r on r.id = ur.role_id\n" +
                    " WHERE r.name like '%ADMIN%') and id = :userId limit 1";

    public static final String GET_USER_INFO_RECEIVER_BY_ID_OF_SERVICE =
            "select \n" +
                    "    COALESCE(concat(u.last_name, ' ', u.first_name), u.name) as userName,\n" +
                    "    u.phone_number as phoneNumber,\n" +
                    "    (\n" +
                    "      SELECT\n" +
                    "           s.service_type\n" +
                    "      FROM {h-schema}services s\n" +
                    "      WHERE\n" +
                    "      s.id =:serviceId \n" +
                    "    ) AS serviceType,\n" +
                    "    (\n" +
                    "      SELECT\n" +
                    "           s.service_code\n" +
                    "      FROM {h-schema}services s\n" +
                    "      WHERE\n" +
                    "      s.id =:serviceId \n" +
                    "    ) AS serviceCode\n" +
                    "from {h-schema}users u \n" +
                    "where u.id = :userId";

    public static final String GET_USER_INFO_RECEIVER_BY_ID_OF_COMBO =
            "select \n" +
                    "    COALESCE(concat(u.last_name, ' ', u.first_name), u.name) as userName,\n" +
                    "    u.phone_number as phoneNumber \n" +
                    "from {h-schema}users u \n" +
                    "where u.id = :userId";

    public static final String GET_USER_INFO_ADDRESS_DEFAULT_BY_USER_ID =
            "SELECT\n" +
                    "    p.id as provinceId, \n" +
                    "    p.code as provinceCode, \n" +
                    "    p.name as provinceName, \n" +
                    "    d.id as districtId, \n" +
                    "    d.code as districtCode, \n" +
                    "    d.name as districtName, \n" +
                    "    w.id as wardId, \n" +
                    "    w.code as wardCode, \n" +
                    "    w.name as wardName, \n" +
                    "    sd.id as streetId,\n" +
                    "    sd.name as streetName,\n" +
                    "    u.last_name as lastName,\n" +
                    "    u.first_name as firstName,\n" +
                    "    u.rep_personal_cert_number as repPersonalCertNumber,\n" +
                    "    u.tin as tin,\n" +
                    "    u.name as name,\n" +
                    "    u.address as address\n" +
                    "FROM {h-schema}users u \n" +
                    "LEFT JOIN {h-schema}province p ON p.code = u.province_code \n" +
                "LEFT JOIN {h-schema}street sd ON sd.id = u.street_id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id AND sd.province_code = p.code \n" +
                    "LEFT JOIN {h-schema}district d ON d.id = u.district_id AND d.province_id = p.id \n" +
                    "LEFT JOIN {h-schema}ward w ON w.id = u.ward_id AND w.district_id = d.id AND w.province_code = p.code \n" +
                    "WHERE u.id = :userId";


    public static final String GET_DETAIL_ADDRESS =
        "SELECT\n" +
            "    p.id as provinceId, \n" +
            "    a.id as id, \n" +
            "    p.code as provinceCode, \n" +
            "    p.name as provinceName, \n" +
            "    d.id as districtId, \n" +
            "    d.code as districtCode, \n" +
            "    d.name as districtName, \n" +
            "    w.id as wardId, \n" +
            "    w.code as wardCode, \n" +
            "    w.name as wardName, \n" +
            "    sd.id as streetId,\n" +
            "    sd.name as streetName,\n" +
            "    u.last_name as lastName,\n" +
            "    u.first_name as firstName,\n" +
            "    u.rep_personal_cert_number as repPersonalCertNumber,\n" +
            "    u.tin as tin,\n" +
            "    u.name as name,\n" +
            "    u.address as address\n" +
            "FROM {h-schema}users u \n" +
            "JOIN {h-schema}address a ON u.id = a.user_id\n" +
            "LEFT JOIN {h-schema}province p ON p.id = a.province_id \n" +
            "LEFT JOIN {h-schema}street sd ON sd.id = a.street_id AND sd.ward_id = a.ward_id AND sd.district_id = u.district_id AND sd.province_code = p.code \n" +
            "LEFT JOIN {h-schema}district d ON d.id = a.district_id AND d.province_id = p.id \n" +
            "LEFT JOIN {h-schema}ward w ON w.id = a.ward_id AND w.district_id = d.id AND w.province_code = p.code \n" +
            "WHERE a.id IN (:ids)";

    public static final String GET_LIST_ASSIGNEE_CONTACT =
        "select distinct \n" +
            "     u_assignee.id as id, \n" +
            "     concat_ws (' ', u_assignee.last_name, u_assignee.first_name) as name\n" +
            "from {h-schema}customer_contact contact\n" +
            "     join {h-schema}users u_assignee on contact.assignee_id = u_assignee.id \n" +
            "where (('' = :assigneeName) or \n" +
            "     concat_ws (' ', u_assignee.last_name, u_assignee.first_name) ilike ('%' || :assigneeName || '%')) ";

    public static final String IS_ROLE_ADMIN =
        "SELECT exists (SELECT user_id FROM {h-schema}users_roles WHERE user_id = :userId AND role_id IN (:roleAdminIds))";

    public static final String GET_LIST_ADMIN_BY_TARGET_ID =
        "SELECT DISTINCT users.* FROM {h-schema}users JOIN {h-schema}crm_revenue_target_value ON admin_id = users.id WHERE target_id = :targetId";

    /**
     * Nếu targetValue = null --> targetValue = 0
     * actual_value (lấy từ func) >= targetValue - check trạng thái hoàn thành của mục tiêu
     * (actual_value (lấy từ func) >= targetValue) = false lấy ds các users chưa hoàn thành mục tiêu
     */
    public static final String GET_LIST_INCOMPLETE_TARGET_ADMIN =
        "SELECT DISTINCT \n"
            + "     users.* \n"
            + "FROM \n"
            + "     {h-schema}users \n"
            + "     JOIN {h-schema}crm_revenue_target_value ON admin_id = users.id \n"
            + "     JOIN {h-schema}feature_view_get_actual_revenue_subscriptions AS mTargetView \n"
            + "         ON crm_revenue_target_value.id = mTargetView.target_value_id \n"
            + "WHERE \n"
            + "     mTargetView.target_id = :targetId \n"
            + "     AND \n"
            + "     (( \n"
            + "          SELECT \n"
            + "               amount\n"
            + "          FROM \n"
            + "               {h-schema}func_get_actual_revenue_by_target_value_id(mTargetView.object_type, mTargetView.start_date, mTargetView.end_date, mTargetView.target_value_id, mTargetView.target_type) \n"
            + "     ) >= COALESCE(mTargetView.target_value, 0)) = false \n";

    public static final String GET_LIST_PARTITION_ADMIN_BY_TARGET_ID =
        "SELECT DISTINCT \n"
            + "     users.* \n"
            + "FROM \n"
            + "     {h-schema}users \n"
            + "     JOIN {h-schema}crm_data_partition ON users.id = ANY(crm_data_partition.lst_admin_id) \n"
            + "     JOIN {h-schema}crm_revenue_target_value ON partition_id = crm_data_partition.id \n"
            + "WHERE \n"
            + "     crm_revenue_target_value.target_id = :targetId ";

    /**
     * Nếu targetValue = null --> targetValue = 0
     * actual_value (lấy từ func) >= targetValue - check trạng thái hoàn thành của mục tiêu
     * (actual_value (lấy từ func) >= targetValue) = false lấy ds các users chưa hoàn thành mục tiêu
     */
    public static final String GET_LIST_INCOMPLETE_TARGET_PARTITION_ADMIN =
        "SELECT DISTINCT \n"
            + "     users.* \n"
            + "FROM \n"
            + "     {h-schema}users \n"
            + "     JOIN {h-schema}crm_data_partition ON users.id = ANY(crm_data_partition.lst_admin_id) \n"
            + "     JOIN {h-schema}crm_revenue_target_value ON partition_id = crm_data_partition.id \n"
            + "     JOIN {h-schema}feature_view_get_actual_revenue_subscriptions AS mTargetView \n"
            + "         ON crm_revenue_target_value.id = mTargetView.target_value_id \n"
            + "WHERE \n"
            + "     mTargetView.target_id = :targetId \n"
            + "     AND \n"
            + "     (( \n"
            + "          SELECT \n"
            + "               amount\n"
            + "          FROM \n"
            + "               {h-schema}func_get_actual_revenue_by_target_value_id(mTargetView.object_type, mTargetView.start_date, mTargetView.end_date, mTargetView.target_value_id, mTargetView.target_type) \n"
            + "     ) >= COALESCE(mTargetView.target_value, 0)) = false \n";

    public static final String FIND_LIST_USER_ID_BY_EMAIL_ID =
        "SELECT email as email, id as userId from {h-schema}users where email in (:emails) and affiliate_type is not null";

    public static final String FIND_FCM_TOKEN_BY_USER_ID =
        "SELECT token FROM {h-schema}fcm_token WHERE user_id = :userId ";

    public static final String FIND_FCM_TOKEN_DTO_BY_USER_ID =
        "SELECT token, client_id as clientId FROM {h-schema}fcm_token WHERE user_id = :userId ";

    public static final String FIND_FCM_TOKEN_BY_USER_ID_IN =
        "SELECT user_id as userId, token, client_id as clientId FROM {h-schema}fcm_token WHERE user_id IN (:setUserId) ";

    public static final String FIND_LIST_USER_AFFILIATE_PARENT_BY_CODE =
        "select users.email as email, \n" +
        "    users.id as userId, \n" +
        "    users.phone_number as phoneNumber, \n" +
        "    users.name as name, \n" +
        "    affiliate_users.affiliate_code as affiliateCode \n" +
        "from {h-schema}users \n" +
        "left join {h-schema}affiliate_users on users.id = affiliate_users.user_id \n" +
        "where affiliate_users.affiliate_code in (:lstParentAffiliateCode)";

    public static final String GET_ALL_AFFILIATE_PHONE_NUMBER =
        "select users.phone_number \n" +
        "from {h-schema}users \n" +
        "    left join {h-schema}users_roles on users.id = users_roles.user_id \n" +
        "    left join {h-schema}role on role.id = users_roles.role_id \n" +
        "where \n" +
        "    role.name in ('ROLE_AFFILIATE_DAILY','ROLE_AFFILIATE_CANHAN') and \n" +
        "    users.deleted_flag = 1 and users.phone_number is not null";

    public static final String GET_ALL_ADMIN_EMAIL =
        "select distinct on (users.email) users.id as userId, users.email as email \n" +
            "from {h-schema}users \n" +
            "    left join {h-schema}users_roles on users.id = users_roles.user_id \n" +
            "where \n" +
            "    users_roles.role_id in (:lstRoleIdAdmin) and \n" +
            "    users.deleted_flag = 1 and users.email is not null";

    public static final String GET_ALL_DEV_OR_AFFILIATE_EMAIL =
        "select users.email \n" +
            "from {h-schema}users \n" +
            "    left join {h-schema}users_roles on users.id = users_roles.user_id \n" +
            "    left join {h-schema}role on role.id = users_roles.role_id \n" +
            "where \n" +
            "    role.name in ('ROLE_AFFILIATE_DAILY','ROLE_AFFILIATE_CANHAN','ROLE_DEVELOPER','ROLE_DEVELOPER_OPERATOR','ROLE_DEVELOPER_BUSINESS','ROLE_FULL_DEV', 'FULL_ADMIN') and \n" +
            "    users.deleted_flag = 1 and users.email is not null";

    public static final String GET_ALL_TIN_AFFILIATE =
        "select users.tin \n" +
            "from {h-schema}users \n" +
            "    left join {h-schema}users_roles on users.id = users_roles.user_id \n" +
            "    left join {h-schema}role on role.id = users_roles.role_id \n" +
            "where \n" +
            "    role.name in ('ROLE_AFFILIATE_DAILY','ROLE_AFFILIATE_CANHAN') and \n" +
            "    users.deleted_flag = 1 and users.tin is not null";

    public static final String GET_ALL_IDENTITY_NO_AFFILIATE =
        "select users.rep_personal_cert_number \n" +
            "from {h-schema}users \n" +
            "    left join {h-schema}users_roles on users.id = users_roles.user_id \n" +
            "    left join {h-schema}role on role.id = users_roles.role_id \n" +
            "where \n" +
            "    role.name in ('ROLE_AFFILIATE_DAILY','ROLE_AFFILIATE_CANHAN') and \n" +
            "    users.deleted_flag = 1 and users.rep_personal_cert_number is not null";

    public static final String EXIST_USER_AFF =
            "select EXISTS ( " +
                    "     select * from {h-schema}affiliate_users au " +
                    "     where au.user_id = (:userId)" +
                    ")";

    public static final String EXIST_BY_EMAIL_IN_SYSTEM_AFF =

            "     select u.id from {h-schema}users u LEFT JOIN {h-schema}users_roles ur ON ur.user_id = u.id  " +
                    "     where u.deleted_flag = 1 and lower(u.email) = lower(:email) " +
                    "     and (-1 = :userId or u.id <> :userId) and ur.role_id IN (:roleAdminDevIds) LIMIT 1 "
            ;

    public static final String DELETED_BY_ID =
            "update {h-schema}users set deleted_flag = 0 where id = :id";

    public static final String FIND_NAME_AND_EMAIL_BY_ID =
        "select \n" +
            "    case \n" +
            "        when customer_type = 'CN' then concat_ws(' ', last_name, first_name) \n" +
            "        else name \n" +
            "    end as name, \n" +
            "    email \n" +
            "from {h-schema}users where id = :userId and deleted_flag = 1";

    public static final String COMBOBOX_SME_NAME =
        "with mSMENameResult as ( \n" +
            "    select distinct \n" +
            "        mUser.customer_type as customerType, \n" +
            "        case \n" +
            "            when mUser.customer_type = 'CN' then concat_ws(' ', mUser.last_name, mUser.first_name) \n" +
            "            else mUser.name \n" +
            "        end as name \n" +
            "    from {h-schema}users mUser \n" +
            "        join {h-schema}view_role_sme mRoleSME on mUser.id = mRoleSME.user_id \n" +
            "    where \n" +
            "        mUser.deleted_flag = 1 and \n" +
            "        mUser.status = 1 and \n" +
            "        (:customerType = '' or mUser.customer_type = :customerType) \n" +
            ") \n" +
            "select * \n" +
            "from mSMENameResult \n" +
            "where \n" +
            "    mSMENameResult.name is not null and \n" +
            "    (:name = '' or mSMENameResult.name ilike ('%' || :name || '%'))";

    public static final String COMBOBOX_SME_REP_PERSONAL_CERT_NUMBER =
        "select distinct \n" +
            "    mUser.rep_personal_cert_number as repPersonalCertNumber \n" +
            "from {h-schema}users mUser \n" +
            "    join {h-schema}view_role_sme mRoleSME on mUser.id = mRoleSME.user_id \n" +
            "where \n" +
            "    mUser.deleted_flag = 1 and \n" +
            "    mUser.status = 1 and \n" +
            "    mUser.rep_personal_cert_number is not null and \n" +
            "    (:customerType = '' or mUser.customer_type = :customerType) and \n" +
            "    (:repPersonalCertNumber = '' or mUser.rep_personal_cert_number ilike ('%' || :repPersonalCertNumber || '%'))";

    public static final String COMBOBOX_SME_REP_NAME =
        "select distinct \n" +
            "    mUser.rep_fullname as repName \n" +
            "from {h-schema}users mUser \n" +
            "    join {h-schema}view_role_sme mRoleSME on mUser.id = mRoleSME.user_id \n" +
            "where \n" +
            "    mUser.deleted_flag = 1 and \n" +
            "    mUser.status = 1 and \n" +
            "    mUser.rep_fullname is not null and \n" +
            "    --Chỉ KHDN, HKD có tên người đại diện \n" +
            "    (mUser.customer_type != 'CN') and \n" +
            "    (:repName = '' or mUser.rep_fullname ilike ('%' || :repName || '%'))";

    public static final String COMBOBOX_SME_TIN =
        "select distinct \n" +
            "    mUser.tin as tin \n" +
            "from {h-schema}users mUser \n" +
            "    join {h-schema}view_role_sme mRoleSME on mUser.id = mRoleSME.user_id \n" +
            "where \n" +
            "    mUser.deleted_flag = 1 and \n" +
            "    mUser.status = 1 and \n" +
            "    mUser.tin is not null and \n" +
            "    (:customerType = '' or mUser.customer_type = :customerType) and \n" +
            "    (:tin = '' or mUser.tin ilike ('%' || :tin || '%'))";

    public static final String INSERT_USERS_ROLES =
        "insert into {h-schema}users_roles (user_id, role_id) values (?,?) ";

    public static final String GET_LIST_DEV_OR_ADMIN_EMAIL =
        "select \n" +
        "    users.email \n" +
        "from {h-schema}users \n" +
        "    left join {h-schema}users_roles on users.id = users_roles.user_id \n" +
        "where users_roles.role_id in (:roleDevOrAdminIds)";

    public static final String IS_EMPLOYEE =
        "select exists (select id from {h-schema}mapping_sme_employee where sme_id = :smeId and employee_id = :employeeId limit 1)";

    public static final String GET_LIST_DEV_OR_ADMIN_PHONE =
        "select \n" +
        "    users.phone_number \n" +
        "from {h-schema}users \n" +
        "    left join {h-schema}users_roles on users.id = users_roles.user_id \n" +
        "where users_roles.role_id in (:roleDevOrAdminIds)";

    public static final String GET_USER_IN_LIST_IDS =
            "select * from {h-schema}users where users.id in (:listIds)";

    public static final String GET_IS_PERSONAL_CUSTOMER_BY_USER_ID =
        "select exists (\n" +
            "    select \n" +
            "        users.id \n" +
            "    from \n" +
            "        {h-schema}users \n" +
            "        join {h-schema}view_role_sme on view_role_sme.user_id = users.id\n" +
            "    where \n" +
            "        users.id = :userId\n" +
            "        and users.customer_type = 'CN'\n" +
            ")";

    public static final String GET_MENTEE_BY_USER_ID =
            "select id from {h-schema}users where parent_id = :userId union all select parent_id from {h-schema}users where id = :userId ";

    public static final String INSERT_OAUTH_CLIENT_DETAIL =
    "INSERT INTO {h-schema}oauth_client_details(client_id, client_secret, authorized_grant_types, access_token_validity, refresh_token_validity, autoapprove) \n" +
    "VALUES \n" +
    "(:apiKey, :clientSecret, 'email,profile,password,authorization_code,refresh_token,client_credentials', :tokenValidity, :refreshTokenValidity, true)";

    public static final String FIND_ALL_COUPONS_FOR_SUPPLIER =
        "with mCouponApplySupplier as (\n" +
            "    select\n" +
            "            coupons.id \n" +
            "    from {h-schema}coupons\n" +
            "    where \n" +
            "        user_id = :supplierId or pricing_type = -1 or supplier_type = -1 \n" +
            "    union\n" +
            "    select\n" +
            "        coupons.id \n" +
            "    FROM {h-schema}coupons\n" +
            "        JOIN {h-schema}coupon_supplier ON coupon_supplier.coupon_id = coupons.id\n" +
            "    where \n" +
            "        coupon_supplier.user_id = :supplierId and supplier_type = 1\n" +
            "    union \n" +
            "    select \n" +
            "        distinct coupons.id \n" +
            "    from {h-schema}coupons \n" +
            "        left join {h-schema}coupon_pricing on coupons.id =  coupon_pricing.coupon_id\n" +
            "        left join {h-schema}coupon_pricing_apply ON coupon_pricing_apply.coupon_id = coupons.id \n" +
            "        left join {h-schema}pricing on coupon_pricing.pricing_id = pricing.id or coupon_pricing_apply.pricing_id = pricing.id\n" +
            "        left join {h-schema}services on pricing.service_id = services.id \n" +
            "    where \n" +
            "        services.user_id = :supplierId and coupons.pricing_type = 1\n" +
            "    union \n" +
            "    select \n" +
            "        distinct coupons.id \n" +
            "    from {h-schema}coupons \n" +
            "        join {h-schema}coupon_pricing_plan on coupons.id =  coupon_pricing_plan.coupon_id\n" +
            "        join {h-schema}pricing_multi_plan on coupons.id = coupon_pricing_plan.pricing_multi_plan_id and coupon_pricing_plan.type = 0\n" +
            "        join {h-schema}pricing on pricing_multi_plan.pricing_id = pricing.id\n" +
            "        left join {h-schema}services on pricing.service_id = services.id \n" +
            "    where \n" +
            "        services.user_id = :supplierId and coupons.pricing_type = 1\n" +
            "    union  \n" +
            "    select \n" +
            "        distinct coupons.id \n" +
            "    from {h-schema}coupons \n" +
            "        join {h-schema}coupon_addons on coupons.id = coupon_addons.coupon_id\n" +
            "        join {h-schema}addons on addons.id = coupon_addons.addons_id\n" +
            "    where \n" +
            "        addons.user_id = :supplierId and coupons.addons_type = 1\n" +
            "    union \n" +
            "    select \n" +
            "        distinct coupons.id \n" +
            "    from {h-schema}coupons \n" +
            "        join {h-schema}coupon_pricing_plan on coupons.id = coupon_pricing_plan.coupon_id\n" +
            "        join {h-schema}pricing_multi_plan_addon on coupon_pricing_plan.pricing_multi_plan_id = pricing_multi_plan_addon.pricing_multi_plan_addon_id\n" +
            "        join {h-schema}addons on addons.id = pricing_multi_plan_addon.addon_id\n" +
            "    where \n" +
            "        addons.user_id = :supplierId and coupons.addons_type = 1\n" +
            "    union \n" +
            "    select \n" +
            "        distinct coupons.id \n" +
            "    from {h-schema}coupons \n" +
            "        left join {h-schema}coupon_combo_plan on coupon_combo_plan.coupon_id = coupons.id\n" +
            "        left join {h-schema}coupon_combo_plan_apply on coupon_combo_plan_apply.coupon_id = coupons.id \n" +
            "        left join {h-schema}combo_plan on combo_plan.id = coupon_combo_plan.combo_plan_id or combo_plan.id = coupon_combo_plan_apply.combo_plan_id \n" +
            "        left join {h-schema}combo on combo.id = combo_plan.combo_id \n" +
            "    where \n" +
            "        combo.user_id = :supplierId and coupons.pricing_type = 1 \n" +
            ")\n" +
            "select  \n" +
            "    coupons.id as id, \n" +
            "    coupons.name as couponName, \n" +
            "    coupons.code as code, \n" +
            "    coupons.promotion_type as promotionType, \n" +
            "    coupons.discount_type as discountType, \n" +
            "    coupons.discount_value as discountValue, \n" +
            "    coupons.max_used as maxUsed, \n" +
            "    coupons.minimum as minimum , \n" +
            "    coupons.minimum_amount as minimumAmount, \n" +
            "    coupons.maximum_promotion as maximumPromotion, \n" +
            "    coupons.start_date as startDate, \n" +
            "    coupons.end_date as endDate, \n" +
            "    coupons.portal as portal , \n" +
            "    coupons.user_id as userId, \n" +
            "    coupons.created_by as createdBy, \n" +
            "    coupons.discount_amount as discountAmount, \n" +
            "    coupons.limited_quantity as limitedQuantity, \n" +
            "    coupons.type as TYPE, \n" +
            "    coupons.times_used_type as timesUsedType, \n" +
            "    coupons.enterprise_type as enterpriseType, \n" +
            "    coupons.addons_type as addonType, \n" +
            "    coupons.pricing_type as pricingType, \n" +
            "    coupons.total_bill_type as totalBillType, \n" +
            "    coupons.supplier_type as supplierType, \n" +
            "    coupons.discount_supplier_type as discountSupplierType, \n" +
            "    coupons.province_id as provinceId,\n" +
            "    coupons.customer_type_code as customerType,\n" +
            "    coupons.visible_status as visibleStatus \n" +
            "from {h-schema}coupons\n" +
            "    join mCouponApplySupplier on mCouponApplySupplier.id = coupons.id\n" +
            "    left join {h-schema}coupon_supplier on coupon_supplier.coupon_id = coupons.id \n" +
            "where \n" +
            "    coupons.customer_type_code ILIKE ('%' || :customerType || '%') and\n" +
            "    coupons.deleted_flag = 1 AND coupons.approve = 1 AND coupons.status = 1 and \n" +
            "    (coupons.supplier_type = -1 or coupon_supplier.user_id = :supplierId)";

    public static final String FIND_ALL_MC_ACTIVITY_COUPON_FOR_SUPPLIER =
        "select \n" +
            "    mc_activity_coupon.coupon_id as couponId, \n" +
            "    mCampaign.id as mcId, \n" +
            "    mCampaign.name as mcName, \n" +
            "    mCampaign.start_time as startTime, \n" +
            "    mCampaign.end_time as endTime, \n" +
            "    mcActivity.name as activityName, \n" +
            "    mcActivity.id as activityId \n" +
            "from {h-schema}mc_activity_coupon \n" +
            "    join {h-schema}mc_activity mcActivity on mcActivity.id = mc_activity_coupon.mc_activity_id \n" +
            "    join {h-schema}marketing_campaign as mCampaign on mCampaign.id = mc_activity_coupon.mc_id \n" +
            "where \n" +
            "    mc_activity_coupon.coupon_id in (:lstCouponId)";

    public static final String GET_LIST_NOTIF_USER_DETAIL =
        "select \n" +
            "    distinct on (users.id)\n" +
            "    users.id,\n" +
            "    users.email, \n" +
            "    case \n" +
            "        WHEN users.affiliate_type IS NOT NULL -- affiliate\n" +
            "            OR cast(role.name as text) = cast('ROLE_DEVELOPER' as text) -- dev\n" +
            "            OR (cast(role.name as text) = cast('ROLE_SME' as text) AND users.customer_type IN ('KHDN', 'HKD')) -- sme KHDN/HKD \n" +
            "            THEN users.name\n" +
            "        ELSE CONCAT_WS(' ', users.last_name, users.first_name) -- admin / KHCN \n" +
            "    end as name,\n" +
            "    case \n" +
            "        when cast(role.name as text) = cast('ROLE_DEVELOPER' as text) then 'DEV'\n" +
            "        when cast(role.name as text) = cast('ROLE_SME' as text) then 'SME'\n" +
            "        else 'ADMIN'\n" +
            "    end as portalName,\n" +
            "    case \n" +
            "        when cast(role.name as text) = cast('ROLE_SME' as text) and (users.customer_type = 'KHDN' or users.customer_type = 'HKD') then users.tin -- KHDN / HKD\n" +
            "        else users.email -- ADMIN \n" +
            "    end as userName \n" +
            "from \n" +
            "    {h-schema}users\n" +
            "    join {h-schema}users_roles on users.id = users_roles.user_id\n" +
            "    join {h-schema}role ON role.id = users_roles.role_id\n" +
            "where \n" +
            "    users.id in (:lstUserId)\n" +
            "order by \n" +
            "    users.id, portalName desc";

    public static final String GET_LIST_NOTIF_ADMIN_USER_DETAIL =
        "select distinct\n" +
            "    adminUser.email,\n" +
            "    to_char(user_metadata.password_leak_at, 'dd/MM/yyyy') as dateStr\n" +
            "from \n" +
            "    {h-schema}users as adminUser\n" +
            "    join {h-schema}users on adminUser.id = users.assignee_id\n" +
            "    join {h-schema}user_metadata on users.id = user_metadata.user_id\n" +
            "where \n" +
            "    users.id in (:lstUserId)\n" +
            "    and adminUser.id in (:lstReceiverId)";

    public static final String COMMON_GET_USER_PORTAL_NAME =
        "    with portalNameCTE as (\n" +
            "        select\n" +
            "            user_id,\n" +
            "            case\n" +
            "                when userRole.roles && (ARRAY [cast('ROLE_ADMIN' as text), cast('ROLE_SUPER_ADMIN' as text), cast('FULL_ADMIN' as text)]) then 'ADMIN'\n" +
            "                when userRole.roles && (ARRAY [cast('ROLE_DEVELOPER' as text), cast('FULL_DEV' as text)]) then 'DEV'\n" +
            "                when userRole.roles && (ARRAY [cast('FULL_SME' as text), cast('ROLE_SME' as text), cast('ROLE_SME_EMPLOYEE' as text)]) then 'SME'\n" +
            "                else ''\n" +
            "            end as portalName\n" +
            "        from \n" +
            "            (\n" +
            "                select \n" +
            "                    ur.user_id, \n" +
            "                    array_agg(distinct text(role.name)) as roles \n" +
            "                from \n" +
            "                    {h-schema}users_roles as ur\n" +
            "                    left join {h-schema}role on ur.role_id = role.id \n" +
            "                group by ur.user_id) as userRole\n" +
            "    )\n";

    public static final String GET_LIST_NOTIF_SC_ACCOUNT_USER_DETAIL =
        COMMON_GET_USER_PORTAL_NAME +
        "select distinct on (subscriptions.id)\n" +
            "    subscriptions.id as subId,\n" +
            "    case\n" +
            "        when users.customer_type = 'CN' then concat_ws(' ', users.last_name, users.first_name)\n" +
            "        else coalesce(users.name, concat_ws(' ', users.last_name, users.first_name))\n" +
            "    end as smeName,  \n" +
            "    users.email, \n" +
            "    users.customer_type AS customerType, \n" +
            "    services.service_name as serviceName, \n" +
            "    pricing.pricing_name as pricingName, \n" +
            "    subscriptions.end_current_cycle - CURRENT_DATE as daysLeft, \n" +
            "    subscriptions.id as subscriptionId, \n" +
            "    subscriptions.sub_code as subCode,\n" +
            "    billings.current_cycle as currentCycle, \n" +
            "    billings.end_date as endDate, \n" +
            "    billings.billing_date as billingDate, \n" +
            "    billings.total_amount as totalAmount,\n" +
            "    portalNameCTE.portalName,\n" +
            "    -- TH ACCOUNT: ACCOUNT là tk đăng kí sub\n" +
            "    users.id as assigneeId,\n" +
            "    case\n" +
            "        when portalNameCTE.portalName = 'DEV' then coalesce(users.name, concat_ws(' ', users.last_name, users.first_name))\n" +
            "        else concat_ws(' ', users.last_name, users.first_name)\n" +
            "    end as assigneeName,\n" +
            "    users.email as assigneeEmail\n" +
            "FROM \n" +
            "    {h-schema}subscriptions \n" +
            "    JOIN {h-schema}users ON subscriptions.user_id = users.id AND users.status = 1 AND users.deleted_flag = 1 \n" +
            "    join portalNameCTE on users.id = portalNameCTE.user_id\n" +
            "    JOIN {h-schema}services ON subscriptions.service_id = services.id AND services.status = 1 AND services.deleted_flag = 1 \n" +
            "    JOIN {h-schema}pricing ON pricing.id = subscriptions.pricing_id AND pricing.status = 1 AND pricing.deleted_flag = 1 \n" +
            "    JOIN {h-schema}billings on subscriptions.id = billings.subscriptions_id \n" +
            "WHERE \n" +
            "    subscriptions.id in (:lstSubId)\n" +
            "    and users.id in (:lstReceiverId)\n" +
            "order by\n" +
            "    subscriptions.id,\n" +
            "    billings.current_cycle desc";

    public static final String GET_LIST_NOTIF_SC_ASSIGNEE_USER_DETAIL =
        COMMON_GET_USER_PORTAL_NAME +
        "select distinct on (subscriptions.id)\n" +
            "    subscriptions.id as subId,\n" +
            "    case\n" +
            "        when users.customer_type = 'CN' then concat_ws(' ', users.last_name, users.first_name)\n" +
            "        else coalesce(users.name, concat_ws(' ', users.last_name, users.first_name))\n" +
            "    end as smeName,  \n" +
            "    users.email, \n" +
            "    users.customer_type AS customerType, \n" +
            "    services.service_name as serviceName, \n" +
            "    pricing.pricing_name as pricingName, \n" +
            "    subscriptions.end_current_cycle - CURRENT_DATE as daysLeft, \n" +
            "    subscriptions.id as subscriptionId, \n" +
            "    subscriptions.sub_code as subCode,\n" +
            "    billings.current_cycle as currentCycle, \n" +
            "    billings.end_date as endDate, \n" +
            "    billings.billing_date as billingDate, \n" +
            "    billings.total_amount as totalAmount,\n" +
            "    -- TH ASSIGNEE: ASSIGNEE là assignee_id của sub\n" +
            "    portalNameCTE.portalName, \n" +
            "    assignee.id as assigneeId,\n" +
            "    case\n" +
            "        when portalNameCTE.portalName = 'DEV' then coalesce(assignee.name, concat_ws(' ', assignee.last_name, assignee.first_name))\n" +
            "        else concat_ws(' ', assignee.last_name, assignee.first_name)\n" +
            "    end as assigneeName,\n" +
            "    assignee.email as assigneeEmail\n" +
            "FROM \n" +
            "    {h-schema}subscriptions \n" +
            "    JOIN {h-schema}users ON subscriptions.user_id = users.id AND users.status = 1 AND users.deleted_flag = 1 \n" +
            "    JOIN {h-schema}users assignee on subscriptions.assignee_id = assignee.id AND assignee.status = 1 AND assignee.deleted_flag = 1 \n" +
            "    join portalNameCTE on assignee.id = portalNameCTE.user_id\n" +
            "    JOIN {h-schema}services ON subscriptions.service_id = services.id AND services.status = 1 AND services.deleted_flag = 1 \n" +
            "    JOIN {h-schema}pricing ON pricing.id = subscriptions.pricing_id AND pricing.status = 1 AND pricing.deleted_flag = 1 \n" +
            "    JOIN {h-schema}billings on subscriptions.id = billings.subscriptions_id \n" +
            "WHERE \n" +
            "    subscriptions.id in (:lstSubId)\n" +
            "    and assignee.id in (:lstReceiverId)\n" +
            "order by\n" +
            "    subscriptions.id,\n" +
            "    billings.current_cycle desc";

    public static final String GET_LIST_NOTIF_SC_OTHER_USER_DETAIL =
        COMMON_GET_USER_PORTAL_NAME +
            "    ,subDetailCTE as (\n" +
            "        select distinct on (subscriptions.id)\n" +
            "            subscriptions.id as subId,\n" +
            "            case\n" +
            "                when users.customer_type = 'CN' then concat_ws(' ', users.last_name, users.first_name)\n" +
            "                else coalesce(users.name, concat_ws(' ', users.last_name, users.first_name))\n" +
            "            end as smeName,  \n" +
            "            users.email, \n" +
            "            users.customer_type AS customerType, \n" +
            "            services.service_name as serviceName, \n" +
            "            pricing.pricing_name as pricingName, \n" +
            "            subscriptions.end_current_cycle - CURRENT_DATE as daysLeft, \n" +
            "            subscriptions.id as subscriptionId, \n" +
            "            subscriptions.sub_code as subCode,\n" +
            "            billings.current_cycle as currentCycle, \n" +
            "            billings.end_date as endDate, \n" +
            "            billings.billing_date as billingDate, \n" +
            "            billings.total_amount as totalAmount\n" +
            "            \n" +
            "        FROM \n" +
            "            {h-schema}subscriptions \n" +
            "            JOIN {h-schema}users ON subscriptions.user_id = users.id AND users.status = 1 AND users.deleted_flag = 1 \n" +
            "            JOIN {h-schema}services ON subscriptions.service_id = services.id AND services.status = 1 AND services.deleted_flag = 1 \n" +
            "            JOIN {h-schema}pricing ON pricing.id = subscriptions.pricing_id AND pricing.status = 1 AND pricing.deleted_flag = 1 \n" +
            "            JOIN {h-schema}billings on subscriptions.id = billings.subscriptions_id \n" +
            "        WHERE \n" +
            "            subscriptions.id in (:lstSubId)\n" +
            "        order by\n" +
            "            subscriptions.id,\n" +
            "            billings.current_cycle desc\n" +
            "    )\n" +
            "select distinct\n" +
            "    users.id as assigneeId,\n" +
            "    case \n" +
            "        when (portalName = 'SME' and users.customer_type <> 'CN') or portalName = 'DEV' then coalesce(users.name, concat_ws(' ', users.last_name, users.first_name))\n" +
            "        else concat_ws(' ', users.last_name, users.first_name) \n" +
            "    end as assigneeName,\n" +
            "    users.email as assigneeEmail,\n" +
            "    portalNameCTE.portalName,\n" +
            "    subDetailCTE.*\n" +
            "from \n" +
            "    {h-schema}users \n" +
            "    join portalNameCTE on users.id = portalNameCTE.user_id\n" +
            "    join subDetailCTE on true -- TH nsu khác: mỗi nhân sự nhân được tất cả sub thỏa mãn\n" +
            "where \n" +
            "    users.id in (:lstReceiverId)";

    private static final String BODY_GET_NOTIF_SC_PARTITION_USER_DETAIL =
        "select distinct on (subscriptions.id, partitionAdmin.id)\n" +
            "    subscriptions.id as subId,\n" +
            "    case\n" +
            "        when users.customer_type = 'CN' then concat_ws(' ', users.last_name, users.first_name)\n" +
            "        else coalesce(users.name, concat_ws(' ', users.last_name, users.first_name))\n" +
            "    end as smeName,  \n" +
        "    users.email, \n" +
        "    users.customer_type AS customerType, \n" +
        "    services.service_name as serviceName, \n" +
        "    pricing.pricing_name as pricingName, \n" +
        "    subscriptions.end_current_cycle - CURRENT_DATE as daysLeft, \n" +
        "    subscriptions.id as subscriptionId, \n" +
        "    subscriptions.sub_code as subCode,\n" +
        "    billings.current_cycle as currentCycle, \n" +
        "    billings.end_date as endDate, \n" +
        "    billings.billing_date as billingDate, \n" +
        "    billings.total_amount as totalAmount,\n" +
        "    partitionAdmin.id as assigneeId,\n" +
            "    portalNameCTE.portalName,\n" +
            "    case \n" +
            "        when (portalName = 'SME' and partitionAdmin.customer_type <> 'CN') or portalName = 'DEV' then coalesce(partitionAdmin.name, concat_ws(' ', partitionAdmin.last_name, partitionAdmin.first_name))\n" +
            "        else concat_ws(' ', partitionAdmin.last_name, partitionAdmin.first_name) \n" +
            "    end as assigneeName,\n" +
        "    partitionAdmin.email as assigneeEmail\n" +
        "from \n" +
        "    adminPartition\n" +
        "    join {h-schema}users partitionAdmin on partitionAdmin.id = adminPartition.admin_id\n" +
            "    join portalNameCTE on partitionAdmin.id = portalNameCTE.user_id\n" +
        "    join {h-schema}subscriptions on subscriptions.id = adminPartition.sub_id\n" +
        "    JOIN {h-schema}users ON subscriptions.user_id = users.id AND users.status = 1 AND users.deleted_flag = 1 \n" +
        "    JOIN {h-schema}services ON subscriptions.service_id = services.id AND services.status = 1 AND services.deleted_flag = 1 \n" +
        "    JOIN {h-schema}pricing ON pricing.id = subscriptions.pricing_id AND pricing.status = 1 AND pricing.deleted_flag = 1 \n" +
        "    JOIN {h-schema}billings on subscriptions.id = billings.subscriptions_id \n" +
        "where \n" +
        "    partitionAdmin.id in (:lstReceiverId)\n" +
        "order by\n" +
        "    subscriptions.id,\n" +
        "    partitionAdmin.id,\n" +
        "    billings.current_cycle desc";

    public static final String GET_LIST_NOTIF_SC_ADMIN_PARTITION_DETAIL =
        COMMON_GET_USER_PORTAL_NAME +
            "    ,mPartitionSub as ( -- lấy danh sách partition của sub thỏa mãn\n" +
            "        select \n" +
            "            subscriptions.id as sub_id,\n" +
            "            unnest(crm_mapping_user_partition.lst_partition_id) as partition_id\n" +
            "        from \n" +
            "            {h-schema}subscriptions \n" +
            "            join {h-schema}crm_mapping_user_partition on crm_mapping_user_partition.user_id = subscriptions.user_id \n" +
            "        where \n" +
            "            subscriptions.deleted_flag = 1 and subscriptions.id in (:lstSubId) \n" +
            "    ),\n" +
            "    adminPartition as ( -- lấy danh sách nsu phụ trách phân vùng của sub\n" +
            "        select distinct\n" +
            "            mPartitionSub.sub_id,\n" +
            "            unnest(crm_data_partition.lst_admin_id) as admin_id\n" +
            "        from\n" +
            "            {h-schema}crm_data_partition \n" +
            "            join mPartitionSub on crm_data_partition.id = mPartitionSub.partition_id \n" +
            "    )\n" +
            BODY_GET_NOTIF_SC_PARTITION_USER_DETAIL;

    public static final String GET_LIST_NOTIF_SC_AM_PARTITION_DETAIL =
        COMMON_GET_USER_PORTAL_NAME +
            "    ,mPartitionSub as ( -- lấy danh sách partition của sub thỏa mãn\n" +
            "        select \n" +
            "            subscriptions.id as sub_id,\n" +
            "            unnest(crm_mapping_user_partition.lst_partition_id) as partition_id\n" +
            "        from \n" +
            "            {h-schema}subscriptions \n" +
            "            join {h-schema}crm_mapping_user_partition on crm_mapping_user_partition.user_id = subscriptions.user_id \n" +
            "        where \n" +
            "            subscriptions.deleted_flag = 1 and subscriptions.id in (:lstSubId) \n" +
            "    ),\n" +
            "    adminPartition as ( -- lấy danh sách nsu AM phân vùng của sub\n" +
            "        select distinct\n" +
            "            mPartitionSub.sub_id,\n" +
            "            unnest(crm_data_partition.lst_am_id) as admin_id\n" +
            "        from\n" +
            "            {h-schema}crm_data_partition \n" +
            "            join mPartitionSub on crm_data_partition.id = mPartitionSub.partition_id \n" +
            "    )\n" +
            BODY_GET_NOTIF_SC_PARTITION_USER_DETAIL;

    public static final String GET_LIST_NOTIF_SC_ADMIN_AND_AM_PARTITION_DETAIL =
        COMMON_GET_USER_PORTAL_NAME +
            "    ,mPartitionSub as ( -- lấy danh sách partition của sub thỏa mãn\n" +
            "        select \n" +
            "            subscriptions.id as sub_id,\n" +
            "            unnest(crm_mapping_user_partition.lst_partition_id) as partition_id\n" +
            "        from \n" +
            "            {h-schema}subscriptions \n" +
            "            join {h-schema}crm_mapping_user_partition on crm_mapping_user_partition.user_id = subscriptions.user_id \n" +
            "        where \n" +
            "            subscriptions.deleted_flag = 1 and subscriptions.id in (:lstSubId) \n" +
            "    ),\n" +
            "    adminPartition as ( -- lấy danh sách nsu AM phân vùng của sub\n" +
            "        select distinct\n" +
            "            mPartitionSub.sub_id,\n" +
            "            unnest(crm_data_partition.lst_admin_id) as admin_id\n" +
            "        from\n" +
            "            {h-schema}crm_data_partition \n" +
            "            join mPartitionSub on crm_data_partition.id = mPartitionSub.partition_id \n" +
            "        union\n" +
            "        select distinct\n" +
            "            mPartitionSub.sub_id,\n" +
            "            unnest(crm_data_partition.lst_am_id) as admin_id\n" +
            "        from\n" +
            "            {h-schema}crm_data_partition \n" +
            "            join mPartitionSub on crm_data_partition.id = mPartitionSub.partition_id \n" +
            "    )\n" +
        BODY_GET_NOTIF_SC_PARTITION_USER_DETAIL;

    public static final String GET_LIST_DUPPLICATE_USER_INFO =
        "select distinct \n" +
            "    mUser.id as id, \n" +
            "    case \n" +
            "        when mUser.customer_type = 'KHDN' then mUser.name \n" +
            "        else concat_ws(' ', mUser.last_name, mUser.first_name) \n" +
            "    end as name, \n" +
            "    mUser.phone_number as phone, \n" +
            "    mUser.email as email, \n" +
            "    mUser.customer_type as customerType, \n" +
            "    mProvince.name as provinceName, \n" +
            "    concat(concat_ws(' ', u_assignee.last_name, u_assignee.first_name), ' - ',u_assignee.phone_number) as assignee, \n" +
            "    (SELECT string_agg(crm_data_partition.name,', ') FROM {h-schema}crm_data_partition WHERE crm_data_partition.id = any(mMapping.lst_partition_id)) as lstPartitionName\n" +
            "from \n" +
            "    {h-schema}users as mUser \n" +
            "    left join {h-schema}province as mProvince on mUser.province_id = mProvince.id \n" +
            "    left join {h-schema}users u_assignee on mUser.assignee_id = u_assignee.id \n" +
            "    left join {h-schema}crm_data_condition on mUser.id = any(crm_data_condition.lst_object_id) and crm_data_condition.object_type = 0 \n" +
            "    left join {h-schema}crm_data_partition on crm_data_condition.partition_id = crm_data_partition.id \n" +
            "    left join {h-schema}crm_mapping_user_partition mMapping on mMapping.user_id = mUser.id \n" +
            "where \n" +
            "    mUser.id in (:setUserId) ";

    public static final String EXIST_BY_EMAIL_AND_CUSTOMER_TYPE =
        "SELECT EXISTS (\n" +
            "    SELECT id FROM {h-schema}users \n" +
            "    WHERE email = :email AND customer_type = :customerType \n" +
            "    AND deleted_flag = 1 AND activation_key is null )";


    public static final String EXIST_USER_BY_EMAIL_AND_TAX_CODE_ONEBSS =
        "select exists ( \n" +
            "select id \n"+
            "from {h-schema}users \n" +
            "where email = :email and tin =:taxCode and deleted_flag = 1)";

    public static final String EXIST_USER_BY_TAX_CODE_PARTNER =
        "select exists ( \n" +
            "select id \n"+
            "from {h-schema}users \n" +
            "where tin = :taxCode and deleted_flag = 1)";

    public static final String EXIST_USER_BY_EMAIL_ONEBSS =
        "select exists ( \n" +
            "select id \n"+
            "from {h-schema}users \n" +
            "where email = :email and tin <> :taxCode and deleted_flag = 1)";

    public static final String EXIST_USER_BY_TAX_CODE_ONEBSS =
        "select exists ( \n" +
            "select id \n"+
            "from {h-schema}users \n" +
            "where email <> :email and tin =:taxCode and deleted_flag = 1)";

    public static final String COMMON_GET_USER_ROLE =
        "    with userRoleCTE as (\n" +
            "        select\n" +
            "            user_id,\n" +
            "            case\n" +
            "                when userRole.roles && (ARRAY [cast('ROLE_AFFILIATE_DAILY' as text), cast('ROLE_AFFILIATE_CANHAN' as text)]) then 'AFFILIATE'\n" +
            "                when userRole.roles && (ARRAY [cast('ROLE_ADMIN' as text), cast('ROLE_SUPER_ADMIN' as text), cast('FULL_ADMIN' as text)]) then 'ADMIN'\n" +
            "                when userRole.roles && (ARRAY [cast('ROLE_DEVELOPER' as text), cast('FULL_DEV' as text)]) then 'DEV'\n" +
            "                when userRole.roles && (ARRAY [cast('FULL_SME' as text), cast('ROLE_SME' as text), cast('ROLE_SME_EMPLOYEE' as text)]) then 'SME'\n" +
            "                else ''\n" +
            "            end as user_role\n" +
            "        from \n" +
            "            (\n" +
            "                select \n" +
            "                    ur.user_id, \n" +
            "                    array_agg(distinct text(role.name)) as roles \n" +
            "                from \n" +
            "                    {h-schema}users_roles as ur\n" +
            "                    left join {h-schema}role on ur.role_id = role.id \n" +
            "                group by ur.user_id) as userRole\n" +
            "    )";

    public static final String GET_LIST_USER_FOR_UPDATE_PASSWORD_REMINDER =
        COMMON_GET_USER_ROLE +
            "select distinct\n" +
            "    users.id,\n" +
            "    case \n" +
            "        when userRoleCTE.user_role = 'SME' and users.customer_type IN ('CN') then concat_ws(' ', users.last_name, users.first_name) -- nếu là KHDN/HKD thì lấy tên DN\n" +
            "        when userRoleCTE.user_role = 'SME' and users.parent_id <> -1 then userParent.name -- nếu là DN con thì lấy tên parent\n" +
            "        else coalesce(users.name, concat_ws(' ', users.last_name, users.first_name)) -- nếu là KHDN/HKD/admin/dev/affiliate  lấy theo cột name\n" +
            "    end as name, \n" +
            "    coalesce(userRoleCTE.user_role = 'SME' and coalesce(users.customer_type = 'CN', false), false)as isPersonal,\n" +
            "    users.email,\n" +
            "    userRoleCTE.user_role as userType,\n" +
            "    case \n" +
            "        when userRoleCTE.user_role = 'ADMIN' then concat('/admin-portal/update-password/', users.uuid)\n" +
            "        when userRoleCTE.user_role = 'DEV' then concat('/dev-portal/update-password/', users.uuid)\n" +
            "        when userRoleCTE.user_role = 'SME' then concat('/sme-portal/update-password/', users.uuid)\n" +
            "        else concat('/affiliate/update-password/', users.uuid)\n" +
            "    end as link\n" +
            "from \n" +
            "    {h-schema}users \n" +
            "    left join {h-schema}users userParent on userParent.id = users.parent_id\n" +
            "    join userRoleCTE on users.id = userRoleCTE.user_id\n" +
            "where \n" +
            "    users.deleted_flag = 1\n" +
            "    and users.status = 1 -- không lấy tk đã khóa\n" +
            "    and (cast(coalesce(users.password_modified_at, users.created_at) as date) <= (:reminderStartDate)) -- lấy user đến hạn đổi mk \n" +
            "    -- gửi mail yêu cầu update password thì KHÔNG gửi mail cảnh báo khóa tk \n" +
            "    and ((:lockAccountDate) = cast('1970-01-01' as date) or cast(coalesce(users.password_modified_at, users.created_at) as date) > (:notificationDaysBeforeLock)) \n" +
            "    --  tần suất gửi yêu cầu\n" +
            "    and ((CURRENT_DATE - cast(coalesce(users.password_modified_at, users.created_at) + cast(:updateInterval  as interval) as date)) % :passwordUpdateEmailInterval = 0) ";

    public static final String UPDATE_IS_PASSWORD_EXPIRED_AFTER_RECEIVING_MAIL =
        "UPDATE {h-schema}users\n" +
            "SET is_password_expire = true\n" +
            "WHERE id IN (\n" +
            "    SELECT users.id\n" +
            "    FROM {h-schema}mail_send_history\n" +
            "    JOIN {h-schema}users ON mail_send_history.object_id = users.id\n" +
            "    WHERE mail_send_history.id IN (:lstMailHistoryId)\n" +
            "      AND mail_send_history.mail_template_code = :mailTemplateCode\n" +
            "      AND mail_send_history.object_type = 2\n" +
            "      AND users.recovery_key IS NOT NULL\n" +
            ")";

    public static final String GET_LST_NOTIF_PW02_USER_DETAIL =
        COMMON_GET_USER_ROLE +
            "select distinct\n" +
            "    users.id,\n" +
            "    users.email, \n" +
            "    case \n" +
            "        WHEN userRoleCTE.user_role = 'AFFILIATE' -- affiliate\n" +
            "            OR userRoleCTE.user_role = 'DEV' -- dev\n" +
            "            OR (userRoleCTE.user_role = 'SME' AND users.customer_type IN ('KHDN', 'HKD')) -- sme KHDN/HKD \n" +
            "            THEN users.name\n" +
            "        ELSE CONCAT_WS(' ', users.last_name, users.first_name) -- admin / KHCN \n" +
            "    end as name,\n" +
            "     case \n" +
            "        when (userRoleCTE.user_role = 'SME') and (users.customer_type = 'KHDN' or users.customer_type = 'HKD') then users.tin -- KHDN / HKD\n" +
            "        else users.email -- ADMIN \n" +
            "    end as userName, \n" +
            "    case \n" +
            "        when userRoleCTE.user_role = 'ADMIN' then concat('/admin-portal/reset-password/', users.id, '/', users.recovery_key)\n" +
            "        when userRoleCTE.user_role = 'DEV' then concat('/dev-portal/reset-password/', users.id, '/', users.recovery_key)\n" +
            "        when userRoleCTE.user_role = 'SME' then concat('/sme-portal/reset-password/', users.id, '/', users.recovery_key)\n" +
            "        else concat('/affiliate/reset-password/', users.id, '/', users.recovery_key)\n" +
            "    end as linkResetPassword\n" +
            "from \n" +
            "    {h-schema}users \n" +
            "    join userRoleCTE on users.id = userRoleCTE.user_id\n" +
            "where \n" +
            "    users.id in (:lstUserId)\n" +
            "    and users.recovery_key is not null\n";

    public static final String GET_LIST_DETAIL_USER_FOR_ACTION_HISTORY =
        COMMON_GET_USER_ROLE +
            "    select distinct\n" +
            "    users.id,\n" +
            "     case \n" +
            "        when (userRoleCTE.user_role = 'SME') and (users.customer_type = 'KHDN' or users.customer_type = 'HKD') then users.tin -- KHDN / HKD\n" +
            "        else users.email -- ADMIN \n" +
            "    end as userName,\n" +
            "    case \n" +
            "        when (userRoleCTE.user_role = 'AFFILIATE') then 'AFFILIATE'\n" +
            "        when (userRoleCTE.user_role = 'ADMIN') then 'ADMIN'\n" +
            "        else 'SME'\n" +
            "    end as actionHistoryObjectType\n" +
            "from \n" +
            "    {h-schema}users \n" +
            "    join userRoleCTE on users.id = userRoleCTE.user_id\n" +
            "where \n" +
            "    users.id in (:lstUserId)\n";

    public static final String GET_LIST_USER_FOR_ACCOUNT_LOCK_REMINDER =
        COMMON_GET_USER_ROLE +
            "select distinct\n" +
            "    users.id,\n" +
            "    case \n" +
            "        when userRoleCTE.user_role = 'SME' and users.customer_type IN ('CN') then concat_ws(' ', users.last_name, users.first_name) -- nếu là KHDN/HKD thì lấy tên DN\n" +
            "        when userRoleCTE.user_role = 'SME' and users.parent_id <> -1 then userParent.name -- nếu là DN con thì lấy tên parent\n" +
            "        else coalesce(users.name, concat_ws(' ', users.last_name, users.first_name)) -- nếu là KHDN/HKD/admin/dev/affiliate  lấy theo cột name\n" +
            "    end as name, \n" +
            "    coalesce(userRoleCTE.user_role = 'SME' and coalesce(users.customer_type = 'CN', false), false) as isPersonal,\n" +
            "    users.email,\n" +
            "    userRoleCTE.user_role as userType,\n" +
            "    case \n" +
            "        when userRoleCTE.user_role = 'ADMIN' then concat('/admin-portal/update-password/', users.uuid)\n" +
            "        when userRoleCTE.user_role = 'DEV' then concat('/dev-portal/update-password/', users.uuid)\n" +
            "        when userRoleCTE.user_role = 'SME' then concat('/sme-portal/update-password/', users.uuid)\n" +
            "        else concat('/affiliate/update-password/', users.uuid)\n" +
            "    end as link,\n" +
            "    cast(coalesce(users.password_modified_at, users.created_at) + cast(:updateInterval as interval) + cast(:lockAccountAfter as interval) as date) as accountLockDate\n" +
            "from \n" +
            "    {h-schema}users \n" +
            "    left join {h-schema}users userParent on userParent.id = users.parent_id\n" +
            "    join userRoleCTE on users.id = userRoleCTE.user_id\n" +
            "where --  tần suất gửi cảnh báo là hàng ngày \n" +
            "    users.deleted_flag = 1\n" +
            "    and users.status = 1 -- không lấy tk đã khóa\n" +
            "    -- không gửi mail nếu tk đã đến thời điểm bị khóa \n" +
            "    and ((:lockAccountDate) = cast('1970-01-01' as date) or cast(coalesce(users.password_modified_at, users.created_at) as date) > (:lockAccountDate)) \n" +
            "    -- chỉ gửi mail cảnh báo trong khoảng thời gian nhắc nhở \n" +
            "    and ((:lockAccountDate) = cast('1970-01-01' as date) or cast(coalesce(users.password_modified_at, users.created_at) as date) <= (:notificationDaysBeforeLock)) ";

    public static final String GET_LST_NOTIF_PW03_FULL_ADMIN_USER_DETAIL =
        "SELECT DISTINCT \n" +
            "    adminUser.email, \n" +
            "    to_char(user_metadata.password_leak_at, 'dd/MM/yyyy') AS dateStr \n" +
            "FROM \n" +
            "    {h-schema}users adminUser \n" +
            "    JOIN {h-schema}view_role_full_admin ON adminUser.id = view_role_full_admin.user_id \n" +
            "    CROSS JOIN {h-schema}users users \n" +
            "    JOIN {h-schema}user_metadata ON users.id = user_metadata.user_id \n" +
            "WHERE \n" +
            "    adminUser.deleted_flag = 1 and \n" +
            "    adminUser.email is not null and \n" +
            "    users.id in (:lstUserId)";

    public static final String GET_LIST_LOCKED_USER_TO_SEND_MAIL =
        COMMON_GET_USER_ROLE +
            "select distinct\n" +
            "    users.id,\n" +
            "    case \n" +
            "        when userRoleCTE.user_role = 'SME' and users.customer_type IN ('CN') then concat_ws(' ', users.last_name, users.first_name) -- nếu là KHDN/HKD thì lấy tên DN\n" +
            "        when userRoleCTE.user_role = 'SME' and users.parent_id <> -1 then userParent.name -- nếu là DN con thì lấy tên parent\n" +
            "        else coalesce(users.name, concat_ws(' ', users.last_name, users.first_name)) -- nếu là KHDN/HKD/admin/dev/affiliate  lấy theo cột name\n" +
            "    end as name, \n" +
            "    coalesce(userRoleCTE.user_role = 'SME' and coalesce(users.customer_type = 'CN', false), false) as isPersonal,\n" +
            "    users.email,\n" +
            "    userRoleCTE.user_role as userType,\n" +
            "    case \n" +
            "        when userRoleCTE.user_role = 'ADMIN' then '/admin-portal/reset-password'\n" +
            "        when userRoleCTE.user_role = 'DEV' then '/dev-portal/reset-password'\n" +
            "        when userRoleCTE.user_role = 'SME' then '/sme-portal/reset-password'\n" +
            "        else '/affiliate/reset-password'\n" +
            "    end as link,\n" +
            "    cast(users.recovery_key AS text) AS recoveryKey\n" +
            "FROM {h-schema}users \n" +
            "    LEFT JOIN {h-schema}users userParent on userParent.id = users.parent_id\n" +
            "    LEFT JOIN userRoleCTE ON userRoleCTE.user_id = users.id\n" +
            "WHERE\n" +
            "    users.deleted_flag = 1 AND \n" +
            "    users.status = 1 AND \n" +
            "    users.password_updated_status = 1 AND \n" +
            "    users.is_password_expire = false AND \n" +
            "    userRoleCTE.user_role IN (:lstRole) AND \n" +
            "    (:lockAccountAfter = '0 DAY' or \n" +
            "        cast(users.password_modified_at + cast(:updateInterval  as interval) + \n" +
            "           cast(:lockAccountAfter as interval) as date) <= current_date) -- lấy user đã bị khoá tk";

    public static final String GET_LIST_PROVIDER_BY_IDS_IN =
        "with serviceCountCTE as ( -- số lượng thiết bị -- \n" +
            "  select \n" +
            "          user_id, \n" +
            "          count(id) as count_num \n" +
            "      from {h-schema}services \n" +
            "      where \n" +
            "           product_type = 1 and \n" +
            "           deleted_flag = 1 and " +
            "           approve = 1 and \n" +
            "           status = 1 \n" +
            "      group by user_id \n" +
            "), \n" +
            "comboCountCTE as ( -- Số lượng combo giải pháp -- \n" +
            "  select \n" +
            "          combo.user_id, \n" +
            "          count(cbPlan.id) as count_num \n" +
            "      from {h-schema}combo_plan as cbPlan \n" +
            "          join ( \n" +
            "                 select max(id) as combo_plan_id from {h-schema}combo_plan group by combo_plan_draft_id \n" +
            "               ) as latestCbPlan on latestCbPlan.combo_plan_id = cbPlan.id \n" +
            "          join {h-schema}combo ON combo.id = (select max(id) from {h-schema}combo where combo_draft_id = cbPlan.combo_draft_id GROUP BY combo_draft_id) \n" +
            "          left join (select id_combo_plan, count(id) as count_pricing from {h-schema}combo_pricing group by id_combo_plan) as countCbPricing on countCbPricing.id_combo_plan = cbPlan.id \n" +
            "      where countCbPricing.count_pricing > 0 \n" +
            "      and combo.status = 1 and combo.approve = 1 and combo.deleted_flag = 1\n" +
            "      group by combo.user_id \n" +
            "), \n" +
            "countSoldOrderCTE as ( -- Số lượng đơn hàng đã bán -- \n" +
            "select \n" +
            "        coalesce(combo.id, services.id) as provider_id, \n" +
            "        count(*) as sold_num \n" +
            "    from {h-schema}subscriptions as sub \n" +
            "       left join {h-schema}services on sub.service_id = services.id \n" +
            "       left join {h-schema}combo_plan as cbPlan on cbPlan.id = sub.combo_plan_id \n" +
            "       left join {h-schema}combo on combo.id = cbPlan.id \n" +
            "    where sub.deleted_flag = 1 and sub.confirm_status = 1 \n" +
            "    group by provider_id  \n" +
            ")  \n" +
            "select \n" +
            "   provider.id AS providerId, \n" +
            "   coalesce(provider.name, concat_ws(' ', provider.last_name, provider.first_name)) as providerName, \n" +
            "   coalesce(avatar.file_path, avatar.ext_link) as avatar, \n" +
            "   provider.created_at as createdAt, \n" +
            "   coalesce(serviceCountCTE.count_num, 0) as numOfServices, \n" +
            "   coalesce(comboCountCTE.count_num, 0) as numOfSolutions, \n" +
            "   coalesce(countSoldOrderCTE.sold_num, 0) as numOfSoldOrders \n" +
            "from {h-schema}users as provider \n" +
            "    join {h-schema}view_role_dev as vDev on vDev.user_id = provider.id \n" +
            "    left join {h-schema}file_attach as avatar on avatar.user_id = provider.id and object_type = 3 \n" +
            "    left join serviceCountCTE on provider.id = serviceCountCTE.user_id \n" +
            "    left join comboCountCTE on provider.id = comboCountCTE.user_id \n" +
            "    left join countSoldOrderCTE on provider.id = countSoldOrderCTE.provider_id \n" +
            "where \n" +
            "    ('' = :keyword or coalesce(provider.name, concat_ws(' ', provider.last_name, provider.first_name)) ilike ('%' || :keyword || '%')) and \n" +
            "    provider.deleted_flag = 1 and \n" +
            "    provider.status = 1 and \n" +
            "    provider.parent_id = -1 ";

    public static final String GET_LIST_CHILD_USER_ID_BY_PARENT_ID =
        "select id from {h-schema}users where (id = :parentId OR parent_id = :parentId) AND deleted_flag = 1 AND status = 1";
}
package com.constant.sql;

public class SQLShoppingCart {

    public static final String GET_SPDV_INFO =
        "select \n" +
            "    mView.calculate_type as calculateType, \n" +
            "    mView.service_unique_id as serviceUniqueId, \n" +
            "    mView.services_id as serviceId, \n" +
            "    mView.pricing_id as pricingId, \n" +
            "    mView.pricing_unique_id as pricingUniqueId, \n" +
            "    mView.pricing_multi_plan_id as pricingMultiPlanId, \n" +
            "    mView.service_name as serviceName, \n" +
            "    mView.pricing_name as pricingName, \n" +
            "    mView.payment_cycle as paymentCycle, \n" +
            "    mView.product_type as productType, \n" +
            "    mView.classification as productClassification, \n" +
            "    mView.combo_type as comboType, \n" +
            "    mView.pricing_default as pricingDefault, \n" +
            "    mView.pricing_draft_id as pricingDraftId, \n" +
            "    mView.draft_id as draftId, \n" +
            "    mView.is_one_time as isOneTime, pre_order_url as preOrderUrl, \n" +
            "    coalesce(combo_avatar.file_path, device_avatar.file_path, service_avatar.file_path, \n" +
            "        coalesce(service_group_avatar.file_path, service_group_avatar.ext_link)) as avatarUrl, \n" +
            "    coalesce(comboplan_avatar.file_path, pricing_avatar.file_path) as pricingAvatarUrl, \n" +
            "    coalesce(mView.allow_multi_sub, 0) <> 0 as allowMultiSub \n" +
            "from {h-schema}feature_view_get_spdv_info mView \n" +
            "    left join {h-schema}bos_feature_view_device_avatar device_avatar on device_avatar.object_id = mView.services_id and mView.calculate_type = 0 \n" +
            "    left join {h-schema}bos_feature_view_service_avatar service_avatar on service_avatar.object_id = mView.services_id and mView.calculate_type = 0 \n" +
            "    left join {h-schema}bos_feature_view_combo_avatar combo_avatar on combo_avatar.object_id = mView.services_id and mView.calculate_type = 1 \n" +
            "    left join {h-schema}bos_feature_view_pricing_avatar pricing_avatar on pricing_avatar.object_id = mView.pricing_id and mView.calculate_type = 0 \n" +
            "    left join {h-schema}bos_feature_view_comboplan_avatar comboplan_avatar on comboplan_avatar.object_id = mView.pricing_id and mView.calculate_type = 1 \n" +
            "    left join {h-schema}file_attach service_group_avatar on service_group_avatar.id = mView.icon_id and mView.calculate_type = 4 \n" +
            "where \n" +
            "    (:providerId = -1 or :providerId = mView.provider_id) and \n" +
            "    (-1 IN (:lstSelectedId) or mView.pricing_id NOT IN (:lstSelectedId)) and \n" +
            "    (:lstCustomerType = '' or (string_to_array(:lstCustomerType, ',') && mView.lst_customer_type)) and \n" +
            "    (:lstCustomerType = '' or (string_to_array(:lstCustomerType, ',') && mView.pricing_lst_customer_type)) and \n" +
            "    (:lstCustomerType = '' or (string_to_array(:lstCustomerType, ',') && mView.pricing_multi_plan_lst_customer_type)) and \n" +
            "    (:categoryId = -1 or :categoryId = any(mView.lst_category)) and \n" +
            "    (:paymentCycle = 'ALL' or :paymentCycle = mView.payment_cycle) and \n" +
            "    (:serviceType = -1 or :serviceType = mView.calculate_type) and \n" +
            "    (:isDevice = -1 or (:isDevice = 1 AND (mView.product_type = 1 OR mView.object_type ilike ('%' || 'DEVICE' || '%')))) and \n" +
            "    (:isTrial = -1 or :isTrial = mView.is_trial) and \n" +
            "    (:serviceOnOsType = -1 or \n" +
            "        (:serviceOnOsType = 0 and mView.service_owner = ANY(ARRAY[0, 1])) or \n" +
            "        (:serviceOnOsType = 1 and (mView.service_owner is null or mView.service_owner = ANY(ARRAY[2, 3]))) or \n" +
            "        (:serviceOnOsType = 2 and mView.service_owner = 4) or \n" +
            "        (:serviceOnOsType = 2 and mView.product_type = 1) \n" +
            "    ) and " +
            "    ( " +
            "       :value = '' or  " +
            "       (:searchProduct = 1 and mView.service_name ilike ('%' || :value || '%')) or  " +
            "       (:searchPricing = 1 and mView.pricing_name ilike ('%' || :value || '%')) " +
            "   ) and" +
            "    ( " +
            "       :productType = 'ALL' or  " +
            "       (:productType = 'DEVICE' and (mView.product_type = 1 or mView.combo_type = 'APP') ) or  " +
            "       (:productType = 'OTHER' and mView.product_type <> 1 and mView.combo_type <> 'APP' ) " +
            "   ) and \n" +
            "    (-1 in (:calculateTypes) or mView.calculate_type in (:calculateTypes)) and \n" +
            "    (:serviceUniqueId = -1 or :serviceUniqueId = mView.service_unique_id) and \n" +
            "    (:pricingUniqueId = -1 or :pricingUniqueId = mView.pricing_unique_id) \n";

    public static final String GET_SPDV_INFO_BY_VNPT_TECHNOLOGY =
        "select \n" +
            "    mView.calculate_type as calculateType, \n" +
            "    mView.service_unique_id as serviceUniqueId, \n" +
            "    mView.services_id as serviceId, \n" +
            "    mView.pricing_id as pricingId, \n" +
            "    mView.pricing_unique_id as pricingUniqueId, \n" +
            "    mView.pricing_multi_plan_id as pricingMultiPlanId, \n" +
            "    mView.service_name as serviceName, \n" +
            "    mView.pricing_name as pricingName, \n" +
            "    mView.payment_cycle as paymentCycle, \n" +
            "    mView.product_type as productType, \n" +
            "    mView.classification as productClassification, \n" +
            "    mView.combo_type as comboType, \n" +
            "    mView.pricing_default as pricingDefault, \n" +
            "    mView.pricing_draft_id as pricingDraftId, \n" +
            "    mView.draft_id as draftId, \n" +
            "    mView.is_one_time as isOneTime, pre_order_url as preOrderUrl, \n" +
            "    coalesce(combo_avatar.file_path, device_avatar.file_path, service_avatar.file_path) as avatarUrl, \n" +
            "    coalesce(comboplan_avatar.file_path, pricing_avatar.file_path) as pricingAvatarUrl, \n" +
            "    coalesce(mView.allow_multi_sub, 0) <> 0 as allowMultiSub \n" +
            "from {h-schema}bos_feature_view_shopping_cart_get_spdv_info_vnpt_tech mView \n" +
            "    left join {h-schema}bos_feature_view_device_avatar device_avatar on device_avatar.object_id = mView.services_id and mView.calculate_type = 0 \n" +
            "    left join {h-schema}bos_feature_view_service_avatar service_avatar on service_avatar.object_id = mView.services_id and mView.calculate_type = 0 \n" +
            "    left join {h-schema}bos_feature_view_combo_avatar combo_avatar on combo_avatar.object_id = mView.services_id and mView.calculate_type = 1 \n" +
            "    left join {h-schema}bos_feature_view_pricing_avatar pricing_avatar on pricing_avatar.object_id = mView.pricing_id and mView.calculate_type = 0 \n" +
            "    left join {h-schema}bos_feature_view_comboplan_avatar comboplan_avatar on comboplan_avatar.object_id = mView.pricing_id and mView.calculate_type = 1 \n" +
            "where \n" +
            "    (:providerId = -1 or :providerId = mView.provider_id) and \n" +
            "    (-1 IN (:lstSelectedId) or mView.pricing_id NOT IN (:lstSelectedId)) and \n" +
            "    (:lstCustomerType = '' or (string_to_array(:lstCustomerType, ',') && mView.lst_customer_type)) and \n" +
            "    (:lstCustomerType = '' or (string_to_array(:lstCustomerType, ',') && mView.pricing_lst_customer_type)) and \n" +
            "    (:lstCustomerType = '' or (string_to_array(:lstCustomerType, ',') && mView.pricing_multi_plan_lst_customer_type)) and \n" +
            "    (:categoryId = -1 or :categoryId = any(mView.lst_category)) and \n" +
            "    (:paymentCycle = 'ALL' or :paymentCycle = mView.payment_cycle) and \n" +
            "    (:serviceType = -1 or :serviceType = mView.calculate_type) and \n" +
            "    (:isDevice = -1 or (:isDevice = 1 AND (mView.product_type = 1 OR mView.object_type ilike ('%' || 'DEVICE' || '%')))) and \n" +
            "    (:isTrial = -1 or :isTrial = mView.is_trial) and \n" +
            "    (:serviceOnOsType = -1 or \n" +
            "        (:serviceOnOsType = 0 and mView.service_owner = ANY(ARRAY[0, 1])) or \n" +
            "        (:serviceOnOsType = 1 and (mView.service_owner is null or mView.service_owner = ANY(ARRAY[2, 3]))) or \n" +
            "        (:serviceOnOsType = 2 and mView.service_owner = 4) or \n" +
            "        (:serviceOnOsType = 2 and mView.product_type = 1) \n" +
            "    ) and " +
            "    ( " +
            "       :value = '' or  " +
            "       (:searchProduct = 1 and mView.service_name ilike ('%' || :value || '%')) or  " +
            "       (:searchPricing = 1 and mView.pricing_name ilike ('%' || :value || '%')) " +
            "   ) and" +
            "    ( " +
            "       :productType = 'ALL' or  " +
            "       (:productType = 'DEVICE' and (mView.product_type = 1 or mView.combo_type = 'APP') ) or  " +
            "       (:productType = 'OTHER' and mView.product_type <> 1 and mView.combo_type <> 'APP' ) " +
            "   ) and \n" +
            "    (-1 in (:calculateTypes) or mView.calculate_type in (:calculateTypes)) and \n" +
            "    (:serviceUniqueId = -1 or :serviceUniqueId = mView.service_unique_id) and \n" +
            "    (:pricingUniqueId = -1 or :pricingUniqueId = mView.pricing_unique_id) \n";

    public static final String GET_SERVICE_INFO =
        "with num_service_reaction as (\n" +
            "        select service_id, count(*) from {h-schema}service_reaction where type = 1 group by service_id\n" +
            "    ),\n" +
            "    num_combo_reaction as (\n" +
            "        select service_id as combo_id, count(*) from {h-schema}service_reaction where type = 2 group by service_id\n" +
            "    ),\n" +
            "    num_combo_vote as (\n" +
            "        select combo_id, count(id) from {h-schema}combo_evaluation group by combo_id\n" +
            "    ),\n" +
            "    avg_combo_vote as (\n" +
            "        select combo_id, round(avg(criteria_service_evaluation.rate), 1) as rating\n" +
            "        from {h-schema}combo_evaluation \n" +
            "            join {h-schema}criteria_service_evaluation on criteria_service_evaluation.combo_evaluation_id = combo_evaluation.id\n" +
            "        group by combo_id\n" +
            "    ),\n" +
            "    all_data as (\n" +
            "        select \n" +
            "            true as isService, \n" +
            "            services.id as serviceId, \n" +
            "            services.service_name as name,\n" +
            "            services.product_type as productType,\n" +
            "            services.customer_type_code as customerTypeCode,\n" +
            "            COALESCE(services.price, 0) as price,\n" +
            "            case\n" +
            "                when coalesce(services.service_owner) in (0, 1) then 'ON'\n" +
            "                else 'OS'\n" +
            "            end as type,\n" +
            "            coalesce(services.provider, users.name) as provider,\n" +
            "            coalesce(num_service_reaction.count, 0) as numLike,\n" +
            "            coalesce(rate.avg_rating, 0.0) as rating,\n" +
            "            coalesce(rate.num_rating, 0) as numRating,\n" +
            "            services.allow_multi_sub as allowMultiSub,\n" +
            "            services.deleted_flag as deletedFlag,\n" +
            "            categories.name as categoryName,\n" +
            "            case \n" +
            "                when services.payment_method is not null then services.payment_method \n" +
            "                when services.service_owner_partner = 0 then 1 \n" +
            "                when services.service_owner_partner is null and services.service_owner in (0, 1) then 1 \n" +
            "                else 2 \n" +
            "            end as paymentMethod,\n" +
            "            services.installation_configuration as installationConfiguration,\n" +
            "            services.status as status\n" +
            "        from\n" +
            "            {h-schema}services\n" +
            "            left join num_service_reaction on num_service_reaction.service_id = services.id\n" +
            "            left join {h-schema}mview_stats_services_rating rate on rate.service_id = services.id \n" +
            "            left join {h-schema}users on users.id = services.created_by\n" +
            "            left join {h-schema}categories on categories.id = services.categories_id \n" +
            "        where services.id = :serviceId\n" +
            "        union all\n" +
            "        select \n" +
            "            false as isService, \n" +
            "            combo.id as serviceId, \n" +
            "            combo.combo_name as name,\n" +
            "            0 as serviceType,\n" +
                "            combo.customer_type_code as customerTypeCode,\n" +
            "            0 as price,\n" +
            "            case\n" +
            "                when coalesce(combo.combo_owner) in (0, 1) then 'ON'\n" +
            "                else 'OS'\n" +
            "            end as type,\n" +
            "            coalesce(combo.publisher, combo.provider, users.name) as provider,\n" +
            "            coalesce(num_combo_reaction.count, 0) as numLike,\n" +
            "            coalesce(avg_combo_vote.rating, 0.0) as rating,\n" +
            "            coalesce(num_combo_vote.count, 0) as numRating,\n" +
            "            combo.allow_multi_sub as allowMultiSub,\n" +
            "            combo.deleted_flag as deletedFlag,\n" +
            "            string_agg(categories.name,' ,') as categoryName,\n" +
            "            case \n" +
            "                when combo.payment_method is not null then combo.payment_method \n" +
            "                when combo.combo_owner in (0, 1) then 1 \n" +
            "                else 2 \n" +
            "            end as paymentMethod,\n" +
            "            null as installationConfiguration,\n" +
            "            combo.status as status\n" +
            "        from\n" +
            "            {h-schema}combo\n" +
            "            left join num_combo_reaction on num_combo_reaction.combo_id = combo.id\n" +
            "            left join avg_combo_vote on avg_combo_vote.combo_id = combo.id\n" +
            "            left join num_combo_vote on num_combo_vote.combo_id = combo.id \n" +
            "            left join {h-schema}users on users.id = combo.created_by\n" +
            "            left join {h-schema}categories on categories.id::::varchar similar to replace(combo.categories_id,',','|') \n" +
            "        where combo.id = :serviceId\n" +
            "        group by combo.id, users.name, num_combo_reaction.count, avg_combo_vote.rating, num_combo_vote.count \n" +
            "    ) \n" +
            "select * from all_data where isService = :isService limit 1";

    public static final String GET_SERVICE_INFO_BY_SERVICE_GROUP_ID =
            "with num_service_reaction as (\n" +
                    "        select service_id, count(*) from {h-schema}service_reaction where type = 6 group by service_id\n" +
                    "    )\n" +

                    "select \n" +
                    "            service_group.id as serviceId, \n" +
                    "            service_group.name as name,\n" +
                    "            case\n" +
                    "                when coalesce(service_group.group_service_owner) in (0, 1) then 'ON'\n" +
                    "                else 'OS'\n" +
                    "            end as type,\n" +
                    "               CASE \n" +
                    "                  WHEN u.parent_id = -1 and u.id = 1 THEN 'Quản trị viên' \n" +
                    "                  WHEN u.parent_id = -1 THEN u.name \n" +
                    "                  ELSE p.name \n" +
                    "               END AS provider, \n" +
                    "            service_group.created_by as providerId,\n" +
                    "            coalesce(num_service_reaction.count, 0) as numLike,\n" +
                    "            service_group.deleted_flag as deletedFlag,\n" +
                    "            service_group.payment_method as paymentMethod,\n" +
                    "            service_group.status as status\n" +
                    "        from\n" +
                    "            {h-schema}service_group \n" +
                    "            left join num_service_reaction on num_service_reaction.service_id = service_group.id\n" +
                    "            left join {h-schema}users u on u.id = service_group.created_by\n" +
                    "            LEFT JOIN {h-schema}users p ON u.parent_id = p.id  \n" +
                    "        where service_group.id = :serviceId\n";

    public static final String SQL_FROM_LIST_SUBSCRIPTION =
        "select distinct \n" +
            "   view_cart_sub.sub_code as subCode, \n" +
            "   view_cart_sub.user_code as userCode, \n" +
            "   view_cart_sub.phone as customerPhone,  \n" +
            "   view_cart_sub.email as customerEmail,  \n" +
            "   COALESCE(view_cart_sub.tax_code, view_cart_sub.personal_cert_number) as identityNo,  \n" +
            "   view_cart_sub.employee_code as referCode, \n" +
            "   view_cart_sub.reactive_date as reactiveDate, \n" +
            "   view_cart_sub.current_cycle as currentCycle, \n" +
            "   view_cart_sub.start_current_cycle as startCurrentCycle, \n" +
            "   view_cart_sub.end_current_cycle as endCurrentCycle, \n" +
            "   view_cart_sub.next_payment_time as nextPaymentTime, \n" +
            "   view_cart_sub.reactive_status as reactiveStatus, \n" +
            "   view_cart_sub.customer_name as customerName, \n" +
            "   view_cart_sub.customer_type as customerType, \n" +
            "   view_cart_sub.service_name as serviceName, \n" +
            "   view_cart_sub.service_id as serviceId, \n" +
            "   case \n" +
            "       when view_cart_sub.is_on = true then 'ON' \n" +
            "       else 'OS' \n" +
            "   end as serviceOwner, \n" +
            "   view_cart_sub.is_combo as isCombo, \n" +
            "   case \n" +
            "       when view_cart_sub.is_combo = true then view_cart_sub.combo_provider \n" +
            "       else provider.name  \n" +
            "   end as provider,\n" +
            "   view_cart_sub.sub_status as subStatus, \n" +
            "   view_cart_sub.status_name as orderStatus, \n" +
            "   case \n" +
            "       when view_cart_sub.cycle_type = 0 then concat(view_cart_sub.payment_cycle,' ngày') \n" +
            "       when view_cart_sub.cycle_type = 1 then concat(view_cart_sub.payment_cycle,' tuần') \n" +
            "       when view_cart_sub.cycle_type = 2 then concat(view_cart_sub.payment_cycle,' tháng') \n" +
            "       when view_cart_sub.cycle_type = 3 then concat(view_cart_sub.payment_cycle,' năm') \n" +
            "   end as paymentCycle, \n" +
            "   view_cart_sub.pricing_name as pricingName, \n" +
            "   view_cart_sub.number_of_cycles as numCycle, \n" +
            "   view_cart_sub.number_of_cycles_reactive as numOfCycleReactive, \n" +
            "   COALESCE(view_cart_sub.total_amount, 0) as totalAmount, \n" +
            "   view_cart_sub.sub_id as subId, \n" +
            "   file_attach.id as fileAttachId, \n" +
            "   file_attach.file_name as fileName, \n" +
            "   file_attach.file_path as filePath, \n" +
            "   file_attach.user_id as userId, \n" +
            "   file_attach.service_id as fileServiceId, \n" +
            "   file_attach.object_type as objectType, \n" +
            "   file_attach.priority as priority, \n" +
            "   file_attach.access_type as accessType, \n" +
            "   file_attach.file_size as fileSize, \n" +
            "   file_attach.ext_link as extLink, \n" +
            "   case\n" +
            "     when view_cart_sub.create_source = 1 then 'oneSME'\n" +
            "     when view_cart_sub.create_source = 2 then 'AM giới thiệu'\n" +
            "     when view_cart_sub.create_source = 3 then 'Affiliate'\n" +
            "     when view_cart_sub.create_source = 4 then 'Dev/Admin'\n" +
            "     when view_cart_sub.create_source = 5 then 'ĐHSXKD'\n" +
            "     when view_cart_sub.create_source = 6 then 'Tích hợp ban KHCN'\n" +
            "     when view_cart_sub.create_source = 7 then 'Workplace (oneBSS)'\n" +
            "   end as createSource,\n" +
            "   view_cart_sub.migrate_time as migrateTime, \n" +
            "   view_cart_sub.migrate_code as migrateCode, \n" +
            "   case\n" +
            "       when categories.categories_id_migration = 210 then 'SIM_KEM_GOI'\n" +
            "       when categories.categories_id_migration = 182 then 'DATA'\n" +
            "       when categories.categories_id_migration = 181 then 'TICH_HOP'\n" +
            "       when categories.categories_id_migration = 199 then 'INTERNET_TRUYEN_HINH'\n" +
            "       when categories.categories_id_migration = 198 then 'INTERNET_CAP_QUANG'\n" +
            "       when categories.categories_id_migration = 183 then 'INTERNET_TRUYEN_HINH_VA_DI_DONG'\n" +
            "       when categories.categories_id_migration = 187 then 'MY_TV_IP_TV'\n" +
            "       else 'UNSET'\n" +
            "   end as migrationServiceTypeEnum,\n" +
            "   case \n" +
            "       when categories.categories_id_migration = 210 then view_cart_sub.sim_metadata ->> 'ma_tb' \n" +
            "       else null \n" +
            "   end as simSubCode, \n" +
            "   services.service_code as serviceCode, \n" +
            "   view_cart_sub.created_at as createdAt,\n" +
            "   view_cart_sub.migration_id as migrateId, \n" +
            "   view_cart_sub.assignee_name as assigneeName, \n" +
            "   view_cart_sub.sub_installed as installationStatus, \n" +
            "   view_cart_sub.price_variant as priceVariant, \n" +
            "   view_cart_sub.variant_name as nameVariant, \n" +
            "   view_cart_sub.is_only_service as isOnlyService, \n" +
            "   view_cart_sub.service_group_name as serviceGroupName, \n" +
            "   view_cart_sub.is_one_time as isOneTime, \n" +
            "   view_cart_sub.quantity as quantity,\n" +
            "   view_cart_sub.quantityVariant as quantityVariant,\n" +
            "   view_cart_sub.has_renew as hasRenew,\n" +
            "   view_cart_sub.active_date as activeDate,\n" +
            "   view_cart_sub.end_current_cycle_contract as endCurrentCycleContract,\n" +
            "   view_cart_sub.cancelled_time as cancelledTime,\n" +
            "   view_cart_sub.freeQuantity as freeQuantity, \n" +
            "   CASE\n" +
            "       WHEN view_cart_sub.am_name IS NOT NULL AND view_cart_sub.am_phone IS NOT NULL THEN concat(view_cart_sub.am_name, '-', view_cart_sub.am_phone)\n" +
            "       WHEN view_cart_sub.am_name IS NOT NULL THEN view_cart_sub.am_name\n" +
            "       WHEN view_cart_sub.am_phone IS NOT NULL THEN view_cart_sub.am_phone\n" +
            "       ELSE ''\n" +
            "   END AS amName, \n" +
            "   view_cart_sub.am_code as amCode, \n" +
            "   coalesce(groupFileAttach.file_path, groupFileAttach.ext_link) as serviceGroupAvatarUrl \n" +
            "from {h-schema}feature_view_get_all_sub_group as view_cart_sub \n" +
            "   left join {h-schema}users provider on view_cart_sub.provider_id = provider.id \n" +
            "   left join {h-schema}users creator on creator.id = view_cart_sub.user_id \n" +
            "   left join {h-schema}file_attach ON \n" +
            "       ((file_attach.object_type = 0 and file_attach.service_id = view_cart_sub.service_id/10 AND view_cart_sub.is_combo = false) or \n" +
            "        (file_attach.object_type = 14 and file_attach.combo_id = (view_cart_sub.service_id-1)/10 AND view_cart_sub.is_combo = true) \n" +
            "       )\n" +
            "    left join {h-schema}file_attach as groupFileAttach on view_cart_sub.group_icon_id = groupFileAttach.id\n" +
            "    left join {h-schema}services on view_cart_sub.service_id/10 = services.id\n" +
            "    left join {h-schema}categories on services.categories_id = categories.id\n";

    public static final String GET_LIST_SUBSCRIPTION_BY_SUB_CODE = SQL_FROM_LIST_SUBSCRIPTION +
        "where \n" +
        "   (view_cart_sub.sub_code in (:subCode)) \n";

    public static final String GET_LIST_SUBSCRIPTION_BY_SUB_CODE_ADMIN = SQL_FROM_LIST_SUBSCRIPTION +
        "where \n" +
        "   (view_cart_sub.sub_code in (:subCode)) \n";

    public static final String GET_LIST_BILLING =
        "select " +
            "   vBillings.billing_code as billingCode, \n" +
            "   vBillings.customer_name as customerName, \n" +
            "   vBillings.customer_type as customerType, \n" +
            "   vBillings.sub_code as subCode, \n" +
            "   COALESCE(vBillings.amount_after_tax, 0) as price, \n" +
            "   vBillings.required_payment_date as paymentDate, \n" +
            "   vBillings.payment_status as paymentStatus, \n" +
            "   vBillings.migrate_time as migrateTime, \n" +
            "   vBillings.migrate_code as migrateCode, \n" +
            "   vBillings.is_cart as isCart, \n" +
            "   vBillings.is_create as isCreate, \n" +
            "   vBillings.billings_id as billingId, \n" +
            "   vBillings.is_one_time as isOneTime, \n" +
            "   vBillings.variant_draft_id as variantDraftId, \n" +
            "   vBillings.is_buy_service as isBuyService, \n" +
            "   case\n" +
            "     when vBillings.create_source = 1 then 'oneSME' \n" +
            "     when vBillings.create_source = 2 then 'AM giới thiệu' \n" +
            "     when vBillings.create_source = 3 then 'Affiliate' \n" +
            "     when vBillings.create_source = 4 then 'Dev/Admin' \n" +
            "     when vBillings.create_source = 5 then 'ĐHSXKD' \n" +
            "   end as createSource\n" +
            "from {h-schema}feature_view_shopping_cart_get_list_billing vBillings \n" +
            "where\n" +
            "   (vBillings.billing_code in (:billingCode))\n" +
            "   and (:subId = -1 or :subId = any(vBillings.sub_id_array))";

    public static final String GET_BILLING_DETAIL =
           "select\n"
           + "   distinct on (view_cart_bill_detail.bill_item_id)\n"
           + "   --thông tin provider \n"
           + "   view_cart_bill_detail.provider_name as providerName, \n"
           + "   view_cart_bill_detail.provider_id as providerId, \n"
           + "   view_cart_bill_detail.provider_tax as providerTaxCode, \n"
           + "   view_cart_bill_detail.provider_address as providerAddress, \n"
           + "   --thông tin customer \n"
           + "   view_cart_bill_detail.user_id as customerId, \n"
           + "   view_cart_bill_detail.customer_name_billing as customerName, \n"
           + "   view_cart_bill_detail.customer_billing_last_name as customerLastName, \n"
           + "   view_cart_bill_detail.customer_billing_first_name as customerFirstName, \n"
           + "   view_cart_bill_detail.customer_type as customerType, \n"
           + "   view_cart_bill_detail.customer_tax_billing as customerTaxCode, \n"
           + "   view_cart_bill_detail.customer_identity_billing as identityNo, \n"
           + "   view_cart_bill_detail.customer_address_billing as customerAddress, \n"
           + "   view_cart_bill_detail.type_address as addressType, \n"
           + "   view_cart_bill_detail.name_company_billing as companyName, \n"
           + "   view_cart_bill_detail.province_id_customer as customerProvinceId, \n"
           + "   view_cart_bill_detail.province_code_customer as customerProvinceCode, \n"
           + "   view_cart_bill_detail.province_name_customer as customerProvinceName, \n"
           + "   view_cart_bill_detail.district_id_customer as customerDistrictId, \n"
           + "   view_cart_bill_detail.district_code_customer as customerDistrictCode, \n"
           + "   view_cart_bill_detail.district_name_customer as customerDistrictName, \n"
           + "   view_cart_bill_detail.ward_id_customer as customerWardId, \n"
           + "   view_cart_bill_detail.ward_code_customer as customerWardCode, \n"
           + "   view_cart_bill_detail.ward_name_customer as customerWardName, \n"
           + "   view_cart_bill_detail.street_id_customer as customerStreetId, \n"
           + "   view_cart_bill_detail.address_id as customerAddressId, \n"
           + "   --thông tin payment \n"
           + "   view_cart_bill_detail.billing_code as billingCode, \n"
           + "   view_cart_bill_detail.payment_status as billingStatus, \n"
           + "   view_cart_bill_detail.payment_method as paymentMethod, \n"
           + "   case when billings.sub_group_code is not null then billings.sub_group_code else view_cart_bill_detail.sub_code end as registerCode, \n"
           + "   billings.sub_group_code  as subGroupCode, \n"
           + "   view_cart_bill_detail.is_cart as isCart, \n"
           + "   view_cart_bill_detail.payment_date as paymentDate, \n"
           + "   view_cart_bill_detail.current_payment_date as currentPaymentDate, \n"
           + "   view_cart_bill_detail.final_payment_term as finalPaymentDate, \n"
           + "   --thông tin bill_item \n"
           + "   view_cart_bill_detail.service_name as serviceName, \n"
           + "   view_cart_bill_detail.service_price as billItemPrice, \n"
           + "   view_cart_bill_detail.billing_id as billingId,\n"
           + "   view_cart_bill_detail.item_name as itemName, \n"
           + "   view_cart_bill_detail.item_price_after_tax as itemPrice, \n"
           + "   view_cart_bill_detail.bill_item_id as billItemId,\n"
           + "   view_cart_bill_detail.service_id as serviceId, \n"
           + "   view_cart_bill_detail.is_combo as isCombo, \n"
           + "   view_cart_bill_detail.is_on as isOn, \n"
           + "   view_cart_bill_detail.object_type as objectType, \n"
           + "   view_cart_bill_detail.group_by_service as groupByService, \n"
           + "   --thông tin sô tiền \n"
           + "   view_cart_bill_detail.total_after_tax as totalAfterTaxAmount, \n"
           + "   view_cart_bill_detail.total_after_tax_final as totalAfterTaxFinalAmount,\n"
           + "   COALESCE(view_cart_bill_detail.is_only_service, false) as isOnlyService,\n"
           + "   --thông tin nguồn tạo của billing\n"
           + "   view_cart_bill_detail.created_source_migration as createdSourceMigration \n"
           + " from {h-schema}feature_view_shopping_cart_get_detail_billing as view_cart_bill_detail \n"
           + "  left join {h-schema}billings on billings.id = view_cart_bill_detail.billing_id \n"
           + " where\n"
           + "   (:userId = -1 or :userId = view_cart_bill_detail.user_id) AND  \n"
           + "   (:adminProvinceId = -1 or :adminProvinceId = view_cart_bill_detail.billing_province) AND  \n"
           + "   (:billingCode = 'ALL' or view_cart_bill_detail.billing_code = :billingCode)\n"
           + " order by view_cart_bill_detail.bill_item_id";

    public static final String GET_BAN_KHCN_BILLING_DETAIL =
        "select\n" +
            "    distinct on (view_cart_bill_detail.bill_item_id)\n" +
            "    --thông tin provider \n" +
            "    view_cart_bill_detail.provider_name as providerName, \n" +
            "    view_cart_bill_detail.provider_id as providerId, \n" +
            "    view_cart_bill_detail.provider_tax as providerTaxCode, \n" +
            "    view_cart_bill_detail.provider_address as providerAddress, \n" +
            "    --thông tin customer \n" +
            "    view_cart_bill_detail.user_id as customerId, \n" +
            "    concat_ws(' ', address.last_name, address.first_name) as customerName, \n" +
            "    address.last_name as customerLastName, \n" +
            "    address.first_name as customerFirstName, \n" +
            "    view_cart_bill_detail.customer_type as customerType, \n" +
            "    view_cart_bill_detail.customer_tax_billing as customerTaxCode, \n" +
            "    address.rep_personal_cert_number as identityNo, \n" +
            "    address.address as customerAddress, \n" +
            "    address.type as addressType, \n" +
            "    view_cart_bill_detail.name_company_billing as companyName, \n" +
            "    view_cart_bill_detail.province_id_customer as customerProvinceId, \n" +
            "    view_cart_bill_detail.province_code_customer as customerProvinceCode, \n" +
            "    view_cart_bill_detail.province_name_customer as customerProvinceName, \n" +
            "    view_cart_bill_detail.district_id_customer as customerDistrictId, \n" +
            "    view_cart_bill_detail.district_code_customer as customerDistrictCode, \n" +
            "    view_cart_bill_detail.district_name_customer as customerDistrictName, \n" +
            "    view_cart_bill_detail.ward_id_customer as customerWardId, \n" +
            "    view_cart_bill_detail.ward_code_customer as customerWardCode, \n" +
            "    view_cart_bill_detail.ward_name_customer as customerWardName, \n" +
            "    view_cart_bill_detail.street_id_customer as customerStreetId, \n" +
            "    view_cart_bill_detail.address_id as customerAddressId, \n" +
            "    --thông tin payment \n" +
            "    view_cart_bill_detail.billing_code as billingCode, \n" +
            "    view_cart_bill_detail.payment_status as billingStatus, \n" +
            "    view_cart_bill_detail.payment_method as paymentMethod, \n" +
            "    case when billings.sub_group_code is not null then billings.sub_group_code else view_cart_bill_detail.sub_code end as registerCode, \n" +
            "    billings.sub_group_code  as subGroupCode, \n" +
            "    view_cart_bill_detail.is_cart as isCart, \n" +
            "    view_cart_bill_detail.payment_date as paymentDate, \n" +
            "    view_cart_bill_detail.current_payment_date as currentPaymentDate, \n" +
            "    view_cart_bill_detail.final_payment_term as finalPaymentDate, \n" +
            "    --thông tin bill_item \n" +
            "    view_cart_bill_detail.service_name as serviceName, \n" +
            "    view_cart_bill_detail.service_price as billItemPrice, \n" +
            "    view_cart_bill_detail.billing_id as billingId,\n" +
            "    view_cart_bill_detail.item_name as itemName, \n" +
            "    view_cart_bill_detail.item_price_after_tax as itemPrice, \n" +
            "    view_cart_bill_detail.bill_item_id as billItemId,\n" +
            "    view_cart_bill_detail.service_id as serviceId, \n" +
            "    view_cart_bill_detail.is_combo as isCombo, \n" +
            "    view_cart_bill_detail.is_on as isOn, \n" +
            "    view_cart_bill_detail.object_type as objectType, \n" +
            "    view_cart_bill_detail.group_by_service as groupByService, \n" +
            "    --thông tin sô tiền \n" +
            "    view_cart_bill_detail.total_after_tax as totalAfterTaxAmount, \n" +
            "    view_cart_bill_detail.total_after_tax_final as totalAfterTaxFinalAmount,\n" +
            "    COALESCE(view_cart_bill_detail.is_only_service, false) as isOnlyService,\n" +
            "    --thông tin nguồn tạo của billing\n" +
            "    view_cart_bill_detail.created_source_migration as createdSourceMigration \n" +
            "from \n" +
            "    {h-schema}feature_view_shopping_cart_get_detail_billing as view_cart_bill_detail \n" +
            "    left join {h-schema}billings on billings.id = view_cart_bill_detail.billing_id \n" +
            "    left join {h-schema}address on view_cart_bill_detail.user_id = address.user_id and address.type = 0 and address.default_location = 1 -- lấy thông tin khách hàng trong địa chỉ xuất hóa đơn default\n" +
            "where\n" +
            "    (:userId = -1 or :userId = view_cart_bill_detail.user_id) AND  \n" +
            "    (:adminProvinceId = -1 or :adminProvinceId = view_cart_bill_detail.billing_province) AND  \n" +
            "    (:billingCode = 'ALL' or view_cart_bill_detail.billing_code = :billingCode) \n" +
            "order by \n" +
            "    view_cart_bill_detail.bill_item_id";

    public static final String GET_BAN_KHCN_VISITING_CUSTOMER_BILLING_DETAIL =
        "select\n" +
            "    distinct on (view_cart_bill_detail.bill_item_id)\n" +
            "    --thông tin provider \n" +
            "    view_cart_bill_detail.provider_name as providerName, \n" +
            "    view_cart_bill_detail.provider_id as providerId, \n" +
            "    view_cart_bill_detail.provider_tax as providerTaxCode, \n" +
            "    view_cart_bill_detail.provider_address as providerAddress, \n" +
            "    --thông tin customer \n" +
            "    view_cart_bill_detail.user_id as customerId, \n" +
            "    subscriptions.contact as customerName, \n" +
            "    view_cart_bill_detail.customer_billing_last_name as customerLastName, \n" +
            "    view_cart_bill_detail.customer_billing_first_name as customerFirstName, \n" +
            "    view_cart_bill_detail.customer_type as customerType, \n" +
            "    view_cart_bill_detail.customer_tax_billing as customerTaxCode, \n" +
            "    view_cart_bill_detail.customer_identity_billing as identityNo, \n" +
            "    subscriptions.address as customerAddress, \n" +
            "    view_cart_bill_detail.type_address as addressType, \n" +
            "    view_cart_bill_detail.name_company_billing as companyName, \n" +
            "    view_cart_bill_detail.province_id_customer as customerProvinceId, \n" +
            "    view_cart_bill_detail.province_code_customer as customerProvinceCode, \n" +
            "    view_cart_bill_detail.province_name_customer as customerProvinceName, \n" +
            "    view_cart_bill_detail.district_id_customer as customerDistrictId, \n" +
            "    view_cart_bill_detail.district_code_customer as customerDistrictCode, \n" +
            "    view_cart_bill_detail.district_name_customer as customerDistrictName, \n" +
            "    view_cart_bill_detail.ward_id_customer as customerWardId, \n" +
            "    view_cart_bill_detail.ward_code_customer as customerWardCode, \n" +
            "    view_cart_bill_detail.ward_name_customer as customerWardName, \n" +
            "    view_cart_bill_detail.street_id_customer as customerStreetId, \n" +
            "    view_cart_bill_detail.address_id as customerAddressId, \n" +
            "    --thông tin payment \n" +
            "    view_cart_bill_detail.billing_code as billingCode, \n" +
            "    view_cart_bill_detail.payment_status as billingStatus, \n" +
            "    view_cart_bill_detail.payment_method as paymentMethod, \n" +
            "    case when billings.sub_group_code is not null then billings.sub_group_code else view_cart_bill_detail.sub_code end as registerCode, \n" +
            "    billings.sub_group_code  as subGroupCode, \n" +
            "    view_cart_bill_detail.is_cart as isCart, \n" +
            "    view_cart_bill_detail.payment_date as paymentDate, \n" +
            "    view_cart_bill_detail.current_payment_date as currentPaymentDate, \n" +
            "    view_cart_bill_detail.final_payment_term as finalPaymentDate, \n" +
            "    --thông tin bill_item \n" +
            "    view_cart_bill_detail.service_name as serviceName, \n" +
            "    view_cart_bill_detail.service_price as billItemPrice, \n" +
            "    view_cart_bill_detail.billing_id as billingId,\n" +
            "    view_cart_bill_detail.item_name as itemName, \n" +
            "    view_cart_bill_detail.item_price_after_tax as itemPrice, \n" +
            "    view_cart_bill_detail.bill_item_id as billItemId,\n" +
            "    view_cart_bill_detail.service_id as serviceId, \n" +
            "    view_cart_bill_detail.is_combo as isCombo, \n" +
            "    view_cart_bill_detail.is_on as isOn, \n" +
            "    view_cart_bill_detail.object_type as objectType, \n" +
            "    view_cart_bill_detail.group_by_service as groupByService, \n" +
            "    --thông tin sô tiền \n" +
            "    view_cart_bill_detail.total_after_tax as totalAfterTaxAmount, \n" +
            "    view_cart_bill_detail.total_after_tax_final as totalAfterTaxFinalAmount,\n" +
            "    COALESCE(view_cart_bill_detail.is_only_service, false) as isOnlyService,\n" +
            "    --thông tin nguồn tạo của billing\n" +
            "    view_cart_bill_detail.created_source_migration as createdSourceMigration \n" +
            "from \n" +
            "    {h-schema}feature_view_shopping_cart_get_detail_billing as view_cart_bill_detail \n" +
            "    left join {h-schema}billings on billings.id = view_cart_bill_detail.billing_id \n" +
            "    left join {h-schema}subscriptions on subscriptions.id = billings.subscriptions_id -- lấy thông tin khách hàng trong sub\n" +
            "where\n" +
            "    (:userId = -1 or :userId = view_cart_bill_detail.user_id) AND  \n" +
            "    (:adminProvinceId = -1 or :adminProvinceId = view_cart_bill_detail.billing_province) AND  \n" +
            "    (:billingCode = 'ALL' or view_cart_bill_detail.billing_code = :billingCode) \n" +
            "order by \n" +
            "    view_cart_bill_detail.bill_item_id\n";

    public static final String CLEAN_UP_UNUSED_CART =
        "delete from {h-schema}shopping_cart where user_id is null and now()::::date - modified_at::::date > :numDayKeepUnusedCart ";

    public static final String GET_LIST_BILLING_DETAIL_SUB =
        "select * \n" +
                "from ( \n" +
                "   select \n" +
                "       distinct on (vBillings.billing_code) \n" +
                "       vBillings.billing_code as billingCode, \n" +
                "       vBillings.billings_id as billingId, \n" +
                "       case \n" +
                "           when sub.group_code is null then vBillings.sub_code \n" +
                "           else sub.group_code \n" +
                "       end as subCode, \n" +
                "       COALESCE(vBillings.amount_after_refund, vBillings.amount_after_tax) as totalAmount, \n" +
                "       vBillings.required_payment_date as paymentDate, \n" +
                "       case when sub.group_code is not null and vBillings.payment_method = 2 and sub.is_only_service then null else vBillings.current_payment_date end as requirePaymentDate, \n" +
                "       vBillings.is_cart as isCart, \n" +
                "       vBillings.is_create as isCreate, \n" +
                "       vBillings.created_at as createdAt, \n" +
                "       vBillings.payment_status as status, \n" +
                "       vBillings.variant_draft_id as variantDraftId, \n" +
                "       vBillings.is_buy_service as isBuyService, \n" +
                "       vBillings.is_only_service as isOnlyService, \n" +
                "       vBillings.is_one_time as isOneTime,\n" +
                "       case\n" +
                "           when categories.categories_id_migration = 210 then 'SIM_KEM_GOI'\n" +
                "           when categories.categories_id_migration = 182 then 'DATA'\n" +
                "           when categories.categories_id_migration = 181 then 'TICH_HOP'\n" +
                "           when categories.categories_id_migration = 199 then 'INTERNET_TRUYEN_HINH'\n" +
                "           when categories.categories_id_migration = 198 then 'INTERNET_CAP_QUANG'\n" +
                "           when categories.categories_id_migration = 183 then 'INTERNET_TRUYEN_HINH_VA_DI_DONG'\n" +
                "           when categories.categories_id_migration = 187 then 'MY_TV_IP_TV'\n" +
                "           else 'UNSET'\n" +
                "       end as migrationServiceType\n" +
                "   from {h-schema}feature_view_shopping_cart_get_list_billing as vBillings \n" +
                "       left join {h-schema}subscriptions sub on sub.id = vBillings.subscriptions_id \n" +
                "       left join {h-schema}services on sub.service_id = services.id\n" +
                "       left join {h-schema}categories on services.categories_id = categories.id\n" +
                "   where \n" +
                "       (:subId = sub.id) \n" +
                "       and (:userId = -1 or :userId = vBillings.user_id) \n" +
                "       and (:adminProvinceId = -1 or :adminProvinceId = vBillings.billing_province) \n" +
                "       and (:devId = -1 or :devId = vBillings.provider_id) \n" +
                "   order by vBillings.billing_code desc \n"+
                ") as a ";

    public static final String GET_SUBSCRIPTIONS_EVALUATION =
        "SELECT DISTINCT\n"
            + "sub.id AS subId,\n"
            + "COALESCE (ser.service_name, cb.combo_name) AS serviceName,\n"
            + "COALESCE (p.pricing_name, cbp.combo_name) AS pricingName,\n"
            + "COALESCE (CAST(ser.categories_id as varchar(300)), cb.categories_id) AS categoriesId,\n"
            + "COALESCE ( sub.total_amount_after_refund, sub.total_amount, ( 0 ) :::: DOUBLE PRECISION ) AS price,\n"
            + "sub.created_at as createdAt,\n"
            + "CASE \n"
            + " WHEN sub.installed = 1 THEN \n"
            + "     aLog.response_at + INTERVAL '1 MONTH' \n"
            + " ELSE \n"
            + "     null \n"
            + "END as evaluationDeadline, \n"
            + "COALESCE (uS.NAME, uC.name) AS ownerName,\n"
            + "faS.file_name as fileName,\n"
            + "faS.file_path as filePath,\n"
            + "faS.file_extension as fileExtension,\n"
            + "CASE \n"
            + " WHEN sub.service_id is not null AND sub.pricing_id is null THEN\n"
            + "     -1\n"
            + " WHEN sub.service_id is null THEN\n"
            + "     1\n"
            + " ELSE p.is_one_time\n"
            + "END as isOneTime,\n"
            + "sub.created_source_migration as createdSourceMigration\n"
            + "FROM {h-schema}subscriptions sub \n"
            + "LEFT JOIN {h-schema}billings bill ON sub.id = bill.subscriptions_id\n"
            + "LEFT JOIN {h-schema}pricing p ON p.id = sub.pricing_id\n"
            + "LEFT JOIN {h-schema}combo_plan cbp ON cbp.id = sub.combo_plan_id \n"
            + "LEFT JOIN {h-schema}services ser ON ser.ID = sub.service_id \n"
            + "LEFT JOIN {h-schema}combo cb ON cbp.combo_id = cb.id \n"
            + "LEFT JOIN {h-schema}file_attach faS ON faS.service_id = ser.ID AND faS.object_type = 0\n"
            + "LEFT JOIN {h-schema}file_attach faC ON faC.combo_id = cb.ID AND faC.object_type = 0\n"
            + "LEFT JOIN {h-schema}users uS ON uS.ID = ser.created_by\n"
            + "LEFT JOIN {h-schema}users uC ON uC.ID = cb.created_by \n"
            + "LEFT join (select count (1) as count, sub_id from {h-schema}service_evaluation \n"
            + "      where user_id = :userId \n"
            + "     GROUP BY sub_id) se on se.sub_id = sub.id \n"
            + "LEFT join (select count (1) as count, sub_id from {h-schema}combo_evaluation  \n"
            + "      where user_id = :userId \n"
            + "     GROUP BY sub_id) ce on ce.sub_id = sub.id \n"
            + "LEFT JOIN {h-schema}transaction_log tLog ON tLog.subscription_id = sub.id \n"
            + "LEFT JOIN {h-schema}activity_log aLog ON aLog.transaction_log_id = tLog.id AND aLog.activity_code = :createSubSpdv AND aLog.status = 1 \n"
            + "WHERE\n"
            + "sub.deleted_flag = 1 \n"
            + "AND sub.status NOT IN (0, 1) \n"
            + "AND ((bill.status = ANY (ARRAY[0, 1, 2, 4])) OR bill.status IS NULL) \n"
            + "AND se.count is null \n"
            + "AND ce.count is null \n"
            + "AND ((sub.combo_plan_id is null AND ser.service_name ILIKE('%' || :name || '%')) OR (cb.combo_name ILIKE('%' || :name || '%')))\n"
            + "AND sub.user_id = :userId\n"
            + "AND (:createdBy = -1 or ser.created_by = :createdBy)\n"
            + "AND ((sub.cancelled_time IS NULL AND sub.from_date < now())\n"
            + "	  OR (sub.cancelled_time IS NOT NULL AND sub.from_date < sub.cancelled_time)) "
            + "ORDER BY sub.created_at DESC\n";

    public static final String GET_NUM_SUB_OF_USER_SERVICE =
        "select count( tbl.id ) \n" +
        "from \n" +
            "( select subscriptions.id \n" +
            "from {h-schema}subscriptions \n" +
            "    left join {h-schema}billings bill on subscriptions.id = bill.subscriptions_id \n" +
            "    left join {h-schema}services on services.id = subscriptions.service_id  \n" +
            "    left join {h-schema}combo on combo.id = subscriptions.service_id " +
            "where \n" +
            "     (:userId = -1 or subscriptions.user_id = :userId) and \n" +
            "     subscriptions.status = 2 and " +
            "     subscriptions.deleted_flag = 1 and \n" +
            "     bill.status = 2 and \n" +
            "     ((:isService = true and services.id = :serviceId) or (:isService = false and combo.id = :serviceId)) \n" +
            "group by subscriptions.id ) tbl \n";

    public static final String EXIST_SUBSCRIPTION =
        "select exists ( \n" +
            "    select subscriptions.id \n" +
            "    from {h-schema}subscriptions \n" +
            "        left join {h-schema}billings bill on subscriptions.id = bill.subscriptions_id \n" +
            "    where \n" +
            "        subscriptions.status = 2 and subscriptions.deleted_flag = 1 and bill.status = 2 and \n" +
            "        ((:isService = true and subscriptions.combo_plan_id is null) or (:isService = false and subscriptions.combo_plan_id is not null)) and \n" +
            "        subscriptions.service_id = :serviceId and subscriptions.user_id = :userId \n" +
            ") ";

    public static final String GET_NUM_SUB_OF_USER_NEW =
            "select count( tbl.id )\n" +
            "from\n" +
            "    ( select subscriptions.id\n" +
            "      from {h-schema}subscriptions \n" +
            "    left join {h-schema}billings bill on subscriptions.id = bill.subscriptions_id\n" +
            "          left join {h-schema}services on services.id = subscriptions.service_id\n" +
            "          left join {h-schema}combo on combo.id = subscriptions.service_id\n" +
            "          left join {h-schema}service_group sg on sg.id = subscriptions.service_group_id\n" +
            "      where\n" +
            "          subscriptions.user_id = :userId and (subscriptions.status = 2 or (:serviceType = 4 and subscriptions.status = 0)) and\n" +
            "          subscriptions.deleted_flag = 1 and\n" +
            "          bill.status in (0,1,2) and\n" +
            "          ((:serviceType = 0 and services.id = :serviceId) or (:serviceType = 1 and combo.id = :serviceId) or :serviceType = 3\n" +
            "              or (:serviceType = 4 and sg.id = :serviceId))\n" +
            "      group by subscriptions.id ) tbl";

    public static final String GET_ALL_VARIANT_BY_SERVICE_ID =
            "WITH availableAllPricing as ( \n" +
                "select \n" +
                "    services.id as service_id, \n" +
                "    array_agg(distinct pricing.id) as pricing_id_arr \n" +
                "    from {h-schema}pricing \n" +
                "        join ( \n" +
                "             select pricing_draft_id, max(id) as latest_id \n" +
                "                from {h-schema}pricing \n" +
                "                where deleted_flag = 1 and status = 1 and approve = 1 group by pricing_draft_id \n" +
                "    ) as latestPricing on pricing.pricing_draft_id = latestPricing.pricing_draft_id and pricing.id = latest_id \n" +
                "    join {h-schema}services on pricing.service_id = services.id \n" +
                "    where pricing.variant_apply = 1 \n" +
                "    group by services.id \n" +
                "), \n" +
                "availableSelectedPricing as ( \n" +
                "select \n" +
                "       variant_draft_id, \n" +
                "       array_agg(distinct pricing_id) as pricing_id_arr \n" +
                "    from {h-schema}pricing_variant \n" +
                "    where variant_draft_id is not null and pricing_id is not null \n" +
                "    group by variant_draft_id \n" +
                ") \n" +
            "select \n" +
                "   v.variant_draft_id as variantDraftId, \n" +
                "   v.id as id, \n" +
                "   v.status as status, \n" +
                "   v.name as name, \n" +
                "   v.full_name as fullName, \n" +
                "   v.weight as weight, \n" +
                "   v.size as size, \n" +
                "   v.specifications as specifications, \n" +
                "   v.feature as feature, \n" +
                "   v.barcode as barcode, \n" +
                "   v.discount as discount, \n" +
                "   v.discount_type as discountType, \n" +
                "   v.variant_code as variantCode, \n" +
                "   v.variant_default as variantDefault, \n" +
                "   v.variant_sku as variantSku, \n" +
                "   v.service_id as serviceId, \n" +
                "   v.total_price as variantExtraPrice, \n" +
                "   v.extra_price as variantPrice, \n" +
                "   v.variant_extra_price_type as variantExtraPriceType, \n" +
                "   v.compare_price as comparePrice, \n" +
                "   v.discount_start as discountStart, \n" +
                "   v.discount_status as discountStatus, \n" +
                "   v.discount_end as discountEnd, \n" +
                "   v.use_manual as useManual, \n" +
                "   v.modified_at as modifiedAt, \n" +
                "   v.id_sort as idSort, \n" +
                "   v.params_config as paramsConfig, \n" +
                "   v.specifications_type as specificationsType, \n" +
                "   v.attributes_value as attributesValue, \n" +
                "   v.attributes_ids as attributesIds, \n" +
                "   text( \n" +
                "       (select array(select distinct elem from unnest(availableSelectedPricing.pricing_id_arr || availableAllPricing.pricing_id_arr) as elem)) \n" +
                "   ) as pricingIdArrStr, \n" +
                "   v.available_stock as availableStock, \n" +
                "   v.reserved_stock as reservedStock, \n" +
                "   v.total_stock as totalStock, \n" +
                "   v.apply_shipping as applyShipping \n" +
                "from {h-schema}variant v \n" +
                "   left join ( \n" +
                "       select variant_draft_id, max(id) as latest_id from {h-schema}variant where deleted_flag = 1 and approved = 1 group by variant_draft_id \n" +
                "   ) as latestVariant ON v.id = latestVariant.latest_id \n" +
                "   left join availableSelectedPricing ON v.variant_draft_id = availableSelectedPricing.variant_draft_id \n" +
                "   left join availableAllPricing ON v.service_id = availableAllPricing.service_id \n" +
                "where v.service_id in (:serviceId) ";

    public static final String GET_ATTRIBUTES_WITH_SERVICES =
            "WITH totalAttributes AS (\n" +
                    "	SELECT\n" +
                    "		service_id AS serviceId,\n" +
                    "		attributes_id AS attributesId,\n" +
                    "		attributes_order AS attributesOrder\n" +
                    "	FROM {h-schema}mapping_attributes_service\n" +
                    "	WHERE service_id IN (:serviceId)\n" +
                    ")\n" +
                    "SELECT\n" +
                    "	ta.attributesId AS attributeId,\n" +
                    "	ta.serviceId AS serviceId,\n" +
                    "	a.name AS name,\n" +
                    "	a.code AS code,\n" +
                    "	a.name_id AS nameId,\n" +
                    "	a.description AS description,\n" +
                    "	a.status AS status,\n" +
                    "	count.count AS totalService,\n" +
                    "	a.attribute_field_type AS fieldType,\n" +
                    "	a.attributes_value AS attributesValue,\n" +
                    "	CASE \n" +
                    "		WHEN a.portal_type = 1 THEN 'ADMIN' \n" +
                    "		WHEN a.portal_type = 2 THEN 'DEV' \n" +
                    "	END AS labelName,\n" +
                    "	a.created_at AS createdAt\n" +
                    "FROM totalAttributes ta\n" +
                    "LEFT JOIN {h-schema}attributes a ON a.id = ta.attributesId\n" +
                    "LEFT JOIN (select count (1), attributes_id from {h-schema}mapping_attributes_service  GROUP BY attributes_id) count ON count.attributes_id = a.id  \n" +
                    "ORDER BY attributesOrder ASC";

    public static final String GET_ATTRIBUTES_WITH_SERVICE_IDS =
            "SELECT \n" +
                    "   attributes.id AS attributeId,\n" +
                    "   mapping_attributes_service.service_id AS serviceId,\n" +
                    "   attributes.name AS name,\n" +
                    "   attributes.name_id AS nameId,\n" +
                    "   attributes.status AS status,\n" +
                    "   attributes.attributes_value AS attributesValue\n" +
                    "FROM {h-schema}attributes\n" +
                    "LEFT JOIN {h-schema}mapping_attributes_service ON mapping_attributes_service.attributes_id = attributes.id\n" +
                    "WHERE mapping_attributes_service.service_id in (:serviceIds)\n" +
                    "ORDER BY mapping_attributes_service.service_id DESC, mapping_attributes_service.attributes_order ASC";

    public static final String GET_BILLING_ID_BY_BILLING_CODE =
        "select \n"
            + " DISTINCT vDetailBilling.billing_id\n"
            + "from {h-schema}feature_view_shopping_cart_get_detail_billing as vDetailBilling\n"
            + "where vDetailBilling.billing_code ilike :billingCode";

    public static final String GET_TOTAL_AMOUNT_AFTER_ADJUSTMENT_BY_BILLING_CODE =
        "select \n"
            + " sum (COALESCE(billings.total_amount_after_adjustment,0))\n"
            + "from {h-schema}billings\n"
            + "where coalesce(billings.cart_code, billings.billing_code) = :billingCode";

    private static final String GET_PAGE_BILLING_CODE_ADMIN_CTE = 
            "    select \n" +
            "        vBillings.billing_code as billingCode \n" +
            "    from {h-schema}view_get_list_billing vBillings \n" +
            "       left join {h-schema}crm_mapping_user_partition mUserMapping on mUserMapping.user_id = vBillings.user_id \n" +
            "       left join {h-schema}enterprise mEnterprise on mEnterprise.user_id = mUserMapping.user_id and mEnterprise.assignee_id = :currentUserId \n" +
            "       left join {h-schema}crm_mapping_enterprise_partition mEnterpriseMapping on mEnterpriseMapping.enterprise_id = mEnterprise.id \n" +
            "    where \n" +
            "        (:taxCode = 'ALL' or vBillings.tax_code ilike ('%' || :taxCode || '%')) and \n" +
            "        ( \n" +
            "            :serviceOwner = 'UNSET' or \n" +
            "            ( \n" +
            "                :serviceOwner = 'VNPT' and \n" +
            "                vBillings.service_owner_partner is null and \n" +
            "                vBillings.service_owner between 0 and 1 \n" +
            "            ) or \n" +
            "            ( \n" +
            "                :serviceOwner = 'NONE' and \n" +
            "                vBillings.service_owner_partner is null and\n" +
            "                vBillings.service_owner between 2 and 3 \n" +
            "            ) or \n" +
            "            ( \n" +
            "                :serviceOwner = 'PARTNER' and \n" +
            "                vBillings.service_owner_partner between 0 and 1 \n" +
            "            ) \n" +
            "        ) and \n" +
            "        (:identityNo = 'ALL' or vBillings.personal_cert_number ilike ('%' || :identityNo || '%')) and \n" +
            "        (:subCode = 'ALL' or vBillings.sub_code ilike ('%' || :subCode || '%')) and \n" +
            "        vBillings.sub_code is not null and \n" +
            "        ( \n" +
            "            :serviceType = -1 or \n" +
            "            ( \n" +
            "                :serviceType = 0 and  \n" +
            "                vBillings.is_combo <> true and \n" +
            "                vBillings.is_on = true \n" +
            "            ) or \n" +
            "            ( \n" +
            "                :serviceType = 1 and \n" +
            "                vBillings.is_combo <> true and \n" +
            "                vBillings.is_on <> true \n" +
            "            ) or \n" +
            "            ( \n" +
            "                :serviceType = 2 and \n" +
            "                vBillings.is_combo = true and \n" +
            "                vBillings.is_on = true \n" +
            "            ) or \n" +
            "            ( \n" +
            "                :serviceType = 3 and \n" +
            "                vBillings.is_combo = true and \n" +
            "                vBillings.is_on <> true \n" +
            "            ) \n" +
            "        ) and \n" +
            "        (:serviceName = 'ALL' or vBillings.service_name ilike ('%' ||:serviceName|| '%')) and \n" +
            "        (:paymentStatus = -1 or :paymentStatus = vBillings.payment_status) and vBillings.payment_status <> 3 and \n" +
            "        (:customerType = 'ALL' or vBillings.customer_type = :customerType) and \n" +
            "        (:billOrSubCode = 'ALL' or vBillings.billing_code ilike ('%' || :billOrSubCode || '%') or vBillings.sub_code ilike ('%' || :billOrSubCode || '%')) and \n" +
            "        (:userId = -1 or :userId = vBillings.user_id) and \n" +
            "        (:devId = -1 or :devId = vBillings.provider_id) and \n" +
            "        ( \n" +
            "            :isSuperAdmin = true or\n" +
            "            vBillings.sub_assignee_id = :currentUserId or \n" +
            "            vBillings.user_assignee_id = :currentUserId or \n" +
            "            mEnterprise.assignee_id = :currentUserId or \n" +
            "            :currentUserId = any(vBillings.sub_lst_assignees_id) or \n" +
            "            ((:lstViewablePartitionId)::::bigint[] && mUserMapping.lst_partition_id) or \n" +
            "            ((:lstViewablePartitionId)::::bigint[] && mEnterpriseMapping.lst_partition_id) \n" +
            "         ) and \n" +
            "         (:adminProvinceId = -1 or :adminProvinceId = vBillings.billing_province) and \n" +
            "       ( " +
            "           :value = '' or \n" +
            "           (:searchBillingCode = 1 and vBillings.billing_code ilike ('%' || :value || '%')) or \n" +
            "           (:searchCustomerName = 1 and vBillings.customer_name ilike ('%' ||:value|| '%'))  \n" +
            "       ) order by vBillings.billings_id desc";
    
    public static final String GET_PAGE_BILLING_CODE_ADMIN =
        GET_PAGE_BILLING_CODE_ADMIN_CTE ;
            
    public static final String GET_PAGE_BILLING_CODE_ADMIN_COUNT =
            "select count(distinct billingCode) \n" +
            "from (" + GET_PAGE_BILLING_CODE_ADMIN_CTE + ") temp";
    
    private static final String GET_PAGE_BILLING_CODE_CTE =
        "with mResultBillCode as ( \n" +
            "    select \n" +
            "        vBillings.billing_code as billingCode \n" +
            "    from {h-schema}view_get_list_billing vBillings \n" +
            "    where \n" +
            "        (:taxCode = 'ALL' or vBillings.tax_code ilike ('%' || :taxCode || '%')) and \n" +
            "        (:identityNo = 'ALL' or vBillings.personal_cert_number ilike ('%' || :identityNo || '%')) and \n" +
            "        (:subCode = 'ALL' or vBillings.sub_code ilike ('%' || :subCode || '%')) and \n" +
            "        vBillings.sub_code is not null and \n" +
            "        ( \n" +
            "            :serviceType = -1 or \n" +
            "            ( \n" +
            "                :serviceType = 0 and  \n" +
            "                vBillings.is_combo <> true and \n" +
            "                vBillings.is_on = true \n" +
            "            ) or \n" +
            "            ( \n" +
            "                :serviceType = 1 and \n" +
            "                vBillings.is_combo <> true and \n" +
            "                vBillings.is_on <> true \n" +
            "            ) or \n" +
            "            ( \n" +
            "                :serviceType = 2 and \n" +
            "                vBillings.is_combo = true and \n" +
            "                vBillings.is_on = true \n" +
            "            ) or \n" +
            "            ( \n" +
            "                :serviceType = 3 and \n" +
            "                vBillings.is_combo = true and \n" +
            "                vBillings.is_on <> true \n" +
            "            ) \n" +
            "        ) and \n" +
            "        (:customerName = 'ALL' or vBillings.customer_name ilike ('%' ||:customerName|| '%')) and \n" +
            "        (:serviceName = 'ALL' or vBillings.service_name ilike ('%' ||:serviceName|| '%')) and \n" +
            "        (:paymentStatus = -1 or :paymentStatus = vBillings.payment_status) and vBillings.payment_status <> 3 and \n" +
            "        (:billingCode = 'ALL' or vBillings.billing_code ilike ('%' || :billingCode || '%')) and \n" +
            "        (:billOrSubCode = 'ALL' or vBillings.billing_code ilike ('%' || :billOrSubCode || '%') or vBillings.sub_code ilike ('%' || :billOrSubCode || '%')) and \n" +
            "        (:customerType = 'ALL' or vBillings.customer_type = :customerType) and \n" +
            "        (:userId = -1 or :userId = vBillings.user_id) and \n" +
            "        (:devId = -1 or :devId = vBillings.provider_id) and \n" +
            "        (:adminProvinceId = -1 or :adminProvinceId = vBillings.billing_province) \n" +
            "    order by vBillings.billings_id desc \n" +
            ") \n";

    public static final String GET_LIST_SUB_CODE_ADMIN_NEW =
        "select \n" +
            "    view_cart_sub.sub_code as subCode, \n" +
            "    max(view_cart_sub.sub_id) as subId \n" +
            "from {h-schema}feature_view_get_all_sub_group as view_cart_sub \n" +
            "    left join {h-schema}crm_mapping_user_partition uMapping on view_cart_sub.user_id = uMapping.user_id \n" +
            "    left join {h-schema}crm_mapping_enterprise_partition eMapping on eMapping.enterprise_id = view_cart_sub.enterprise_id \n" +
            "    left join {h-schema}users provider on provider.id = view_cart_sub.provider_id \n" +
            "    left join {h-schema}users creator on creator.id = view_cart_sub.user_id \n" +
            "    left join {h-schema}subscription_metadata as metadata on metadata.subscription_id = view_cart_sub.sub_id \n" +
            "where \n" +
            "    (\n" +
            "        (view_cart_sub.is_combo and view_cart_sub.provider_type <> 4) or -- nếu là combo: ko hiện các combo của VNPT tech\n" +
            "        (not view_cart_sub.is_combo and (view_cart_sub.product_type <> 1 or (view_cart_sub.product_type = 1 and view_cart_sub.provider_type <> 4))) -- nếu là service: ko hiện các thiết bị của vnpt tech\n" +
            "    ) and \n" +
            "    (view_cart_sub.sub_code is not null AND view_cart_sub.service_id is not null) and \n" +
            "    ((:serviceOwner = 'VNPT' and view_cart_sub.service_owner in (1,3) and view_cart_sub.service_owner_partner is null) \n" +
            "       OR (:serviceOwner = 'NONE' and view_cart_sub.service_owner in (0,2) and view_cart_sub.service_owner_partner is null) \n" +
            "       OR (:serviceOwner = 'PARTNER' and view_cart_sub.service_owner_partner in (0,1)) \n" +
            "       OR (:serviceOwner = 'UNSET')) AND \n" +
            "    (:provider = 'ALL' or provider.name ilike ('%' || :provider || '%')) and \n" +
            "    (:subStatus = -1 or view_cart_sub.sub_status = :subStatus) and \n" +
            "    (-1 in (:subInstalled) or view_cart_sub.sub_installed in (:subInstalled)) and \n" +
            "    (:progressStatus = -1 or view_cart_sub.status_id  = :progressStatus) and \n" +
            "    (:serviceType = -1 or (:serviceType = 0 and view_cart_sub.is_combo <> true and view_cart_sub.is_on = true) \n" +
            "        or (:serviceType = 1 and view_cart_sub.is_combo <> true and view_cart_sub.is_on <> true) \n" +
            "        or (:serviceType = 2 and view_cart_sub.is_combo = true and view_cart_sub.is_on = true) \n" +
            "        or (:serviceType = 3 and view_cart_sub.is_combo = true and view_cart_sub.is_on <> true) \n" +
            "        or (:serviceType = 4 and view_cart_sub.group_code is not null)) and \n" +
            "    (:customerType = 'ALL' or view_cart_sub.customer_type IN (:customerType)) and \n" +
            "    (:taxCode = 'ALL' or view_cart_sub.tax_code like ('%' || :taxCode || '%')) and \n" +
            "    (:identityNo = 'ALL' or view_cart_sub.personal_cert_number like ('%' || :identityNo || '%')) and \n" +
            "    (:email = 'ALL' or view_cart_sub.email ilike ('%' || :email || '%')) and \n" +
            "    (:phone = 'ALL' or view_cart_sub.phone ilike ('%' || :phone || '%')) and \n" +
            "    (:repName = 'ALL' or view_cart_sub.rep_name ilike ('%' || :repName || '%')) and \n" +
            "    (CAST('1970-01-01' as date) = :startFoundingDate   or cast(creator.founding_date as date) >= :startFoundingDate) and \n" +
            "    (CAST('1970-01-01'as date) = :endFoundingDate      or cast(creator.founding_date as date) <= :endFoundingDate) and \n" +
            "    (CAST('1970-01-01' as date) = :startPayment        or cast(view_cart_sub.payment_date as date) >= :startPayment) and \n" +
            "    (CAST('1970-01-01'as date) = :endPayment           or cast(view_cart_sub.payment_date as date) <= :endPayment) and \n" +
            "    (CAST('1970-01-01' as date) = :startSubCreated     or cast(view_cart_sub.sub_created_at as date) >= :startSubCreated) and \n" +
            "    (CAST('1970-01-01'as date) = :endSubCreated        or cast(view_cart_sub.sub_created_at as date) <= :endSubCreated) and \n" +
            "    (:businessArea = 'ALL' or :businessArea ilike ('%' || view_cart_sub.business_area_name || '%')) and \n" +
            "    (:businessSize = 'ALL' or :businessSize ilike ('%' || view_cart_sub.business_size_name || '%')) and \n" +
            "    (:createdSource = -1 or view_cart_sub.create_source = :createdSource) and \n" +
            "    (:provinceId = -1 or view_cart_sub.province_id = :provinceId) and \n" +
            "    (-1 in :provinceIdLst or view_cart_sub.province_id in :provinceIdLst) and \n" +
            "    (:paymentCycle = -1 or view_cart_sub.payment_cycle = :paymentCycle) and \n" +
            "    (:cycleType = -1 or view_cart_sub.cycle_type = :cycleType) and \n" +
            "    (-1 in :paymentMethod or view_cart_sub.payment_method in :paymentMethod) and \n" +
            "    (:userId = -1 or :userId = view_cart_sub.user_id) and \n" +
            "    (:devId = -1 or :devId = view_cart_sub.provider_id) and \n" +
            "    (:dhsxCode = 'ALL' or view_cart_sub.dhsxkd_sub_code ilike ('%' || :dhsxCode || '%') or view_cart_sub.transaction_code ilike ('%' || :dhsxCode || '%')) and \n" +
            "    ((:isSuperAdmin = TRUE) \n" +
            "       or ((:lstViewablePartitionId)::::bigint[] && uMapping.lst_partition_id) \n" +
            "       or ((:lstViewablePartitionId)::::bigint[] && eMapping.lst_partition_id) \n" +
            "       or :currentUserId = ANY(view_cart_sub.lst_assignees_id) \n" +
            "       or view_cart_sub.sub_assignee_id = :currentUserId \n" +
            "       or view_cart_sub.user_assignee_id = :currentUserId \n" +
            "       or view_cart_sub.enterprise_assignee_id = :currentUserId \n" +
            "    ) and \n" +
            "    (:searchID = 1 or :searchServiceName = 1 or :searchCustomerName = 1) and \n" +
            "    ('' = :referCode or lower(view_cart_sub.employee_code) like '%' || :referCode || '%') and \n" +
            "    ('' = :khcnPhone or metadata.khcn_phone like '%' || :khcnPhone || '%') and \n" +
            "    ('ALL' in :amDepartments or view_cart_sub.am_department in :amDepartments) and\n" +
            "    ('ALL' in :amCodes or view_cart_sub.am_code in :amCodes) and\n" +
            "    ('ALL' in :amNames or view_cart_sub.am_name in :amNames) and\n" +
            "    ('ALL' in :amPhones or view_cart_sub.am_phone in :amPhones) and\n" +
            "    ( \n" +
            "       :value = '' or \n" +
            "       (:searchUserCode = 1 and view_cart_sub.user_code ILIKE ('%' || :value || '%')) or \n" +
            "       (:searchID = 1 and view_cart_sub.sub_code ILIKE ('%' || :value || '%')) or \n" +
            "       (:searchServiceName = 1 and view_cart_sub.service_name ILIKE ('%' || :value || '%')) or \n" +
            "       (:searchCustomerName = 1 and view_cart_sub.customer_name ILIKE ('%' || :value || '%')) or \n" +
            "       (:searchTaxCode = 1 and view_cart_sub.tax_code ILIKE ('%' || :value || '%')) or \n" +
            "       (:searchIdentityNo = 1 and view_cart_sub.personal_cert_number ILIKE ('%' || :value || '%')) or \n" +
            "       (:searchEmail = 1 and view_cart_sub.email ILIKE ('%' || :value || '%'))  \n" +
            "   ) \n" +
            "   group by view_cart_sub.sub_code \n";

    public static final String GET_COUNT_LIST_SUB_CODE_ADMIN_NEW =
        "select count(1) from (" + GET_LIST_SUB_CODE_ADMIN_NEW + ") as view_sub_code";

    public static final String GET_LIST_SUB_CODE_SME =
        "select \n" +
            "   view_cart_sub.sub_code as subCode, \n" +
            "   max(view_cart_sub.sub_id) as subId \n" +
            "from {h-schema}feature_view_get_all_sub_group as view_cart_sub \n" +
            "    left join {h-schema}users creator on creator.id = view_cart_sub.user_id \n" +
            "    left join {h-schema}users provider on provider.id = view_cart_sub.provider_id  \n"+
            "    left join {h-schema}subscription_metadata as metadata on metadata.subscription_id = view_cart_sub.sub_id \n" +
            "where \n" +
            "    (\n" +
            "        (view_cart_sub.is_combo and view_cart_sub.provider_type <> 4) or -- nếu là combo: ko hiện các combo của VNPT tech\n" +
            "        (not view_cart_sub.is_combo and (view_cart_sub.product_type <> 1 or (view_cart_sub.product_type = 1 and view_cart_sub.provider_type <> 4))) -- nếu là service: ko hiện các thiết bị của vnpt tech\n" +
            "    ) and \n" +
            "   (view_cart_sub.sub_code is not null AND view_cart_sub.service_id is not null) and \n" +
            "   (:subCode = 'ALL' or view_cart_sub.sub_code ilike ('%' || :subCode || '%')) and \n" +
            "   (:productId = -1 or view_cart_sub.service_id = :productId) and\n" +
            "   (:product = 'ALL' or view_cart_sub.service_name ilike ('%'|| :product || '%')) and\n" +
            "   (:customer = 'ALL' or view_cart_sub.customer_name ilike ('%' || :customer || '%')) and\n" +
            "   (:provider = 'ALL' or provider.name ilike ('%' || :provider || '%')) and\n" +
            "   (-1 in (:subStatus) or view_cart_sub.sub_status in (:subStatus)) and\n" +
            "   (-1 in (:subInstalled) or view_cart_sub.sub_installed in (:subInstalled)) and\n" +
            "   (:progressStatus = -1 or view_cart_sub.status_id  = :progressStatus) and\n" +
            "   (:serviceType = -1 or (:serviceType = 0 and view_cart_sub.is_combo <> true and view_cart_sub.is_on = true) \n" +
            "       or (:serviceType = 1 and view_cart_sub.is_combo <> true and view_cart_sub.is_on <> true) \n" +
            "       or (:serviceType = 2 and view_cart_sub.is_combo = true and view_cart_sub.is_on = true) \n" +
            "       or (:serviceType = 3 and view_cart_sub.is_combo = true and view_cart_sub.is_on <> true) \n" +
            "       or (:serviceType = 4 and view_cart_sub.group_code is not null)) and \n" +
            "   (:customerType = 'ALL' or view_cart_sub.customer_type IN (:customerType)) and\n" +
            "   (:taxCode = 'ALL' or view_cart_sub.tax_code like ('%' || :taxCode || '%')) and\n" +
            "   (:identityNo = 'ALL' or view_cart_sub.personal_cert_number like ('%' || :identityNo || '%')) and\n" +
            "   (:email = 'ALL' or view_cart_sub.email ilike ('%' || :email || '%')) and\n" +
            "   (:phone = 'ALL' or view_cart_sub.phone ilike ('%' || :phone || '%')) and\n" +
            "   (:repName = 'ALL' or view_cart_sub.rep_name ilike ('%' || :repName || '%')) and\n" +
            "    (CAST('1970-01-01' as date) = :startFoundingDate   or cast(creator.founding_date as date) >= :startFoundingDate) and \n" +
            "    (CAST('1970-01-01'as date) = :endFoundingDate      or cast(creator.founding_date as date) <= :endFoundingDate) and \n" +
            "    (CAST('1970-01-01' as date) = :startPayment        or cast(view_cart_sub.payment_date as date) >= :startPayment) and \n" +
            "    (CAST('1970-01-01'as date) = :endPayment           or cast(view_cart_sub.payment_date as date) <= :endPayment) and \n" +
            "    (CAST('1970-01-01' as date) = :startSubCreated     or cast(view_cart_sub.sub_created_at as date) >= :startSubCreated) and \n" +
            "    (CAST('1970-01-01'as date) = :endSubCreated        or cast(view_cart_sub.sub_created_at as date) <= :endSubCreated) and \n" +
            "    (:businessArea = 'ALL' or :businessArea ilike ('%' || view_cart_sub.business_area_name || '%')) and \n" +
            "    (:businessSize = 'ALL' or :businessSize ilike ('%' || view_cart_sub.business_size_name || '%')) and \n" +
            "    (:createdSource = -1 or view_cart_sub.create_source = :createdSource) and\n" +
            "    (:provinceId = -1 or view_cart_sub.province_id = :provinceId) and\n" +
            "    (-1 in :provinceIdLst or view_cart_sub.province_id in :provinceIdLst) and \n" +
            "    (:paymentCycle = -1 or view_cart_sub.payment_cycle = :paymentCycle) and\n" +
            "    (-1 in :paymentMethod or view_cart_sub.payment_method in :paymentMethod) and \n" +
            "    (:cycleType = -1 or view_cart_sub.cycle_type = :cycleType) and\n" +
            "    (:userId = -1 or :userId = view_cart_sub.user_id) and \n" +
            "    (:devId = -1 or :devId = view_cart_sub.provider_id) and \n" +
            "    ('ALL' in :amDepartments or view_cart_sub.am_department in :amDepartments) and\n" +
            "    ('ALL' in :amCodes or view_cart_sub.am_code in :amCodes) and\n" +
            "    ('ALL' in :amNames or view_cart_sub.am_name in :amNames) and\n" +
            "    ('ALL' in :amPhones or view_cart_sub.am_phone in :amPhones) and\n" +
            "    (:adminProvinceId = -1 or :adminProvinceId = view_cart_sub.billing_province) and \n" +
            "    ('' = :referCode or lower(view_cart_sub.employee_code) like '%' || :referCode || '%') and \n" +
            "    ('' = :khcnPhone or metadata.khcn_phone like '%' || :khcnPhone || '%') and \n" +
            "    ( -- lọc id thuê bao + tên sp trên giao diện mới\n" +
            "       :value = '' or \n" +
            "       (:searchId = 1 and view_cart_sub.sub_code ILIKE ('%' || :value || '%')) or \n" +
            "       (:searchServiceName = 1 and view_cart_sub.service_name ILIKE ('%' || :value || '%')) \n" +
            "    ) and \n" +
            "    (-- Lọc theo loại đơn hàng hay thuê bao\n" +
            "         :subOnOsType = 'UNSET' or :subOnOsType = 'ON' or (:subOnOsType = 'OS' and view_cart_sub.is_on = false) \n" +
            "    ) and \n" +
            "    ( -- Lọc các sub có thể gia hạn/kích họạt lại trên mobile \n" +
            "        :subAction = 'UNSET' or " +
            "        ( \n" +
            "            :subAction = 'RENEW' and \n" +
            "            view_cart_sub.has_renew = 1 and \n" +
            "            (view_cart_sub.sub_status = 2 or view_cart_sub.sub_status = 5) and \n" +
            "            (\n" +
            "                view_cart_sub.end_current_cycle_contract is null or \n" +
            "                (view_cart_sub.end_current_cycle_contract is not null and view_cart_sub.end_current_cycle > view_cart_sub.end_current_cycle_contract)\n" +
            "            ) \n" +
            "        ) or \n" +
            "        ( \n" +
            "            :subAction = 'REACTIVE' and \n" +
            "            view_cart_sub.is_on = true and view_cart_sub.sub_status = 3 and \n" +
            "            (\n" +
            "                view_cart_sub.active_date = -1 or \n" +
            "                (view_cart_sub.active_date > 0 and (view_cart_sub.cancelled_time + cast(concat(view_cart_sub.active_date, ' DAY') as interval) > now()))\n" +
            "            ) \n" +
            "        ) \n" +
            "    ) \n" +
            "group by view_cart_sub.sub_code \n";

    public static final String GET_COUNT_LIST_SUB_CODE_SME=
        "select count(1) from (" + GET_LIST_SUB_CODE_SME + ") as view_sub_code";

    public static final String GET_PAGE_BILLING_CODE =
        GET_PAGE_BILLING_CODE_CTE +
            "select distinct billingCode \n" +
            "from mResultBillCode";

    public static final String GET_PAGE_BILLING_CODE_COUNT =
        GET_PAGE_BILLING_CODE_CTE +
            "select count(distinct billingCode) \n" +
            "from mResultBillCode";

    public static final String GET_LIST_DETAIL_SUBSCRIPTION_BY_SUB_CODE_ADMIN =
        "select distinct \n" +
            "   view_cart_sub.sub_code as subCode, \n" +
            "   view_cart_sub.reactive_date as reactiveDate, \n" +
            "   view_cart_sub.current_cycle as currentCycle, \n" +
            "   view_cart_sub.start_current_cycle as startCurrentCycle, \n" +
            "   view_cart_sub.end_current_cycle as endCurrentCycle, \n" +
            "   view_cart_sub.next_payment_time as nextPaymentTime, \n" +
            "   view_cart_sub.reactive_status as reactiveStatus, \n" +
            "   view_cart_sub.customer_name as customerName, \n" +
            "   view_cart_sub.phone as customerPhone,  \n" +
            "   view_cart_sub.email as customerEmail,  \n" +
            "   COALESCE(view_cart_sub.tax_code, view_cart_sub.personal_cert_number) as identityNo,  \n" +
            "   view_cart_sub.customer_type as customerType, \n" +
            "   view_cart_sub.service_name as serviceName, \n" +
            "   view_cart_sub.service_id as serviceId, \n" +
            "   case \n" +
            "       when view_cart_sub.is_on = true then 'ON' \n" +
            "       else 'OS' \n" +
            "   end as serviceOwner, \n" +
            "   view_cart_sub.is_combo as isCombo, \n" +
            "   provider.name as provider, \n" +
            "   view_cart_sub.sub_status as subStatus, \n" +
            "   view_cart_sub.status_name as orderStatus, \n" +
            "   case \n" +
            "       when view_cart_sub.cycle_type = 0 then concat(view_cart_sub.payment_cycle,' ngày') \n" +
            "       when view_cart_sub.cycle_type = 1 then concat(view_cart_sub.payment_cycle,' tuần') \n" +
            "       when view_cart_sub.cycle_type = 2 then concat(view_cart_sub.payment_cycle,' tháng') \n" +
            "       when view_cart_sub.cycle_type = 3 then concat(view_cart_sub.payment_cycle,' năm') \n" +
            "   end as paymentCycle, \n" +
            "   view_cart_sub.pricing_name as pricingName, \n" +
            "   view_cart_sub.number_of_cycles as numCycle, \n" +
            "   view_cart_sub.number_of_cycles_reactive as numOfCycleReactive, \n" +
            "   COALESCE(view_cart_sub.total_amount, 0) as totalAmount, \n" +
            "   view_cart_sub.sub_id as subId, \n" +
            "   file_attach.id as fileAttachId, \n" +
            "   file_attach.file_name as fileName, \n" +
            "   file_attach.file_path as filePath, \n" +
            "   file_attach.user_id as userId, \n" +
            "   file_attach.service_id as fileServiceId, \n" +
            "   file_attach.object_type as objectType, \n" +
            "   file_attach.priority as priority, \n" +
            "   file_attach.access_type as accessType, \n" +
            "   file_attach.file_size as fileSize, \n" +
            "   file_attach.ext_link as extLink, \n" +
            "   case\n" +
            "     when view_cart_sub.create_source = 1 then 'oneSME'\n" +
            "     when view_cart_sub.create_source = 2 then 'AM giới thiệu'\n" +
            "     when view_cart_sub.create_source = 3 then 'Affiliate'\n" +
            "     when view_cart_sub.create_source = 4 then 'Dev/Admin'\n" +
            "     when view_cart_sub.create_source = 5 then 'ĐHSXKD'\n" +
            "   end as createSource,\n" +
            "   view_cart_sub.migrate_time as migrateTime, \n" +
            "   view_cart_sub.migrate_code as migrateCode, \n" +
            "   view_cart_sub.created_at as createdAt,\n" +
            "   view_cart_sub.migration_id as migrateId, \n" +
            "   view_cart_sub.assignee_name as assigneeName, \n" +
            "   view_cart_sub.sub_installed as installationStatus, \n" +
            "   view_cart_sub.price_variant as priceVariant, \n" +
            "   view_cart_sub.variant_name as nameVariant, \n" +
            "   view_cart_sub.is_only_service as isOnlyService, \n" +
            "   view_cart_sub.is_one_time as isOneTime \n" +
            "from {h-schema}feature_view_get_all_sub_group as view_cart_sub \n" +
            "   left join {h-schema}users provider on view_cart_sub.provider_id = provider.id \n" +
            "   left join {h-schema}file_attach ON file_attach.object_type = 0 and \n" +
            "       ((file_attach.service_id = view_cart_sub.service_id/10 AND view_cart_sub.is_combo = false) or \n" +
            "        (file_attach.combo_id = (view_cart_sub.service_id-1)/10 AND view_cart_sub.is_combo = true) \n" +
            "       )\n" +
            "    left join (\n" +
            "       select id, service_owner, service_owner_partner, false as is_combo FROM {h-schema}services UNION ALL \n" +
            "       select id, combo_owner as service_owner, null as service_owner_partner, true as is_combo FROM {h-schema}combo\n" +
            "       ) sc on view_cart_sub.is_combo = sc.is_combo and ((view_cart_sub.is_combo = false and sc.id = (view_cart_sub.service_id / 10)) or \n" +
            "   (view_cart_sub.is_combo = true and sc.id = ((view_cart_sub.service_id - 1) / 10))) \n" +
            "where \n" +
            "   (view_cart_sub.sub_code is not null AND view_cart_sub.service_id is not null) and \n" +
            "   ((:serviceOwner = 'VNPT' and sc.service_owner in (1,3) and sc.service_owner_partner is null)  \n" +
            "       OR (:serviceOwner = 'NONE' and sc.service_owner in (0,2) and sc.service_owner_partner is null) \n" +
            "       OR (:serviceOwner = 'PARTNER' and sc.service_owner_partner in (0,1))  \n" +
            "       OR (:serviceOwner = 'UNSET')) AND \n" +
            "   (view_cart_sub.sub_code in (:subCode)) and\n" +
            "   (:provider = 'ALL' or provider.name ilike ('%' || :provider || '%')) and\n" +
            "   (:subStatus = -1 or view_cart_sub.sub_status = :subStatus) and\n" +
            "   (-1 in (:subInstalled) or view_cart_sub.sub_installed in (:subInstalled)) and\n" +
            "   (:progressStatus = -1 or view_cart_sub.status_id  = :progressStatus) and\n" +
            "   (:serviceType = -1 or (:serviceType = 0 and view_cart_sub.is_combo <> true and view_cart_sub.is_on = true) \n" +
            "                      or (:serviceType = 1 and view_cart_sub.is_combo <> true and view_cart_sub.is_on <> true) \n" +
            "                      or (:serviceType = 2 and view_cart_sub.is_combo = true and view_cart_sub.is_on = true) \n" +
            "                      or (:serviceType = 3 and view_cart_sub.is_combo = true and view_cart_sub.is_on <> true)) and \n" +
            "   (:customerType = 'ALL' or view_cart_sub.customer_type = (:customerType)) and\n" +
            "   (:taxCode = 'ALL' or view_cart_sub.tax_code like ('%' || :taxCode || '%')) and\n" +
            "   (:identityNo = 'ALL' or view_cart_sub.personal_cert_number like ('%' || :identityNo || '%')) and\n" +
            "   (:email = 'ALL' or view_cart_sub.email ilike ('%' || :email || '%')) and\n" +
            "   (:phone = 'ALL' or view_cart_sub.phone ilike ('%' || :phone || '%')) and\n" +
            "   (:repName = 'ALL' or view_cart_sub.rep_name ilike ('%' || :repName || '%')) and\n" +
            "   (CAST('1970-01-01' as date) = CAST(:startFoundingDate as date) or provider.founding_date >= :startFoundingDate) and\n" +
            "   (CAST('1970-01-01'as date) = CAST(:endFoundingDate as date) or provider.founding_date <= :endFoundingDate) and\n" +
            "    (:businessArea = 'ALL' or :businessArea ilike ('%' || view_cart_sub.business_area_name || '%')) and \n" +
            "    (:businessSize = 'ALL' or :businessSize ilike ('%' || view_cart_sub.business_size_name || '%')) and \n" +
            "   (CAST('1970-01-01' as date) = CAST(:startPayment as date) or view_cart_sub.payment_date >= :startPayment) and\n" +
            "   (CAST('1970-01-01' as date) = CAST(:endPayment as date) or view_cart_sub.payment_date <= :endPayment) and\n" +
            "   (:createdSource = -1 or view_cart_sub.create_source = :createdSource) and\n" +
            "   (:provinceId = -1 or view_cart_sub.province_id = :provinceId) and\n" +
            "    (-1 in (:provinceIdLst) or view_cart_sub.province_id in (:provinceIdLst)) and \n" +
            "   (CAST('1970-01-01' as date) = CAST(:startSubCreated as date) or view_cart_sub.sub_created_at >= :startSubCreated) and\n" +
            "   (CAST('1970-01-01' as date) = CAST(:endSubCreated as date) or view_cart_sub.sub_created_at <= :endSubCreated) and\n" +
            "   (:paymentCycle = -1 or view_cart_sub.payment_cycle = :paymentCycle) and\n" +
            "   (:cycleType = -1 or view_cart_sub.cycle_type = :cycleType) and\n" +
            "   (:userId = -1 or :userId = view_cart_sub.user_id) and \n" +
            "   (:devId = -1 or :devId = view_cart_sub.provider_id) and \n" +
            "   (:dhsxCode = 'ALL' or view_cart_sub.dhsxkd_sub_code ilike ('%' || :dhsxCode || '%') or view_cart_sub.transaction_code ilike ('%' || :dhsxCode || '%')) and \n" +
            "   (:adminProvinceId = -1 or :adminProvinceId = view_cart_sub.billing_province) and\n" +
            "   ( \n" +
            "       :value = '' or \n" +
            "       (:searchID = 1 and view_cart_sub.sub_code ilike ('%' || :value || '%')) or \n" +
            "       (:searchServiceName = 1 and view_cart_sub.service_name ilike ('%' || :value || '%')) or \n" +
            "       (:searchCustomerName = 1 and view_cart_sub.customer_name ilike ('%' || :value || '%'))  \n" +
            "   ) ";
}

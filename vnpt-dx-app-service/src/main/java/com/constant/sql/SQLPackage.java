package com.constant.sql;

public class SQLPackage {

    public static final String SEARCH_PACKAGE =
            // l<PERSON>y danh sách id spdv thuộc package
             "WITH product_ids_cte AS (\n" +
                    "SELECT pd.id AS package_id,\n" +
                            "    string_agg(DISTINCT p.id::::text, ',') AS product_ids\n" +
                    " FROM {h-schema}package_draft pd\n" +
                            "    JOIN {h-schema}package_mappings pm ON pm.package_draft_id = pd.id\n" +
                            "    JOIN {h-schema}package_items pi ON pm.package_item_id = pi.id\n" +
                            "    JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = pi.plan_id\n" +
                            "    JOIN {h-schema}pricing p ON p.id = pmp.pricing_id\n" +
                            "    WHERE (-1 IN :productIds OR p.id IN :productIds) GROUP BY pd.id)\n" +
             "SELECT pd.id as id, " +
                    "pd.name as packageName," +
                    "u.name as providerName, " +
                    "pd.visibility as visibility," +
                    "pd.state as state, pd.recommended as recommended, " +
                    "pd.created_at as createdAt," +
                    "pd.icon_url as iconUrl," +
                    "pic.product_ids as productIds\n" +
            "FROM {h-schema}package_draft pd \n" +
                    "LEFT JOIN users u ON pd.created_by = u.id\n" +
                    "LEFT JOIN product_ids_cte pic ON pd.id = pic.package_id\n" +
            "WHERE \n" +
                    "(:value = ''  " +
                    "OR (:isName = 1 AND pd.name ILIKE ('%' || :value || '%')) \n" +
                    "OR (:isCode = 1 AND pd.code ILIKE ('%' || :value || '%')))\n" +
                    "AND (:approveStatus = -1 OR pd.state = :approveStatus ) \n" +
                    "AND (:displayStatus = -1 OR pd.visibility = :displayStatus) \n" +
                    "AND (:customerType  = 'ALL' OR (EXISTS (SELECT 1\n" +
                    "FROM jsonb_array_elements_text(pd.apply_condition::::jsonb -> 'customerTypes') AS ct\n" +
                    "WHERE ct = :customerType )))\n" +
                    "AND (-1 IN :createdIds OR pd.created_by IN :createdIds)\n" +
                    "AND (-1 = :userId OR pd.provider_id = :userId)\n" +
                    "AND (pd.deleted_flag = 1)\n" +
                    "AND ('-1' IN :customerTypeLst OR (EXISTS (SELECT 1\n" +
                         "FROM jsonb_array_elements_text(pd.apply_condition::::jsonb -> 'customerTypes') AS ctLst\n" +
                         "WHERE ctLst IN :customerTypeLst )))";

    public static final String GET_LIST_SERVICE_PRODUCT_BY_PACKAGE_IDS =
            "SELECT \n" +
                    "    p.id AS id,\n" +
                    "    p.pricing_name AS pricingName,\n" +
                    "    p.created_at AS createdAt,\n" +
                    "    u.name AS providerName,\n" +
                    "    p.approve AS approvalStatus,\n" +
                    "    pm.package_draft_id AS packageDraftId,\n" +
                    "    COALESCE((pi.total_amount), 0) + COALESCE(SUM(pia.total_amount), 0) AS price\n" +
                    "FROM {h-schema}package_mappings pm\n" +
                    "   JOIN {h-schema}package_items pi ON pi.id = pm.package_item_id\n" +
                    "   LEFT JOIN {h-schema}package_item_addons pia ON pia.package_item_id = pi.id\n" +
                    "   JOIN {h-schema}pricing_multi_plan pmp ON pmp.id = pi.plan_id\n" +
                    "   JOIN {h-schema}pricing p ON p.id = pmp.pricing_id\n" +
                    "   LEFT JOIN {h-schema}users u ON p.created_by = u.id\n" +
                    "WHERE (-1 IN :productIds OR p.id IN :productIds) AND pm.package_id IS NOT NULL\n" +
                    "   GROUP BY p.id, p.pricing_name, p.created_at, u.name, p.approve, pm.package_draft_id,pi.total_amount\n";

    public static final String GET_LIST_CREATED_BY_IN_SEARCH =
            "SELECT DISTINCT u.id," +
                    "CASE u.portal\n" +
                    "WHEN 'DEV' THEN CONCAT(u.first_name,' ',u.last_name,'-','DEV')\n" +
                    "WHEN 'ADMIN' THEN CONCAT(u.first_name,' ',u.last_name,'-','ADMIN')\n" +
                    "ELSE CONCAT(u.first_name,' ',u.last_name) \n" +
                    "END as name \n" +
            "FROM package_draft pd JOIN \n" +
                    "users u ON pd.created_by = u.id";

    public static final String GET_LIST_PRODUCT_IN_SEARCH =
            "SELECT DISTINCT  p.id as id , p.pricing_name as name\n" +
                    "  FROM package_draft pd \n" +
                    "            JOIN package_mappings pm ON pm.package_draft_id = pd.id \n" +
                    "            JOIN package_items pi ON pm.package_item_id = pi.id \n" +
                    "            JOIN pricing_multi_plan pmp ON pmp.id = pi.plan_id \n" +
                    "            JOIN pricing p ON p.id = pmp.pricing_id ";

    public static final String IS_LATEST_VERSION =
        "SELECT :packageId = max(id) FROM {h-schema}packages WHERE draft_id = :draftId GROUP BY draft_id";


    public static final String GET_SUGGEST_GROUP_DETAIL =
        "select \n" +
            "    packages.id, \n" +
            "    packages.draft_id as draftId, \n" +
            "    packages.name as name, \n" +
            "    provider.name as providerName, \n" +
            "    cast(array_agg(categories.name) as varchar) as categoryNames \n" +
            "from {h-schema}packages \n" +
            "    left join {h-schema}users provider on provider.id = packages.provider_id \n" +
            "    left join {h-schema}categories on categories.id = any(packages.category_ids) \n" +
            "where packages.draft_id = :packageDraftId and packages.deleted_flag = 1 and packages.visibility = 1 \n" +
            "group by packages.id, packages.draft_id, packages.name, provider.name \n" +
            "order by packages.id desc limit 1";

    public static final String GET_SUGGEST_ITEM_DETAIL =
        "with stats as ( \n" +
            "    select \n" +
            "        count(distinct subscriptions.id) as sold_number, \n" +
            "        count(distinct evaluations.id) as num_rating, \n" +
            "        coalesce(avg(evaluation_ratings.star), 0) as avg_rating \n" +
            "    from {h-schema}subscription_metadata metadata \n" +
            "        join {h-schema}subscriptions on subscriptions.id = metadata.subscription_id \n" +
            "        join {h-schema}product_solutions solution on solution.id = metadata.solution_id \n" +
            "        join {h-schema}packages on packages.id = metadata.package_id \n" +
            "        left join {h-schema}evaluations on evaluations.subscription_id = subscriptions.id \n" +
            "        left join {h-schema}evaluation_ratings on evaluation_ratings.evaluation_id = evaluations.id \n" +
            "    where \n" +
            "        subscriptions.deleted_flag = 1 and subscriptions.confirm_status = 1 and subscriptions.reg_type = 1 and \n" +
            "        solution.draft_id = :solutionDraftId and solution.deleted_flag = 1 and solution.visibility = 1 and \n" +
            "        packages.draft_id = :packageDraftId and packages.deleted_flag = 1 and packages.visibility = 1 \n" +
            ") \n" +
            "select \n" +
            "    packages.id, \n" +
            "    packages.draft_id as draftId, \n" +
            "    solution.id as parentId, \n" +
            "    packages.name as name, \n" +
            "    stats.sold_number as soldNumber, \n" +
            "    stats.num_rating as numRating, \n" +
            "    stats.avg_rating as avgRating \n" +
            "from {h-schema}solution_packages mapping \n" +
            "    left join stats on true \n" +
            "    left join {h-schema}packages on packages.id = mapping.package_id \n" +
            "    left join {h-schema}product_solutions solution on solution.id = mapping.solution_id \n" +
            "where \n" +
            "    packages.draft_id = :packageDraftId and \n" +
            "    packages.deleted_flag = 1 and \n" +
            "    packages.visibility = 1 and \n" +
            "    solution.draft_id = :solutionDraftId and \n" +
            "    solution.deleted_flag = 1 and \n" +
            "    solution.visibility = 1 \n" +
            "order by solution.id desc, packages.id desc limit 1 ";
}

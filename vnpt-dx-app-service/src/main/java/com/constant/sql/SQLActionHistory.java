package com.constant.sql;

public class SQLActionHistory {

    public static final String GET_LIST_OBJECT_ACTION_HISTORY =
        "WITH userRole AS ("
            + "SELECT \n"
            + "          users_roles.user_id AS userId, \n"
            + "         '{' || string_agg(text(users_roles.role_id), ',') || '}' AS lstRoleId, \n"
            + "         '{' || string_agg(role.name, ',') || '}' AS lstRoleName \n"
            + "     FROM {h-schema}users_roles \n"
            + "     JOIN {h-schema}role ON users_roles.role_id = role.id \n"
            + "     GROUP BY users_roles.user_id \n"
            + "), \n"
            + "actionFileAttach as (\n"
            + "  select fileAttach.object_id, cast(json_agg(fileAttach.*) as text) as file_attach_json \n"
            + "       from ( \n"
            + "         select id, object_id, file_path \n"
            + "         from {h-schema}file_attach \n"
            + "         where object_type = 11000 \n"
            + "  ) as fileAttach \n"
            + "  group by fileAttach.object_id \n"
            + ")" +
        "SELECT \n"
            + "     actHistory.id AS id, \n"
            + "     actHistory.object_type AS objectType, \n"
            + "     actHistory.object_id AS objectId, \n"
            + "     actHistory.action_code AS actionCode, \n"
            + "     actHistory.action_name AS actionName, \n"
            + "     actHistory.action_type AS actionType, \n"
            + "     CASE \n"
            + "         WHEN actHistory.content = '' AND actHistory.action_name = 'Báo giá đã gửi' THEN \n"
            + "             'Báo giá được thay đổi trạng thái từ đang xử lí đến Báo giá đã gửi' \n"
            + "         ELSE \n"
            + "             actHistory.content \n"
            + "     END AS content, \n"
            + "     actHistory.created_by AS actorId,  \n"
            + "     concat_ws(' ', users.last_name, users.first_name) AS actorName, \n"
            + "     users.email AS actorEmail, \n"
            + "     CASE \n"
            + "            WHEN cast(userRole.lstRoleId as int8[]) && cast(array[1,2,5,9,12,50] as int8[]) THEN 'Admin' \n"
            + "            WHEN cast(userRole.lstRoleId as int8[]) && cast(array[4,7,8,10] as int8[]) THEN 'Dev' \n"
            + "     ELSE 'SME' \n"
            + "     END AS actorRole, \n"
            + "     actHistory.created_at AS createdAt, \n"
            + "     coalesce(actHistory.creation_time, actHistory.created_at)  AS creationTime, \n"
            + "     coalesce(actionFileAttach.file_attach_json, '{}') as fileAttachJson, \n"
            + "     COALESCE(actHistoryType.predefined, false) AS isPredefined \n"
            + "FROM {h-schema}action_history AS actHistory \n"
            + "     LEFT JOIN {h-schema}users ON actHistory.created_by = users.id \n"
            + "     LEFT JOIN userRole ON userRole.userId = actHistory.created_by \n"
            + "     LEFT JOIN {h-schema}action_history_type AS actHistoryType ON actHistoryType.code = actHistory.action_type \n"
            + "     LEFT JOIN actionFileAttach on actionFileAttach.object_id = actHistory.id \n"
            + "WHERE actHistory.action_code <> 'QUOTATION8' AND \n"
            + "     (\n"
            + "         (actHistory.object_type = :objectType) AND \n"
            + "         (\n"
            + "            (:objectType <> 2) OR \n"
            + "            (:isSuperAdmin = true OR actHistory.action_type NOT IN (1611, 1612) OR actHistory.created_by = :currentUserId) \n"
            + "         ) \n"
            + "     ) AND \n"
            + "     (actHistory.object_id = :objectId) AND \n"
            + "     ( CAST('1970-01-01' AS date) = :startDate OR CAST(actHistory.created_at AS DATE) >= :startDate ) AND \n"
            + "     ( CAST('1970-01-01' AS date) = :endDate OR CAST(actHistory.created_at AS DATE) <= :endDate ) AND \n"
            + "     (-1 = :actionType OR actHistory.action_type = :actionType) \n";

    public static final String GET_LIST_USER_ENTERPRISE_ACTION_HISTORY =
        "WITH userRole AS ("
            + "SELECT \n"
            + "          users_roles.user_id AS userId, \n"
            + "         '{' || string_agg(text(users_roles.role_id), ',') || '}' AS lstRoleId, \n"
            + "         '{' || string_agg(role.name, ',') || '}' AS lstRoleName \n"
            + "     FROM {h-schema}users_roles \n"
            + "     JOIN {h-schema}role ON users_roles.role_id = role.id \n"
            + "     GROUP BY users_roles.user_id \n"
            + ") \n" +
            "SELECT \n"
            + "     actHistory.id AS id, \n"
            + "     actHistory.object_type AS objectType, \n"
            + "     actHistory.object_id AS objectId, \n"
            + "     actHistory.action_code AS actionCode, \n"
            + "     actHistory.action_name AS actionName, \n"
            + "     actHistory.content AS content, \n"
            + "     actHistory.created_by AS actorId,  \n"
            + "     concat_ws(' ', users.last_name, users.first_name) AS actorName, \n"
            + "     users.email AS actorEmail, \n"
            + "     CASE \n"
            + "            WHEN cast(userRole.lstRoleId as int8[]) && cast(array[1,2,5,9,12,50] as int8[]) THEN 'Admin' \n"
            + "            WHEN cast(userRole.lstRoleId as int8[]) && cast(array[4,7,8,10] as int8[]) THEN 'Dev' \n"
            + "     ELSE 'SME' \n"
            + "     END AS actorRole, \n"
            + "     actHistory.created_at AS createdAt, \n"
            + "     COALESCE(actHistoryType.predefined, false) AS isPredefined, \n"
            + "     actHistory.action_type AS actionType \n"
            + "FROM {h-schema}action_history AS actHistory \n"
            + "     LEFT JOIN {h-schema}users ON actHistory.created_by = users.id \n"
            + "     LEFT JOIN userRole ON userRole.userId = actHistory.created_by \n"
            + "     LEFT JOIN {h-schema}action_history_type AS actHistoryType ON actHistoryType.code = actHistory.action_type \n"
            + "WHERE \n"
            + "     (\n"
            + "         (actHistory.object_type = 0 AND actHistory.object_id = :userId) OR \n"
            + "         (actHistory.object_type = 10 AND actHistory.object_id = :enterpriseId) \n"
            + "       ) AND \n"
            + "     ( CAST('1970-01-01' AS date) = :startDate OR CAST(actHistory.created_at AS DATE) >= :startDate ) AND \n"
            + "     ( CAST('1970-01-01' AS date) = :endDate OR CAST(actHistory.created_at AS DATE) <= :endDate ) AND \n"
            + "     (-1 = :actionType OR actHistory.action_type = :actionType) ";

    public static final String GET_PAGE_ALL_OBJECT_ACTION_HISTORY =
        "(  \n" +
        "    with actionFileAttach as ( \n" +
        "        select fileAttach.object_id, cast(json_agg(fileAttach.*) as text) as file_attach_json \n" +
        "        from ( \n" +
        "            select id, object_id, file_path from {h-schema}file_attach where object_type = 11001 \n" +
        "        ) as fileAttach \n" +
        "        group by fileAttach.object_id \n" +
        "    )  \n" +
        "    SELECT   \n" +
        "        'NOTE' as type,  \n" +
        "        actNote.id AS id,   \n" +
        "        actNote.title AS title,   \n" +
        "        actNote.content AS content,   \n" +
        "        creator.id AS creatorId,   \n" +
        "        CONCAT_WS(' ', creator.last_name, creator.first_name) AS creatorName,   \n" +
        "        creator.email AS creatorEmail,   \n" +
        "        COALESCE(actNote.creation_time, actNote.created_at) as createdAt,   \n" +
        "        coalesce(actionFileAttach.file_attach_json, '{}') as fileAttachJson, \n" +
        "        COALESCE(actNote.modified_at, actNote.created_at) AS lastModifiedAt,  \n" +
        "        cast(null as int) as objectType,  \n" +
        "        cast(null as bigint) AS objectId,   \n" +
        "        null AS actionCode,   \n" +
        "        null AS actionName,   \n" +
        "        cast(null as int4) AS actionType,   \n" +
        "        cast(null as bigint) AS actorId,    \n" +
        "        null AS actorName,   \n" +
        "        null AS actorEmail,   \n" +
        "        null AS actorRole,   \n" +
        "        coalesce(actNote.creation_time, actNote.created_at) AS creationTime,   \n" +
        "        cast(null as bool) AS isPredefined,  \n" +
        "        cast(null as int) as status  \n" +
        "    FROM {h-schema}action_history_note AS actNote   \n" +
        "        LEFT JOIN {h-schema}users AS creator ON creator.id = actNote.created_by   \n" +
        "        LEFT JOIN actionFileAttach on actionFileAttach.object_id = actNote.id   \n" +
        "    WHERE   \n" +
        "        (:search = '' OR actNote.title ILIKE ('%' || :search || '%') OR actNote.content ILIKE ('%' || :search || '%')) AND   \n" +
        "        ( CAST('1970-01-01' AS date) = :startDate OR CAST(actNote.created_at AS DATE) >= :startDate ) AND   \n" +
        "        ( CAST('1970-01-01' AS date) = :endDate OR CAST(actNote.created_at AS DATE) <= :endDate ) and   \n" +
        "        (actNote.object_type = :objectNoteType AND actNote.object_id = :objectId) \n" +
        ")  \n" +
        "union   \n" +
        "(  \n" +
        "    WITH userRole AS (  \n" +
        "        SELECT   \n" +
        "                  users_roles.user_id AS userId,   \n" +
        "                 '{' || string_agg(text(users_roles.role_id), ',') || '}' AS lstRoleId,   \n" +
        "                 '{' || string_agg(role.name, ',') || '}' AS lstRoleName   \n" +
        "             FROM {h-schema}users_roles   \n" +
        "             JOIN {h-schema}role ON users_roles.role_id = role.id   \n" +
        "             GROUP BY users_roles.user_id   \n" +
        "    ),   \n" +
        "    actionFileAttach as (  \n" +
        "        select fileAttach.object_id, cast(json_agg(fileAttach.*) as text) as file_attach_json " +
        "        from ( \n" +
        "            select id, object_id, file_path from {h-schema}file_attach where object_type = 11000 \n" +
        "        ) as fileAttach \n" +
        "        group by fileAttach.object_id \n" +
        "    )  \n" +
        "    SELECT   \n" +
        "         'ACTION' as type,  \n" +
        "         actHistory.id AS id,   \n" +
        "         null as title,  \n" +
        "         CASE  \n" +
        "           WHEN actHistory.content = '' AND actHistory.action_name = 'Báo giá đã gửi' THEN  \n" +
        "               'Báo giá được thay đổi trạng thái từ đang xử lí đến Báo giá đã gửi'  \n" +
        "           ELSE  \n" +
        "               actHistory.content  \n" +
        "         END AS content,  \n" +
        "         cast(null as bigint) as creatorId,  \n" +
        "         null as creatorName,  \n" +
        "         null as creatorEmail,  \n" +
        "         actHistory.created_at as createdAt,  \n" +
        "         coalesce(actionFileAttach.file_attach_json, '{}') as fileAttachJson, \n" +
        "         COALESCE(actHistory.modified_at, actHistory.created_at) AS lastModifiedAt,  \n" +
        "         actHistory.object_type AS objectType,   \n" +
        "         actHistory.object_id AS objectId,   \n" +
        "         actHistory.action_code AS actionCode,   \n" +
        "         actHistory.action_name AS actionName,   \n" +
        "         actHistory.action_type AS actionType,   \n" +
        "         actHistory.created_by AS actorId,    \n" +
        "         concat_ws(' ', users.last_name, users.first_name) AS actorName,   \n" +
        "         users.email AS actorEmail,   \n" +
        "         CASE   \n" +
        "                WHEN cast(userRole.lstRoleId as int8[]) && cast(array[1,2,5,9,12,50] as int8[]) THEN 'Admin'   \n" +
        "                WHEN cast(userRole.lstRoleId as int8[]) && cast(array[4,7,8,10] as int8[]) THEN 'Dev'   \n" +
        "         ELSE 'SME'   \n" +
        "         END AS actorRole,   \n" +
        "         coalesce(actHistory.creation_time, actHistory.created_at)  AS creationTime,   \n" +
        "         COALESCE(actHistoryType.predefined, false) AS isPredefined,  \n" +
        "         cast(null as int) as status  \n" +
        "    FROM {h-schema}action_history AS actHistory   \n" +
        "         LEFT JOIN {h-schema}users ON actHistory.created_by = users.id   \n" +
        "         LEFT JOIN userRole ON userRole.userId = actHistory.created_by   \n" +
        "         LEFT JOIN {h-schema}action_history_type AS actHistoryType ON actHistoryType.code = actHistory.action_type   \n" +
        "         LEFT JOIN actionFileAttach on actionFileAttach.object_id = actHistory.id   \n" +
        "    WHERE   \n" +
        "         (:search = '' OR actHistoryType.name ILIKE ('%' || :search || '%') OR actHistory.content ILIKE ('%' || :search || '%')) AND   \n" +
            "         ( \n" +
            "            (:objectType in (0,10) AND -- lấy tất cả log của objectType ENTERPRISE + USER\n" +
            "                (\n" +
            "                    (actHistory.object_type = 0 and actHistory.object_id = :userId) OR \n" +
            "                    (actHistory.object_type = 10 and actHistory.object_id = :enterpriseId)\n" +
            "                )\n" +
            "            ) OR\n" +
            "            (\n" +
            "                :objectType not in (0,10) and \n" +
            "                 (  \n" +
            "                     (actHistory.object_type = :objectType) AND   \n" +
            "                     (  \n" +
            "                        (:objectType <> 2) OR   \n" +
            "                        (:isSuperAdmin = true OR actHistory.action_type NOT IN (1611, 1612) OR actHistory.created_by = :currentUserId)   \n" +
            "                     ) AND (actHistory.object_id = :objectId)    \n" +
            "                 ) \n" +
            "            )\n" +
        "         ) AND   \n" +
        "         ( CAST('1970-01-01' AS date) = :startDate OR CAST(actHistory.created_at AS DATE) >= :startDate ) AND   \n" +
        "         ( CAST('1970-01-01' AS date) = :endDate OR CAST(actHistory.created_at AS DATE) <= :endDate ) AND   \n" +
        "         (-1 in (:actionType) OR actHistory.action_type in (:actionType)) AND   \n" +
        "         (actHistory.object_type <> 60 OR actHistory.action_type <> 6108 OR actHistory.created_by <> -1) \n" +
        ")  \n" +
        "union   \n" +
        "(  \n" +
        "    select   \n" +
        "       'NOTIFY' as type,\n" +
        "       mail_send_history.id as id,\n" +
        "       mail_send_history.subject as title,\n" +
        "       mail_send_history.content as content,\n" +
        "       mail_send_history.create_by as creatorId,\n" +
        "       concat_ws(' ', users.last_name, users.first_name) as creatorName,\n" +
        "       users.email as creatorEmail,\n" +
        "       mail_send_history.create_at as createdAt,\n" +
        "       '{}' as fileAttachJson,\n" +
        "       mail_send_history.modified_at as lastModifiedAt,\n" +
        "       mail_send_history.object_type as objectType,\n" +
        "       mail_send_history.object_id AS objectId,\n" +
        "       'QUOTATION8' AS actionCode,\n" +
        "       'Gửi mail' AS actionName,\n" +
        "       cast(null as int4) AS actionType,\n" +
        "       cast(null as bigint) AS actorId,\n" +
        "       null AS actorName,\n" +
        "       null AS actorEmail,\n" +
        "       null AS actorRole,\n" +
        "       mail_send_history.create_at AS creationTime,\n" +
        "       cast(null as bool) AS isPredefined,\n" +
        "       mail_send_history.send_status as status\n" +
        "    from   \n" +
        "        {h-schema}mail_send_history   \n" +
        "    left join \n" +
        "        {h-schema}users on mail_send_history.create_by = users.id   \n" +
        "    where   \n" +
        "        CASE   \n" +
        "           WHEN :objectType in (0, 1, 2) THEN mail_send_history.object_type in (2, 3) -- nếu là tk user(sme/admin/affiliate) -- \n" +
        "           WHEN :objectType = 10 THEN mail_send_history.object_type = 0 \n" +
        "           WHEN :objectType = 20 THEN mail_send_history.object_type = 1 \n" +
        "           ELSE false \n" +
        "        END and  \n" +
        "        mail_send_history.object_id = :objectId and \n" +
        "        (:search = '' OR mail_send_history.subject ILIKE ('%' || :search || '%') OR 'Gửi mail' ILIKE ('%' || :search || '%')) AND \n" +
        "        ('ALL' IN (:lstReceiver) OR mail_send_history.receiver in (:lstReceiver)) AND \n" +
        "        (date('1970-01-01') = date(:startDate) or date(:startDate) <= date(mail_send_history.create_at)) and   \n" +
        "        (date('1970-01-01') = date(:endDate) or date(:endDate) >= date(mail_send_history.create_at))   \n" +
        ") \n" +
        "UNION \n" +
        "(\n" +
        "    WITH userRole AS (  \n" +
        "        SELECT   \n" +
        "              users_roles.user_id AS userId,   \n" +
        "             '{' || string_agg(text(users_roles.role_id), ',') || '}' AS lstRoleId,   \n" +
        "             '{' || string_agg(role.name, ',') || '}' AS lstRoleName   \n" +
        "         FROM {h-schema}users_roles   \n" +
        "         JOIN {h-schema}role ON users_roles.role_id = role.id   \n" +
        "         GROUP BY users_roles.user_id   \n" +
        "    )\n" +
        "     SELECT   \n" +
        "         'ACTION' as type,  \n" +
        "         d.id AS id,   \n" +
        "         null as title,  \n" +
        "         d.content AS content,  \n" +
        "         cast(null as bigint) as creatorId,  \n" +
        "         null as creatorName,  \n" +
        "         null as creatorEmail,  \n" +
        "         d.actionDate as createdAt,  \n" +
        "         null as fileAttachJson,  \n" +
        "         d.actionDate AS lastModifiedAt,  \n" +
        "         null AS objectType,   \n" +
        "         null AS objectId,   \n" +
        "         null AS actionCode,   \n" +
        "         d.actionName AS actionName,   \n" +
        "         d.actionType AS actionType,   \n" +
        "         d.actorId AS actorId,    \n" +
        "         concat_ws(' ', users.last_name, users.first_name) AS actorName,   \n" +
        "         d.actorEmail AS actorEmail,   \n" +
        "         CASE   \n" +
        "                WHEN cast(userRole.lstRoleId as int8[]) && cast(array[1,2,5,9,12,50] as int8[]) THEN 'Admin'   \n" +
        "                WHEN cast(userRole.lstRoleId as int8[]) && cast(array[4,7,8,10] as int8[]) THEN 'Dev'   \n" +
        "         ELSE 'SME'   \n" +
        "         END AS actorRole,     \n" +
        "         d.actionDate  AS creationTime,   \n" +
        "         true AS isPredefined,  \n" +
        "         cast(null as int) as status  \n" +
        "    FROM \n" +
        "        (\n" +
        "         select \n" +
        "             se.id as id, \n" +
        "             'Đánh giá sản phẩm' as actionName, \n" +
        "             2 as actionType, \n" +
        "             cmt.comment as content, \n" +
        "             -2 AS contentType, \n" +
        "              coalesce(se.modified_at, se.created_at) as actionDate, \n" +
        "              sub.id as subscription_id, \n" +
        "              actor.id as actorId, \n" +
        "              actor.email as actorEmail \n" +
        "         from {h-schema}service_evaluation se \n" +
        "         inner join {h-schema}subscriptions as sub on sub.service_id = se.service_id and sub.user_id = se.user_id \n" +
        "         left join {h-schema}users actor on actor.id = se.created_by \n" +
        "         left join {h-schema}service_evaluation_cmt as cmt on cmt.service_evaluation_id = se.id \n" +
        "         union \n" +
        "         select \n" +
        "              sh.id as id, \n" +
        "              CASE \n" +
        "                  WHEN sh.content_type IN (0, 30) THEN 'Đăng ký dùng thử'\n" +
        "                  WHEN sh.content_type IN (4,5,29,31) THEN 'Đăng ký thuê bao'\n" +
        "                  ELSE 'Cập nhật thuê bao'\n" +
        "              END AS actionName,\n" +
        "              CASE \n" +
        "                  WHEN sh.content_type IN (0, 30) THEN 4\n" +
        "                  WHEN sh.content_type IN (4,5,29,31) THEN 5\n" +
        "                  ELSE 3\n" +
        "              END AS actionType,\n" +
        "              sh.content as content, \n" +
        "              sh.content_type as contentType, \n" +
        "              sh.created_at as actionDate, \n" +
        "              sh.subscription_id as subscription_id, \n" +
        "              actor.id as actorId, \n" +
        "              actor.email as actorEmail \n" +
        "         from {h-schema}subscription_history sh \n" +
        "         left join {h-schema}users actor on actor.id = sh.created_by \n" +
        "        )as d \n" +
        "         inner join {h-schema}subscriptions s on subscription_id = s.id \n" +
        "         inner join {h-schema}users on users.id = s.user_id \n" +
        "         inner join {h-schema}enterprise as en on en.user_id = users.id \n" +
        "         LEFT JOIN userRole ON userRole.userId = d.actorId\n" +
        "         WHERE :objectType != 60 AND \n" +
        "               :objectId = en.id AND \n" +
        "                (:search = '' OR \n" +
        "                    d.actionName ILIKE ('%' || :search || '%') OR \n" +
        "                    d.content ILIKE ('%' || :search || '%')) AND   \n" +
        "                ( CAST('1970-01-01' AS date) = :startDate OR CAST(d.actionDate AS DATE) >= :startDate ) AND   \n" +
        "                ( CAST('1970-01-01' AS date) = :endDate OR CAST(d.actionDate AS DATE) <= :endDate )   \n" +
        ")";

    public static final String GET_LIST_EMAIL_SMS_HISTORY =
        "select \n" +
            "    mail_send_history.create_at as createdAt, \n" +
            "    mail_send_history.subject as title, \n" +
            "    mail_send_history.send_status as status \n" +
            "from \n" +
            "    {h-schema}mail_send_history \n" +
            "where \n" +
            "    mail_send_history.type = :type and \n" +
            "    ((:type = 0 and mail_send_history.receiver in (:lstEmail)) or \n" +
            "        (:type = 2 and mail_send_history.receiver in (:lstPhone))) and \n" +
            "    (date('1970-01-01') = date(:startDate) or date(:startDate) <= date(mail_send_history.create_at)) and \n" +
            "    (date('3000-01-01') = date(:endDate) or date(:endDate) >= date(mail_send_history.create_at))";

    public static final String GET_LIST_QUOTATION_LOGGED_ACTION_TYPE =
        "SELECT \n" +
            "    DISTINCT CAST(action_type AS int8) \n" +
            "FROM {h-schema}action_history \n" +
            "WHERE object_type = 60 AND object_id = :quotationId ";


}

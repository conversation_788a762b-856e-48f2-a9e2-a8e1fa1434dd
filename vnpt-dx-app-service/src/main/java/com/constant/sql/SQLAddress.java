package com.constant.sql;

public class SQLAddress {

    public static final String GET_ADDRESS_DEFAULT_BY_TYPE =
            "select \n" +
            "    * \n" +
            "from {h-schema}address a \n" +
            "where a.default_location = 1 and a.type = :type and a.user_id = :userId limit 1\n";

    public static final String SEARCH =
            "SELECT * \n" +
                    "FROM {h-schema}address a \n" +
                    "WHERE \n" +
                    "   type = 1" +
                    "   and :address = '' OR a.address ILIKE ('%' || :address || '%') \n" +
                    "ORDER BY a.id DESC";

    public static final String ADDRESS_USER =
            "SELECT CONCAT(COALESCE(sd.name,''),' ',COALESCE(w.name,''),' ',COALESCE(d.name,''),' ',COALESCE(p.name,''),' ',na.name)\n" +
                    "FROM {h-schema}users u  \n" +
                    "	LEFT JOIN {h-schema}province p ON p.code = u.province_code  \n" +
                "	LEFT JOIN {h-schema}street sd ON sd.id = u.street_id AND sd.ward_id = u.ward_id AND sd.district_id = u.district_id AND sd.province_code = p.code \n" +
                    "	LEFT JOIN {h-schema}district d ON d.id = u.district_id AND d.province_id = p.id  \n" +
                    "	LEFT JOIN {h-schema}ward w ON w.id = u.ward_id AND w.district_id = d.id AND w.province_code = p.code  \n" +
                    "	LEFT join {h-schema}nation na on na.id = u.nation_id\n" +
                    "	WHERE u.id = :userId";
}

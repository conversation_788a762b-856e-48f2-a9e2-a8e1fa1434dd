package com.enums;

import java.util.HashMap;
import java.util.Map;


public enum EventTypeEnum {
    // COUPON Events
    COUPON_EXPIRED("COUPON_EXPIRED"),
    COUPON_APPLY_EXCEED("COUPON_APPLY_EXCEED"),
    COUPON_UPGRADED("COUPON_UPGRADED"),

    // PRODUCT Events
    PRICING_STATUS_CHANGED("PRICING_STATUS_CHANGED"),
    PRICING_UPGRADED("PRICING_UPGRADED"),
    VARIANT_STATUS_CHANGED("VARIANT_STATUS_CHANGED"),
    VARIANT_UPGRADED("VARIANT_UPGRADED"),
    ADDON_STATUS_CHANGED("ADDON_STATUS_CHANGED"),
    ADDON_UPGRADED("ADDON_UPGRADED"),

    // ORDER Events
    ORDER_STATUS("ORDER_STATUS"),

    // PAYMENT Events
    PAYMENT_STATUS("PAYMENT_STATUS"),

    // SHIPMENT Events
    SHIPPING_STATUS("SHIPPING_STATUS"),

    // RECONCILIATION Events
    RECONCILIATION_CREATED("RECONCILIATION_CREATED"),
    RECONCILIATION_COMPLETED("RECONCILIATION_COMPLETED"),
    RECONCILIATION_PAYMENT_SUCCESS("RECONCILIATION_PAYMENT_SUCCESS");

    public final String value;

    EventTypeEnum(String value) {
        this.value = value;
    }

    private static final Map<String, EventTypeEnum> map = new HashMap<>();

    static {
        for (EventTypeEnum eventType : EventTypeEnum.values()) {
            map.put(eventType.value, eventType);
        }
    }

    public static EventTypeEnum valueOfLabel(String value) {
        return map.get(value);
    }
}

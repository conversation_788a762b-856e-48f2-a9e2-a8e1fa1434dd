package com.controller.email;

import java.util.Date;
import java.util.List;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.dto.email.ArrIdDTO;
import com.onedx.common.constants.values.DatabaseConstant;
import com.onedx.common.entity.emails.MailSendHistory;
import com.onedx.common.entity.emails.MailSendMain;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.repository.emails.EmailMainRepository;
import com.service.email.MailSendMainService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> QuyenCV
 * @version 1.3 09/28/2021
 */
@RestController
@RequestMapping("/api/admin-portal/history-email")
@Slf4j(topic = "APPLICATION")
@Validated
public class MailHistoryController {

    @Autowired
    MailSendMainService mailSendMainService;

    @Autowired
    EmailMainRepository emailMainRepository;

    @GetMapping("")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<MailSendMain>> getAllHistory(
            @PageableDefault(sort = {"createAt"}, direction = Sort.Direction.DESC) Pageable pageable,
            @RequestParam(name = "type", defaultValue = "-1", required = false) int type,
            @RequestParam(name = "receiver", defaultValue = "", required = false) String receiver,
            @RequestParam(name = "subject", defaultValue = "", required = false) String subject,
            @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date startDate,
            @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date endDate
    ) {
        if (startDate == null) {
            startDate = DateUtil.toDate("01-01-1970", "dd-MM-yyyy");
        }
        if (endDate == null) {
            endDate = new Date();
        }
        Page<MailSendMain> listMailMain = mailSendMainService.getListMailSendMain(type, receiver, subject, startDate, endDate, pageable);
        return ResponseEntity.ok(listMailMain);
    }


    @GetMapping("/detail/{mainId}")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<MailSendHistory>> getAllHistoryDetailByMainId(
            @PathVariable Long mainId,
            @PageableDefault(sort = {"createAt"}, direction = Sort.Direction.DESC) Pageable pageable,
            @RequestParam(name = "sendType", defaultValue = "0", required = false) int sendType,
            @RequestParam(name = "receiver", defaultValue = "", required = false) String receiver,
            @RequestParam(name = "subject", defaultValue = "", required = false) String subject,
            @RequestParam(name = "startDate", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date startDate,
            @RequestParam(name = "endDate", required = false) @DateTimeFormat(pattern = "dd/MM/yyyy") Date endDate
    ) {
        MailSendMain main = mailSendMainService.getDetailMailMain(mainId);
        if (startDate == null) {
            startDate = DateUtil.toDate("01-01-1970", "dd-MM-yyyy");
        }
        if (endDate == null) {
            endDate = new Date();
        }
        Page<MailSendHistory> listMailMainDetail = mailSendMainService.getListMailSendMainDetail(main, sendType, receiver, subject, startDate, endDate, pageable);
        return ResponseEntity.ok(listMailMainDetail);
    }

    @DeleteMapping("{id}")
    public ResponseEntity<Void> deleteHistory(
            @PathVariable Long id
    ) {
        emailMainRepository.deleteById(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/batch-delete")
    public ResponseEntity<Void> batchDeleteHistory(@RequestBody ArrIdDTO arrId) {
        mailSendMainService.batchDeleteMailSendMain(arrId.getArrId());

        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();

    }

    @GetMapping("{id}")
    public ResponseEntity<MailSendMain> getDetail(@PathVariable Long id) {
        MailSendMain mailSendMain = mailSendMainService.getDetailMailMain(id);
        return ResponseEntity.ok(mailSendMain);
    }

    @GetMapping("/sub-detail/{id}")
    public ResponseEntity<MailSendHistory> getSubDetail(@PathVariable Long id) {
        MailSendHistory mailSendHistory = mailSendMainService.getDetailMailHistory(id);
        return ResponseEntity.ok(mailSendHistory);
    }

    @GetMapping("status/{id}")
    public ResponseEntity<MailSendHistory> getSendStatus(@PathVariable Long id) {
        MailSendHistory mailSendHistory = mailSendMainService.getSendStatus(id);
        return ResponseEntity.ok(mailSendHistory);
    }

    @PutMapping("/update-status-by-id-in")
    public void sendMail(@RequestBody List<Long> ids) {
        mailSendMainService.updateStatusByIdIn(ids);
    }

    @GetMapping("/{mainId}/mailing-list")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<Page<MailSendHistory>> getAllMailDetailByMainId(
            @PathVariable Long mainId,
            @ParameterObject Pageable pageable
    ) {
        Page<MailSendHistory> listMailMainDetail = mailSendMainService.getAllMailDetailByMainId(mainId, pageable);
        return ResponseEntity.ok(listMailMainDetail);
    }

    @GetMapping("{id}/detail")
    public ResponseEntity<MailSendMain> getDetailMailSendMain(@PathVariable Long id) {
        MailSendMain mailSendMain = mailSendMainService.getDetailMailSendMain(id);
        return ResponseEntity.ok(mailSendMain);
    }
}

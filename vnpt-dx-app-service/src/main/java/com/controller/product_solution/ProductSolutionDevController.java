package com.controller.product_solution;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.service.product_solutions.ProductSolutionService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> VinhNT
 * @version : 1.0 10/05/2021
 */
@RestController
@Slf4j
@Validated
@RequestMapping("/api/dev-portal/product-solutions")
public class ProductSolutionDevController {

    @Autowired
    private ProductSolutionService productSolutionService;

    @Operation(description = "<PERSON><PERSON><PERSON> yêu cầu phê du<PERSON><PERSON><PERSON> gi<PERSON><PERSON> ph<PERSON>p sản phẩm")
    @PostMapping("/submit")
    @PreAuthorize("@dynamicAccessAspect.hasPermission('P_QLGP_YEU_CAU_PHE_DUYET_GIAI_PHAP')")
    public void submitSolution(@RequestParam(name = "ids") List<Long> solutionDraftIds) {
        productSolutionService.submitSolution(solutionDraftIds);
    }
}

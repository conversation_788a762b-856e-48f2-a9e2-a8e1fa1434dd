package com.controller.product_solution;

import org.springframework.security.access.prepost.PreAuthorize;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.service.product_solutions.PackageBundlingService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/dev-portal/packages")
@RequiredArgsConstructor
public class PackageDevController {

    private final PackageBundlingService packageBundlingService;

    @Operation(description = "Gửi yêu cầu phê duyệt package")
    @PostMapping("/submit")
    @PreAuthorize("@dynamicAccessAspect.hasPermission('P_QLGB_YEU_CAU_PHE_DUYET_PACKAGE')")
    public void submitPackage(@RequestParam(name = "ids") List<Long> packageDraftIds) {
        packageBundlingService.submitPackage(packageDraftIds);
    }
}

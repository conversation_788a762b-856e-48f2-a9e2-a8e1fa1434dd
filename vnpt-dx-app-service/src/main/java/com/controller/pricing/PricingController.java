package com.controller.pricing;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.constant.enums.pricing.ValidateCodeAction;
import com.constant.enums.pricing.ValidateCodeType;
import com.onedx.common.dto.integration.backend.subscription.SubscriptionPricingPeriodAddonDTO;
import com.dto.pricing.PricingSaaSResDTO;
import com.entity.addons.AddonDraft;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.component.BaseController;
import com.component.BaseController.ListRequest;
import com.constant.enums.coupon.CouponPricingTypeEnum;
import com.constant.enums.coupon.CouponServiceTypeEnum;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.dto.coupons.CouponPopupTreeResDTO;
import com.dto.packages.PackageDetailDTO;
import com.dto.packages.PackageRegisterDTO;
import com.dto.pricing.IDetailPricingMultiPlanDTO;
import com.dto.pricing.IGetTopViewPricingDTO;
import com.dto.pricing.IPaymentCycleDTO;
import com.dto.pricing.PricingNameDTO;
import com.dto.pricing.PricingSettingReqDTO;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.services.OnOsTypeEnum;
import com.onedx.common.constants.values.DatabaseConstant;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Pricing;
import com.service.packages.PackageService;
import com.service.pricing.PricingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> VinhNT
 * @version    : 1.0
 * 10/05/2021
 */
@RestController
@RequestMapping("/api/portal/pricing")
@Slf4j
@Validated
public class PricingController {

    @Autowired
    private PricingService pricingService;

    @Autowired
    PackageService packageService;


    /**
     * Lấy danh sách các gói dịch vụ của admin-developer tạo
     */
    @GetMapping()
    @Operation(description = "Lấy danh sách các gói dịch vụ của admin-developer tạo")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<CouponPopupTreeResDTO> getListPricing(
        @Parameter(description = "Tìm kiếm theo tên dịch vụ", example = "gói dịch vụ 1")
        @RequestParam(name = "serviceName", required = false, defaultValue = "") String serviceName,
        @Parameter(description = "Tìm kiếm theo mã dịch vụ")
        @RequestParam(name = "serviceType", required = false, defaultValue = "ALL") CouponServiceTypeEnum serviceType,
        @Parameter(description = "Tìm kiếm theo loại dịch vụ")
        @RequestParam(name = "pricingName", required = false, defaultValue = "") String pricingName,
        @Parameter(description = "Tìm kiếm theo mã danh mục")
        @RequestParam(name = "pricingType", required = false, defaultValue = "ALL") CouponPricingTypeEnum pricingType,
        @Parameter(description = "Tìm kiếm theo mã danh mục")
        @RequestParam(name = "categoryId", required = false, defaultValue = "-1") Long categoryId,
        @Parameter(description = "list combo plan đã được chọn")
        @RequestParam(name = "paymentCycle", required = false, defaultValue = "-1") Integer paymentCycle,
        @Parameter(description = "list pricing id")
        @RequestParam(name = "pricingIds", required = false, defaultValue = "-1") Set<Long> pricingIds,
        @Parameter(description = "list pricing multi plan id")
        @RequestParam(name = "multiPlanId", required = false, defaultValue = "-1") Set<Long> multiPlanId,
        @Parameter(description = "list combo plan id")
        @RequestParam(name = "combosId", required = false, defaultValue = "-1") Set<Long> combosId,
        @Parameter(description = "list combo plan đã được chọn")
        @RequestParam(name = "cycleType", required = false, defaultValue = "UNSET") CycleTypeEnum cycleType,
        @Parameter(description = "list customer type")
        @RequestParam(required = false) Set<CustomerTypeEnum> customerTypes,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
        @RequestParam(name = "size", required = false, defaultValue = "50") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false) String sort,
        @RequestParam(name = "portalType", required = false) PortalType portalType) {
        log.info("--- Execute getListPricing method: Start--");
        BaseController.ListRequest listRequest = new BaseController.ListRequest(size, page, sort);
        CouponPopupTreeResDTO pricingListResDTOS = pricingService
            .getListPricing(listRequest.getPageable(), serviceName, serviceType, pricingName, pricingType, categoryId, paymentCycle, cycleType,
                pricingIds, multiPlanId, combosId, customerTypes, portalType);
        log.info("--- Execute getListPricing method: End--");
        return ResponseEntity.ok().body(pricingListResDTOS);
    }

    /**
     * Lấy danh sách tên các dịch vụ
     * @param name tên gói dịch vụ
     * @return danh sách tên gói dịch vụ
     */
    @GetMapping("/pricing-name")
    @Operation(description = "Lấy danh sách tên các dịch vụ")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<List<PricingNameDTO>> getListPricingName(
        @Parameter(description = Pricing.NAME, example = Example.PRICING_NAME)
        @RequestParam(name = "name", required = false, defaultValue = "") String name,
        @Parameter(description = Pricing.SERVICE_ID, example = Example.ID)
        @RequestParam(name = "serviceId", required = false, defaultValue = "-1") Long id) {
        log.info("--- Execute getListPricingName method: Start--");
        List<PricingNameDTO> res = pricingService.getListPricingName(name, id);
        log.info("--- Execute getListPricingName method: End--");
        return ResponseEntity.ok().body(res);
    }

    @GetMapping(value = "/checkCanRemoveCustomerType")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public ResponseEntity<List<AddonDraft>> checkCanRemoveCustomerType(
        @RequestParam(name = "pricingId") Long pricingId,
        @RequestParam(name = "customerType") String customerType
    )   {
        log.info("--- Execute checkCustomerType: Start--");
        List<AddonDraft> addonRelationPricingDTO = pricingService.checkCanRemoveCustomerType(pricingId ,  customerType );
        log.info("--- Execute checkCustomerType: Start--");
        return ResponseEntity.ok().body(addonRelationPricingDTO);
    }

    /**
     * Lấy thông tin thiết lập của gói dịch vụ
     *
     */
    @Operation(description = "Lấy thông tin thiết lập của gói dịch vụ")
    @GetMapping("/setting/{serviceId}/{type}")
    public ResponseEntity<PricingSettingReqDTO> getSettingInfo(
            @Parameter(description = SwaggerConstant.Pricing.SERVICE_ID, example = SwaggerConstant.Example.ID)
            @PathVariable Long serviceId,
            @PathVariable Integer type) {
        log.info("--- Execute getSettingInfo method: Start--");
        PricingSettingReqDTO result = pricingService.getSettingInfo(serviceId, type);
        log.info("--- Execute getSettingInfo method: End --");
        return ResponseEntity.ok(result);
    }

    /**
     * Lấy số chu kì của pricing by subId
     *
     */

    @GetMapping("/get-number-of-cycle")
    @Operation(description = "Lấy number-of-cycle của pricing theo subId")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Long> getNumberOfCycle(@RequestParam(name ="subId") Long subId) {
        log.info("--- Execute getNumberOfCycle method: Start--");
        Long numberOfCycle = pricingService.getNumberOfCycleBySubId(subId);
        log.info("--- Execute getNumberOfCycle method: End--");
        return ResponseEntity.ok().body(numberOfCycle);
    }

    @Operation(description = "Lấy danh sách addon gắn với một gói dịch vụ")
    @GetMapping("/{pricingId}/addons")
    public List<SubscriptionPricingPeriodAddonDTO> getAddonByPricingId(
        @Validated @NonNull @PathVariable Long pricingId,
        @RequestParam(required = false , name ="pricingMultiPlanId", defaultValue = "-1") Long pricingMultiPlanId){
        return pricingService.getAddonByPricingId(pricingId, pricingMultiPlanId);
    }

    /**
     * Lấy chi tiết gói dịch vụ SaaS
     *
     * @return the page
     */
    @Operation(description = "Lấy chi tiết gói dịch vụ SaaS")
    @GetMapping("/detail/{pricingId}")
    public ResponseEntity<PricingSaaSResDTO> getPricingSubscriptionByPricingId(
            @Parameter(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
            @PathVariable Long pricingId) {
        log.info("--- Execute getPricingSubscriptionByPricingId method: Start --");
        PricingSaaSResDTO response = pricingService.getPricingSubscriptionByPricingId(pricingId);
        log.info("--- Execute getPricingSubscriptionByPricingId method: End --");
        return ResponseEntity.ok(response);
    }

    /**
     * Danh sách các gói được xem nhiều nhất
     *
     * @param customerType        Loại khách hàng
     * @param serviceProductTypes Danh sách các productType của dịch vụ
     * @param categories          Danh sách category của dịch vụ
     */
    @Operation(description = "Lấy danh sách các gói phổ biến (có nhiều lượt xem) nhất")
    @GetMapping("/top-view")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Page<IGetTopViewPricingDTO> getTopView(
        @RequestParam(name = "customerType", required = false, defaultValue = "PERSONAL") CustomerTypeEnum customerType,
        @RequestParam(name = "serviceProductTypes", required = false, defaultValue = "UNSET") Set<ServiceProductTypeEnum> serviceProductTypes,
        @RequestParam(name = "categoryMigrationIds", required = false, defaultValue = "-1") Set<Long> categoryMigrationIds,
        @RequestParam(name = "serviceMigrationIds", required = false, defaultValue = "-1") Set<Long> serviceMigrationIds,
        @RequestParam(name = "categories", required = false, defaultValue = "-1") Set<Long> categories,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        ListRequest listRequest = new ListRequest(size, page, "ranking,asc");
        return pricingService.getTopView(customerType, serviceProductTypes, categoryMigrationIds, serviceMigrationIds, categories,
            listRequest.getPageable());
    }

    /**
     * Danh sách các gói bán chạy nhất
     *
     * @param customerType        Loại khách hàng
     * @param serviceProductTypes Danh sách các productType của dịch vụ
     * @param categories          Danh sách category của dịch vụ
     */
    @Operation(description = "Lấy danh sách các gói bán chạy nhất")
    @GetMapping("/top-selling")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Page<IGetTopViewPricingDTO> getTopSelling(
        @RequestParam(name = "customerType", required = false, defaultValue = "PERSONAL") CustomerTypeEnum customerType,
        @RequestParam(name = "serviceProductTypes", required = false, defaultValue = "UNSET") Set<ServiceProductTypeEnum> serviceProductTypes,
        @RequestParam(name = "categoryMigrationIds", required = false, defaultValue = "-1") Set<Long> categoryMigrationIds,
        @RequestParam(name = "serviceMigrationIds", required = false, defaultValue = "-1") Set<Long> serviceMigrationIds,
        @RequestParam(name = "categories", required = false, defaultValue = "-1") Set<Long> categories,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        ListRequest listRequest = new ListRequest(size, page, "ranking,asc");
        return pricingService.getTopSelling(customerType, serviceProductTypes, categoryMigrationIds, serviceMigrationIds, categories,
            listRequest.getPageable());
    }

    @Operation(description = "Lấy danh sách các gói cho KHCN theo id dịch vụ migration")
    @GetMapping("/personal")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Page<IGetTopViewPricingDTO> getPricing(
        @RequestParam(name = "serviceMigrationIds", required = false, defaultValue = "-1") Set<Long> serviceMigrationIds,
        @RequestParam(name = "pricingName", required = false, defaultValue = "") String pricingName,
        @RequestParam(name = "paymentCycle", required = false, defaultValue = "-1") Integer paymentCycle,
        @RequestParam(name = "circleType", required = false, defaultValue = "-1") Integer circleType,
        @RequestParam(name = "sort", required = false, defaultValue = "click,desc") String sort,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(name = "size", required = false, defaultValue = "100000") Integer size) {
        if(!(sort.contains("price") || sort.contains("desc"))) {
            sort = "id,desc";
        }
        ListRequest listRequest = new ListRequest(size, page, sort);
        return pricingService.getFromServiceByMigrationIds(pricingName, paymentCycle, circleType, serviceMigrationIds,
            listRequest.getPageable());
    }

    @Operation(description = "Lấy danh sách chu kỳ thanh toán theo id dịch vụ")
    @GetMapping("/personal/payment-cycle")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public List<IPaymentCycleDTO> getLstPaymentCycle (
        @RequestParam(name = "serviceMigrationIds",required = false, defaultValue = "-1") List<Long> serviceMigrationIds
    ) {
        return pricingService.getPaymentCycleByMigrationIds(serviceMigrationIds);
    }

    @Operation(description = "Lấy danh sách chu kỳ thanh toán theo id dịch vụ")
    @GetMapping("/personal/multi-plan")
    public List<IDetailPricingMultiPlanDTO> getLstMultiPlanByPricingId (
        @RequestParam(name = "pricingId",required = false, defaultValue = "-1") Long pricingId
    ) {
        return pricingService.getMultiPlanByPricingId(pricingId);
    }

    @GetMapping("get-all")
    @Transactional(label = DatabaseConstant.RWDB_TRANSACTIONAL_READONLY, readOnly = true)
    public Page<IGetTopViewPricingDTO> getListPackage(
        @RequestParam(name = "value", required = false, defaultValue = "") String value,
        @RequestParam(name = "paymentCycle", required = false, defaultValue = "-1") Integer paymentCycle,
        @RequestParam(name = "cycleType", required = false, defaultValue = "-1") Integer cycleType,
        @RequestParam(name = "price", required = false, defaultValue = "-1") Long price,
        @RequestParam(name = "OnOsTypeEnum", required = false, defaultValue = "UNSET") OnOsTypeEnum serviceOnOsType,
        @RequestParam(name = "customerType", required = false, defaultValue = "PERSONAL") CustomerTypeEnum customerType,
        @RequestParam(name = "serviceProductTypes", required = false, defaultValue = "UNSET") Set<ServiceProductTypeEnum> serviceProductTypes,
        @RequestParam(name = "categories", required = false, defaultValue = "-1") Set<Long> categories,
        @RequestParam(name = "categoryMigrationIds", required = false, defaultValue = "-1") Set<Long> categoryMigrationIds,
        @RequestParam(name = "serviceMigrationIds", required = false, defaultValue = "-1") Set<Long> serviceMigrationIds,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @RequestParam(name = "sort", required = false, defaultValue = "ranking,asc") String sort
    ) {
        ListRequest listRequest = new ListRequest(size, page, sort);
        return packageService.getListPackageBase(value, paymentCycle, cycleType, price, serviceOnOsType, customerType, categoryMigrationIds,
            serviceMigrationIds, categories, serviceProductTypes, listRequest.getPageable());
    }

    @GetMapping("/{pricingId}/detail")
    public PackageDetailDTO getPackageDetail(@PathVariable Long pricingId) {
        return packageService.getPricingDetail(pricingId);
    }

    @PostMapping("/mobile/send-otp")
    public ResponseEntity<Map<String, String>> sendRequestOTP(
        @RequestBody PackageRegisterDTO packageRegisterDTO
    ) {
        Map<String, String> responseSendOTP = packageService.sendRequestOTP(packageRegisterDTO);
        if (Objects.nonNull(responseSendOTP) && Objects.equals(responseSendOTP.get("RESPONSE_CODE"), "00")) {
            responseSendOTP.put("PHONE_NUMBER", packageRegisterDTO.getPhoneNumber());
            return ResponseEntity.ok().body(responseSendOTP);
        }
        return ResponseEntity.badRequest().body(responseSendOTP);
    }

    @GetMapping("/validate-code")
    public ResponseEntity<Boolean> validatePeriodCodeAndPricingCodeWithoutService(
            @RequestParam(name = "type", required = true) ValidateCodeType type,
            @RequestParam(name = "code", required = true) String code,
            @RequestParam(name = "action", required = true) ValidateCodeAction action
    ) {
        return ResponseEntity.ok(pricingService.validatePeriodCodeAndPricingCodeWithoutService( type, code, action));
    }
}

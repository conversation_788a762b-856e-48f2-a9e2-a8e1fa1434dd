package com.controller.openapis.v1.orders;

import java.util.Date;
import com.dto.openapis.v1.orders.*;
import com.dto.openapis.v1.trigger.OrderStatusReceivedDTO;
import com.enums.partner.ProductTypeEnum;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.dto.base.ListRequest;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.GsonMapperUtil;
import com.service.openapis.v1.orders.OrderV1Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/api/v1/orders")
@RequiredArgsConstructor
public class OrderV1Controller {

    private final OrderV1Service orderV1Service;

    /**
     * <PERSON><PERSON><PERSON> tác gửi đơn hàng đã thanh toán thành công lên Marketplace
     *
     * @param request Thông tin đơn hàng
     * @return Thông tin đơn hàng đã được tạo
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public OrderCreationResDTO createOrder(@Valid @RequestBody OrderCreationReqDTO request,
        @RequestHeader(CharacterConstant.X_API_KEY) String apiKey) {
        return orderV1Service.createOrder(request, apiKey);
    }

    /**
     * Đối tác kiểm tra trạng thái đơn hàng trên Marketplace
     *
     * @param orderId Mã đơn hàng
     * @return Thông tin trạng thái đơn hàng
     */
    @GetMapping("/{orderId}/status")
    public OrderStatusRespDTO getOrderStatus(
        @RequestHeader(CharacterConstant.X_API_KEY) String apiKey,
        @PathVariable String orderId) {
        return orderV1Service.getOrderStatus(apiKey, orderId);
    }

    /**
     * Đối tác cập nhật trạng thái đơn hàng
     *
     * @param orderId Mã đơn hàng
     * @param request Thông tin cập nhật trạng thái đơn hàng
     */
    @PutMapping("/{orderId}/status")
    public void updateOrderStatus(
        @RequestHeader(CharacterConstant.X_API_KEY) String apiKey,
        @PathVariable String orderId,
        @RequestBody OrderStatusReqDTO request) {
        orderV1Service.updateOrderStatus(apiKey, orderId, request);
    }

    /**
     * Đối tác lấy danh sách các đơn hàng đã tạo
     *
     * @param page   Số trang
     * @param size   Số lượng đơn hàng trên mỗi trang
     * @param sortBy Sắp xếp theo trường nào
     * @return Danh sách đơn hàng
     */
    @GetMapping
    public Page<OrderBriefDTO> getOrders(
        @RequestHeader(CharacterConstant.X_API_KEY) String apiKey,
        @RequestParam(name = "partnerId", required = false, defaultValue = "") String partnerId,
        @RequestParam(name = "status", required = false, defaultValue = "") String status,
        @RequestParam(name = "fromDate", required = false, defaultValue = "01/01/1970")
        @DateTimeFormat(pattern = DateUtil.FORMAT_YYYY_MM_DD) Date fromDate,
        @RequestParam(name = "toDate", required = false, defaultValue = "01/01/3000")
        @DateTimeFormat(pattern = DateUtil.FORMAT_YYYY_MM_DD) Date toDate,
        @RequestParam(name = "paymentStatus", required = false, defaultValue = "") String paymentStatus,
        @RequestParam(name = "productType", required = false, defaultValue = "ALL") ProductTypeEnum productType,
        @RequestParam(name = "categoryId", required = false, defaultValue = "-1") Long categoryId,
        @RequestParam(name = "customerType", required = false, defaultValue = "UNSET") CustomerTypeEnum customerType,
        @RequestParam(name = "taxId", required = false, defaultValue = "") String tin,
        @RequestParam(name = "customerEmail", required = false, defaultValue = "") String customerEmail,
        @RequestParam(name = "customerPhone", required = false, defaultValue = "") String customerPhone,
        @RequestParam(required = false, defaultValue = "0") int page,
        @RequestParam(required = false, defaultValue = "10") int size,
        @RequestParam(required = false, defaultValue = "createdAt,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        return orderV1Service.getOrders(apiKey, partnerId, status, fromDate, toDate, paymentStatus, productType, categoryId, customerType, tin, customerEmail,
            customerPhone, listRequest.getPageable());
    }

    /**
     * Đối tác lấy thông tin chi tiết của một đơn hàng
     *
     * @param orderId Mã đơn hàng
     * @return Thông tin chi tiết của đơn hàng
     */
    @GetMapping("/{orderId}")
    public OrderDetailDTO getDetail(
        @RequestHeader(CharacterConstant.X_API_KEY) String apiKey,
        @PathVariable Long orderId) {
        return orderV1Service.getDetail(apiKey, orderId);
    }

    /**
     * Đối tác gửi yêu cầu hủy đơn hàng
     *
     * @param orderId Mã đơn hàng cần xóa
     */
    @DeleteMapping
    public DeleteOrderResDTO delete(
        @RequestParam(name = "orderId", required = false) Long orderId,
        @RequestParam(name = "cartCode", required = false) String cartCode,
        @RequestHeader(CharacterConstant.X_API_KEY) String apiKey,
        @RequestBody DeleteOrderReqDTO request
    ) {
        return orderV1Service.delete(orderId, cartCode, request, apiKey);
    }

    /**
     * Thêm để test trigger
     */
    @GetMapping("/trigger")
    public void trigger() {
        orderV1Service.trigger();
    }

    @PostMapping("/received")
    public void received(
        @RequestBody OrderStatusReceivedDTO orderStatusReqDTO
    ) {
        log.info("Order status event received response body {}", GsonMapperUtil.toJson(orderStatusReqDTO));
    }
}

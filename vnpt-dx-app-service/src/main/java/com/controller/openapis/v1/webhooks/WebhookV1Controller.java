package com.controller.openapis.v1.webhooks;

import java.util.List;
import javax.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import com.dto.openapis.v1.webhooks.WebhookRegisterDTO;
import com.service.openapis.v1.webhooks.WebhookV1Service;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/v1/webhooks")
@RequiredArgsConstructor
public class WebhookV1Controller {

    private final WebhookV1Service webhookV1Service;

    /**
     * API đối tác đăng ký webhook để nhận các event từ hệ thống
     *
     * @param webhookDTO Thông tin đăng ký webhook của đối tác
     * @return ID của webhook đã đăng ký
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(description = "Đăng ký webhook")
    public Long register(@Valid @RequestBody WebhookRegisterDTO webhookDTO) {
        return webhookV1Service.register(webhookDTO);
    }

    /**
     * API đối tác cập nhật thông tin webhook đã đăng ký
     *
     * @param webhookId  ID của webhook cần cập nhật
     * @param webhookDTO Thông tin cập nhật của webhook
     * @return ID của webhook đã cập nhật
     */
    @PutMapping("/{webhookId}")
    @Operation(description = "Cập nhật webhook đã đăng ký")
    public Long update(@PathVariable Long webhookId, @Valid @RequestBody WebhookRegisterDTO webhookDTO) {
        return webhookV1Service.update(webhookId, webhookDTO);
    }

    /**
     * API đối tác hủy đăng ký webhook để nhận các event từ hệ thống
     *
     * @param webhookId ID của webhook cần hủy đăng ký
     */
    @DeleteMapping("/{webhookId}")
    @Operation(description = "Hủy đăng ký webhook")
    public void unregister(@PathVariable Long webhookId) {
        webhookV1Service.unregister(webhookId);
    }

    /**
     * API đối tác lấy danh sách webhook đã đăng ký
     *
     * @return Danh sách webhook đã đăng ký
     */
    @GetMapping
    @Operation(description = "Lấy danh sách các webhook đã đăng ký")
    public List<WebhookRegisterDTO> getAll() {
        return webhookV1Service.getAll();
    }
}

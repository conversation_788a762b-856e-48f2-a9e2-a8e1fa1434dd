package com.repository.events;

import com.entity.events.Events;
import com.onedx.common.repository.CustomJpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventsRepository extends CustomJpaRepository<Events, Long> {

    /**
     * <PERSON><PERSON>y danh sách events chưa được xử lý (status = 0)
     * Sắp xếp theo thời gian tạo tăng dần (xử lý events cũ trước)
     */
    @Query("SELECT e FROM Events e WHERE e.status = 0 ORDER BY e.createdAt ASC")
    List<Events> findUnprocessedEvents();

}

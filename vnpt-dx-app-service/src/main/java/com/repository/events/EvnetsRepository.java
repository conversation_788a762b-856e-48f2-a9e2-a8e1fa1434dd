package com.repository.faq;

import com.constant.sql.SQLFaq;
import com.dto.faq.ListCatalogDTO;
import com.dto.faq.ListQuestionDTO;
import com.entity.faq.FaqFaqsQuestion;
import com.onedx.common.repository.CustomJpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FaqsQuestionRepository extends CustomJpaRepository<FaqFaqsQuestion, Long> {

    List<FaqFaqsQuestion> findAllByTopicId(Long id);

    List<FaqFaqsQuestion> findAllByQuestionId(Long id);

    List<FaqFaqsQuestion> findAllByCatalogIdIn(List<Long> ids);

    List<FaqFaqsQuestion> findAllByTopicDraftId(Long id);

    void deleteByTopicId(Long id);

    @Query(nativeQuery = true, value = SQLFaq.GET_CATALOG_BY_TOPIC_ID)
    List<ListCatalogDTO> getCatalogByTopicId(Long id, Integer isDraft, String quesName);

    @Query(nativeQuery = true, value = SQLFaq.GET_QUESTION_BY_TOPIC_ID)
    List<ListCatalogDTO> getQuestionByTopicId(Long id, Integer isDraft, String quesName);

    @Query(nativeQuery = true, value = SQLFaq.GET_QUESTION_BY_TOPIC_ID_AND_CATALOG_ID)
    List<ListQuestionDTO> getQuestionByTopicIdAndCatalogId(Long topicId, Long catalogId, Integer isDraft,String quesName);

    @Query(nativeQuery = true, value = SQLFaq.GET_CATALOG_BY_ID_IN)
    List<ListCatalogDTO> getCatalogByIdIn(List<Long> ids, Long topicId, Integer isDraft);

    @Query(nativeQuery = true, value = SQLFaq.GET_QUESTION_BY_ID_IN)
    List<ListCatalogDTO> getQuestionByIdIn(List<Long> ids, Long topicId, Long catalogId, Integer isDraft);


    @Query(nativeQuery = true, value = SQLFaq.GET_QUESTION_NOT_CATALOG)
    List<ListCatalogDTO> getQuestionByIdInNotCatalog(List<Long> ids, Long topicId, Integer isDraft);
}

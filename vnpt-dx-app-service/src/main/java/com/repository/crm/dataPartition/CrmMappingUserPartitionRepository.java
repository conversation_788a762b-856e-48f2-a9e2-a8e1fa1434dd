package com.repository.crm.dataPartition;

import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import com.entity.crm.dataPartition.CrmMappingUserPartition;

public interface CrmMappingUserPartitionRepository extends JpaRepository<CrmMappingUserPartition, Long> {

    Set<CrmMappingUserPartition> findByUserIdIn(Set<Long> lstUserId);

    Optional<CrmMappingUserPartition> findByUserId(Long userId);

}
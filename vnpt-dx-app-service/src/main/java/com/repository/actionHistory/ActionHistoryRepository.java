package com.repository.actionHistory;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.constant.sql.SQLActionHistory;
import com.dto.actionHistory.IGetObjectActionHistory;
import com.dto.actionHistory.IGetPageAllActionHistoryDTO;
import com.dto.users.IEmailSmsHistoryDTO;
import com.entity.actionHistory.ActionHistory;

@Repository
public interface ActionHistoryRepository extends JpaRepository<ActionHistory, Long> {

    @Query(nativeQuery = true, value = SQLActionHistory.GET_LIST_OBJECT_ACTION_HISTORY)
    List<IGetObjectActionHistory> getListObjectActionHistory(Long currentUserId, Integer objectType, Long objectId, Date startDate, Date endDate,
        Integer actionType, Boolean isSuperAdmin);

    @Query(nativeQuery = true, value = SQLActionHistory.GET_LIST_OBJECT_ACTION_HISTORY)
    Page<IGetObjectActionHistory> getPageObjectActionHistory(Long currentUserId, Integer objectType, Long objectId, Date startDate, Date endDate,
        Integer actionType, Boolean isSuperAdmin, Pageable pageable);

    List<ActionHistory> findByObjectTypeAndObjectId(Integer objectType, Long objectId);

    @Query(nativeQuery = true, value = SQLActionHistory.GET_LIST_USER_ENTERPRISE_ACTION_HISTORY)
    List<IGetObjectActionHistory> getListUserEnterpriseActionHistory(Long enterpriseId, Long userId, Date startDate, Date endDate, Integer actionType);

    Optional<ActionHistory> findByIdAndActionTypeGreaterThanEqual(Long id, Integer lowestCustomType);

    @Transactional
    @Modifying
    void deleteByIdAndActionTypeGreaterThanEqual(Long id, Integer lowestCustomType);

    @Query(nativeQuery = true, value = SQLActionHistory.GET_PAGE_ALL_OBJECT_ACTION_HISTORY)
    Page<IGetPageAllActionHistoryDTO> getPageAllObjectActionHistory(String search, Long currentUserId, Integer objectType, Long objectId,
        Long userId, Long enterpriseId, Date startDate, Date endDate,
        Integer actionType, Boolean isSuperAdmin, Integer objectNoteType, List<String> lstReceiver, Pageable pageable);

    @Query(nativeQuery = true, value = SQLActionHistory.GET_LIST_EMAIL_SMS_HISTORY)
    Page<IEmailSmsHistoryDTO> getPageEmailSmsHistory(Integer type, List<String> lstEmail, List<String> lstPhone, Date startDate, Date endDate,
        Pageable pageable);

    @Query(nativeQuery = true, value = SQLActionHistory.GET_LIST_QUOTATION_LOGGED_ACTION_TYPE)
    Set<Long> getListQuotationLoggedActionType(Long quotationId);
}

package com.repository.product_variant;

import com.constant.sql.SQLProductVariant;
import com.dto.product_variant.ILatestAndOldVariantId;
import com.dto.product_variant.VariantDTO;
import com.dto.product_variant.VariantResDTO;
import com.entity.product_variant.VariantDraft;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface VariantDraftRepository extends JpaRepository<VariantDraft, Long> {

    @Query(nativeQuery = true, value = SQLProductVariant.GET_VARIANT_DRAFT_BY_APPROVED_AND_DELETED_FLAG)
    VariantDraft getVariantDraftByApprovedAndDeletedFlag(Long id);

    @Query(nativeQuery = true, value = SQLProductVariant.GET_VARIANT_DRAFT_ID_DRAFT_BY_VARIANT_ID)
    VariantDraft getVariantDraftIdByVariantId(Long variantId);

    @Query(nativeQuery = true, value = SQLProductVariant.GET_LIST_IDS_APPROVED)
    List<Long> getListVariantDraftApprovedId(Long serviceId);

    @Query(nativeQuery = true, value = SQLProductVariant.GET_LIST_VARIANT_ID_BY_SERVICE_ID)
    List<Long> getListVariantIdByServiceId(Long serviceId);

    @Query(nativeQuery = true, value = SQLProductVariant.GET_LIST_VARIANT_ID_APPROVED)
    List<Long> getListVariantIdApproved(Long serviceId);

    @Query(nativeQuery = true, value = SQLProductVariant.CHECK_EXISTS_VARIANT_BUY)
    boolean checkExistsVariant(Long serviceId);

    @Query(nativeQuery = true, value = SQLProductVariant.GET_LIST_VARIANT_DRAFT_UPDATE_ATT)
    List<VariantDraft> getListVariantDraftUpdateAttributes(Long id, Long serviceId);

    List<VariantDraft> findAllByServiceIdAndApprovedAndDeletedFlag(Long serviceId, Integer approved, Integer deletedFlag);

    @Query(value = SQLProductVariant.GET_ALL_VARIANT_BY_SERVICE_ID, nativeQuery = true)
    List<VariantResDTO> getAllVariantDraftByServiceId(Long serviceId);

    @Query(value = SQLProductVariant.GET_DETAIL_VARIANT_BY_SERVICE_ID_AND_DRAFT_ID, nativeQuery = true)
    VariantResDTO getDetailVariantByServiceIdAndDraftId(Long serviceId, Long variantDraftId);

    @Query(value = SQLProductVariant.GET_LIST_VARIANT_DRAFT_ID_IS_SUB, nativeQuery = true)
    Set<Long> getListVariantDraftIdIsSub(Long serviceId);

    @Transactional
    @Modifying
    @Query(value = SQLProductVariant.DELETE_VARIANT_DRAFT_BY_IDS, nativeQuery = true)
    void updateDeletedFlagByIds(Set<Long> ids);

    @Query(nativeQuery = true, value = SQLProductVariant.GET_LIST_VARIANT_DRAFT)
    List<VariantDTO> getListVariantDraft(Long serviceId);

    @Query(value = SQLProductVariant.GET_EXISTED_SKU_BY_VARIANT_SKU, nativeQuery = true)
    List<String> getExistedSkuByVariantSkuIn(List<String> variantSkuList);

    @Query(value = SQLProductVariant.GET_LATEST_VARIANT_ID_BY_SERVICE_ID_AND_OLD_VARIANT_IN, nativeQuery = true)
    List<ILatestAndOldVariantId> getLstLatestVariantDraftIdByServiceIdAndOldIdIn(Long serviceId, List<Long> lstVariantDraftId);

    @Query(value = SQLProductVariant.GET_ALL_VARIANT_DRAFT_BY_SERVICE_ID_BOS, nativeQuery = true)
    List<VariantResDTO> getAllVariantDraftByServiceIdBOS(Long serviceId);

    @Query(value = SQLProductVariant.GET_DEFAULT_VARIANT_DRAFT_BY_SERVICE_ID_BOS, nativeQuery = true)
    Optional<VariantResDTO> getDefaultVariantDraftByServiceIdBOS(Long serviceId);

    @Query(value = SQLProductVariant.GET_LATEST_VARIANT_ID_BY_DRAFT_ID, nativeQuery = true)
    Long getLatestVariantIdByDraftId(Long variantDraftId);

    @Query(nativeQuery = true, value = SQLProductVariant.FIND_DEFAULT_VARIANT_DRAFT_BY_SERVICE_ID)
    VariantDraft findDefaultVariantDraftByServiceId(Long serviceId);

    VariantDraft findByIdAndServiceIdAndDeletedFlag(Long variantId, Long serviceId, Integer deleteFlag);
}

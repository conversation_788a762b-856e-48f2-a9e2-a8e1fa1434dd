package com.repository.file.attach;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.constant.sql.SQLFileAttach;
import com.constant.sql.SQLSeo;
import com.constant.sql.SQLService;
import com.dto.coupons.IBannerCouponResponse;
import com.dto.customerTicket.FileAttachResDTO;
import com.dto.file.attach.FileAttachLibraryResponse;
import com.dto.file.attach.IFileAttachResponse;
import com.dto.file.attach.IFileAttachVariantDTO;
import com.dto.file.attach.ObjectTypeDTO;
import com.dto.serviceGroup.IServiceGroupUrlDTO;
import com.dto.services.ServiceDetailFileAttachDTO;
import com.entity.file.attach.FileAttach;

/**
 * <AUTHOR> KhanhND2
 * @version : 1.0 26/2/2021
 */
@Transactional
@Repository
public interface FileAttachRepository extends JpaRepository<FileAttach, Long> {

    @Query(nativeQuery = true, value = SQLService.GET_FILE_ID_AND_URL_BY_ID)
    List<IServiceGroupUrlDTO> getListFileIdAndUrlByIdIn(Set<Long> lstFileId);

    Optional<FileAttach> findByObjectTypeAndUserId(Integer objectType, Long userId);

    Optional<FileAttach> findFirstByObjectTypeAndObjectIdOrderByIdDesc(Integer objectType, Long objectId);

    List<FileAttach> findByUserIdAndObjectType(Long userId, Integer objectType);

    List<FileAttach> findAllByObjectTypeAndObjectIdAndFileType(Integer objectType, Long objectId, Integer fileType);

    List<FileAttach> findAllByUserIdAndObjectTypeIn(Long userId, Set<Integer> objectType);

    Optional<List<FileAttach>> findByObjectTypeAndServiceId(
        @Param("objectType") Integer objectType,
        @Param("serviceId") Long serviceId);

    @Query(nativeQuery = true, value = SQLService.GET_FILE_ATTACH_BY_SERVICE_ID)
    Optional<List<FileAttach>> getByObjectTypeAndServiceId(Integer objectType, Long serviceId, Long serviceDraftId);

    @Query(nativeQuery = true, value = SQLService.GET_FILE_ATTACH_BY_SERVICE_ID)
    Optional<List<FileAttach>> getByObjectTypeInAndServiceIdOrderByIdDesc(Set<Integer> objectType, Long serviceId, Long serviceDraftId);

    Optional<List<FileAttach>> findByServiceId(
        @Param("serviceId") Long serviceId);

    @Query(value = SQLService.GET_SERVICE_FILES_ATTACH)
    List<ServiceDetailFileAttachDTO> getLstServiceFileAttach(@Param("serviceId") Long serviceId);

    @Query(value = SQLService.GET_SERVICE_TECH)
    List<ServiceDetailFileAttachDTO> getLstServiceTech(@Param("serviceId") Long serviceId);

    @Query(nativeQuery = true, value = SQLFileAttach.GET_PATH_BY_OBJECT_TYPE_AND_OBJECT_ID)
    String getPathByObjectTypeAndObjectId(Integer objectType, Long objectId);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.sourceId = :id, f.sourceType = :type, f.objectType = :objectType WHERE f.id = :fileId")
    void updateFilesAttachOfTicket(
        @Param("id") Long id, @Param("fileId") Long fileId, @Param("objectType") Integer objectType,
        @Param("type") Integer type);

    List<FileAttach> findByIdIn(List<Long> ids);


    @Query(value =
        "SELECT new com.dto.customerTicket.FileAttachResDTO(f.id, f.fileName, f.fileSize) " +
            "FROM FileAttach f WHERE f.sourceType = :type AND f.sourceId = :ticketId AND f.objectType <> 0")
    List<FileAttachResDTO> findAttachIdsOfTicket(@Param("ticketId") Long ticketId,
        @Param("type") Integer type);

    @Query(value =
        "SELECT new com.dto.customerTicket.FileAttachResDTO(f.id, f.fileName, f.fileSize, f.sourceId) "
            +
            "FROM FileAttach f WHERE f.sourceType = :type AND f.sourceId IN :ticketIds AND f.objectType <> 0")
    List<FileAttachResDTO> findAttachIdsOfTickets(@Param("ticketIds") List<Long> ticketIds,
        @Param("type") Integer type);

    @Query(value = "SELECT f FROM FileAttach f WHERE f.sourceId = :ticketId AND f.sourceType = :type AND f.objectType <> 0")
    List<FileAttach> findAttachesOfTicketId(@Param("ticketId") Long ticketId,
        @Param("type") Integer type);

    void deleteAllByServiceIdAndObjectType(Long id, Integer objectType);

    List<FileAttach> findByComboDraftId(Long comboDraftId);

    List<FileAttach> findAllByComboDraftId(Long id);

    Optional<List<FileAttach>> findByObjectTypeAndComboId(
        @Param("objectType") Integer objectType,
        @Param("comboId") Long comboId);

    @Query(nativeQuery = true, value =
            " SELECT" +
            " fa.* " +
            " FROM" +
            " {h-schema}file_attach fa" +
            " WHERE" +
            " fa.service_id = :serviceId" +
            " AND fa.object_type = 0" +
            " ORDER BY" +
            " fa.id DESC LIMIT 1")
    Optional<FileAttach> getFileAttach(Long serviceId);

    List<FileAttach> findByComboIdAndObjectType(Long comboId, Integer objectType);

    Optional<List<FileAttach>> findByObjectTypeInAndServiceIdOrderByIdDesc(
            @Param("objectType") Set<Integer> objectType,
            @Param("serviceId") Long serviceId);

    Optional<List<FileAttach>> findByObjectTypeInAndComboId(
            @Param("objectType") Set<Integer> objectType,
            @Param("serviceId") Long serviceId);

    void deleteAllByIdIn(List<Long> ids);

    @Query(value = SQLService.GET_SERVICE_DOC_GUIDE)
    List<ServiceDetailFileAttachDTO> getLstServiceDocGuideOrderByIdDesc(@Param("serviceId") Long serviceId);

    @Query(value = SQLService.GET_COMBO_DOC_GUIDE)
    List<ServiceDetailFileAttachDTO> getLstComboDocGuide(@Param("comboId") Long comboId);

    @Query(value = SQLService.GET_SERVICE_VIDEO_GUIDE)
    List<ServiceDetailFileAttachDTO> getLstServiceVideoGuideOrderByIdDesc(@Param("serviceId") Long serviceId);

    @Query(value = SQLService.GET_COMBO_VIDEO_GUIDE)
    List<ServiceDetailFileAttachDTO> getLstComboVideoGuide(@Param("comboId") Long comboId);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.visible = :visible WHERE f.serviceId = :serviceId")
    void updateVisible(@Param("serviceId") Long serviceId, @Param("visible") Integer visible);

    @Query(value = "SELECT f FROM FileAttach f WHERE f.seoId = :seoId")
    List<FileAttach> findAttachesOfSeoId(@Param("seoId") Long seoId);

    List<FileAttach> findAllByIdIn(List<Long> ids);

    List<FileAttach> findAllByIdIn(Set<Long> ids);

    List<FileAttach> findAllByObjectTypeAndObjectIdIn(Integer objectType, Set<Long> ids);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.seoId = :id WHERE f.id = :fileId")
    void updateFilesAttachOfSeo(@Param("id") Long id, @Param("fileId") Long fileId);

    Long deleteBySeoId(Long seoId);

    @Query(value = SQLSeo.GET_FILE_ATTACH, nativeQuery = true)
        Set<Long> getFiledAttachBySeoId(Long seoId);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.title = :title, f.description =:description, f.fileType =:fileType WHERE f.id = :id")
    void updateDescription(@Param("id") Long id, @Param("title") String title, @Param("description") String description, @Param("fileType") Integer fileType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.title = :title, f.description =:description, f.serviceId =:serviceId, f.objectType =:objectType, f.fileType =:fileType WHERE f.id = :id")
    void updateDescription(@Param("id") Long id, @Param("title") String title,
                           @Param("description") String description,
                           @Param("serviceId") Long serviceId,
                           @Param("objectType") Integer objectType,
                           @Param("fileType") Integer fileType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.title = :title, f.description =:description, f.servicesDraftId =:serviceDraftId, f.objectType =:objectType, f.fileType =:fileType WHERE f.id = :id")
    void updateDescriptionServiceDraft(@Param("id") Long id, @Param("title") String title,
        @Param("description") String description,
        @Param("serviceDraftId") Long serviceDraftId,
        @Param("objectType") Integer objectType,
        @Param("fileType") Integer fileType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.objectId = :objectId, f.objectType = :objectType, f.fileType = 1 WHERE f.id = :id")
    void updateObject(@Param("id") Long id, @Param("objectId") Long objectId, @Param("objectType") Integer objectType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.objectId = :objectId, f.objectType = :objectType, f.fileType = :fileType WHERE f.id in :ids")
    void updateById(@Param("ids") Set<Long> ids,
                    @Param("objectId") Long objectId,
                    @Param("objectType") Integer objectType,
                    @Param("fileType") Integer fileType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.serviceId = null WHERE f.serviceId = :serviceId and f.objectType = :objectType")
    void clearByServiceId(@Param("serviceId") Long serviceId, @Param("objectType") Integer objectType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.serviceId = null WHERE f.serviceId = :serviceId And f.objectType not in (17,18)")
    void clearAllByServiceId(@Param("serviceId") Long serviceId);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.serviceId = :serviceId, f.objectType = :objectType, f.fileType = 1 WHERE f.id = :id")
    void assignToService(@Param("id") Long id, @Param("serviceId") Long serviceId, @Param("objectType") Integer objectType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.servicesDraftId = null WHERE f.servicesDraftId = :serviceDraftId and f.objectType = :objectType")
    void clearByServiceDraftId(@Param("serviceDraftId") Long serviceDraftId, @Param("objectType") Integer objectType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.servicesDraftId = :serviceDraftId, f.objectType = :objectType, f.fileType = 1 WHERE f.id = :id")
    void assignToServiceDraft(@Param("id") Long id, @Param("serviceDraftId") Long serviceDraftId, @Param("objectType") Integer objectType);

    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.servicesDraftId = :serviceDraftId, f.objectType = :objectType, f.fileType = :fileType WHERE f.id = :id")
    void assignVideoToServiceDraft(@Param("id") Long id, @Param("serviceDraftId") Long serviceDraftId, @Param("objectType") Integer objectType,
        @Param("fileType") Integer fileType);

    List<FileAttach> findAllByServiceIdAndObjectTypeIn(Long serviceId, Set<Integer> types);
    List<FileAttach> findAllByServiceIdAndObjectTypeInOrderById(Long serviceId, Set<Integer> types);

    List<FileAttach> findAllByServicesDraftIdAndObjectTypeInOrderByPriority(Long serviceId, Set<Integer> types);

    List<FileAttach> findAllByServicesDraftId(Long id);

    List<FileAttach> findAllByServiceId(Long id);

    List<FileAttach> findAllByObjectType(Integer objectType);

    boolean existsByIdAndServicesDraftIdIsNotNull(Long icon);

    boolean existsByIdAndObjectIdIsNull(Long id);

    @Query(nativeQuery = true, value =
        " SELECT" +
            " fa.file_path " +
            " FROM" +
            " {h-schema}file_attach fa" +
            " WHERE" +
            " fa.service_id = :userId" +
            " AND fa.object_type = 5" +
            " ORDER BY" +
            " fa.id DESC LIMIT 1")
    String getAvataOfSMEUser(Long userId);

    @Query(value = SQLSeo.GET_ENTERPRISE_CONTACT_AVATAR, nativeQuery = true)
    IFileAttachResponse getEnterpriseContactAvatar(Long enterpriseId);

    @Query(value = SQLSeo.GET_USER_AVATAR, nativeQuery = true)
    IFileAttachResponse getUserAvatar(Long userId);

    @Query(value = SQLSeo.GET_BY_OBJECT_TYPE_AND_USER_ID, nativeQuery = true)
    List<IFileAttachResponse> getAllByObjectTypeAndUserId(Integer objectType, Long userId);

    @Query(value = SQLSeo.GET_FILE_ATTACH_DTO_BY_ID, nativeQuery = true)
    IFileAttachResponse getFileAttachDataResponseDto(Long fileAttachId);

    @Query(nativeQuery = true, value = SQLFileAttach.GET_COMBO_AVATAR_BY_COMPO_PLAN_ID)
    FileAttach getComboAvatarByComboPlanId(Long comboPlanId);

    @Modifying
    @Query(value =
        "update FileAttach fa \n" +
        "    set fa.objectType = :objectType, fa.objectId = :objectId, fa.objectName = :objectName \n" +
        "where fa.id in (:lstAttachFileId) ")
    void updateObjectTypeAndObjectIdAndObjectName(Set<Long> lstAttachFileId, Integer objectType, Long objectId, String objectName);

    void deleteAllByObjectIdAndObjectTypeAndResolutionAndIdNotAndObjectNameAndDescription(Long objectId, Integer objectType, Integer resolution, Long id, String objectName, String description);

    void deleteAllByObjectIdAndObjectTypeAndResolutionAndObjectNameAndDescription(Long objectId, Integer objectType, Integer resolution, String objectName, String description);

    void deleteAllByObjectIdAndObjectType(Long objectId, Integer objectType);

    List<FileAttach> findAllByObjectIdAndObjectTypeAndObjectNameAndDescription(Long objectId, Integer objectType, String objectName, String description);

    @Query(value = "SELECT * FROM {h-schema}file_attach WHERE object_id = :objectId AND object_type = :objectType AND resolution = :seq ORDER BY id DESC LIMIT 1", nativeQuery = true)
    Optional<FileAttach> findByObjectIdAndObjectType(Long objectId, Integer objectType, Integer seq);

    @Modifying
    @Query(value =
        "update FileAttach fa \n" +
        "    set fa.objectType = :objectType, fa.objectId = :objectId, fa.objectName = :objectName, fa.resolution = :resolution \n" +
        "where fa.id in (:lstAttachFileId) ")
    void updateObjectTypeAndObjectIdAndObjectNameAndResolution(Set<Long> lstAttachFileId, Integer objectType, Long objectId, String objectName, Integer resolution);

    @Modifying
    @Query(value = "update FileAttach fa set fa.fileSizeLibrary = :fileSizeLibrary where fa.id = :id")
    void updateFileSizeLibrary(Long id, String fileSizeLibrary);

    void deleteAllByObjectId(Long objectId);

    @Query(value = SQLFileAttach.GET_LIST_FILE_ATTACH, nativeQuery = true)
    Page<FileAttachLibraryResponse> getListFileAttach(String name, Integer fileType, String objectName, Integer typeResolution,
        Integer statusDisplay, String fileSizeLibrary, String fileExtension, Pageable pageable);

    @Query(value = "select ot.id, ot.object_name as objectName from {h-schema}object_type ot where (:filter = '' or ot.object_name ilike ('%' || :filter || '%'))", nativeQuery = true)
    List<ObjectTypeDTO> getListTypeImage(String filter);

    @Query(value = SQLFileAttach.GET_LIST_OBJECT_TYPE, nativeQuery = true)
    List<String> getListTypeImage1(Integer fileType, String filter);

    @Query(value = "select distinct(file_extension) from {h-schema}file_attach where file_type = :fileType and file_extension is not null and (:filter = '' or file_extension ilike ('%' || :filter || '%')) ", nativeQuery = true)
    List<String> getListFileExtension(Integer fileType, String filter);

    @Query(value = "select distinct(file_size_library) from {h-schema}file_attach where file_type = :fileType and file_size_library is not null", nativeQuery = true)
    List<String> getListFileSizeLibrary(Integer fileType);

    @Query(value = "select distinct(file_size_library) from {h-schema}file_attach where file_type = :fileType and file_size_library is not null and (:resolution = -1 or resolution = :resolution) and (:filter = '' or file_size_library ilike ('%' || :filter || '%'))", nativeQuery = true)
    List<String> getListFileSizeLibrary(Integer fileType, String filter, Integer resolution);

    @Query(value = SQLFileAttach.GET_DETAIL_FILE_ATTACH, nativeQuery = true)
    FileAttachLibraryResponse getFileAttachDetail(Long id);

    void deleteAllByPageBulderId(Long id);

    Optional<FileAttach> findByObjectTypeAndId(Integer objectType, Long id);

    @Query(value = SQLFileAttach.GET_FILE_ATTACH_DTO_BY_OBJECT_TYPE_AND_SERVICE_ID, nativeQuery = true)
    IFileAttachResponse getFileAttachDataResponseDto(Integer objectType, Long serviceId);

    @Query(value = SQLFileAttach.GET_FILE_ATTACH_VARIANT_OR_SERVICE, nativeQuery = true)
    IFileAttachResponse getFileAttachVariantOrService(List<Integer> objectType, Long objectId, Long serviceId);

    @Query(value = SQLFileAttach.GET_FILE_ATTACH_DTO_BY_OBJECT_TYPE_AND_PRICING_ID, nativeQuery = true)
    IFileAttachResponse getFileAttachPricingResponseDto(Long serviceId, Long pricingId);

    @Query(value = SQLFileAttach.GET_COMBO_AVATAR, nativeQuery = true)
    IFileAttachResponse getComboAvatar(Long comboId);

    @Query(value = SQLFileAttach.GET_COMBO_PLAN_AVATAR, nativeQuery = true)
    IFileAttachResponse getComboPlanAvatar(Long comboPlanId, Integer objectType);

    @Query(value = SQLFileAttach.GET_VARIANT_AVATAR_BY_VARIANT_DRAFT_ID, nativeQuery = true)
    IFileAttachResponse getVariantAvatar(Long variantDraftId);

    @Query(value = SQLFileAttach.GET_SERVICE_GROUP_AVATAR, nativeQuery = true)
    IFileAttachResponse getAvatarById(Long id);

    @Query(value = SQLFileAttach.GET_FILE_ATTACH_IN_VARIANT_ID, nativeQuery = true)
    List<IFileAttachVariantDTO> getFileAttachInVariantId(List<Long> variantIds);

    @Query(value = SQLFileAttach.GET_LIST_ATTACH_FILE ,nativeQuery = true)
    List<IFileAttachResponse> getListFileAttach(List<Long> ids);

    @Modifying
    @Query(nativeQuery = true, value = SQLFileAttach.UPDATE_ALL_BY_USER_ID_AND_OBJECT_TYPE)
    void updateAllByUserIdAndObjectType(Long userId, Integer objectType);

    @Modifying
    @Query(nativeQuery = true, value = SQLFileAttach.UPDATE_SOURCE_TYPE_TICKET)
    void updateSourceTypeForTicket(Integer sourceType, Long sourceId, List<Long> lstAttachFileId);

    @Query(nativeQuery = true, value = SQLFileAttach.GET_AVATAR_SERVICE_BY_TICKET_ID)
    IFileAttachResponse getAvatarByTicketId(Long ticketId);

    List<FileAttach> findAllByObjectIdInAndObjectType(Set<Long> ids, Integer objectType);

    @Query(nativeQuery = true, value = SQLFileAttach.FIND_ALL_FILE_ATTACH_BY_CONTACT_ID)
    List<IFileAttachResponse> findAllFileAttachByContactId(Long contactId);

    @Query(nativeQuery = true, value = SQLFileAttach.GET_LIST_FILE_ID_BY_CONTACT_ID)
    List<Long> getContactFileIds(Long contactId);

    @Query(nativeQuery = true, value = SQLFileAttach.GET_LIST_FILE_ID_BY_ENTERPRISE_ID)
    List<Long> getEnterpriseFileIds(Long enterpriseId);

    List<FileAttach> findByObjectTypeAndObjectDraftId(Integer objectType, Long objectDraftId);

    List<FileAttach> findByObjectTypeAndObjectId(Integer objectType, Long objectId);

    FileAttach findFirstByObjectTypeAndObjectDraftIdOrderByIdDesc(Integer objectType, Long objectId);

    Set<FileAttach> findByObjectTypeAndObjectIdIn(Integer objectType, Set<Long> setObjectId);

    @Query(value = SQLService.GET_SERVICE_FILES_ATTACH_AVATAR_DEVICE)
    List<ServiceDetailFileAttachDTO> getLstServiceFileAttachAvatarDevice(@Param("serviceId") Long serviceId,
        @Param("serviceId") Integer objectType);

    @javax.transaction.Transactional
    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.serviceId = :serviceId, f.objectType = :objectType, f.fileType = 1 WHERE f.id IN (:ids)")
    void assignListToService(@Param("ids") List<Long> ids, @Param("serviceId") Long serviceId, @Param("objectType") Integer objectType);

    @javax.transaction.Transactional
    @Modifying
    @Query(value = "UPDATE FileAttach f SET f.servicesDraftId = :serviceDraftId, f.objectType = :objectType, f.fileType = 1 WHERE f.id IN :ids")
    void assignListToServiceDraft(@Param("ids") List<Long> ids, @Param("serviceDraftId") Long serviceDraftId,
        @Param("objectType") Integer objectType);

    List<FileAttach> findAllByObjectIdAndObjectType(Long objectId, Integer objectType);

    @Query(value = SQLFileAttach.GET_LIST_FILE_ATTACH_BY_LIST_OBJECT_ID, nativeQuery = true)
    List<FileAttach> getLstFileAttachByLstObjectId(List<Long> lstObjectId, Integer objectType);

    @Query(nativeQuery = true, value = SQLFileAttach.GET_BANNER_COUPONS_BY_COUPON_ID)
    List<IBannerCouponResponse> getBannerCouponsByCouponId(Set<Long> lstCouponId);

    boolean existsByObjectIdAndObjectType(Long objectId, Integer objectType);
}

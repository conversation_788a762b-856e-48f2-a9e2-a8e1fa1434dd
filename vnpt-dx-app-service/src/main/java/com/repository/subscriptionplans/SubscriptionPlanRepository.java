package com.repository.subscriptionplans;

import com.constant.sql.SQLSubscriptionPlan;
import com.dto.subscription_plans.OrderListResponseDTO;
import com.dto.subscription_plans.SubscriptionPlanAdminDetailDTO;
import com.dto.subscription_plans.SubscriptionPlanResponse2DTO;
import com.dto.subscription_plans.SubscriptionPlanResponseDTO;
import com.entity.subscription_plans.SubscriptionPlan;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> KienDV
 * @version    : 1.0
 * 25/2/2021
 */
@Repository
public interface SubscriptionPlanRepository extends JpaRepository<SubscriptionPlan, Long> {

	@Query(value = " select id from {h-schema}subscription_plan where id =:id ", nativeQuery = true)
	Long checkUserServiceExist(@Param("id") Long id);

	@Modifying
	@Query("update SubscriptionPlan c set c.deletedFlag = 0 where c.id = :id ")
	void deleteSubscriptionPlan(@Param("id") Long id);

    @Query(nativeQuery = true,value = SQLSubscriptionPlan.FIND_ALL)
    Page<SubscriptionPlanResponseDTO> findAllSubscriptionPlans(Pageable pageable);
    
	@Query(nativeQuery = true, value = SQLSubscriptionPlan.FIND_ALL_SUBSCRIPTION_PLANS_DEV,
	countQuery = "SELECT COUNT (*) FROM ( " + SQLSubscriptionPlan.FIND_ALL_SUBSCRIPTION_PLANS_DEV + ") as tmpl")
	Page<SubscriptionPlanResponse2DTO> findAllSubscriptionPlansForDev(@Param("email") String email,
			@Param("serviceId") String serviceId, @Param("name") String name, @Param("approveStatus") String approveStatus,
			@Param("displayedStatus") String displayedStatus, @Param("searchText") String searchText,
			Pageable pageable);

	@Query(nativeQuery = true, value = SQLSubscriptionPlan.FIND_ALL_SUBSCRIPTION_PLANS_ADMIN)
	Page<SubscriptionPlanResponse2DTO> findAllSubscriptionPlansForAdmin(@Param("serviceId") String serviceId,
			@Param("name") String name, @Param("approveStatus") String approveStatus,
			@Param("displayedStatus") String displayedStatus, @Param("searchText") String searchText,
			Pageable pageable);

    @Query("SELECT sp FROM SubscriptionPlan sp WHERE sp.id = :id AND sp.deletedFlag != 0")
    Optional<SubscriptionPlan> findById(@Param("id") Long id);
    
    @Query(nativeQuery = true, value = SQLSubscriptionPlan.SUBSCRIPTION_PLAN_DETAIL)
    SubscriptionPlanAdminDetailDTO subscriptionPlanDetail(@Param("id") Long id);

    @Query(nativeQuery = true, value = SQLSubscriptionPlan.SUBSCRIPTION_PLAN_DETAIL_DEV)
    SubscriptionPlanAdminDetailDTO subscriptionPlanDetailDev(@Param("userId") Long userId, @Param("id") Long id);

    @Modifying
	@Query(value = SQLSubscriptionPlan.CHANGE_STATUS, nativeQuery = true)
	void changeStatus(@Param("status") Integer status, @Param("id") Long id);

    @Query(value = 	"select sp.cycle from {h-schema}subscriptions s2 " +
					" join " +
					" {h-schema}sub_plan_service sps on " +
					" s2.sub_plan_service_id = sps.id " +
					" join " +
					" {h-schema}subscription_plan sp on " +
					" sp.id = sps.subscription_plan_id " +
					" where s2.id = :subscriptionId", nativeQuery = true)
    Optional<Long> findBySubscriptionId(@Param("subscriptionId") Long subscriptionId);

    @Query(SQLSubscriptionPlan.GET_ORDER_LIST)
	List<OrderListResponseDTO> getOrderList(@Param("serviceId") Long serviceId);

    List<SubscriptionPlan> findByIdInAndDeletedFlag(List<Long> ids, Integer deletedFlag);

	Optional<SubscriptionPlan> findByIdAndDeletedFlag(Long id, Integer deletedFlag);
}

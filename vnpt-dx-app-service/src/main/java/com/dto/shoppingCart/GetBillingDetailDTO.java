package com.dto.shoppingCart;

import com.onedx.common.dto.integration.backend.subscription.detail.SubscriptionOneTimeFee;
import com.onedx.common.constants.enums.subscriptions.CalculateTypeEnum;
import com.dto.bills.BillDetailInfoResDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.dto.creditNote.CreditNoteCalculateDTO;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;

@Getter
@Setter
@NoArgsConstructor
public class GetBillingDetailDTO {

    private Provider provider;
    private Customer customer;
    private Payment payment;
    private CalculateTypeEnum calculateTypeEnum;
    private List<BillItem> billItem = new ArrayList<>();
    private Double totalAfterTaxAmount;
    private Double totalAfterTaxFinalAmount;
    private List<EInvoice> eInvoice = new ArrayList<>();
    private List<CreditNoteCalculateDTO> creditNoteApplies;
    private List<CreditNoteCalculateDTO> creditNoteNews;
    private CreatedSourceMigrationEnum createdSourceMigration;
    private BillDetailInfoResDTO billing;
    private List<SubscriptionOneTimeFee> onceTimeFee;

    @Getter
    @Setter
    public static class Provider {
        private String providerName;
        private String providerTaxCode;
        private String providerAddress;
    }

    @Getter
    @Setter
    public static class Customer {
        private String customerName;
        private String customerLastName;
        private String customerFirstName;
        private String customerType;
        private String customerTaxCode;
        private String identityNo;
        private String customerAddress;
        private Integer addressType;
        private String companyName;

        private Long customerProvinceId;
        private String customerProvinceCode;
        private String customerProvinceName;

        private Long customerDistrictId;
        private String customerDistrictCode;
        private String customerDistrictName;

        private Long customerWardId;
        private String customerWardCode;
        private String customerWardName;

        private Long customerStreetId;
        private String customerStreetName;

        private Long customerAddressId;
    }

    @Getter
    @Setter
    public static class Payment {
        private String billingCode;
        private Integer billingStatus;
        private Integer paymentMethod;
        private String registerCode;
        private Boolean isCart;
        @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_HH_MM_SS, timezone = DateUtil.TIME_ZONE)
        private Date paymentDate;
        @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
        private Date currentPaymentDate;
        @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
        private Date finalPaymentDate;
        private Integer numberOfDayExportBill;
    }

    @Getter
    @Setter
    public static class BillItem {
        private Long no;
        private String serviceName;
        private Double price;
        private Double priceVariant;
        private Boolean isBuyService;
        private Boolean isOnlyService;
        private String variantName;
        private Boolean isCombo;
        private Boolean isOn;
        private Long billingId;
        private List<Item> lstBillItem = new ArrayList<>();

        @Getter
        @Setter
        public static class Item {
            private Long itemId;
            private String itemName;
            private Double price;
        }
    }

    @Getter
    @Setter
    public static class EInvoice {
        private String invoiceNo;
        private String invoiceDate;
        private Double price;
        private String fkey;
    }

}

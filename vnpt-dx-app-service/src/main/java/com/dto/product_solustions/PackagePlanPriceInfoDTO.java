package com.dto.product_solustions;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO chứa thông tin giá của một PackagePlanCreateDTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Thông tin giá của một PackagePlanCreateDTO")
public class PackagePlanPriceInfoDTO {

    @Schema(description = "ID gói")
    private Long pricingId;

    @Schema(description = "Tên gói")
    private String pricingName;

    @Schema(description = "Giá gốc")
    private BigDecimal originPrice = BigDecimal.ZERO;

    @Schema(description = "Giá trước thuế")
    private BigDecimal pricePreTax = BigDecimal.ZERO;

    @Schema(description = "Tổng thuế")
    private BigDecimal totalTax = BigDecimal.ZERO;

    @Schema(description = "Tổng phí")
    private BigDecimal totalFee = BigDecimal.ZERO;

    @Schema(description = "Giá sau khuyến mại")
    private BigDecimal priceAfterDiscount = BigDecimal.ZERO;

    @Schema(description = "Tổng tiền")
    private BigDecimal total = BigDecimal.ZERO;

    @Schema(description = "Danh sách thông tin giá của các addon")
    private List<AddonPriceInfoDTO> addonPriceInfos;

    /**
     * DTO chứa thông tin giá của một PackageAddonCreateDTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Thông tin giá của một PackageAddonCreateDTO")
    public static class AddonPriceInfoDTO {

        @Schema(description = "ID addon")
        private Long addonId;

        @Schema(description = "Tên addon")
        private String addonName;

        @Schema(description = "Giá gốc")
        private BigDecimal originPrice = BigDecimal.ZERO;

        @Schema(description = "Giá trước thuế")
        private BigDecimal pricePreTax = BigDecimal.ZERO;

        @Schema(description = "Tổng thuế")
        private BigDecimal totalTax = BigDecimal.ZERO;

        @Schema(description = "Tổng phí")
        private BigDecimal totalFee = BigDecimal.ZERO;

        @Schema(description = "Giá sau khuyến mại")
        private BigDecimal priceAfterDiscount = BigDecimal.ZERO;

        @Schema(description = "Tổng tiền")
        private BigDecimal total = BigDecimal.ZERO;
    }
}

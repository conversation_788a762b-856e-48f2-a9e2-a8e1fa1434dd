package com.dto.services;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.onedx.common.constants.enums.services.*;
import com.onedx.common.constants.enums.PortalType;
import org.hibernate.validator.constraints.Range;
import com.annotation.Url;
import com.constant.ServicesConstant;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.constant.enums.services.ServiceStatusEnum;
import com.dto.seo.SeoReqDTO;
import com.onedx.common.annotation.TrimString;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.Service;
import com.onedx.common.dto.customFields.CustomFieldValueDTO;
import com.onedx.common.exception.MessageKeyConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> KienND2
 * @version    : 1.0
 * 14/1/2021
 */
@Setter
@Getter
@TrimString(fieldNames = {"name", "urlSetup", "tokenSPDV", "url", "email", "phoneNumber", "serviceTypeId"})
public class ServiceUpdateDTO {

    @Schema(description = SwaggerConstant.Service.ID, example = SwaggerConstant.Example.ID)
    private Long id;

    private List<Long> categoriesApp;
    private Integer serviceTypeApplication;
    private List<Long> permissionIds;
    @Schema(description = SwaggerConstant.Category.ID, example = SwaggerConstant.Example.ID)
    @Range(max = Integer.MAX_VALUE, message = MessageKeyConstant.Validation.RANGE)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private Long categoriesId;

    @Schema(description = SwaggerConstant.Service.NAME, example = SwaggerConstant.Example.SERVICE_NAME)
    @Size(max = ServicesConstant.ServiceName.MAX_LENGTH, message = MessageKeyConstant.Validation.SIZE)
    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    private String name;

    @Schema(description = SwaggerConstant.Service.DISPLAY, example = SwaggerConstant.Example.DISPLAY)
    private ServiceStatusEnum displayed;

    @Schema(description = SwaggerConstant.Service.URL_SETUP, example = SwaggerConstant.Example.URL)
    @Size(max = ServicesConstant.ServiceUrlSetup.MAX_LENGTH, message = MessageKeyConstant.Validation.SIZE)
    private String urlSetup;

    @Schema(description = SwaggerConstant.Service.TOKEN, example = SwaggerConstant.Example.TOKEN)
    @Size(max = ServicesConstant.ServiceToken.MAX_LENGTH, message = MessageKeyConstant.Validation.SIZE)
    private String tokenSPDV;

    @Schema(description = SwaggerConstant.Service.URL, example = SwaggerConstant.Example.URL)
    @Size(max = ServicesConstant.UrlService.MAX_LENGTH, message = MessageKeyConstant.Validation.SIZE)
    @Url
    private String url;

    private Integer urlServiceStatus; // 0- Luôn hiển thị, 1 - Hiển thị khi khách hàng đăng nhập

    @Size(min = ServicesConstant.LanguageType.MIN_SIZE, max = ServicesConstant.LanguageType.MAX_SIZE,
            message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = SwaggerConstant.Service.LANGUAGE, example = SwaggerConstant.Example.LANGUAGE)
    private String[] language;

    @Size(max = ServicesConstant.Email.MAX_LENGTH, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = SwaggerConstant.Service.EMAIL, example = SwaggerConstant.Example.EMAIL)
    private String email;

    @Size(max = ServicesConstant.PhoneNumber.MAX_LENGTH, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = SwaggerConstant.Service.PHONE, example = SwaggerConstant.Example.PHONE)
    private String phoneNumber;

    @Schema(description = SwaggerConstant.Service.SERVICE_TYPE, example = SwaggerConstant.Example.SERVICE_TYPE)
    private ServiceTypeEnum serviceOwner;

    private OnOsTypeEnum onOsType; // Loại dịch vụ. 0: ON, 1:OS

    private ProviderTypeEnum providerType;  // Đơn vị phát triển. 0:VNPT, 1:PARTNER, 2:THIRD_PARTY, 3:Tích hợp KHCN, 4:VNPT_TECHNOLOGY

    private ServiceOwnerPartner serviceOwnerPartner;

    private Long video;

    @Schema(description = SwaggerConstant.Service.URL_PRE_ORDER, example = SwaggerConstant.Example.URL)
    @Url
    private String urlPreOrder;

    @Schema(description = SwaggerConstant.Service.SERVICE_CODE, example = SwaggerConstant.Example.BILL_CODE)
    private String serviceCode;

    @Schema(description = SwaggerConstant.Service.SERVICE_TYPE_ID, example = SwaggerConstant.Example.SERVICE_TYPE_ID)
    @Size(max = ServicesConstant.UrlService.MAX_LENGTH, message = MessageKeyConstant.Validation.SIZE)
    private String serviceTypeId;

    @Schema(description = SwaggerConstant.Service.SERVICE_TYPE_ID, example = SwaggerConstant.Example.SERVICE_TYPE_ID)
    SeoReqDTO seoReqDTO;

    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    @Schema(description = SwaggerConstant.Service.ICON_ID, example = SwaggerConstant.Example.ID)
    private Long icon;

    private List<Long> iconService = new ArrayList<>(); // update logic tạo dịch vụ thiết bị

    @Schema(description = SwaggerConstant.Service.BANNER_ID, example = SwaggerConstant.Example.ID)
    private Long banner;

    @Schema(description = Service.COMMENT, example = Example.COMMENT)
    private String reason;

    @Schema(description = Service.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_NAME)
    @NotNull(message = MessageKeyConstant.Validation.NOT_NULL)
    private Set<CustomerTypeEnum> customerTypeCode;

    private Integer allowMultiSub;
    private Integer registerEcontract;

    private Long creationLayoutId;
    private List<CustomFieldValueDTO> lstCustomField = new ArrayList<>();
    private ServiceOwnerVNPTEnum serviceOwnerVNPT;

    private TaxInfoConvertDTO tax;

    private SetupFeeInfoConvertDTO setupFee;

    private BigDecimal price;

    private ServiceProductTypeEnum productType; // loại sản phẩm cũ
    private ProductClassificationEnum classification; // loại sản phẩm

    private String description;
    private String plainTextDescription; // lưu giá trị plaintext của description, phục vụ tìm kiếm SPDV = description
    private VariantApplyDTO variantApply;
    private String specifications;
    private PaymentMethodEnum paymentMethod;
    private Integer installationConfiguration; // Cấu hình lắp đặt : 1: Lắp đặt tại nhà, 0: Không lắp đặt
    private List<Long> lstCategoryId = new ArrayList<>();
    private Long providerId; // id nhà cung cấp
    private Long  manufacturerId; // id nhà sản xuất
    private String sku;
    private PromotionInfoDTO promotionInfo; // thông tin khuyến mại
    private InventoryInfoDTO inventoryInfo; // thông tin tồn kho
    private ShippingInfoDTO shippingInfo; // thông tin vận chuyển
    private WarrantyInfoDTO warrantyInfo; // thông tin bảo hành
    private PortalType portalType;

    public Integer getPaymentMethodValue() {
        return paymentMethod == null ? null : paymentMethod.getValue();
    }
}

package com.dto.services.sugesstion;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.springframework.beans.BeanUtils;
import com.dto.TaxCommonDetailDTO;
import com.dto.pricing.PricingCommonDetailDTO;
import com.dto.product_solustions.GetSPDVBundlingDTO.AttributeSPDVDTO;
import com.dto.product_solustions.GetSPDVBundlingDTO.VariantSPDVDTO;
import com.dto.product_solustions.MetadataDTO;
import com.dto.services.ServiceSuggestionDTO;
import com.enums.product_solutions.SuggestionTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DiscountTypeEnum;
import com.onedx.common.constants.enums.subscriptions.PaymentMethodEnum;
import com.onedx.common.constants.values.CharacterConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceCommonDetailDTO {

    Long id;

    String name;

    String description;

    SuggestionTypeEnum suggestionType;

    String imageUrl;

    Integer numPricing;

    BigDecimal price;

    BigDecimal priceFrom;

    Long draftId;

    @Schema(description = "Thông tin nhà cung cấp")
    String providerName;

    String categoryName;

    @Schema(description = "Tổng số lượt đã mua mà khách hàng đã đăng ký/ đặt hàng")
    Long numSub;

    @Schema(description = "Số điểm đánh giá trung bình")
    Double avgRating;

    @Schema(description = "Mô tả giới thiệu Sản phẩm")
    String descriptions;

    @Schema(description = "Đối tượng khách hàng")
    Set<CustomerTypeEnum> customerTypes;

    BigDecimal pricePreTax;

    @Schema(description = "Phương thức thanh toán")
    PaymentMethodEnum paymentMethod;

    @Schema(description = "Loại khuyến mại")
    DiscountTypeEnum discountType;

    @Schema(description = "Giá trị khuyến mại")
    BigDecimal discountValue;

    MetadataDTO suggestMetadata;

    @Schema(description = "Danh sách các tính năng")
    List<String> features;

    @JsonIgnore
    List<TaxCommonDetailDTO> taxes = new ArrayList<>();

    List<PricingCommonDetailDTO> lstPricing;

    List<VariantSPDVDTO> lstVariantSPDV;

    List<AttributeSPDVDTO> lstAttributeSPDV;
    
    public ServiceCommonDetailDTO(ServiceSuggestionDTO serviceSuggestionDTO) {
        BeanUtils.copyProperties(serviceSuggestionDTO, this);
        this.features = Objects.nonNull(serviceSuggestionDTO.getFeatures()) ?
            Arrays.asList(serviceSuggestionDTO.getFeatures().split(CharacterConstant.SEMICOLON)) : null;
        this.taxes = serviceSuggestionDTO.getTaxes();
    }

    public ServiceCommonDetailDTO(ServiceSuggestionDTO serviceSuggestionDTO, List<PricingCommonDetailDTO> lstPricing) {
        BeanUtils.copyProperties(serviceSuggestionDTO, this);
        this.features = Objects.nonNull(serviceSuggestionDTO.getFeatures()) ?
            Arrays.asList(serviceSuggestionDTO.getFeatures().split(CharacterConstant.SEMICOLON)) : null;
        this.taxes = serviceSuggestionDTO.getTaxes();
        this.lstPricing = lstPricing;
    }

}

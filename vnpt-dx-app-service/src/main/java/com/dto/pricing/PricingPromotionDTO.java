package com.dto.pricing;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Transient;
import com.constant.enums.product_variant.ExtraPriceTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.onedx.common.constants.enums.YesNoEnum;
import com.onedx.common.utils.DateUtil;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PricingPromotionDTO implements Serializable {
    private YesNoEnum sameVariant ; // Bật tắt áp dụng chung quy tắc với biến thể
    private BigDecimal discountValue; // giá trị chiết khấu
    private ExtraPriceTypeEnum discountType; // kiểu chiết khấu
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date discountFrom; // Thời gian bắt đầu chiết khấu
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date discountTo; // Thời gian kết thúc chiết khấu
    @Transient
    private BigDecimal discountAmount;
}
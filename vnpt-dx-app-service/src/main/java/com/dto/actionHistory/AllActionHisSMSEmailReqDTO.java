package com.dto.actionHistory;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryObjectTypeEnum;
import com.onedx.common.constants.enums.history.actionHistory.ActionNoteObjectTypeEnum;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.ObjectUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AllActionHisSMSEmailReqDTO {
    ActionHistoryObjectTypeEnum objectType;
    ActionNoteObjectTypeEnum objectNoteType;
    Long objectId;
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date startDate;
    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    Date endDate;
    String search;
    Integer actionType;
    Integer page = 0;
    Integer size = 10;
    String sort = "creationTime,desc";

    // Logic objectType ENTERPRISE và SME lấy gọp log
    Long enterpriseId;
    Long userId;

    public Date getStartDate() {
        //Giá trị mặc định là 1 ngày nhỏ
        return (this.startDate != null) ? this.startDate : new GregorianCalendar(1970, Calendar.JANUARY, 1).getTime();
    }

    public Date getEndDate() {
        //Giá trị mặc định là 1 ngày lớn
        return (this.endDate != null) ? this.endDate : new GregorianCalendar(3000, Calendar.JANUARY, 1).getTime();
    }

    public String getSearch(){
        return (this.search != null) ? this.search : "";
    }

    public Integer getActionType(){
        return (this.actionType != null) ? this.actionType : -1;
    }

    public Long getUserId() {
        return ObjectUtil.getOrDefault(this.userId, -1L);
    }

    public Long getEnterpriseId() {
        return ObjectUtil.getOrDefault(this.enterpriseId, -1L);
    }
}

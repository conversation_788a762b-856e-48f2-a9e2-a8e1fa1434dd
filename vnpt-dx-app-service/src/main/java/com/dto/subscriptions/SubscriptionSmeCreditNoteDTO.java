package com.dto.subscriptions;

import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.CreditNote;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * <AUTHOR> Halt
 * @version : 1.0
 * 16/06/2021
 */
public interface SubscriptionSmeCreditNoteDTO {
    @Schema(description = SwaggerConstant.CreditNote.ID, example = SwaggerConstant.Example.ID)
    Long getId();

    @Schema(description = SwaggerConstant.CreditNote.CODE, example = SwaggerConstant.Example.BILL_CODE)
    String getCode();

    @Schema(description = SwaggerConstant.CreditNote.BILLING_CODE, example = SwaggerConstant.Example.BILL_CODE)
    String getBillingCode();

    @Schema(description = SwaggerConstant.CreditNote.MONEY_REFUND, example = SwaggerConstant.Example.TOTAL)
    Float getMoneyRefund();

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    @Schema(description = SwaggerConstant.CreditNote.CREATED_AT, example = SwaggerConstant.Example.DATE)
    Date getCreatedAt();

    @Schema(description = SwaggerConstant.CreditNote.STATUS, example = SwaggerConstant.Example.TYPE_NUMBER)
    Integer getStatus();

    @Schema(description = SwaggerConstant.CreditNote.STATUS, example = SwaggerConstant.Example.TYPE_NUMBER)
    Integer getCreditNoteType();

    @Schema(description = CreditNote.REASON_REFUND, example = Example.REASON_REFUND)
    String getReason();

    @Schema(description = CreditNote.CONTENT, example = Example.REASON_REFUND)
    String getContent();
}
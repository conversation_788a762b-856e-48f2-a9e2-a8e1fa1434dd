package com.dto.subscriptions;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SubscriptionsDTO {
    private String serviceName;

    private String subId;

    private String ownerName;

    private String pricingName;

    private List<Long> categoriesId;

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YY_HH_MM_SS, timezone = DateUtil.TIME_ZONE)
    private Date createdAt;

    @JsonFormat(pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH, timezone = DateUtil.TIME_ZONE)
    private Date evaluationDeadline;

    private Double price;

    private String fileName;

    private String filePath;

    private String fileExtension;

    private Integer isOneTime;

    private Integer createdSourceMigration;
}

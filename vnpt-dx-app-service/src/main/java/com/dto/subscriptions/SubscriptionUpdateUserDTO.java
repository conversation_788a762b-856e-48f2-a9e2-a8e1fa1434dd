package com.dto.subscriptions;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import com.onedx.common.constants.values.LoginConst;
import com.onedx.common.exception.MessageKeyConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> CuongLV2
 * @version    : 1.0
 * 27/11/2021
 */
@Data
public class SubscriptionUpdateUserDTO {
    @Size(min = 1, max = 20, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = "Mã số thuế doanh nghiệp", example = "*********")
    private String taxCode;

    @Size(min = 0, max = 500)
    @Schema(description = "Tên doanh nghiệp", example = "Công ty cổ phần ACB ")
    private String name;

    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    @Size(min = 1, max = 500, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = "Địa chỉ doanh nghiệp", example = "số 57 Huỳnh Thúc Kháng, quận Đống Đa, thành phố Hà Nội, Việt Nam")
    private String address;

    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    @Pattern(regexp = LoginConst.PHONE_REGEX, message = MessageKeyConstant.Validation.PATTERN)
    @Schema(description = "Số điện thoại doanh nghiệp", example = "02437877777")
    private String phoneNumber;

    @NotBlank(message = MessageKeyConstant.Validation.NOT_BLANK)
    @Pattern(regexp = LoginConst.EMAIL_REGEX)
    @Size(min = 0, max = 100, message = MessageKeyConstant.Validation.SIZE)
    @Schema(description = "Địa chỉ email", example = "<EMAIL>")
    private String email;

    @Schema(description = "Tên người dùng", example = "Huy")
    private String firstName;

    @Schema(description = "Họ + tên đệm của người dùng", example = "Trịnh Văn")
    private String lastName;

    @Schema(description = "Số chứng thực cá nhân", example = "001099005682")
    private String identityNo;

    @Schema(description = "Địa chỉ lap dat", example = "số 57 Huỳnh Thúc Kháng, quận Đống Đa, thành phố Hà Nội, Việt Nam")
    private String setupAddress;

    @NotNull(message = MessageKeyConstant.Validation.NULL)
    private Long provinceId;

    private String provinceCode;

    private String provinceName;

    @NotNull(message = MessageKeyConstant.Validation.NULL)
    private Long districtId;

    private String districtCode;

    private String districtName;

    @NotNull(message = MessageKeyConstant.Validation.NULL)
    private Long wardId;

    private String wardCode;

    private String wardName;

    @NotNull(message = MessageKeyConstant.Validation.NULL)
    private Long streetId;

    private String streetName;

}

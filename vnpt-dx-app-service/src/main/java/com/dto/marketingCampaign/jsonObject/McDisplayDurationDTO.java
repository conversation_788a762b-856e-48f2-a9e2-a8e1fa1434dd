package com.dto.marketingCampaign.jsonObject;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class McDisplayDurationDTO {

    @JsonProperty("type")
    private Integer type = 0; /* 0: cho tới khi người dùng tắt; 1: tù<PERSON> chọn */

    // TODO: khác null khi type = 1, chỉ gồm các giá trị 0, 1, 2
    @JsonProperty("unit")
    private Integer unit; /* 0: <PERSON><PERSON><PERSON><PERSON>, 1: <PERSON><PERSON><PERSON>,  2: Gi<PERSON> */

    // TODO: khác null khi type = 1
    @JsonProperty("value")
    private Long value;
}

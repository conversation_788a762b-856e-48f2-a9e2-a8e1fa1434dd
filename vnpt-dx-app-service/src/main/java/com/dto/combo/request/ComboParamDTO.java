/**
 * 
 */
package com.dto.combo.request;

import java.util.Collections;
import java.util.List;
import javax.validation.constraints.Size;
import com.enums.DisplayStatus;
import com.onedx.common.annotation.TrimString;
import com.onedx.common.constants.values.SwaggerConstant.Category;
import com.onedx.common.constants.values.SwaggerConstant.Combo;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import com.onedx.common.constants.values.SwaggerConstant.User;
import com.onedx.common.exception.MessageKeyConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> HienNT6
 * @version : 1.0
 * @create_date: Jun 23, 2021
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TrimString(fieldNames = {"name","createdName"})
public class ComboParamDTO {
    @Schema(description = Combo.COMBO_NAME, example = Example.COMBO_NAME)
    @Size(min = 1, max = 100, message = MessageKeyConstant.Validation.SIZE)
    private String name;
    @Schema(description = Category.ID, example = Example.ID)
    private String categoryId;
//    @Schema(description = Combo.APPROVED_STATUS, example = Example.APPROVE)
//    private ApproveStatusEnum approvedStatus;
//    @Schema(description = Combo.DISPLAY_STATUS, example = Example.DISPLAY)
    private DisplayStatus displayedStatus;
    @Schema(description = User.FULL_NAME, example = Example.FULL_NAME)
    private String createdName;
//    @Schema(description = User.ID, example = Example.ID)
//    private Long createdBy;
//    @Schema(description = Service.CUSTOMER_TYPE_CODE, example = Example.CUSTOMER_TYPE_CODE)
//    private CustomerTypeEnum customerTypeCode;
    private Integer allowMultiSub;
    private Integer isName = 0;
    private Integer isCode = 0;
    private Integer isDes = 0;
    private String value = "";

    private List<Integer> types = Collections.singletonList(-1);
    private List<String> comboTypes = Collections.singletonList("ALL");
    private String[] customerTypeCode = new String[]{"ALL"};
    private List<Integer> approvedStatus = Collections.singletonList(-1);
    private List<String> createdBy = Collections.singletonList("-1");

}

package com.dto.categories;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import com.constant.enums.services.ServiceProductTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.util.StringUtil;
import io.swagger.v3.oas.annotations.media.Schema;

public interface ICategoryServicesDTO {

    @Schema(description = "id danh mục")
    Long getId();

    @Schema(description = "tên danh mục")
    String getName();

    Integer getSizeService();

    @JsonIgnore
    String getListServiceProductTypeStr();

    @Schema(description = "Danh sách productType của ca SPDV được gắn với danh mục")
    default Set<ServiceProductTypeEnum> getListServiceProductType() {
        List<Integer> productTypeLst = StringUtil.convertStringArrToListInteger(this.getListServiceProductTypeStr());
        return ObjectUtils.isNotEmpty(productTypeLst) ? productTypeLst.stream().map(ServiceProductTypeEnum::fromValue).filter(Objects::nonNull)
            .collect(Collectors.toSet()) : Collections.emptySet();
    }

}

package com.dto.coupons;

import com.onedx.common.constants.values.SwaggerConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.onedx.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * * Coupon Enterprise Response
 *
 * <AUTHOR>
 *  5/7/2021 1:49 PM
 */

public interface CouponEnterpriseResponse {

    @Schema(description = SwaggerConstant.User.ID, example = SwaggerConstant.Example.ID)
    Long getUserId();

    @Schema(description = SwaggerConstant.User.NAME_SME, example = SwaggerConstant.Example.NAME_SME)
    String getName();

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            timezone = DateUtil.TIME_ZONE)
    @Schema(description = SwaggerConstant.Category.CREATEDTIME, example = SwaggerConstant.Example.DATE)
    Date getCreatedAt();
}

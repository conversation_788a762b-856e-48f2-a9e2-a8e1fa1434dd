package com.dto.openapis.v1.payments;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.OptBoolean;
import com.onedx.common.utils.DateUtil;

import java.math.BigDecimal;
import java.util.Date;

public interface IGetPaymentStatusDTO {
    Long getOrderId();
    BigDecimal getAmount();
    String getCurrency();
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH,
            lenient = OptBoolean.FALSE)
    Date getPaidAt();
    Integer getStatus(); // từ bảng billing
}

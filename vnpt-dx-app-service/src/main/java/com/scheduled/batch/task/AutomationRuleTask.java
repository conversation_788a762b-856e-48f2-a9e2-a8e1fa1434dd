package com.scheduled.batch.task;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.common.CommonUtils;
import com.constant.enums.crm.assignmentRule.AssignmentMethodEnum;
import com.constant.enums.crm.assignmentRule.FrequencyAutomationRuleEnum;
import com.constant.sql.SQLFieldForceConstant;
import com.dto.TimeInfoDTO;
import com.dto.actionNotification.ActionNotificationParamDTO;
import com.dto.actionNotification.CommonNotifParamDTO;
import com.dto.crm.assignmentRule.AssignmentRuleAssigneeDTO;
import com.dto.crm.assignmentRule.IGetAssignmentRuleAssigneeDTO;
import com.dto.crm.assignmentRule.IGetObjectInteractiveInfoDTO;
import com.dto.crm.assignmentRule.ISubscriptionIdAndCartCodeDTO;
import com.dto.crm.assignmentRule.RuleInactiveConfigDTO;
import com.dto.crm.assignmentRule.RuleScanningPolicyDTO;
import com.dto.crm.assignmentRule.notification.AssignmentRuleMailDetailDTO;
import com.dto.crm.assignmentRule.notification.AutoRuleCountSubDTO;
import com.dto.crm.assignmentRule.notification.AutoRuleSubDetailDTO;
import com.dto.crm.assignmentRule.notification.IAutoRulePartitionSubDetailDTO;
import com.dto.crm.assignmentRule.notification.IAutoRuleProvinceSubDetailDTO;
import com.dto.crm.assignmentRule.notification.IObjectDetailDTO;
import com.dto.crm.assignmentRule.notification.ObjectInteractiveInfoDTO;
import com.dto.subscriptions.mail.NotifScDetailDTO;
import com.dto.subscriptions.mail.NotifScSubDetailDTO;
import com.entity.actionHistory.ActionHistory;
import com.entity.crm.assignmentRule.AutomationRule;
import com.entity.crm.assignmentRule.AutomationRuleCache;
import com.entity.crm.assignmentRule.HistoryInactiveWarning;
import com.entity.crm.assignmentRule.RuleActionAssignment;
import com.entity.crm.assignmentRule.RuleActionNotification;
import com.entity.crm.assignmentRule.RuleActionUpdate;
import com.entity.crm.assignmentRule.RuleCondition;
import com.entity.report.AutoSendReportConfig;
import com.enums.ActionNotificationEnum;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.helpers.ScanAutoSendReportTask;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.assignmentRule.AutomationRuleActionTypeEnum;
import com.onedx.common.constants.enums.assignmentRule.actionNotification.ReceiverTypeEnum;
import com.onedx.common.constants.enums.crm.CrmObjectTypeEnum;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryObjectTypeEnum;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryTypeEnum;
import com.onedx.common.constants.values.AutomationRuleConstant;
import com.onedx.common.dto.automationRule.RuleNotificationContentDTO;
import com.onedx.common.dto.base.ICommonIdNameEmail;
import com.onedx.common.utils.DateUtil;
import com.onedx.common.utils.ObjectUtil;
import com.repository.actionHistory.ActionHistoryRepository;
import com.repository.crm.assignmentRule.AutomationRuleRepository;
import com.repository.crm.assignmentRule.CrmAssignmentRuleRepository;
import com.repository.crm.assignmentRule.HistoryInactiveHistoryRepository;
import com.repository.crm.assignmentRule.RuleActionAssignmentRepository;
import com.repository.crm.assignmentRule.RuleActionNotificationRepository;
import com.repository.crm.assignmentRule.RuleActionUpdateRepository;
import com.repository.crm.assignmentRule.RuleConditionRepository;
import com.repository.crm.assignmentRule.mongodb.AutomationRuleCachesRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.users.UserRepository;
import com.service.crm.automationRule.impl.AutomationRuleServiceImpl;
import com.service.notification.ActionNotificationService;
import com.service.notification.GTB11;
import com.service.notification.NotificationService;
import com.service.notification.PGD11Custom;
import com.service.notification.PGD11Template;
import com.service.notification.enums.AutoRuleNotifyContentTypeEnum;
import com.service.notification.enums.NotificationTypeEnum;
import com.service.notification.template.NotificationContentConstant;
import com.service.notification.template.PGD05;
import com.service.notification.template.PGD06;
import com.service.notification.template.PGD07;
import com.service.notification.template.PGD09;
import com.service.notification.template.PGD10;
import com.service.notification.template.PW01;
import com.service.notification.template.PW02;
import com.service.notification.template.PW03;
import com.service.notification.template.SB01;
import com.service.notification.template.SB02;
import com.service.notification.template.SCD01;
import com.service.notification.template.SCD02;
import com.service.users.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

@RequiredArgsConstructor
@Component("batch-task-scanning-automation-rule")
@Slf4j
public class AutomationRuleTask {

    private final CrmAssignmentRuleRepository assignmentRuleRepository;
    private final UserService userService;
    private final NotificationService notificationService;
    private final ActionNotificationService actionNotificationService;
    private final JdbcTemplate jdbcTemplate;
    private final ActionHistoryRepository actionHistoryRepository;
    private final AutomationRuleRepository automationRuleRepository;
    private final RuleConditionRepository ruleConditionRepository;
    private final RuleActionAssignmentRepository ruleActionAssignmentRepository;
    private final RuleActionUpdateRepository ruleActionUpdateRepository;
    private final RuleActionNotificationRepository ruleActionNotificationRepository;
    private final AutomationRuleServiceImpl automationRuleService;
    private final HistoryInactiveHistoryRepository historyInactiveHistoryRepository;
    private final AutomationRuleCachesRepository automationRuleCachesRepository;
    private final ScanAutoSendReportTask autoSendReportTask;
    private final UserRepository userRepository;
    private final SubscriptionRepository subscriptionRepository;

    @Value(value = "${web.host}")
    private String webHost;

    List<ActionNotificationEnum> lstPasswordLeakNotifCode = Arrays.asList(ActionNotificationEnum.PW_01, ActionNotificationEnum.PW_02,
        ActionNotificationEnum.PW_03);

    Integer MAX_PARTITION_SIZE = 100;

    private final AtomicInteger updatedCount = new AtomicInteger(0);

    /**
     * Định kì quét các automation rule có cấu hình chạy theo thời gian
     */


    public void scanningAutomationRule() throws ParseException {
        log.info("autoSendNotify: start");
        TimeInfoDTO currentTime = new TimeInfoDTO();
        Timestamp timestamp = new java.sql.Timestamp(Calendar.getInstance().getTimeInMillis());
        //Lấy các condition rule còn lưu thông tin trên cache chưa được quét
        List<AutomationRuleCache> lstAutomationRuleCache = automationRuleCachesRepository.findAll();
        Map<Long, AutomationRuleCache> mapRuleCache = lstAutomationRuleCache.stream()
            .collect(Collectors.toMap(AutomationRuleCache::getId, Function.identity()));
        Set<Long> setRuleConditionCacheIds = lstAutomationRuleCache.stream().map(AutomationRuleCache::getId).collect(Collectors.toSet());
        Set<Long> setRuleCacheIds = ObjectUtil.getOrDefault(getLstRuleCacheId(lstAutomationRuleCache), Collections.singleton(-1L));
        // Lấy ra toàn bộ bản ghi assignment rule có cấu hình thời gian chạy khác ngay lập tức
        List<AutomationRule> lstActiveRule = automationRuleRepository.getAllRuleNonImmediate(setRuleCacheIds);
        //List<AutomationRule> lstActiveRule = automationRuleRepository.findALLById(3127L); // for test only
        // Gán assigneeId theo rule
        List<Object[]> batchUpdateArgs = new ArrayList<>();

        for (AutomationRule rule : lstActiveRule) {
            // Nếu không thỏa mãn thời gian cấu hình rule và không có thông tin trong cache thì bỏ qua
            boolean ruleMatchingTime = matchingTime(currentTime, rule);
            if (!ruleMatchingTime && !setRuleCacheIds.contains(rule.getId())) {
                continue;
            }
            CrmObjectTypeEnum objectType = rule.getObjectType();
            // Quét gán assigneeId
            List<RuleCondition> lstRuleCondition = ruleConditionRepository.findAllByRuleId(rule.getId());
            for (RuleCondition ruleCondition : lstRuleCondition) {
                try {
                    // Nếu là loại thông báo thì check theo tần suất gửi thông báo
                    AutomationRuleActionTypeEnum actionTypeEnum = ruleCondition.getActionType();
                    // Nếu rule một lần và đã được quét thì bỏ qua
                    if (Objects.equals(AutomationRuleConstant.SCANNING_ONETIME, rule.getScanningPolicy().getType()) &&
                        ruleConditionRepository.existsScanning(ruleCondition.getId()) &&
                        !setRuleConditionCacheIds.contains(ruleCondition.getId())) {
                        continue;
                    }

                    String ruleConditionSql = getScanConditionSQL(ruleCondition.getActionType(), ruleCondition.getConditionSql());
                    Integer numApplyingObject = assignmentRuleRepository.getNumApplyingObject(objectType.getValue(), ruleConditionSql);
                    Set<Long> lstObjectId = new LinkedHashSet<>(
                        assignmentRuleRepository.getListObjectIdByCondition(ruleConditionSql, objectType.getValue()));
                    switch (actionTypeEnum) {
                        case ASSIGN:
                            RuleActionAssignment ruleActionAssignment = ruleActionAssignmentRepository.findByRuleConditionId(
                                ruleCondition.getId()).orElse(null);
                            if (Objects.isNull(ruleActionAssignment)) {
                                continue;
                            }
                            AssignmentMethodEnum currentAssignmentMethod = ObjectUtil.getOrDefault(ruleActionAssignment.getAssignmentMethod(),
                                AssignmentMethodEnum.EQUALLY_DIVIDED);
                            scanningAssigningRule(objectType, currentAssignmentMethod, numApplyingObject, lstObjectId, ruleCondition);
                            break;
                        case UPDATE:
                            RuleActionUpdate ruleActionUpdate = ruleActionUpdateRepository.findByRuleConditionId(
                                ruleCondition.getId()).orElse(null);
                            if (Objects.isNull(ruleActionUpdate) || numApplyingObject == 0) {
                                continue;
                            }
                            ruleCondition.setUpdateSql(ruleActionUpdate.getUpdateSql());
                            scanningUpdatingRule(objectType, lstObjectId, ruleCondition, rule.getName());
                            break;
                        case NOTIFICATION:
                            if (!lstObjectId.isEmpty()) {
                                // Nếu quét gửi mail tần suất thì lấy dữ liệu từ cache
                                if (!ruleMatchingTime) {
                                    Set<Long> lstObjectCacheId = getLstObjectCacheId(mapRuleCache.get(ruleCondition.getId()));
                                    lstObjectCacheId.removeIf(item -> !lstObjectId.contains(item));
                                    scanningSendingNotificationRule(lstObjectId, objectType.getValue(), ruleCondition);
                                } else {
                                    // Cập nhật lại cache
                                    updateAutomationRuleCache(rule.getId(), ruleCondition.getId(), lstObjectId);
                                    scanningSendingNotificationRule(lstObjectId, objectType.getValue(), ruleCondition);
                                }
                            } else {
                                automationRuleCachesRepository.deleteById(ruleCondition.getId());
                            }
                            break;
                        default:
                            break;
                    }
                    batchUpdateArgs.add(new Object[]{timestamp, ruleCondition.getId()});
                } catch (Exception e) {
                    log.error("scanningRuleByObjectType: failed to scanning rule (id {}, code {}), condition {} with error: {}",
                        ruleCondition.getId(), ruleCondition.getName(), ruleCondition.getConditionSql(), e.getMessage());
                    // Lưu lịch sử quét rule thất bại
                    String content = "Quét quy tắc %s thất bại với đối tượng %s";
                    actionHistoryRepository.save(
                        new ActionHistory(ActionHistoryTypeEnum.AUTOMATION_RULE_SCAN_FAILURE, ActionHistoryObjectTypeEnum.AUTOMATION_RULE,
                            rule.getId(), -1L, String.format(content, rule.getName(), rule.getObjectType().name())));
                }
            }
        }
        if (!batchUpdateArgs.isEmpty()) {
            jdbcTemplate.batchUpdate(SQLFieldForceConstant.updateRuleConditionScannedAtSuccess, batchUpdateArgs);
        }
    }

    public void updateAutomationRuleCache(Long ruleId, Long ruleConditionId, Set<Long> lstObjectId) {
        automationRuleCachesRepository.deleteById(ruleConditionId);
        Map<String, Object> mapRuleData = new HashMap<>();
        mapRuleData.put("ruleId", ruleId);
        mapRuleData.put("lstObjectId", lstObjectId);
        AutomationRuleCache currentRuleCache = AutomationRuleCache.builder().id(ruleConditionId).info(mapRuleData).build();
        automationRuleCachesRepository.save(currentRuleCache);
    }

    private Set<Long> getLstObjectCacheId(AutomationRuleCache automationRuleCache) {
        if (Objects.nonNull(automationRuleCache) && Objects.nonNull(automationRuleCache.getInfo()) &&
            automationRuleCache.getInfo().containsKey("lstObjectId")) {
            return new LinkedHashSet<>((List<Long>) automationRuleCache.getInfo().get("lstObjectId"));
        }
        return new LinkedHashSet<>();
    }

    private Set<Long> getLstRuleCacheId(List<AutomationRuleCache> lstAutomationRuleCache) {
        return lstAutomationRuleCache.stream().filter(item -> item.getInfo().containsKey("ruleId"))
            .map(ruleCache -> (Long) ruleCache.getInfo().get("ruleId")).collect(Collectors.toSet());
    }

    public String getScanConditionSQL(AutomationRuleActionTypeEnum actionTypeEnum, String ruleConditionSql) {
        RuleInactiveConfigDTO ruleInactiveConfig = automationRuleService.getRuleInactiveConfig();
        String conditionSql = Objects.equals(actionTypeEnum, AutomationRuleActionTypeEnum.ASSIGN) ?
                ruleConditionSql + " and assignee_id is null " : ruleConditionSql;
        if (Objects.nonNull(ruleInactiveConfig) && Objects.nonNull(ruleInactiveConfig.getRuleNoPwdChangeConfig())) {
            return String.format(conditionSql, ruleInactiveConfig.getRuleNoPwdChangeConfig().getDurationTimeInterval());
        }
        return conditionSql;
    }

    private boolean matchingTime(TimeInfoDTO currentTime, AutomationRule rule) throws ParseException {
        RuleScanningPolicyDTO ruleScanningPolicy = rule.getScanningPolicy();
        TimeInfoDTO timeInfoConfig = new TimeInfoDTO(ruleScanningPolicy);
        String currentFrequency = timeInfoConfig.intervalType;
        if (Objects.equals(AutomationRuleConstant.OCCASION_SPECIFIC, ruleScanningPolicy.getOccasion())) {
            return validateTime(currentTime, timeInfoConfig) && currentTime.startDate.compareTo(timeInfoConfig.startDate) == 0;
        } else {
            if (Objects.equals(FrequencyAutomationRuleEnum.DAILY.name(), currentFrequency) && validateTime(currentTime, timeInfoConfig)) {
                return Boolean.TRUE;
            } else if (Objects.equals(FrequencyAutomationRuleEnum.WEEKLY.name(), currentFrequency)
                && validateDayOfWeek(currentTime, timeInfoConfig) && validateTime(currentTime, timeInfoConfig)) {
                return Boolean.TRUE;
            } else if (Objects.equals(FrequencyAutomationRuleEnum.MONTHLY.name(), currentFrequency) &&
                validateDayOfMonth(currentTime, timeInfoConfig) && validateTime(currentTime, timeInfoConfig)) {
                return Boolean.TRUE;
            } else if (Objects.equals(FrequencyAutomationRuleEnum.CUSTOM.name(), currentFrequency) &&
                validateLstDayOfMonth(currentTime, timeInfoConfig) && validateTime(currentTime, timeInfoConfig)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private boolean validateTime(TimeInfoDTO currentTime, TimeInfoDTO timeInfoConfig) {
        if (timeInfoConfig.period == 1 && currentTime.hour == timeInfoConfig.hour && currentTime.minute == timeInfoConfig.minute) return Boolean.TRUE;
        if (timeInfoConfig.period > 1 && TimeInfoDTO.comparePeriod(currentTime, timeInfoConfig)) return Boolean.TRUE;
        return Boolean.FALSE;
    }

    private boolean validateDayOfWeek(TimeInfoDTO currentTime, TimeInfoDTO timeInfoConfig) {
        if (timeInfoConfig.dayOfWeek == currentTime.dayOfWeek) return Boolean.TRUE;
        return Boolean.FALSE;
    }

    private boolean validateDayOfMonth(TimeInfoDTO currentTime, TimeInfoDTO timeInfoConfig) {
        if (timeInfoConfig.dayOfMonth == currentTime.dayOfMonth) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private boolean validateLstDayOfMonth(TimeInfoDTO current, TimeInfoDTO timeInfoConfig) {
        if (timeInfoConfig.setDayOfMonth.contains(current.dayOfMonth)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    public void scanningSendingNotificationRule(Set<Long> lstObjectId, Integer objectType, RuleCondition ruleCondition) {
        String ruleConditionSql = ruleCondition.getConditionSql();
        log.info("==== START scanningSendingNotificationRule with ruleConditionId = {}, ruleConditionSql = {}, objectType = {}, num of objects = {}",
            ruleCondition.getId(), ruleConditionSql, objectType, lstObjectId.size());
        RuleActionNotification ruleActionNotification = ruleActionNotificationRepository.findByRuleConditionId(ruleCondition.getId())
                .orElse(null);
        if (Objects.isNull(ruleActionNotification) || Objects.isNull(ruleActionNotification.getLstReceiverId())) {
            return;
        }
        // Kiểm tra thông tin cấu hình tần suất gửi mail
        if (Objects.nonNull(ruleActionNotification.getFrequencyConfig())) {
            AutoSendReportConfig autoSendReportConfig = new AutoSendReportConfig();
            BeanUtils.copyProperties(ruleActionNotification.getFrequencyConfig(), autoSendReportConfig);
            if (!autoSendReportTask.matchingTime(new TimeInfoDTO(), autoSendReportConfig)) {
                return;
            }
        } 

        // Loại nội dung thông báo
        List<RuleNotificationContentDTO> lstContentConfig = ruleActionNotification.getLstContentConfig();
        List<IAutoRulePartitionSubDetailDTO> lstPartitionDetail;
        List<IAutoRuleProvinceSubDetailDTO> lstTelegramProvinceDetail = new ArrayList<>();
        List<IAutoRuleProvinceSubDetailDTO> lstMailProvinceDetail = new ArrayList<>();

        log.info("scanningSendingNotificationRule: Calling assignmentRuleRepository.getLstPartitionSubDetail with ruleConditionSql = {} and num of object = {}",
            ruleCondition.getId(), lstObjectId.size());
        lstPartitionDetail = assignmentRuleRepository.getLstPartitionSubDetail(lstObjectId);

        // nếu có ít nhất 1 hình thức gửi loại nội dung là Mẫu + template là GTB11 --> lấy data nội dung template GTB 11 trước
        // (tránh get data đó trong for loop)
        List<RuleNotificationContentDTO> lstGTB11Config = lstContentConfig.stream()
            .filter((config -> Objects.equals(config.getContentType(), AutoRuleNotifyContentTypeEnum.TEMPLATE.getValue()) &&
                Objects.equals(config.getTemplateCode(), ActionNotificationEnum.GTB_11.getCode())))
            .collect(Collectors.toList());

        if (lstGTB11Config.stream().anyMatch(config -> Objects.equals(config.getNotificationType(), NotificationTypeEnum.TELEGRAM.toString()))) {
            // TH1: gửi template GTB-11 telegram
            log.info("scanningSendingNotificationRule: Calling assignmentRuleRepository.getLstTelegramProvinceSubDetail with ruleConditionSql = {} and num of object = {}",
                ruleCondition.getId(), lstObjectId.size());
            lstTelegramProvinceDetail = assignmentRuleRepository.getLstTelegramProvinceSubDetail(lstObjectId);
        }
        if (lstGTB11Config.stream().anyMatch(config -> Objects.equals(config.getNotificationType(), NotificationTypeEnum.EMAIL.toString()))) {
            // TH2: gửi template GTB-11 mail
            log.info("scanningSendingNotificationRule: Calling assignmentRuleRepository.getLstMailProvinceSubDetail with ruleConditionSql = {} and num of object = {}",
                ruleCondition.getId(), lstObjectId.size());

            lstMailProvinceDetail = assignmentRuleRepository.getLstMailProvinceSubDetail(lstObjectId, ruleActionNotification.getReceiverType());
        }

        // --------------- SEND NOTIFICATION ------------------
        // TELEGRAM: gửi 1 tn 1 lần
        if (ruleCondition.getLstNotificationType().contains(NotificationTypeEnum.TELEGRAM.toString())) {
            getAndSendTelegramAutomationRule(ruleCondition, lstPartitionDetail, lstTelegramProvinceDetail, lstContentConfig);
        }

        // EMAIL:
        if (ruleCondition.getLstNotificationType().contains(NotificationTypeEnum.EMAIL.toString())) {
            RuleNotificationContentDTO contentConfig = lstContentConfig.stream()
                .filter(config -> Objects.equals(config.getNotificationType(), NotificationTypeEnum.EMAIL.toString()))
                .findFirst().orElse(null);

            if (Objects.nonNull(contentConfig)) {
                // http://wiki.vnpt-technology.vn/pages/viewpage.action?pageId=165971645#Thi%E1%BA%BFtl%E1%BA%ADpquyt%E1%BA%AFcth%C3%B4ngb%C3%A1o%C4%91%C6%A1nh%C3%A0ngch%C6%B0atri%E1%BB%83nkhai-Qu%E1%BA%A3nl%C3%BDlogg%E1%BB%ADith%C3%B4ngb%C3%A1oc%E1%BB%A7ah%E1%BB%87th%E1%BB%91ng
                List<IAutoRuleProvinceSubDetailDTO> finalLstProvinceDetail = lstMailProvinceDetail;

                List<String> receiverTypeLst = ruleActionNotification.getReceiverType().stream()
                        .filter(e -> !Arrays.asList(ReceiverTypeEnum.ADMIN_PARTITION.name(), ReceiverTypeEnum.AM_PARTITION.name()).contains(e) ||
                                (e.equals(ReceiverTypeEnum.ADMIN_PARTITION.name()) && !ruleActionNotification.getReceiverType().contains(ReceiverTypeEnum.AM_PARTITION.name())) ||
                                (e.equals(ReceiverTypeEnum.AM_PARTITION.name()) && !ruleActionNotification.getReceiverType().contains(ReceiverTypeEnum.ADMIN_PARTITION.name())))
                        .collect(Collectors.toList());
                if (ruleActionNotification.getReceiverType().contains(ReceiverTypeEnum.AM_PARTITION.name()) &&
                        ruleActionNotification.getReceiverType().contains(ReceiverTypeEnum.ADMIN_PARTITION.name())) {
                    receiverTypeLst.add(ReceiverTypeEnum.ALL_PARTITION.name());
                }
                receiverTypeLst.forEach(receiverType -> {
                    Set<Long> lstReceiverId = new HashSet<>();
                    // Nếu (objectType TÀI KHOẢN or AFFILIATE) && TEMPLATE MAIL IN (PW-01, PW-02, PW-03)
                    // ==> Người nhận sẽ theo logic như sau:
                    // PW01: chỉ gửi cho đối tượng Tài khoản (không gửi cho NSPT, NS khác, NS phân vùng)
                    // PW02: giống PW01
                    // PW03: chỉ gửi cho quản trị viên của hệ thống (FULL_ADMIN) hoặc NSPT
                    ActionNotificationEnum notifCode = ActionNotificationEnum.fromCode(contentConfig.getTemplateCode());
                    if (Objects.nonNull(notifCode)) {
                        if (lstPasswordLeakNotifCode.contains(notifCode) &&
                            (Objects.equals(CrmObjectTypeEnum.fromValue(objectType), CrmObjectTypeEnum.USER) ||
                                Objects.equals(CrmObjectTypeEnum.fromValue(objectType), CrmObjectTypeEnum.AFFILIATE))) {

                            switch (notifCode) {
                                case PW_01:
                                case PW_02:
                                    lstReceiverId.addAll(lstObjectId);
                                    break;
                                case PW_03:
                                    if (Objects.equals(receiverType, ReceiverTypeEnum.ASSIGNEE.name())) { // nhân sự phụ trách
                                        lstReceiverId.addAll(new LinkedHashSet<>(
                                            assignmentRuleRepository.getListObjectAssigneeIdByCondition(ruleConditionSql, objectType)));
                                    }
                                    break;
                                default:
                                    lstReceiverId = new HashSet<>();
                            }

                        } else {
                            if (Objects.equals(receiverType, ReceiverTypeEnum.ASSIGNEE.name())) { // nhân sự phụ trách
                                lstReceiverId.addAll(new LinkedHashSet<>(
                                    assignmentRuleRepository.getListObjectAssigneeIdByCondition(ruleConditionSql, objectType)));
                            } else if (Objects.equals(receiverType, ReceiverTypeEnum.OTHER.name())) { // nhân sự Khac
                                lstReceiverId.addAll(Arrays.asList(ruleActionNotification.getLstReceiverId()));
                            } else if (Objects.equals(receiverType,
                                ReceiverTypeEnum.ALL_PARTITION.name())) { // Nhân sự phân vùng - Quản lý + Nhân viên
                                lstReceiverId.addAll(new LinkedHashSet<>(
                                    assignmentRuleRepository.getListAdminPartitionIdByCondition(ruleConditionSql, objectType)));
                                lstReceiverId.addAll(new LinkedHashSet<>(
                                    assignmentRuleRepository.getListAmPartitionIdByCondition(ruleConditionSql, objectType)));
                            } else if (Objects.equals(receiverType, ReceiverTypeEnum.ADMIN_PARTITION.name())) { //Nhân sự phân vùng - Quản lý
                                lstReceiverId.addAll(new LinkedHashSet<>(
                                    assignmentRuleRepository.getListAdminPartitionIdByCondition(ruleConditionSql, objectType)));
                            } else if (Objects.equals(receiverType, ReceiverTypeEnum.AM_PARTITION.name())) { // Nhân sự phân vùng - Nhân viên
                                lstReceiverId.addAll(new LinkedHashSet<>(
                                    assignmentRuleRepository.getListAmPartitionIdByCondition(ruleConditionSql, objectType)));
                            } else if (Objects.equals(receiverType, ReceiverTypeEnum.ACCOUNT.name())) {
                                // 15/05/2025: hiện tại receiverType chỉ == ACCOUNT khi đối tượng rule là Tài khoản OR Affilate OR Thuê bao
                                if (Objects.equals(objectType, CrmObjectTypeEnum.SUBSCRIPTION.getValue())) {
                                    lstReceiverId.addAll(subscriptionRepository.findAllUserIdByIdIn(lstObjectId));
                                } else if (Objects.equals(objectType, CrmObjectTypeEnum.USER.getValue()) ||
                                    Objects.equals(objectType, CrmObjectTypeEnum.AFFILIATE.getValue())) {
                                    lstReceiverId.addAll(lstObjectId);
                                }

                            }
                        }
                        lstReceiverId.removeIf(Objects::isNull);
                        List<ICommonIdNameEmail> lstReceiverDetail = new ArrayList<>();
                        switch (notifCode) {
                            case PW_02:
                            case PW_01:
                                // set recovery key cho các tk sắp bị khóa
                                List<User> lstUser = userRepository.findAllById(lstReceiverId);
                                for (User user : lstUser) {
                                    UUID recoveryKey = UUID.randomUUID();
                                    user.setRecoveryKey(recoveryKey);
                                }
                                userRepository.saveAll(lstUser);
                                lstReceiverDetail.addAll(userRepository.findLstNotifPW02UserDetail(lstReceiverId));
                                break;
                            case PW_03:
                                // PW03: lấy ngày tk bị lộ mk từ các tk mà người nhận đang phụ trách
                                lstReceiverDetail.addAll(userService.findLstNotifAssigneeUserDetail(lstReceiverId, lstObjectId));
                                lstReceiverDetail.addAll(userRepository.findLstNotifPW03FullAdminUserDetail(lstObjectId)); // luôn gửi cho guản trị viên
                                break;
                            case SB01:
                            case SB02:
                            case SCD_01:
                            case SCD_02:
                                lstReceiverDetail.addAll(getReceiverDetailForNotifSC(receiverType, lstReceiverId, lstObjectId));
                                break;
                            default:
                                lstReceiverDetail.addAll(userService.findLstNotifUserDetail(lstReceiverId));
                        }

                        getAndSendMailAutomationRule(ruleActionNotification, lstReceiverDetail, ruleCondition, receiverType,
                            lstPartitionDetail, finalLstProvinceDetail, contentConfig);
                    }

                });
            }
            log.info("==== END scanningSendingNotificationRule with ruleConditionId = {}, ruleConditionSql = {}, objectType = {}, num of objects = {}",
                ruleCondition.getId(), ruleConditionSql, objectType, lstObjectId.size());
        }
    }

    private List<ICommonIdNameEmail> getReceiverDetailForNotifSC(String receiverType, Set<Long> lstReceiverId, Set<Long> lstObjectId) {
        if (Objects.equals(receiverType, ReceiverTypeEnum.ASSIGNEE.name())) { // nhân sự phụ trách
             // trong TH Đối tượng là thuê bao: nsu phụ trách là assigneeId của sub
            return userRepository.findLstNotifSCAssigneeDetail(lstReceiverId, lstObjectId);
        } else if (Objects.equals(receiverType, ReceiverTypeEnum.OTHER.name())) { // nhân sự Khac
            return userRepository.findLstNotifSCOtherUserDetail(lstReceiverId, lstObjectId);
        } else if (Objects.equals(receiverType, ReceiverTypeEnum.ALL_PARTITION.name())) { // Nhân sự phân vùng - Quản lý + Nhân viên
            return userRepository.findLstNotifSCAdminAndAMPartitionDetail(lstReceiverId, lstObjectId);
        } else if (Objects.equals(receiverType, ReceiverTypeEnum.ADMIN_PARTITION.name())) { //Nhân sự phân vùng - Quản lý
            return userRepository.findLstNotifSCAdminPartitionDetail(lstReceiverId, lstObjectId);
        } else if (Objects.equals(receiverType, ReceiverTypeEnum.AM_PARTITION.name())) { // Nhân sự phân vùng - Nhân viên
            return userRepository.findLstNotifSCAMPartitionDetail(lstReceiverId, lstObjectId);
        } else if (Objects.equals(receiverType, ReceiverTypeEnum.ACCOUNT.name())) {
            // trong TH Đối tượng là thuê bao: ACCOUNT là user đăng kí sub
            return userRepository.findLstNotifSCAccountUserDetail(lstReceiverId, lstObjectId);
        }
        return new ArrayList<>();
    }

    private void getAndSendTelegramAutomationRule(RuleCondition ruleCondition, List<IAutoRulePartitionSubDetailDTO> lstPartitionDetail,
        List<IAutoRuleProvinceSubDetailDTO> lstProvinceDetail, List<RuleNotificationContentDTO> lstContentConfig) {
        log.info("====== START getAndSendTelegramAutomationRule =======");
        RuleNotificationContentDTO contentConfig = lstContentConfig.stream()
            .filter(config -> Objects.equals(config.getNotificationType(), NotificationTypeEnum.TELEGRAM.toString()))
            .findFirst().orElse(null);
        if (Objects.isNull(contentConfig)) {
            return;
        }
        ActionNotificationParamDTO paramDTO = new ActionNotificationParamDTO();
        CommonNotifParamDTO commonParamDTO = new CommonNotifParamDTO();
        if (Objects.equals(contentConfig.getContentType(), AutoRuleNotifyContentTypeEnum.TEMPLATE.getValue())) {
            // --- Nôi dung thông báo là loại Mẫu:
            Integer weekNumber = DateUtil.getCurrentWeekNumber(); // tham số tuần
            Integer currentMonthDate = DateUtil.getCurrentMonthDate(); // tham số ngày trong tháng
            Integer monthNumber = DateUtil.getCurrentMonthNumber(); // tham số tháng
            switch (contentConfig.getTemplateCode()) {
                case "PGD-11":
                    PGD11Template.getTemplateContent(new ArrayList<>(), new HashMap<>(), webHost, commonParamDTO, ActionNotificationEnum.PGD_11);
                    paramDTO = getTelegramPGD11ParamDTO(contentConfig, lstPartitionDetail, weekNumber, currentMonthDate);
                    break;
                case "GTB-11":
                    PGD11Template.getTemplateContent(new ArrayList<>(), new HashMap<>(), webHost, commonParamDTO, ActionNotificationEnum.GTB_11);
                    paramDTO = getTelegramGTB11ParamDTO(contentConfig, lstProvinceDetail, weekNumber, currentMonthDate, monthNumber);
                    break;
                // TODO: lấy nội dung các template khác
                default:
                    break;
            }
        } else {
            // Tùy chỉnh: ng dùng nhập nội dung email
            commonParamDTO.setTelegramContent(contentConfig.getContent());
            paramDTO = new PGD11Custom(Collections.singletonList(contentConfig), null).getParam();
        }
        if (Objects.nonNull(paramDTO)) {
            actionNotificationService.sendTelegramAutomationRule(paramDTO, ruleCondition.getLstNotificationType(), commonParamDTO);
        }
        log.info("====== END getAndSendTelegramAutomationRule =======");
    }

    private ActionNotificationParamDTO getTelegramGTB11ParamDTO(RuleNotificationContentDTO contentConfig,
        List<IAutoRuleProvinceSubDetailDTO> lstProvinceSubDetail, Integer weekNumber, Integer currentMonthDate, Integer monthNumber) {
        log.info("====== START getTelegramGTB11ParamDTO =======");
        if (Objects.equals(lstProvinceSubDetail.size(), 0)) {
            log.info("====== END getTelegramGTB11ParamDTO, lstProvinceSubDetail is empty =======");
            return null;
        }
        AutoRuleSubDetailDTO notifDetail = new AutoRuleSubDetailDTO();
        List<AutoRuleCountSubDTO> lstProvinceCountSub = new ArrayList<>();
        // tạo 1 mail cho receiver hiện tại (trong mail GTB11 có thông tin số lượng sub chưa hoàn thành cho nhiều tỉnh)
        // step 1: add các thông tin chung
        notifDetail.setTotalCountSub(lstProvinceSubDetail.stream().mapToInt(IAutoRuleProvinceSubDetailDTO::getNumSub).sum());
        notifDetail.setWeekNumber(weekNumber);
        notifDetail.setCurrentMonthDate(currentMonthDate);
        notifDetail.setMonthNumber(monthNumber);

        // step 2: map từng tỉnh
        for (IAutoRuleProvinceSubDetailDTO province : lstProvinceSubDetail) {
            AutoRuleCountSubDTO provinceCountSub = new AutoRuleCountSubDTO();
            provinceCountSub.setName(province.getProvinceCode());
            provinceCountSub.setCountSub(province.getNumSub()); // đếm số lượng sub của từng province
            lstProvinceCountSub.add(provinceCountSub);
        }

        // step 3: set lst số lượng sub của mỗi tỉnh
        notifDetail.setLstPartitionCountSub(lstProvinceCountSub);

        log.info("====== END getTelegramGTB11ParamDTO =======");
        return new GTB11(contentConfig, Collections.singletonList(notifDetail), webHost).getParam();
    }

    private ActionNotificationParamDTO getTelegramPGD11ParamDTO(RuleNotificationContentDTO contentConfig,
        List<IAutoRulePartitionSubDetailDTO> lstPartitionSubDetail, Integer weekNumber, Integer currentMonthDate) {
        log.info("====== START getTelegramPGD11ParamDTO =======");
        if (Objects.equals(lstPartitionSubDetail.size(), 0)) {
            log.info("====== END getTelegramPGD11ParamDTO, lstPartitionSubDetail is empty =======");
            return null;
        }
        // tạo 1 mail cho list phân vùng của receiver hiện tại
        AutoRuleSubDetailDTO notifDetail = new AutoRuleSubDetailDTO();
        List<AutoRuleCountSubDTO> lstCountSub = new ArrayList<>();
        notifDetail.setWeekNumber(weekNumber);
        notifDetail.setCurrentMonthDate(currentMonthDate);
        Set<String> lstPartitionAdded = new HashSet<>();

        Integer currentNumOfPartition = 0;
        for (IAutoRulePartitionSubDetailDTO iAutoRulePartitionSubDetailDTO : lstPartitionSubDetail) {
            if (!lstPartitionAdded.add(iAutoRulePartitionSubDetailDTO.getPartitionName())) {
                continue;
            }
            if (Objects.equals(currentNumOfPartition, MAX_PARTITION_SIZE)) {
                // tin nhắn telegram chỉ đc tối đa 100 phân vùng;
                log.info("getTelegramPGD11ParamDTO: Excluding some partitions because lst partition limit for telegram message is {}", MAX_PARTITION_SIZE);
                break;
            }
            AutoRuleCountSubDTO partitionCountSub = new AutoRuleCountSubDTO(iAutoRulePartitionSubDetailDTO);
            lstCountSub.add(partitionCountSub);
            currentNumOfPartition++;
        }

        notifDetail.setLstPartitionCountSub(lstCountSub);

        log.info("====== END getTelegramPGD11ParamDTO =======");
        return new PGD11Template(contentConfig, Collections.singletonList(notifDetail), webHost).getParam();
    }

    private void getAndSendMailAutomationRule(RuleActionNotification ruleActionNotification,
        List<ICommonIdNameEmail> lstReceiverDetail, RuleCondition ruleCondition, String receiverType,
        List<IAutoRulePartitionSubDetailDTO> lstPartitionDetail, List<IAutoRuleProvinceSubDetailDTO> lstProvinceDetail,
        RuleNotificationContentDTO contentConfig) {
        log.info("=== START getAndSendMailAutomationRule for ruleConditionId = {} ====", ruleCondition.getId());
        if (CollectionUtils.isEmpty(lstReceiverDetail)) {
            log.info("=== END getAndSendMailAutomationRule for ruleConditionId = {} because there are no valid receiver ====", ruleCondition.getId());
            return;
        }
        ActionNotificationParamDTO paramDTO = new ActionNotificationParamDTO();
        CommonNotifParamDTO commonParamDTO = new CommonNotifParamDTO();
        if (Objects.equals(contentConfig.getContentType(), AutoRuleNotifyContentTypeEnum.TEMPLATE.getValue())) {
            ActionNotificationEnum notificationTemplateCode = ActionNotificationEnum.fromCode(contentConfig.getTemplateCode());
            if (Objects.nonNull(notificationTemplateCode)) {
                // --- Nôi dung thông báo là loại Mẫu:
                switch (notificationTemplateCode) {
                    case PGD_11:
                        PGD11Template.getTemplateContent(new ArrayList<>(), new HashMap<>(), webHost, commonParamDTO, ActionNotificationEnum.PGD_11);
                        paramDTO = getMailPGD11ParamDTO(ruleActionNotification, contentConfig, lstReceiverDetail, lstPartitionDetail, receiverType);
                        break;
                    case GTB_11:
                        PGD11Template.getTemplateContent(new ArrayList<>(), new HashMap<>(), webHost, commonParamDTO, ActionNotificationEnum.GTB_11);
                        paramDTO = getMailGTB11ParamDTO(ruleActionNotification, contentConfig, lstReceiverDetail, lstProvinceDetail, receiverType);
                        break;
                    case PW_01:
                        RuleInactiveConfigDTO config = automationRuleService.getRuleInactiveConfig();
                        String duration =
                            Objects.nonNull(config.getRuleNoPwdChangeConfig()) ? config.getRuleNoPwdChangeConfig().getDurationTimeDisplay()
                                : "1 ngày";
                        paramDTO = new PW01(lstReceiverDetail, duration, webHost).getParam();
                        break;
                    case PW_02:
                        paramDTO = new PW02(lstReceiverDetail, webHost).getParam();
                        break;
                    case PW_03:
                        paramDTO = new PW03(lstReceiverDetail, null).getParam();
                        break;
                    case SB01:
                        paramDTO = new SB01(lstReceiverDetail, webHost).getParam();
                        break;
                    case SB02:
                        paramDTO = new SB02(lstReceiverDetail, webHost).getParam();
                        break;
                    case SCD_01:
                        List<NotifScDetailDTO> lstNotifSCD01DetailDTO = getLstNotifScDetailDTO(lstReceiverDetail);
                        paramDTO = new SCD01(lstNotifSCD01DetailDTO).getParam();
                        break;
                    case SCD_02:
                        List<NotifScDetailDTO> lstNotifSCD02DetailDTO = getLstNotifScDetailDTO(lstReceiverDetail);
                        paramDTO = new SCD02(lstNotifSCD02DetailDTO).getParam();
                        break;
                    // TODO: lấy nội dung các template khác
                    default:
                        break;
                }
            }
        } else {
            // Tùy chỉnh: ng dùng nhập nội dung email
            contentConfig.setContent(contentConfig.getContent().replaceAll("\n","<br>"));
            commonParamDTO.setTelegramContent(contentConfig.getContent());
            paramDTO = new PGD11Custom(Collections.singletonList(contentConfig), lstReceiverDetail).getParam();
        }
         actionNotificationService.sendEmailAutomationRule(paramDTO, ruleCondition.getLstNotificationType(), receiverType, lstPartitionDetail, commonParamDTO);
        log.info("=== END getAndSendMailAutomationRule for ruleConditionId = {} ====", ruleCondition.getId());
    }

    private static List<NotifScDetailDTO> getLstNotifScDetailDTO(List<ICommonIdNameEmail> lstReceiverDetail) {
        Map<Long, List<ICommonIdNameEmail>> assigneeToLstSub = new HashMap<>();
        assigneeToLstSub = lstReceiverDetail.stream().collect(Collectors.groupingBy(ICommonIdNameEmail::getAssigneeId));

        return assigneeToLstSub.entrySet().stream()
            .map(entry -> {
                Long assigneeId = entry.getKey();
                List<ICommonIdNameEmail> lstCommonDetail = entry.getValue();

                // Thông tin assignee chung
                ICommonIdNameEmail assigneeDetail = lstCommonDetail.get(0);

                NotifScDetailDTO notifScDetailDTO = new NotifScDetailDTO();
                notifScDetailDTO.setAssigneeId(assigneeId);
                BeanUtils.copyProperties(assigneeDetail, notifScDetailDTO);

                // get list sub của từng assignee
                List<NotifScSubDetailDTO> lstSub = new ArrayList<>();
                for (ICommonIdNameEmail subDetail : lstCommonDetail) {
                    NotifScSubDetailDTO sub = new NotifScSubDetailDTO();
                    BeanUtils.copyProperties(subDetail, sub);
                    lstSub.add(sub);
                }
                notifScDetailDTO.setLstSubDetail(lstSub);
                return notifScDetailDTO;
            })
            .collect(Collectors.toList());
    }


    // gửi email + telegram cho hành động Gửi thông báo khi có Bản ghi mới
    private void sendNotifNewRecordAutoRule(RuleActionNotification ruleActionNotification,
        List<ICommonIdNameEmail> lstReceiverDetail, Long objectId, RuleCondition ruleCondition, String finalReceiverType) {
        log.info("=== START sendNotifNewRecordAutoRule ====");

        // Loại nội dung thông báo
        List<RuleNotificationContentDTO> lstContentConfig = ruleActionNotification.getLstContentConfig();
        List<IAutoRulePartitionSubDetailDTO> lstPartitionDetail;
        List<IAutoRuleProvinceSubDetailDTO> lstTelegramProvinceDetail = new ArrayList<>();
        List<IAutoRuleProvinceSubDetailDTO> lstMailProvinceDetail = new ArrayList<>();

        lstPartitionDetail = assignmentRuleRepository.getLstPartitionSubDetail(Collections.singleton(objectId));

        // nếu có ít nhất 1 hình thức gửi loại nội dung là Mẫu + template là GTB11 --> lấy data nội dung template GTB 11 trước
        // (tránh get data đó trong for loop)
        List<RuleNotificationContentDTO> lstGTB11Config = lstContentConfig.stream()
            .filter((config -> Objects.equals(config.getContentType(), AutoRuleNotifyContentTypeEnum.TEMPLATE.getValue()) &&
                Objects.equals(config.getTemplateCode(), ActionNotificationEnum.GTB_11.getCode())))
            .collect(Collectors.toList());

        if (lstGTB11Config.stream().anyMatch(config -> Objects.equals(config.getNotificationType(), NotificationTypeEnum.TELEGRAM.toString()))) {
            // TH1: gửi template GTB-11 telegram
            log.info("sendNotifNewRecordAutoRule: Calling assignmentRuleRepository.getLstTelegramProvinceSubDetail with ruleConditionSql = {} and objectId = {}",
                ruleCondition.getId(), objectId);
            lstTelegramProvinceDetail = assignmentRuleRepository.getLstTelegramProvinceSubDetail(Collections.singleton(objectId));
        }
        if (lstGTB11Config.stream().anyMatch(config -> Objects.equals(config.getNotificationType(), NotificationTypeEnum.EMAIL.toString()))) {
            // TH2: gửi template GTB-11 mail
            log.info("sendNotifNewRecordAutoRule: Calling assignmentRuleRepository.getLstMailProvinceSubDetail with ruleConditionSql = {} and objectId = {}",
                ruleCondition.getId(), objectId);

            lstMailProvinceDetail = assignmentRuleRepository.getLstMailProvinceSubDetail(Collections.singleton(objectId), ruleActionNotification.getReceiverType());
        }

        // --------------- SEND NOTIFICATION ------------------
        // TELEGRAM: gửi 1 tn 1 lần
        if (ruleCondition.getLstNotificationType().contains(NotificationTypeEnum.TELEGRAM.toString())) {
            getAndSendTelegramAutomationRule(ruleCondition, lstPartitionDetail, lstTelegramProvinceDetail, lstContentConfig);
        }

        // EMAIL:
        if (ruleCondition.getLstNotificationType().contains(NotificationTypeEnum.EMAIL.toString())) {
            RuleNotificationContentDTO contentConfig = lstContentConfig.stream()
                .filter(config -> Objects.equals(config.getNotificationType(), NotificationTypeEnum.EMAIL.toString()))
                .findFirst().orElse(null);
            if (Objects.nonNull(contentConfig)) {
                getAndSendMailAutomationRule(ruleActionNotification, lstReceiverDetail, ruleCondition, finalReceiverType,
                    lstPartitionDetail, lstMailProvinceDetail, contentConfig);
            }
        }

        log.info("=== END sendNotifNewRecordAutoRule ====");
    }

    private ActionNotificationParamDTO getMailGTB11ParamDTO(RuleActionNotification ruleActionNotification, RuleNotificationContentDTO contentConfig,
        List<ICommonIdNameEmail> lstReceiverDetail, List<IAutoRuleProvinceSubDetailDTO> lstProvinceDetail, String receiverType) {
        log.info("START getGTB11ParamDTO");
        ActionNotificationParamDTO paramDTO;
        List<Long> lstOtherReceiverId = Arrays.asList(ruleActionNotification.getLstReceiverId());

        // với mỗi người dùng -> gửi 1 mail thông báo
        List<AutoRuleSubDetailDTO> lstMailDetail = new ArrayList<>();
        Integer weekNumber = DateUtil.getCurrentWeekNumber(); // tham số tuần
        Integer currentMonthDate = DateUtil.getCurrentMonthDate(); // tham số ngày trong tháng
        Integer monthNumber = DateUtil.getCurrentMonthNumber(); // tham số tháng
        for (ICommonIdNameEmail receiver : lstReceiverDetail) {
            // lấy lst data của receiver hiện tại
            List<IAutoRuleProvinceSubDetailDTO> lstProvinceSubDetail;
            //check trường họp người nhận:
            if (Objects.equals(receiverType,ReceiverTypeEnum.OTHER.name()) && lstOtherReceiverId.contains(receiver.getId())) {
                // nếu rule có người nhận là Nhân sự khác -> lấy tất cả các sub (receiver hiện tại nhận tất cả mail của tất cả phân vùng)
                lstProvinceSubDetail = lstProvinceDetail;
            } else {
                lstProvinceSubDetail = lstProvinceDetail.stream().filter(d -> Objects.equals(d.getReceiverId(), receiver.getId()))
                    .collect(Collectors.toList());
            }
            getGTB11MailDetail(receiver, lstProvinceSubDetail, weekNumber, currentMonthDate, monthNumber, lstMailDetail);
        }
        paramDTO = new GTB11(contentConfig, lstMailDetail, webHost).getParam();
        log.info("END getGTB11ParamDTO");
        return paramDTO;
    }

    private void getGTB11MailDetail(ICommonIdNameEmail receiver, List<IAutoRuleProvinceSubDetailDTO> lstProvinceSubDetail, Integer weekNumber,
        Integer currentMonthDate, Integer monthNumber, List<AutoRuleSubDetailDTO> lstMailDetail) {
        if (Objects.equals(lstProvinceSubDetail.size(), 0)) {
            return;
        }
        AutoRuleSubDetailDTO notifDetail = new AutoRuleSubDetailDTO();
        List<AutoRuleCountSubDTO> lstProvinceCountSub = new ArrayList<>();
        // tạo 1 mail cho receiver hiện tại (trong mail GTB11 có thông tin số lượng sub chưa hoàn thành cho nhiều tỉnh)
        // step 1: add các thông tin chung
        notifDetail.setEmail(receiver.getEmail());
        notifDetail.setTotalCountSub(lstProvinceSubDetail.stream().mapToInt(IAutoRuleProvinceSubDetailDTO::getNumSub).sum());
        notifDetail.setPortalName(receiver.getPortalName());
        notifDetail.setReceiverId(receiver.getId());
        notifDetail.setReceiverName(receiver.getName());
        notifDetail.setWeekNumber(weekNumber);
        notifDetail.setCurrentMonthDate(currentMonthDate);
        notifDetail.setMonthNumber(monthNumber);

        // step 2: map từng tỉnh
        for (IAutoRuleProvinceSubDetailDTO province : lstProvinceSubDetail) {
            AutoRuleCountSubDTO provinceCountSub = new AutoRuleCountSubDTO();
            provinceCountSub.setName(province.getProvinceCode());
            provinceCountSub.setCountSub(province.getNumSub()); // đếm số lượng sub của từng province
            lstProvinceCountSub.add(provinceCountSub);
        }

        // step 3: set lst số lượng sub của mỗi tỉnh
        notifDetail.setLstPartitionCountSub(lstProvinceCountSub);
        lstMailDetail.add(notifDetail);
    }

    private ActionNotificationParamDTO getMailPGD11ParamDTO(RuleActionNotification ruleActionNotification, RuleNotificationContentDTO contentConfig,
        List<ICommonIdNameEmail> lstReceiverDetail, List<IAutoRulePartitionSubDetailDTO> lstPartitionDetail, String receiverType) {
        log.info("START getPGD11ParamDTO");
        ActionNotificationParamDTO paramDTO;
        List<Long> lstOtherReceiverId = Arrays.asList(ruleActionNotification.getLstReceiverId());

        // với mỗi người dùng -> gửi 1 mail thông báo cho lst phân vùng
        List<AutoRuleSubDetailDTO> lstMailDetail = new ArrayList<>();
        Integer weekNumber = DateUtil.getCurrentWeekNumber();
        Integer currentMonthDate = DateUtil.getCurrentMonthDate();
        for (ICommonIdNameEmail receiver : lstReceiverDetail) {
            // lấy lst data của receiver hiện tại
            List<IAutoRulePartitionSubDetailDTO> lstPartitionSubDetail = new ArrayList<>();
            //check trường họp người nhận:
            if (Objects.equals(receiverType, ReceiverTypeEnum.OTHER.name()) && lstOtherReceiverId.contains(receiver.getId())) {
                // nếu rule có người nhận là Nhân sự khác -> lấy tất cả các sub (receiver hiện tại nhận tất cả mail của tất cả phân vùng)
                lstPartitionSubDetail = lstPartitionDetail.stream().filter(d -> Objects.isNull(d.getAssigneeId())).collect(Collectors.toList());
            } else if (Objects.equals(receiverType,ReceiverTypeEnum.ASSIGNEE.name())) {
                // nếu rule có người nhận là Nhân sự phụ trách -> lấy các sub mà receiver hiện tại phụ trách
                lstPartitionSubDetail = lstPartitionDetail.stream().filter(d -> Objects.equals(receiver.getId(), d.getAssigneeId()))
                    .collect(Collectors.toList());
            } else if (Objects.equals(receiverType,ReceiverTypeEnum.ADMIN_PARTITION.name())) {
                // nếu rule có người nhận là Quản lý phân vùng -> lấy tất cả các sub thuộc các phân vùng mà receiver hiện tại quản lý
                lstPartitionSubDetail = lstPartitionDetail.stream().filter(d -> d.getLstAdminId().contains(receiver.getId()))
                    .collect(Collectors.toList());
            } else if (Objects.equals(receiverType,ReceiverTypeEnum.AM_PARTITION.name())) {
                // nếu rule có người nhận là Nhân viên phân vùng -> lấy tất cả các sub thuộc các phân vùng mà receiver hiện tại làm nhân viên
                lstPartitionSubDetail = lstPartitionDetail.stream().filter(d -> d.getLstAmId().contains(receiver.getId()))
                    .collect(Collectors.toList());
            } else if (Objects.equals(receiverType,ReceiverTypeEnum.ALL_PARTITION.name())) {
                lstPartitionSubDetail = lstPartitionDetail.stream().filter(d -> (d.getLstAmId().contains(receiver.getId()) || d.getLstAdminId().contains(receiver.getId())))
                    .collect(Collectors.toList());
            }
            getPGD11MailDetail(receiver, lstPartitionSubDetail, weekNumber, currentMonthDate, lstMailDetail);
        }
        paramDTO = new PGD11Template(contentConfig, lstMailDetail, webHost).getParam();
        log.info("END getPGD11ParamDTO");
        return paramDTO;
    }

    private static void getPGD11MailDetail(ICommonIdNameEmail receiver, List<IAutoRulePartitionSubDetailDTO> lstPartitionSubDetail, Integer weekNumber,
        Integer currentMonthDate, List<AutoRuleSubDetailDTO> lstMailDetail) {
        log.info("==== START getPGD11MailDetail with receiver id = {}, email = {} ======", receiver.getId(), receiver.getEmail());
        if (Objects.equals(lstPartitionSubDetail.size(), 0)) {
            log.info("==== END getPGD11MailDetail, lstPartitionSubDetail is empty ======");
            return;
        }
        // tạo 1 mail cho list phân vùng của receiver hiện tại
        AutoRuleSubDetailDTO notifDetail = new AutoRuleSubDetailDTO();
        List<AutoRuleCountSubDTO> lstCountSub = lstPartitionSubDetail.stream().map(AutoRuleCountSubDTO::new).collect(Collectors.toList());
        notifDetail.setEmail(receiver.getEmail());
        notifDetail.setPortalName(receiver.getPortalName());
        notifDetail.setReceiverId(receiver.getId());
        notifDetail.setReceiverName(receiver.getName());
        notifDetail.setWeekNumber(weekNumber);
        notifDetail.setCurrentMonthDate(currentMonthDate);
        notifDetail.setLstPartitionCountSub(lstCountSub);
        lstMailDetail.add(notifDetail);
        log.info("==== END getPGD11MailDetail with receiver id = {}, email = {} ======", receiver.getId(), receiver.getEmail());
    }

    public void scanningSendingNotificationRule(Set<Long> lstAssigneeId, Long objectId, Set<Long> lstAdminPartition, Set<Long> lstAmPartition,
                                                RuleCondition ruleCondition, Integer objectType) {
        RuleActionNotification ruleActionNotification = ruleActionNotificationRepository.findByRuleConditionId(ruleCondition.getId())
                .orElse(null);
        if (Objects.isNull(ruleActionNotification) || Objects.isNull(ruleActionNotification.getLstReceiverId())) {
            return;
        }

        List<String> receiverTypeLst = ruleActionNotification.getReceiverType().stream()
                .filter(e -> !Arrays.asList(ReceiverTypeEnum.ADMIN_PARTITION.name(), ReceiverTypeEnum.AM_PARTITION.name()).contains(e) ||
                        (e.equals(ReceiverTypeEnum.ADMIN_PARTITION.name()) && !ruleActionNotification.getReceiverType().contains(ReceiverTypeEnum.AM_PARTITION.name())) ||
                        (e.equals(ReceiverTypeEnum.AM_PARTITION.name()) && !ruleActionNotification.getReceiverType().contains(ReceiverTypeEnum.ADMIN_PARTITION.name())))
                .collect(Collectors.toList());
        if (ruleActionNotification.getReceiverType().contains(ReceiverTypeEnum.AM_PARTITION.name()) &&
                ruleActionNotification.getReceiverType().contains(ReceiverTypeEnum.ADMIN_PARTITION.name())) {
            receiverTypeLst.add(ReceiverTypeEnum.ALL_PARTITION.name());
        }
        receiverTypeLst.forEach(receiverType -> {
            Set<Long> lstReceiverId = new HashSet<>();
            if (Objects.equals(receiverType, ReceiverTypeEnum.ASSIGNEE.name())) {
                lstReceiverId.addAll(lstAssigneeId);
            } else if (Objects.equals(receiverType, ReceiverTypeEnum.OTHER.name())) {
                lstReceiverId.addAll(Arrays.asList(ruleActionNotification.getLstReceiverId()));
            } else if (receiverType.equals(ReceiverTypeEnum.ALL_PARTITION.name())) { // Nhân sự phân vùng - Quản lý + Nhân viên
                lstReceiverId.addAll(lstAmPartition);
                lstReceiverId.addAll(lstAdminPartition);
            } else if (Objects.equals(receiverType, ReceiverTypeEnum.ADMIN_PARTITION.name())) { // Nhân sự phân vùng - Nhân viên
                lstReceiverId.addAll(lstAmPartition);
            } else if (Objects.equals(receiverType, ReceiverTypeEnum.AM_PARTITION.name())) { // Nhân sự phân vùng - Nhân viên
                lstReceiverId.addAll(lstAdminPartition);
            }

            lstReceiverId.removeIf(Objects::isNull);
            List<ICommonIdNameEmail> lstReceiverDetail = userService.findLstNotifUserDetail(lstReceiverId);
            sendNotifNewRecordAutoRule(ruleActionNotification, lstReceiverDetail, objectId, ruleCondition, receiverType);
        });
    }

    @Transactional
    public void scanningUpdatingRule(CrmObjectTypeEnum objectTypeEnum, Set<Long> lstObjectId, RuleCondition ruleCondition, String ruleName) {
        if (Objects.isNull(ruleCondition.getUpdateSql()) || ruleCondition.getUpdateSql().contains("false")) {
            return;
        }
        // Lấy thông tin batchInsertArgs, batchUpdateArgs cho các đối tượng khác users, enterprise
        String updateSql = ruleCondition.getUpdateSql();
        List<Object[]> batchUpdateArgs = new ArrayList<>();
        if (!Objects.equals(CrmObjectTypeEnum.USER, objectTypeEnum) && !Objects.equals(CrmObjectTypeEnum.ENTERPRISE, objectTypeEnum)) {
            batchUpdateArgs.add(new Object[]{lstObjectId.toArray(new Long[0])});
        }
        switch (objectTypeEnum) {
            case USER:
                fillDataDetailToTable(assignmentRuleRepository.maxUserId(),
                    String.format("UPDATE vnpt_dev.users SET %s where id = any(?)", updateSql),
                    "VACUUM ANALYZE vnpt_dev.users (assignee_id)", null, lstObjectId);
                break;
            case CUSTOMER_TICKET:
                jdbcTemplate.batchUpdate(String.format(SQLFieldForceConstant.UPDATE_RULE_DATA_FIELD_FOR_TICKET, updateSql), batchUpdateArgs);
                break;
            case ENTERPRISE:
                fillDataDetailToTable(assignmentRuleRepository.maxEnterpriseId(),
                    String.format("UPDATE vnpt_dev.enterprise SET %s where id = any(?)", updateSql),
                    "VACUUM ANALYZE vnpt_dev.enterprise (assignee_id)", null, lstObjectId);
                break;
            case CUSTOMER_CONTACT:
                jdbcTemplate.batchUpdate(String.format(SQLFieldForceConstant.UPDATE_RULE_DATA_FIELD_FOR_CONTACT, updateSql), batchUpdateArgs);
                break;
            case SUBSCRIPTION:
                jdbcTemplate.batchUpdate(String.format(SQLFieldForceConstant.UPDATE_RULE_DATA_FIELD_FOR_SUBSCRIPTIONS, updateSql),
                    batchUpdateArgs);
                break;
            case AFFILIATE:
                jdbcTemplate.batchUpdate(String.format(SQLFieldForceConstant.UPDATE_RULE_DATA_FIELD_FOR_AFFILIATE, updateSql), batchUpdateArgs);
                break;
        }

        // Lưu lịch sử quét rule thành công
        String content = "Quét quy tắc %s thành công với đối tượng %s";
        actionHistoryRepository.save(
            new ActionHistory(ActionHistoryTypeEnum.AUTOMATION_RULE_SCAN_SUCCESS, ActionHistoryObjectTypeEnum.AUTOMATION_RULE,
                ruleCondition.getId(), -1L, String.format(content, ruleCondition.getName(), objectTypeEnum.name())));

        // NOTIFICATION: gửi thư hành động cập nhật bản ghi
        if (!lstObjectId.isEmpty()) {
            try {
                long startTime = System.currentTimeMillis();

                // lấy chi tiết đối tượng
                List<IObjectDetailDTO> lstAllObjectDetail = getLstObjectDetail(lstObjectId, objectTypeEnum);

                if (!lstAllObjectDetail.isEmpty() && Objects.nonNull(lstAllObjectDetail.get(0).getAssigneeId())) {
                    // map lst object id với assignee id
                    // 14/03/2023: lấy assignee đầu tiên
                    // vì 1 rule condition hành động UPDATE chỉ có 1 đk "thay đổi nsu phụ trách"
                    // --> các bản ghi bị gán lại sẽ chung 1 NSPT
                    Long assigneeId = lstAllObjectDetail.get(0).getAssigneeId();
                    Map<Long, Set<Long>> mapObjectIdByAssigneeId = new HashMap<>();
                    mapObjectIdByAssigneeId.put(assigneeId, lstObjectId);

                    List<ICommonIdNameEmail> lstReceiverDetail = userService.findLstNotifUserDetail(mapObjectIdByAssigneeId.keySet());
                    Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> mapAssigneeToObjectContent =
                        fillRuleDataToMailTemplateMap(lstReceiverDetail, mapObjectIdByAssigneeId, lstAllObjectDetail, objectTypeEnum, null);

                    ActionNotificationParamDTO paramDTO = new PGD10(mapAssigneeToObjectContent, objectTypeEnum, ruleCondition.getName(), ruleName,
                        ruleCondition.getRuleId(), webHost)
                        .getParam();
                    if (Objects.nonNull(paramDTO)) {
                        log.info("scanningUpdatingRule: calling sendNotificationWithRuleCondition for ruleConditionId = {}", ruleCondition.getId());
                        actionNotificationService.sendNotificationWithRuleCondition(paramDTO, ruleCondition.getId(),
                            ruleCondition.getLstNotificationType(), null);
                        log.info("scanningUpdatingRule: send notification end after {} ms", System.currentTimeMillis() - startTime);
                    }
                    mapAssigneeToObjectContent.clear();
                }
            } catch (Exception e) {
                log.info("scanningUpdatingRule: ERROR while sending notification for ruleConditionId id = {}, exception = {}",
                    ruleCondition.getId(), e.getMessage());
            }
        }
    }

    public void scanningAssigneeNonInteractive() {
        log.info("======= START scanningAssigneeNonInteractive =======");
        long startTime = System.currentTimeMillis();
        RuleInactiveConfigDTO ruleInactiveConfig = automationRuleService.getRuleInactiveConfig();

        List<HistoryInactiveWarning> lstHistoryInactiveWarning = new ArrayList<>();

        String unitOpen = AutomationRuleConstant.DEFAULT_HOUR_UNIT;
        String unitUpdate = AutomationRuleConstant.DEFAULT_HOUR_UNIT;

        // test only
//        List<IGetObjectInteractiveInfoDTO> lstObjectReassignInteractive = automationRuleRepository.findAllObjectReassignInteractiveByConditionId(
//            ruleInactiveConfig.getObjectInactiveConfig().getIntervalTimeOpen(),
//            ruleInactiveConfig.getObjectInactiveConfig().getIntervalTimeUpdate(), unitOpen, unitUpdate, 77L);

        // test only
//        List<IGetObjectInteractiveInfoDTO> lstDataWarningInteractive = automationRuleRepository.findAllObjectWarningInteractiveByConditionId(
//            ruleInactiveConfig.getObjectInactiveConfig().getIntervalTimeOpen(),
//            ruleInactiveConfig.getObjectInactiveConfig().getIntervalTimeUpdate(), unitOpen, unitUpdate, 862L);

        // Lấy danh sách nhân sự phụ trách ko tương tác + đối tượng cần warning/reassign
        List<IGetObjectInteractiveInfoDTO> lstObjectReassignInteractive = automationRuleRepository.findAllObjectReassignInteractive(
            ruleInactiveConfig.getObjectInactiveConfig().getIntervalTimeOpen(),
            ruleInactiveConfig.getObjectInactiveConfig().getIntervalTimeUpdate(), unitOpen, unitUpdate);

        List<IGetObjectInteractiveInfoDTO> lstDataWarningInteractive = automationRuleRepository.findAllObjectWarningInteractive(
            ruleInactiveConfig.getObjectInactiveConfig().getIntervalTimeOpen(),
            ruleInactiveConfig.getObjectInactiveConfig().getIntervalTimeUpdate(), unitOpen, unitUpdate);

        List<ObjectInteractiveInfoDTO> lstObjectWarningInteractive = lstDataWarningInteractive.stream().map(ObjectInteractiveInfoDTO::new)
            .collect(Collectors.toList());

        // set inactiveTime
        lstObjectWarningInteractive.forEach(object -> {
           if (object.getIsInactiveUpdate()) {
               String timeUnit = Objects.equals("hour", ruleInactiveConfig.getObjectInactiveConfig().getTimeUnitUpdate().toLowerCase()) ? "giờ" : "ngày";
               object.setInactiveTime(String.format("%s %s", ruleInactiveConfig.getObjectInactiveConfig().getTimeUpdate(), timeUnit));
           } else {
               String timeUnit = Objects.equals("hour", ruleInactiveConfig.getObjectInactiveConfig().getTimeUnitOpen().toLowerCase()) ? "giờ" : "ngày";
               object.setInactiveTime(String.format("%s %s", ruleInactiveConfig.getObjectInactiveConfig().getTimeOpen(), timeUnit));
           }
        });

        // Thực hiện tự động gán các bản ghi không được tương tác cho các nhân sự phụ trách còn lại
        List<IGetObjectInteractiveInfoDTO> lstObjectForRemainAssignee = lstObjectReassignInteractive.stream().filter(
            item -> Objects.nonNull(item.getReassignPolicy())).collect(Collectors.toList());

        // với mỗi rule condition -> gửi cảnh báo + thư gán lại bản ghi
        Map<Long, List<ObjectInteractiveInfoDTO>> mapConditionIdToLstWarningObject =
            lstObjectWarningInteractive.stream().collect(Collectors.groupingBy(ObjectInteractiveInfoDTO::getRuleConditionId));
        Map<Long, List<IGetObjectInteractiveInfoDTO>> mapConditionIdToLstReassignObject =
            lstObjectForRemainAssignee.stream().collect(Collectors.groupingBy(IGetObjectInteractiveInfoDTO::getRuleConditionId));

        for (Map.Entry<Long, List<ObjectInteractiveInfoDTO>> entry : mapConditionIdToLstWarningObject.entrySet()) {
            if (!entry.getValue().isEmpty()) {
                log.info("scanningAssigneeNonInteractive: calling autoWarnInactiveToAssignee for ruleConditionId = {}", entry.getKey());
                // gửi thư cảnh báo cho nsu phụ trách / nsu khác
                autoWarnInactiveToAssignee(lstHistoryInactiveWarning, entry.getKey(), entry.getValue());
            }
        }

        for (Map.Entry<Long, List<IGetObjectInteractiveInfoDTO>> entry : mapConditionIdToLstReassignObject.entrySet() ) {
            if (!entry.getValue().isEmpty()) {
                log.info("scanningAssigneeNonInteractive: calling autoAssignObjectForAssignee for ruleConditionId = {}", entry.getKey());
                // gán nsu + gửi mail tự động gán bản ghi không tương tác
                autoAssignObjectForAssignee(entry.getValue());
            }
        }

        log.info("==== END scanningAssigneeNonInteractive: end after {} ms ====", System.currentTimeMillis() - startTime);
    }

    /**
     * Khi gửi mail cho nhân sự phụ trách:
     * method tạo+add mail param và các object HistoryInactiveWarning vào list tương ứng
     * @param objectTypeEnum            : đối tượng (KH, liên hệ, phiếu hỗ trợ, vv.)
     * @param lstAllObjectDTO           : tất cả các bản ghi không tương tác
     * @param lstParamDTO               : lst param mail cần gửi
     * @param lstHistoryInactiveWarning : lst bản ghi history inactive warning
     */
    private void addMailParamAndInactiveHistoryWarningForAssignee(CrmObjectTypeEnum objectTypeEnum,
        List<ObjectInteractiveInfoDTO> lstAllObjectDTO, List<ActionNotificationParamDTO> lstParamDTO,
        List<HistoryInactiveWarning> lstHistoryInactiveWarning) {
        List<ObjectInteractiveInfoDTO> lstObjectDTO = lstAllObjectDTO.stream()
            .filter(object -> Objects.equals(CrmObjectTypeEnum.valueOf(object.getObjectType()), objectTypeEnum))
            .collect(Collectors.toList());
        if (!lstObjectDTO.isEmpty()) {
            // get content mail cho đối tượng
            Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> mapAssigneeToObjectContent = getMapAssigneeToObjectContent(
                lstObjectDTO, objectTypeEnum);
            ActionNotificationParamDTO paramDTO = new PGD06(mapAssigneeToObjectContent, objectTypeEnum).getParam();
            if (Objects.nonNull(paramDTO)) {
                lstParamDTO.add(paramDTO);
                addHistoryInactiveWarning(lstObjectDTO, objectTypeEnum, lstHistoryInactiveWarning);
            }
        }
    }

    /**
     * Khi gửi mail cho nhân sự khác:
     * method tạo+add mail param và các object HistoryInactiveWarning vào list tương ứng
     * @param objectTypeEnum           : đối tượng (KH, liên hệ, phiếu hỗ trợ, vv.)
     * @param lstALLObjectDTO          : tất cả các bản ghi không tương tác
     * @param curOtherAssignee         : assignee hiện tại
     * @param lstParamDTO              : lst param mail cần gửi
     * @param lstHistoryInactiveWarning: lst bản ghi history inactive warning
     */
    private void addMailParamAndInactiveWarningHistoryForOtherAssignee(CrmObjectTypeEnum objectTypeEnum,
        List<ObjectInteractiveInfoDTO> lstALLObjectDTO, ICommonIdNameEmail curOtherAssignee, List<ActionNotificationParamDTO> lstParamDTO,
        List<HistoryInactiveWarning> lstHistoryInactiveWarning) {

        List<ObjectInteractiveInfoDTO> lstObjectDTO = lstALLObjectDTO.stream()
            .filter(object -> Objects.equals(CrmObjectTypeEnum.valueOf(object.getObjectType()), objectTypeEnum))
            .collect(Collectors.toList());
        if (!lstObjectDTO.isEmpty()) {
            // get content mail cho đối tượng
            Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> mapAssigneeToObjectContent = getMapAssigneeToObjectContent(
                lstObjectDTO, objectTypeEnum);

            // map
            // key: detail other assignee (email, name, ...)
            // value: map assignee and their assigned objects
            Map<ICommonIdNameEmail, Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO>> mapOtherAssigneeToAssigneeAndObject = new HashMap<>();
            mapOtherAssigneeToAssigneeAndObject.put(curOtherAssignee, mapAssigneeToObjectContent);
            ActionNotificationParamDTO paramDTO = new PGD07(mapOtherAssigneeToAssigneeAndObject, objectTypeEnum).getParam();
            if (Objects.nonNull(paramDTO)) {
                lstParamDTO.add(paramDTO);
                addHistoryInactiveWarning(lstObjectDTO, objectTypeEnum, lstHistoryInactiveWarning);
            }
        }
    }

    private void addHistoryInactiveWarning(List<ObjectInteractiveInfoDTO> lstObjectDTO, CrmObjectTypeEnum objectTypeEnum,
        List<HistoryInactiveWarning> lstHistoryInactiveWarning) {

        // với từng assignee --> record lịch sử cho từng object id
        lstObjectDTO.forEach(objectDTO ->
            {
                // copy thông tin chung của từng object (assignee_id, rule_condition_id, object_type, created_at)
                HistoryInactiveWarning commonHistoryInfo = new HistoryInactiveWarning();
                BeanUtils.copyProperties(objectDTO, commonHistoryInfo);
                commonHistoryInfo.setObjectType(objectTypeEnum);

                // record lịch sử cho từng object id
                objectDTO.getLstObjectId().forEach(objectId ->
                    {
                        HistoryInactiveWarning history = new HistoryInactiveWarning();
                        // copy thông tin chung
                        BeanUtils.copyProperties(commonHistoryInfo, history);
                        history.setObjectId(objectId);
                        lstHistoryInactiveWarning.add(history);
                    }
                );
            }
        );
    }

    private void autoAssignObjectForAssignee(List<IGetObjectInteractiveInfoDTO> lstObjectForOtherAssignee) {
        Map<Long, List<IGetObjectInteractiveInfoDTO>> mapObjectInteractiveByRuleId = lstObjectForOtherAssignee.stream()
            .collect(Collectors.groupingBy(IGetObjectInteractiveInfoDTO::getRuleConditionId));
        log.info("===== START autoAssignObjectForAssignee ======");
        // lấy ruleCondition, map id với entity của rule đó
        Set<Long> lstRuleConditionId = new HashSet<>();
        lstObjectForOtherAssignee.forEach(object -> lstRuleConditionId.add(object.getRuleConditionId()));
        List<RuleCondition> lstRuleCondition = ruleConditionRepository.findAllById(lstRuleConditionId);
        Map<Long, RuleCondition> mapConditionIdToCondition = new HashMap<>();
        lstRuleCondition.forEach(object -> mapConditionIdToCondition.put(object.getId(), object));

        Map<Long, String> mapObjectTypeByRuleId = new HashMap<>();
        for (IGetObjectInteractiveInfoDTO iGetObjectInteractiveAssignee : lstObjectForOtherAssignee) {
            if (!mapObjectTypeByRuleId.containsKey(iGetObjectInteractiveAssignee.getRuleConditionId())) {
                mapObjectTypeByRuleId.put(iGetObjectInteractiveAssignee.getRuleConditionId(), iGetObjectInteractiveAssignee.getObjectType());
            }
        }

        java.sql.Timestamp timestampSql = new java.sql.Timestamp(Calendar.getInstance().getTimeInMillis());
        for (Map.Entry<Long, List<IGetObjectInteractiveInfoDTO>> ruleEntry : mapObjectInteractiveByRuleId.entrySet()) {
            Long currentRuleConditionId = ruleEntry.getKey();
            CrmObjectTypeEnum objectTypeEnum = CrmObjectTypeEnum.fromValueStr(mapObjectTypeByRuleId.get(currentRuleConditionId));
            Long otherAssigneeId = ruleEntry.getValue().get(0).getReassignPolicy().getIsConfigForOtherAssignee() ?
                ruleEntry.getValue().get(0).getReassignPolicy().getReceiverId() : null;
            if (Objects.isNull(objectTypeEnum) ||
                (Objects.nonNull(otherAssigneeId) && ruleEntry.getValue().stream().anyMatch(item -> Objects.equals(
                    item.getAssigneeId(), otherAssigneeId)))) {
                continue;
            }
            // Map danh sách Id đối tượng với id nhân sự phụ trách
            List<Object[]> batchDeleteArgs = new ArrayList<>();
            List<Object[]> batchInsertArgs = new ArrayList<>();
            List<Object[]> batchUpdateArgs = new ArrayList<>();

            // THop 1: các bản ghi không được tương tác sẽ đc gán cho các assignee khác
            // tạo map id assignee mới với lst id bản ghi sẽ được chuyển giao
            Map<Long, Set<Long>> mapObjectIdByNewAssigneeId = new HashMap<>();

            // THop 2: các nhân sự không tương tác sẽ nhân thư Loại bỏ các bản ghi không được tương tác cho nsu khác
            // tạo map id assignee cũ với lst id bản ghi sẽ được chuyển giao
            Map<Long, Set<Long>> mapObjectIdByOldAssigneeId = new HashMap<>();

            Set<Long> lstAllObjectId = new HashSet<>();
            for (IGetObjectInteractiveInfoDTO objectInteractiveInfo : ruleEntry.getValue()) {
                putDataToMap(currentRuleConditionId, objectInteractiveInfo.getAssigneeId(),
                    ObjectUtil.getOrDefault(objectInteractiveInfo.getAssignmentMethod(), AssignmentMethodEnum.EQUALLY_DIVIDED.name()),
                    otherAssigneeId, objectTypeEnum, objectInteractiveInfo.getLstObjectId(), mapObjectIdByNewAssigneeId);
                mapObjectIdByOldAssigneeId.put(objectInteractiveInfo.getAssigneeId(), objectInteractiveInfo.getLstObjectId());
                for (Long objectId : objectInteractiveInfo.getLstObjectId()) {
                    batchDeleteArgs.add(new Object[]{currentRuleConditionId, objectInteractiveInfo.getAssigneeId(), objectId});
                }
                lstAllObjectId.addAll(objectInteractiveInfo.getLstObjectId());
            }
            // Lấy thông tin batchInsertArgs, batchUpdateArgs cho các đối tượng khác users, enterprise
            putDataReAssignToBatchArgs(currentRuleConditionId, objectTypeEnum, timestampSql, batchUpdateArgs, batchInsertArgs,
                mapObjectIdByNewAssigneeId);

            // Xóa thông tin thống kê phân giao
            log.info("===== autoAssignObjectForAssignee: delete history assignment - DELETE_CRM_ASSIGNMENT_RULE_STATISTIC ======");
            jdbcTemplate.batchUpdate(SQLFieldForceConstant.DELETE_CRM_ASSIGNMENT_RULE_STATISTIC, batchDeleteArgs);
            // Cập nhật phân giao cho nhân viên khác
            switch (objectTypeEnum) {
                case USER:
                    fillDataToTable(assignmentRuleRepository.maxUserId(),
                        "UPDATE vnpt_dev.users SET assignee_id = ? WHERE id = any(?)",
                        "VACUUM ANALYZE vnpt_dev.users (assignee_id)", mapObjectIdByNewAssigneeId);
                    break;
                case CUSTOMER_TICKET:
                    jdbcTemplate.batchUpdate(SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_TICKET, batchUpdateArgs);
                    break;
                case ENTERPRISE:
                    fillDataToTable(assignmentRuleRepository.maxEnterpriseId(),
                        "UPDATE vnpt_dev.enterprise SET assignee_id = ? WHERE id = any(?)",
                        "VACUUM ANALYZE vnpt_dev.enterprise (assignee_id)", mapObjectIdByNewAssigneeId);
                    break;
                case CUSTOMER_CONTACT:
                    jdbcTemplate.batchUpdate(SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_CONTACT, batchUpdateArgs);
                    break;
                case SUBSCRIPTION:
                    jdbcTemplate.batchUpdate(SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_SUBSCRIPTIONS, batchUpdateArgs);
                case AFFILIATE:
                    jdbcTemplate.batchUpdate(SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_AFFILIATE, batchUpdateArgs);
                    break;
            }
            log.info("===== autoAssignObjectForAssignee: insert new history assignment - INSERT_CRM_ASSIGNMENT_RULE_STATISTIC_REASSIGN ======");
            jdbcTemplate.batchUpdate(SQLFieldForceConstant.INSERT_CRM_ASSIGNMENT_RULE_STATISTIC_REASSIGN, batchInsertArgs);

            // gửi thư
            RuleCondition ruleCondition = mapConditionIdToCondition.get(currentRuleConditionId);
            try {
                log.info("========= autoWarnInactiveToAssignee: START sending reassign mail for ruleConditionId = {}", currentRuleConditionId);
                long startTime = System.currentTimeMillis();
                List<ActionNotificationParamDTO> lstParamDTO = new ArrayList<>();

                // ICommonIdNameEmail: chứa thông tin assignee (tên, email)
                // AssignmentRuleMailDetailDTO: chứa nội dung còn lại của email (table, số lượng bản ghi, ...)
                // map cho mail phân giao bản ghi mới PGC05
                Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> mapNewAssigneeToObjectContent = new HashMap<>();

                // map cho mail Loại bỏ bản ghi không tương tác PGC09
                Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> mapOldAssigneeToObjectContent = new HashMap<>();

                // lấy thông tin của assignee cũ / mới của các bản ghi
                List<ICommonIdNameEmail> lstNewAssigneeDetail = userService.findLstNotifUserDetail(mapObjectIdByNewAssigneeId.keySet());
                List<ICommonIdNameEmail> lstOldAssigneeDetail = userService.findLstNotifUserDetail(mapObjectIdByOldAssigneeId.keySet());

                // lấy thông tin tất cả bản ghi
                List<IObjectDetailDTO> lstAllObjectDetail = getLstObjectDetail(lstAllObjectId, objectTypeEnum);

                mapNewAssigneeToObjectContent = fillRuleDataToMailTemplateMap(lstNewAssigneeDetail, mapObjectIdByNewAssigneeId,
                    lstAllObjectDetail, objectTypeEnum, null);
                mapOldAssigneeToObjectContent = fillRuleDataToMailTemplateMap(lstOldAssigneeDetail, mapObjectIdByOldAssigneeId,
                    lstAllObjectDetail, objectTypeEnum, null);

                // gửi thư phân giao mới cho nsu còn lại/nsu khác
                if (!mapNewAssigneeToObjectContent.isEmpty()) {
                    lstParamDTO.add(new PGD05(mapNewAssigneeToObjectContent, objectTypeEnum).getParam());
                }

                // gửi thư loại bỏ nhân viên khỏi các bản ghi ko tương tác
                if (!mapOldAssigneeToObjectContent.isEmpty()) {
                    lstParamDTO.add(
                        new PGD09(mapOldAssigneeToObjectContent, objectTypeEnum).getParam());
                }

                if (!lstParamDTO.isEmpty()) {
                    log.info("autoAssignObjectForAssignee: calling sendNotificationWithRuleCondition for ruleConditionId = {}", ruleCondition.getId());
                    lstParamDTO.forEach(paramDTO ->
                        actionNotificationService.sendNotificationWithRuleCondition(paramDTO, ruleCondition.getId(),
                            ruleCondition.getLstNotificationType(), null));
                }

                log.info("autoAssignObjectForAssignee: END sending reassign mail for ruleConditionId = {},  end after {} ms",
                    currentRuleConditionId,
                    System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.info("autoAssignObjectForAssignee: ERROR while sending notification for ruleConditionId id = {}, exception = {}",
                    currentRuleConditionId, e.getMessage());
            }

        }

        log.info("===== END autoAssignObjectForAssignee ======");
    }

    private Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> getMapAssigneeToObjectContent(List<ObjectInteractiveInfoDTO> lstObject,
        CrmObjectTypeEnum objectTypeEnum) {

        Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> mapAssigneeToObjectContent = new HashMap<>();

        // Key: assignee ID
        // value: lst object id không tương tác
        Map<Long, Set<Long>> mapObjectIdByAssigneeId = new HashMap<>();
        lstObject.forEach(object ->
            mapObjectIdByAssigneeId.put(object.getAssigneeId(), object.getLstObjectId())
        );

        // cho các set object id vào chung 1 set
        Set<Long> lstALLObjectId = new HashSet<>();
        lstObject.forEach(object -> {
            if (Objects.nonNull(object.getLstObjectId())) {
                lstALLObjectId.addAll(object.getLstObjectId());
            }
        });

        if (!lstALLObjectId.isEmpty()) {
            // lấy thông tin ngày không tương tác cho mail cảnh báo
            String inactiveTime = lstObject.get(0).getInactiveTime();
            // ICommonIdNameEmail: chứa thông tin assignee (tên, email)
            // AssignmentRuleMailDetailDTO: chứa nội dung còn lại của email (table, số lượng bản ghi, ...)
            mapAssigneeToObjectContent =
                getMapAssigneeToObjectContent(lstALLObjectId, objectTypeEnum, mapObjectIdByAssigneeId, inactiveTime);

        }

        return mapAssigneeToObjectContent;

    }

    /**
     * Xử lý hành động phân giao + gửi thông báo
     *
     * @param ruleCondition Thông tin rule condition
     */
    @Transactional
    public void scanningAssigningRule(CrmObjectTypeEnum objectType, AssignmentMethodEnum currentAssignmentMethod, Integer numApplyingObject,
        Set<Long> lstObjectId, RuleCondition ruleCondition) {
        // Gán assigneeId theo rule
        try {
            // Lấy thông tin nhân sự theo dõi
            Long[] lstAssigneesId = ruleActionAssignmentRepository.getLstAssigneesIdActiveByRuleConditionId(ruleCondition.getId())
                .toArray(new Long[0]);
            // Map danh sách Id đối tượng với id nhân sự phụ trách
            Map<Long, Set<Long>> mapObjectIdByAssigneeId = new HashMap<>();
            // lưu 1 bản của lstObjectId để lấy thông tin cho mail ở bên dưới
            Set<Long> newLstObjectId = new LinkedHashSet<>(lstObjectId);
            // chú ý: đang dùng mapObjectIdByAssigneeId chứa id của assignee + object để lấy thông tin cho notification
            putDataToMap(ruleCondition.getId(), -1L, currentAssignmentMethod.name(), null,
                objectType, lstObjectId, mapObjectIdByAssigneeId);
            scanningAssigningRuleCondition(objectType, numApplyingObject, newLstObjectId, lstAssigneesId, ruleCondition,
                mapObjectIdByAssigneeId);
            mapObjectIdByAssigneeId = null;
        } catch (Exception e) {
            log.error("scanningRuleByObjectType: failed to scanning rule (id {}, code {}), condition {} with exception {}",
                ruleCondition.getId(), ruleCondition.getName(), ruleCondition.getConditionSql(), e.getMessage());
            // Lưu lịch sử quét rule thất bại
            String content = "Quét quy tắc %s thất bại với đối tượng %s";
            actionHistoryRepository.save(
                new ActionHistory(ActionHistoryTypeEnum.AUTOMATION_RULE_SCAN_FAILURE, ActionHistoryObjectTypeEnum.AUTOMATION_RULE,
                    ruleCondition.getId(), -1L, String.format(content, ruleCondition.getName(), objectType.name())));
        }
    }

    private void removeInvalidAssigneeId(Integer objectType) {
        CrmObjectTypeEnum objectTypeEnum = CrmObjectTypeEnum.fromValue(objectType);
        switch (objectTypeEnum) {
            case USER:
                handleQueryInBatch(assignmentRuleRepository.maxUserId(),
                    SQLFieldForceConstant.JDBC_REMOVE_INVALID_ASSIGNEE_FROM_USER,
                    null);
                break;
            case CUSTOMER_TICKET:
                handleQueryInBatch(assignmentRuleRepository.maxTicketId(),
                    SQLFieldForceConstant.JDBC_REMOVE_INVALID_ASSIGNEE_FROM_TICKET,
                    null);
                break;
            case ENTERPRISE:
                handleQueryInBatch(assignmentRuleRepository.maxEnterpriseId(),
                    SQLFieldForceConstant.JDBC_REMOVE_INVALID_ASSIGNEE_FROM_ENTERPRISE,
                    null);
                break;
            case CUSTOMER_CONTACT:
                handleQueryInBatch(assignmentRuleRepository.maxContactId(),
                    SQLFieldForceConstant.JDBC_REMOVE_INVALID_ASSIGNEE_FROM_CONTACT,
                    null);
                break;
            case SUBSCRIPTION:
                handleQueryInBatch(assignmentRuleRepository.maxSubscriptionId(),
                    SQLFieldForceConstant.JDBC_REMOVE_INVALID_ASSIGNEE_FROM_SUBSCRIPTION,
                    null);
            case AFFILIATE:
                handleQueryInBatch(assignmentRuleRepository.maxUserIdAffiliate(),
                    SQLFieldForceConstant.JDBC_REMOVE_INVALID_ASSIGNEE_FROM_USER_AFFILIATE,
                    null);
                break;
        }
    }

    @Transactional
    public void scanningAssigningRuleCondition(CrmObjectTypeEnum objectTypeEnum, Integer numApplyingObject,
        Set<Long> newLstObjectId, Long[] lstAssigneesId, RuleCondition ruleCondition, Map<Long, Set<Long>> mapObjectIdByAssigneeId) {
        log.info("Start scanning ruleConditionId {}", ruleCondition.getId());

        List<Object[]> lstParamAssigneeId = new ArrayList<>();
        lstParamAssigneeId.add(new Object[]{lstAssigneesId, lstAssigneesId});
        long startTime = System.currentTimeMillis();
        // Lấy thông tin batchInsertArgs, batchUpdateArgs cho các đối tượng khác users, enterprise
        List<Object[]> batchInsertArgs = new ArrayList<>();
        List<Object[]> batchUpdateArgs = new ArrayList<>();
        Timestamp timestampSql = new java.sql.Timestamp(Calendar.getInstance().getTimeInMillis());
        putDataAssignToBatchArgs(ruleCondition.getId(), objectTypeEnum, timestampSql, batchUpdateArgs, batchInsertArgs, mapObjectIdByAssigneeId);
        String batchUpdateSql;
        String updateLstAssigneeCondSql = getScanConditionSQL(AutomationRuleActionTypeEnum.UNSET, ruleCondition.getConditionSql());
        long start;
        switch (objectTypeEnum) {
            case USER:
                if (numApplyingObject > 0) {
                    start = System.currentTimeMillis();
                    log.info("== ruleConditionId = {}, case USER : CALLING fillDataToTable, startTime: {} millis",
                        ruleCondition.getId(), start);
                    fillDataToTable(assignmentRuleRepository.maxUserId(),
                        "UPDATE vnpt_dev.users SET assignee_id = ? WHERE assignee_id IS NULL AND id = any(?)",
                        "VACUUM ANALYZE vnpt_dev.users (assignee_id)", mapObjectIdByAssigneeId);
                    log.info("== ruleConditionId = {}, case USER : FINISHED fillDataToTable, totalTimeCost: {} millis",
                        ruleCondition.getId(), System.currentTimeMillis() - start);
                }

                start = System.currentTimeMillis();
                batchUpdateSql = SQLFieldForceConstant.UPDATE_RULE_LIST_ASSIGNEES_FOR_USER + updateLstAssigneeCondSql;
                log.info("=== ruleConditionId = {}, CALLING batchUpdate with case USER, sql = {}, batchArgs = {}", ruleCondition.getId(), batchUpdateSql,
                    lstParamAssigneeId.toArray());
                jdbcTemplate.batchUpdate(batchUpdateSql, lstParamAssigneeId);
                log.info("=== END batchUpdate with case USER, sql = {}, batchArgs = {}, totalTime = {} ms", batchUpdateSql, lstParamAssigneeId.toArray(),
                    System.currentTimeMillis() - start);
                break;
            case CUSTOMER_TICKET:
                start = System.currentTimeMillis();
                log.info("== * ruleConditionId = {}, case CUSTOMER_TICKET : CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_TICKET, batchUpdateArgs, start);
                jdbcTemplate.batchUpdate(SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_TICKET, batchUpdateArgs);
                log.info("=== * END batchUpdate with case CUSTOMER_TICKET, sql = {}, batchArgs = {}, totalTime = {} ms",
                    SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_TICKET, batchUpdateArgs, System.currentTimeMillis() - start);

                batchUpdateSql = SQLFieldForceConstant.UPDATE_RULE_LIST_ASSIGNEES_FOR_TICKET + updateLstAssigneeCondSql;
                start = System.currentTimeMillis();
                log.info("== ** ruleConditionId = {}, case CUSTOMER_TICKET : CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), batchUpdateSql, lstParamAssigneeId.toArray(), start);
                jdbcTemplate.batchUpdate(batchUpdateSql, lstParamAssigneeId);
                log.info("=== ** END batchUpdate with case CUSTOMER_TICKET, sql = {}, batchArgs = {}, totalTime = {} ms", batchUpdateSql,
                    lstParamAssigneeId.toArray(), System.currentTimeMillis() - start);
                break;
            case ENTERPRISE:
                if (numApplyingObject > 0) {
                    start = System.currentTimeMillis();
                    log.info("== ruleConditionId = {}, case ENTERPRISE : CALLING fillDataToTable, startTime: {} millis",
                        ruleCondition.getId(), start);
                    fillDataToTable(assignmentRuleRepository.maxEnterpriseId(),
                        "UPDATE vnpt_dev.enterprise SET assignee_id = ? WHERE assignee_id IS NULL AND id = any(?)",
                        "VACUUM ANALYZE vnpt_dev.enterprise (assignee_id)", mapObjectIdByAssigneeId);
                    log.info("== ruleConditionId = {}, case ENTERPRISE : FINISHED fillDataToTable, totalTimeCost: {} millis",
                        ruleCondition.getId(), System.currentTimeMillis() - start);
                }

                start = System.currentTimeMillis();
                batchUpdateSql = SQLFieldForceConstant.UPDATE_RULE_LIST_ASSIGNEES_FOR_ENTERPRISE + updateLstAssigneeCondSql;
                log.info("== * ruleConditionId = {}, case ENTERPRISE : CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), batchUpdateSql, lstParamAssigneeId.toArray(), start);
                jdbcTemplate.batchUpdate(batchUpdateSql, lstParamAssigneeId);
                log.info("=== * END batchUpdate with case ENTERPRISE, sql = {}, batchArgs = {}, totalTime = {} ms", batchUpdateSql, lstParamAssigneeId.toArray(),
                    System.currentTimeMillis() - start);
                break;
            case CUSTOMER_CONTACT:
                start = System.currentTimeMillis();
                log.info("== * ruleConditionId = {}, case CUSTOMER_CONTACT : CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_CONTACT, batchUpdateArgs, start);
                jdbcTemplate.batchUpdate(SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_CONTACT, batchUpdateArgs);
                log.info("=== * END batchUpdate with case CUSTOMER_CONTACT, sql = {}, batchArgs = {}, totalTime = {} ms",
                    SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_CONTACT, batchUpdateArgs, System.currentTimeMillis() - start);

                start = System.currentTimeMillis();
                batchUpdateSql = SQLFieldForceConstant.UPDATE_RULE_LIST_ASSIGNEES_FOR_CONTACT + updateLstAssigneeCondSql;
                log.info("== ** ruleConditionId = {}, case CUSTOMER_CONTACT : CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), batchUpdateSql, batchUpdateArgs, start);
                jdbcTemplate.batchUpdate(batchUpdateSql, lstParamAssigneeId);
                log.info("=== ** END batchUpdate with case CUSTOMER_CONTACT, sql = {}, batchArgs = {}, totalTime = {} ms", batchUpdateSql,
                    lstParamAssigneeId.toArray(), System.currentTimeMillis() - start);
                break;
            case SUBSCRIPTION:
                start = System.currentTimeMillis();
                log.info("== * ruleConditionId = {}, case SUBSCRIPTION : CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_SUBSCRIPTIONS, batchUpdateArgs, start);
                jdbcTemplate.batchUpdate(SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_SUBSCRIPTIONS, batchUpdateArgs);
                log.info("=== * END batchUpdate with case SUBSCRIPTION, sql = {}, batchArgs = {}, totalTime = {} ms",
                    SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_SUBSCRIPTIONS, batchUpdateArgs, System.currentTimeMillis() - start);

                start = System.currentTimeMillis();
                batchUpdateSql = SQLFieldForceConstant.UPDATE_RULE_LIST_ASSIGNEES_FOR_SUBSCRIPTIONS + updateLstAssigneeCondSql;
                log.info("== ** ruleConditionId = {}, case SUBSCRIPTION : CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), batchUpdateSql, lstParamAssigneeId.toArray(), start);
                jdbcTemplate.batchUpdate(batchUpdateSql, lstParamAssigneeId);
                log.info("=== ** END batchUpdate with case SUBSCRIPTION, sql = {}, batchArgs = {}, totalTime = {} ms", batchUpdateSql,
                    lstParamAssigneeId.toArray(), System.currentTimeMillis() - start);
                break;
            case AFFILIATE:
                start = System.currentTimeMillis();
                log.info("== * ruleConditionId = {}, case AFFILIATE : CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_AFFILIATE, batchUpdateArgs, start);
                jdbcTemplate.batchUpdate(SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_AFFILIATE, batchUpdateArgs);
                log.info("=== * END batchUpdate with case AFFILIATE, sql = {}, batchArgs = {}, totalTime = {} ms",
                    SQLFieldForceConstant.UPDATE_RULE_ASSIGNEE_FOR_AFFILIATE, batchUpdateArgs, System.currentTimeMillis() - start);

                start = System.currentTimeMillis();
                batchUpdateSql = SQLFieldForceConstant.UPDATE_RULE_LIST_ASSIGNEES_FOR_AFFILIATE + updateLstAssigneeCondSql;
                log.info("== ** ruleConditionId = {}, case AFFILIATE: CALLING batchUpdate with sql = {}, batchArgs = {}, startTime: {} millis",
                    ruleCondition.getId(), batchUpdateSql, lstParamAssigneeId.toArray(), start);
                jdbcTemplate.batchUpdate(batchUpdateSql, lstParamAssigneeId);
                log.info("=== ** END batchUpdate with case AFFILIATE, sql = {}, batchArgs = {}, totalTime = {} ms", batchUpdateSql,
                    lstParamAssigneeId.toArray(), System.currentTimeMillis() - start);
                break;
        }

        log.info("updateAssigneeId: {} items, end after {} ms", numApplyingObject, System.currentTimeMillis() - startTime);

        // Gửi mail thông báo
        if (numApplyingObject > 0) {
            try {
                // Lưu lịch sự gán/tương tác của nhân sự phụ trách với từng bản ghi
                jdbcTemplate.batchUpdate(SQLFieldForceConstant.INSERT_CRM_ASSIGNMENT_RULE_STATISTIC, batchInsertArgs);
                startTime = System.currentTimeMillis();
                // ICommonIdNameEmail: chứa thông tin assignee (tên, email)
                // AssignmentRuleMailDetailDTO: chứa nội dung còn lại của email (table, số lượng bản ghi, ...)
                Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> mapAssigneeToObjectContent =
                    getMapAssigneeToObjectContent(newLstObjectId, objectTypeEnum, mapObjectIdByAssigneeId, null);
                ActionNotificationParamDTO paramDTO = new PGD05(mapAssigneeToObjectContent, objectTypeEnum).getParam();
                if (paramDTO != null) {
                    log.info("scanningAssigningRuleCondition: calling sendNotificationWithRuleCondition for ruleConditionId = {}", ruleCondition.getId());
                    actionNotificationService.sendNotificationWithRuleCondition(paramDTO, ruleCondition.getId(),
                        ruleCondition.getLstNotificationType(), null);
                }
                mapAssigneeToObjectContent.clear();
                log.info("scanningAssigningRuleCondition: send notification end after {} ms", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                log.info("scanningAssigningRuleCondition: ERROR while sending notification for ruleConditionId id = {}, exception = {}",
                    ruleCondition.getId(), e.getMessage());
            }
        }

        // Lưu lịch sử quét rule thành công
        String content = "Quét quy tắc %s thành công với đối tượng %s";
        actionHistoryRepository.save(new ActionHistory(ActionHistoryTypeEnum.AUTOMATION_RULE_SCAN_SUCCESS, ActionHistoryObjectTypeEnum.AUTOMATION_RULE,
            ruleCondition.getId(), -1L, String.format(content, ruleCondition.getName(), objectTypeEnum.name())));
    }

    // lay map thong tin user to thong tin cac ban ghi dc gan
    public Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> getMapAssigneeToObjectContent(Set<Long> lstObjectId,
        CrmObjectTypeEnum objectTypeEnum, Map<Long, Set<Long>> mapObjectIdByAssigneeId, String inactiveTime) {

        List<ICommonIdNameEmail> lstReceiverDetail = userService.findLstNotifUserDetail(mapObjectIdByAssigneeId.keySet());
        // lấy chi tiết đối tượng
        List<IObjectDetailDTO> lstAllObjectDetail = getLstObjectDetail(lstObjectId, objectTypeEnum);
        return fillRuleDataToMailTemplateMap(lstReceiverDetail, mapObjectIdByAssigneeId, lstAllObjectDetail, objectTypeEnum, inactiveTime);
    }

    private Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> fillRuleDataToMailTemplateMap(List<ICommonIdNameEmail> lstReceiverDetail,
        Map<Long, Set<Long>> mapObjectIdByAssigneeId, List<IObjectDetailDTO> lstAllObjectDetail,
        CrmObjectTypeEnum objectTypeEnum, String inactiveTime) {
        // lấy link tới màn chi tiết của đối tượng
        String linkObjectDetail = String.format("%s/%s",
            CommonUtils.removeEndSlashChar(webHost), CrmObjectTypeEnum.getLinkObjectDetailFromValue(objectTypeEnum.getValue()));

        Map<ICommonIdNameEmail, AssignmentRuleMailDetailDTO> result = new HashMap<>();
        String rowTemplate = NotificationContentConstant.PGC_RULE_OBJECT_ROW;
        String tableTemplate = "";
        switch (objectTypeEnum) {
            case USER:
                tableTemplate = NotificationContentConstant.PGC_RULE_OBJECT_USER_TABLE;
                break;
            case CUSTOMER_TICKET:
                tableTemplate = NotificationContentConstant.PGC_RULE_OBJECT_CUSTOMER_TICKET_TABLE;
                break;
            case ENTERPRISE:
            case CUSTOMER_CONTACT:
                tableTemplate = NotificationContentConstant.PGC_RULE_OBJECT_CUSTOMER_TABLE;
                break;
            case SUBSCRIPTION:
                tableTemplate = NotificationContentConstant.PGC_RULE_OBJECT_SUBSCRIPTION_TABLE;
                break;
            case AFFILIATE:
                tableTemplate = NotificationContentConstant.PGC_RULE_OBJECT_AFFILIATE_TABLE;
                break;
        }

        // với từng assignee -> fill data các object đc phân giao vào 1 string html
        for (Map.Entry<Long, Set<Long>> entry : mapObjectIdByAssigneeId.entrySet()) {
            // lấy assignee hiện tại
            ICommonIdNameEmail receiver = lstReceiverDetail.stream()
                .filter(r -> Objects.equals(entry.getKey(), r.getId()))
                .findAny().orElse(null);
            // lấy số lượng bản ghi đã đc gán cho assignee hiện tại
            int numApplyingObject;
            // lấy các object của assignee hiện tại
            List<IObjectDetailDTO> lstCurrentObjectDetail = lstAllObjectDetail.stream()
                .filter(u -> entry.getValue().contains(u.getId()))
                .collect(Collectors.toList());

            StringBuilder tableRowHTML = new StringBuilder();

            Integer rowNumber = 1;
            if (Objects.equals(objectTypeEnum, CrmObjectTypeEnum.SUBSCRIPTION)) {
                Map<String, List<IObjectDetailDTO>> mapSubCodeToLstObject = lstCurrentObjectDetail.stream().collect(Collectors.groupingBy(IObjectDetailDTO::getSubCode));
                numApplyingObject = mapSubCodeToLstObject.size();
                for (Map.Entry<String, List<IObjectDetailDTO>> curEntry : mapSubCodeToLstObject.entrySet()) {
                    // gộp thuê bao cùng giỏ hàng
                    List<IObjectDetailDTO> lstObjectSameCart = curEntry.getValue();
                    // gộp tên SPDV cho các thuê bao cùng giỏ hàng
                    StringBuilder lstServiceNameStr = new StringBuilder();
                    lstObjectSameCart.forEach(object -> lstServiceNameStr.append("- ").append(object.getServiceName()).append("<br>"));

                    String fullSubLink = linkObjectDetail;

                    // với đơn hàng thuộc giỏ hàng / nhóm  -> link tới màn danh sách đơn hàng
                    // với đơn hàng KHÔNG thuộc giỏ hàng / nhóm  -> link tới màn chi tiết đơn hàng
                    if (lstObjectSameCart.get(0).getCountSubCode() <= 1) {
                        fullSubLink = linkObjectDetail + lstObjectSameCart.get(0).getObjectDetailLinkSlug();
                    }
                    // append vòa template
                    tableRowHTML.append(String.format(rowTemplate, rowNumber, fullSubLink, lstObjectSameCart.get(0).getSubCode(),
                        lstServiceNameStr, lstObjectSameCart.get(0).getSmeName()));
                    rowNumber++;
                }
            } else {
                // fill thông tin của các objects vào table html
                numApplyingObject = lstCurrentObjectDetail.size();
                for (IObjectDetailDTO objectDetail : lstCurrentObjectDetail) {
                    switch (objectTypeEnum) {
                        case ENTERPRISE:
                        case USER:
                            tableRowHTML.append(String.format(rowTemplate, rowNumber,
                                linkObjectDetail + objectDetail.getObjectDetailLinkSlug(), objectDetail.getSmeName(), objectDetail.getEmail(), objectDetail.getPhoneNumber()));
                            break;
                        case CUSTOMER_CONTACT:
                            Gson gson = new Gson();
                            List<String> lstEmail = gson.fromJson(objectDetail.getEmail(), new TypeToken<ArrayList<String>>(){}.getType());
                            List<String> lstPhone = gson.fromJson(objectDetail.getPhoneNumber(), new TypeToken<ArrayList<String>>(){}.getType());
                            String lstEmailStr = String.join(", ", lstEmail);
                            String lstPhoneStr = String.join(", ", lstPhone);
                            tableRowHTML.append(String.format(rowTemplate, rowNumber,
                                linkObjectDetail + objectDetail.getObjectDetailLinkSlug(), objectDetail.getSmeName(), lstEmailStr, lstPhoneStr));
                            break;
                        case AFFILIATE:
                            tableRowHTML.append(String.format(rowTemplate, rowNumber,
                                linkObjectDetail + objectDetail.getObjectDetailLinkSlug(), objectDetail.getSmeName(), objectDetail.getAffiliateCode(), objectDetail.getEmail()));
                            break;
                        case CUSTOMER_TICKET:
                            tableRowHTML.append(String.format(rowTemplate, rowNumber,
                                linkObjectDetail + objectDetail.getObjectDetailLinkSlug(), objectDetail.getTicketCode(), objectDetail.getSmeName(), objectDetail.getTicketName()));
                            break;
                    }
                    rowNumber++;

                }
            }


            // map assignee to html table (contain object detail)
            if (Objects.nonNull(receiver)) {
                AssignmentRuleMailDetailDTO content = new AssignmentRuleMailDetailDTO();
                // inactiveTime: tgian không tương tác
                if (Objects.nonNull(inactiveTime)) {
                    content.setInactiveTimeStr(inactiveTime);
                }
                String tableHTML = String.format(tableTemplate, tableRowHTML);
                content.setTableHTML(tableHTML);
                content.setNumApplyingObject(numApplyingObject);
                result.put(receiver, content);
            }
        }
        return result;
    }

    private void autoWarnInactiveToAssignee(List<HistoryInactiveWarning> lstHistoryInactiveWarning, Long ruleConditionId,
        List<ObjectInteractiveInfoDTO> lstObjectWarningInteractive) {
        log.info("========= START autoWarnInactiveToAssignee for {} items, ruleConditionId = {} =========", lstObjectWarningInteractive.size(), ruleConditionId);
        long startSendNotifTime = System.currentTimeMillis();
        List<ActionNotificationParamDTO> lstParamDTO = new ArrayList<>();
        try {
            Integer numWarningConfig = lstObjectWarningInteractive.get(0).getFrequency();

            List<String> lstNotificationType = lstObjectWarningInteractive.get(0).getLstNotificationType();
            // Thực hiện tự động gửi cảnh báo cho nhân viên phụ trách
            fillMailParamForInactiveWarningToAssignee(numWarningConfig, lstHistoryInactiveWarning, lstParamDTO, lstObjectWarningInteractive);

            // Thực hiện tự động gửi cảnh báo cho nhân viên khác
            fillMailParamForInactiveWarningToOtherAssignee(numWarningConfig, lstHistoryInactiveWarning, lstParamDTO,
                lstObjectWarningInteractive);

            // gửi thư warning
            if (!lstParamDTO.isEmpty()) {
                for (ActionNotificationParamDTO paramDTO : lstParamDTO) {
                    log.info("autoWarnInactiveToAssignee: calling sendNotificationWithRuleCondition for ruleConditionId = {}", ruleConditionId);
                    actionNotificationService.sendNotificationWithRuleCondition(paramDTO, ruleConditionId, lstNotificationType, lstHistoryInactiveWarning);
                }
            }
        } catch (Exception e) {
            log.info("autoWarnInactiveToAssignee: ERROR while sending inactive warning to other assignee for ruleConditionId = {}, exception = {}",
                ruleConditionId, e.getMessage());
        }
        log.info("==== END autoWarnInactiveToAssignee: send {} notifications, ruleConditionId = {}, end after {} ms =====", lstParamDTO.size(),
            ruleConditionId, System.currentTimeMillis() - startSendNotifTime);
    }

    private void fillMailParamForInactiveWarningToOtherAssignee(int numWarningConfig, List<HistoryInactiveWarning> lstHistoryInactiveWarning,
        List<ActionNotificationParamDTO> lstParamDTO, List<ObjectInteractiveInfoDTO> lstObjectWarningInteractive) {
        // Thực hiện tự động gửi cảnh báo cho nhân viên khác
        List<ObjectInteractiveInfoDTO> lstObjectForWarningOtherAssignee = lstObjectWarningInteractive.stream().filter(
                item -> Objects.nonNull(item.getWarningPolicy()) && item.getNumWarning() < numWarningConfig &&
                    item.getWarningPolicy().getLstAssignType().contains("OTHER") && Objects.nonNull(item.getWarningPolicy().getLstOtherAssigneeId()))
            .collect(Collectors.toList());

        // gui notification cảnh báo cho nhân sự khác
        if (!lstObjectForWarningOtherAssignee.isEmpty()) {

            // lst id tất cả nhân sự khác
            Set<Long> lstAllOtherAssigneeId = new HashSet<>();

            // với mỗi nhân sự khác --> lấy danh sách nhân viên + các bản ghi tương ứng
            // map:
            // key: id của nhân sự khác
            // value: lst assignee và object của nhân sự khác
            Map<Long, List<ObjectInteractiveInfoDTO>> mapOtherAssigneeToObject = new HashMap<>();

            lstObjectForWarningOtherAssignee.forEach(object -> {
                List<Long> otherAssigneeId = object.getWarningPolicy().getLstOtherAssigneeId();
                if (Objects.nonNull(otherAssigneeId)) {
                    otherAssigneeId.forEach(id -> mapOtherAssigneeToObject.put(id, lstObjectForWarningOtherAssignee));
                    lstAllOtherAssigneeId.addAll(otherAssigneeId);
                }
            });

            // lst detail tất cả nhân sự khác
            List<ICommonIdNameEmail> lstAllOtherAssignee = userService.findLstNotifUserDetail(lstAllOtherAssigneeId);

            // với mỗi nhân sự khác
            for (Map.Entry<Long, List<ObjectInteractiveInfoDTO>> entry : mapOtherAssigneeToObject.entrySet()) {
                // mỗi mail dành cho 1 đối tượng --> chia ra nhiều đối tượng
                // lấy thông tin nhân sự khác
                ICommonIdNameEmail curOtherAssignee = lstAllOtherAssignee.stream()
                    .filter(assignee -> Objects.equals(entry.getKey(), assignee.getId()))
                    .findAny().orElse(null);

                List<ObjectInteractiveInfoDTO> lstCurrentObject = entry.getValue();

                // Đối tượng KH
                addMailParamAndInactiveWarningHistoryForOtherAssignee(CrmObjectTypeEnum.ENTERPRISE, lstCurrentObject, curOtherAssignee,
                    lstParamDTO, lstHistoryInactiveWarning);
                // Đối tượng liên hệ
                addMailParamAndInactiveWarningHistoryForOtherAssignee(CrmObjectTypeEnum.CUSTOMER_CONTACT, lstCurrentObject, curOtherAssignee,
                    lstParamDTO, lstHistoryInactiveWarning);
                // Đối tượng TK
                addMailParamAndInactiveWarningHistoryForOtherAssignee(CrmObjectTypeEnum.USER, lstCurrentObject, curOtherAssignee,
                    lstParamDTO, lstHistoryInactiveWarning);
                // Đối tượng affiliate
                addMailParamAndInactiveWarningHistoryForOtherAssignee(CrmObjectTypeEnum.AFFILIATE, lstCurrentObject, curOtherAssignee,
                    lstParamDTO, lstHistoryInactiveWarning);
                // Đối tượng thuê bao
                addMailParamAndInactiveWarningHistoryForOtherAssignee(CrmObjectTypeEnum.SUBSCRIPTION, lstCurrentObject, curOtherAssignee,
                    lstParamDTO, lstHistoryInactiveWarning);
                // Đối tượng Phiếu hỗ trợ
                addMailParamAndInactiveWarningHistoryForOtherAssignee(CrmObjectTypeEnum.CUSTOMER_TICKET, lstCurrentObject, curOtherAssignee,
                    lstParamDTO, lstHistoryInactiveWarning);
            }
        }
    }

    private void fillMailParamForInactiveWarningToAssignee(int numWarningConfig, List<HistoryInactiveWarning> lstHistoryInactiveWarning,
        List<ActionNotificationParamDTO> lstParamDTO, List<ObjectInteractiveInfoDTO> lstObjectWarningInteractive) {
        // Thực hiện tự động gửi cảnh báo cho nhân viên phụ trách
        List<ObjectInteractiveInfoDTO> lstObjectForWarningManager = lstObjectWarningInteractive.stream().filter(
            item -> Objects.nonNull(item.getWarningPolicy()) && item.getNumWarning() < numWarningConfig &&
                item.getWarningPolicy().getLstAssignType().contains("ASSIGNEE")).collect(Collectors.toList());

        // gui notification cảnh báo cho nhân sự phụ trách
        if (!lstObjectForWarningManager.isEmpty()) {

            // mỗi mail dành cho 1 đối tượng --> chia ra nhiều đối tượng
            // Đối tượng KH
            addMailParamAndInactiveHistoryWarningForAssignee(CrmObjectTypeEnum.ENTERPRISE, lstObjectForWarningManager,
                lstParamDTO, lstHistoryInactiveWarning);
            // Đối tượng liên hệ
            addMailParamAndInactiveHistoryWarningForAssignee(CrmObjectTypeEnum.CUSTOMER_CONTACT, lstObjectForWarningManager,
                lstParamDTO, lstHistoryInactiveWarning);
            // Đối tượng TK
            addMailParamAndInactiveHistoryWarningForAssignee(CrmObjectTypeEnum.USER, lstObjectForWarningManager,
                lstParamDTO, lstHistoryInactiveWarning);
            // Đối tượng affiliate
            addMailParamAndInactiveHistoryWarningForAssignee(CrmObjectTypeEnum.AFFILIATE, lstObjectForWarningManager,
                lstParamDTO, lstHistoryInactiveWarning);
            // Đối tượng thuê bao
            addMailParamAndInactiveHistoryWarningForAssignee(CrmObjectTypeEnum.SUBSCRIPTION, lstObjectForWarningManager,
                lstParamDTO, lstHistoryInactiveWarning);
            // Đối tượng Phiếu hỗ trợ
            addMailParamAndInactiveHistoryWarningForAssignee(CrmObjectTypeEnum.CUSTOMER_TICKET, lstObjectForWarningManager,
                lstParamDTO, lstHistoryInactiveWarning);
        }
    }

    private List<IObjectDetailDTO> getLstObjectDetail(Set<Long> lstObjectId, CrmObjectTypeEnum objectTypeEnum) {
        List<IObjectDetailDTO> lstAllObjectDetail = new ArrayList<>();
        switch (objectTypeEnum) {
            case USER:
                lstAllObjectDetail = automationRuleRepository.getLstUserDetail(lstObjectId);
                break;
            case CUSTOMER_TICKET:
                lstAllObjectDetail = automationRuleRepository.getLstTicketDetail(lstObjectId);
                break;
            case ENTERPRISE:
                lstAllObjectDetail = automationRuleRepository.getLstEnterpriseDetail(lstObjectId);
                break;
            case CUSTOMER_CONTACT:
                lstAllObjectDetail = automationRuleRepository.getLstContactDetail(lstObjectId);
                break;
            case SUBSCRIPTION:
                lstAllObjectDetail = automationRuleRepository.getLstSubscriptionDetail(lstObjectId);
                break;
            case AFFILIATE:
                lstAllObjectDetail = automationRuleRepository.getLstAffiliateUserDetail(lstObjectId);
                break;
            default:
                break;
        }
        return lstAllObjectDetail;
    }

    public void handleQueryInBatch(Long totalSize, String query, String vacuumQuery) {
        Long batchSize = 100000L;
        for (int i = 0; i < totalSize / batchSize + 1; i++) {
            long start = batchSize * i + 1;
            long end = batchSize * (i + 1);
            long startTime = System.currentTimeMillis();
            jdbcTemplate.batchUpdate(query + " AND id BETWEEN " + start + " AND " + end);
            log.info("handleQueryInBatch: batchUpdate {}-{} end after {} ms", start, end, (System.currentTimeMillis() - startTime));
            if ((vacuumQuery != null) && (i % 9 == 8)) { // Thực hiện vacuum sau mỗi 800k entry
                startTime = System.currentTimeMillis();
                jdbcTemplate.execute(vacuumQuery);
                log.info("handleQueryInBatch: vacuumAnalyze {}-{} end after {} ms", start, end, (System.currentTimeMillis() - startTime));
            }
        }
    }

    public void fillDataToTable(Long totalSize, String updateQuery, String vacuumQuery,
        Map<Long, Set<Long>> mapObjectIdByAssigneeId) {
        log.info("====== START fillDataToTable with totalSize = {}, updateQuery = {}, vacuumQuery = {}, mapObjectIdByAssigneeId = {} ",
            totalSize, updateQuery, vacuumQuery, mapObjectIdByAssigneeId);
        // Gán nhân sự phụ trách cho từng bản ghi
        for (Map.Entry<Long, Set<Long>> entry : mapObjectIdByAssigneeId.entrySet()) {
            fillDataDetailToTable(totalSize, updateQuery, vacuumQuery, entry.getKey(), entry.getValue());
        }

        log.info("====== END fillDataToTable with totalSize = {}, updateQuery = {}, vacuumQuery = {}, mapObjectIdByAssigneeId = {} ",
            totalSize, updateQuery, vacuumQuery, mapObjectIdByAssigneeId);
    }

    public void fillDataDetailToTable(Long totalSize, String updateQuery, String vacuumQuery, Long assigneeId, Set<Long> lstObjectId) {
        // Gán nhân sự phụ trách cho từng bản ghi
        Long batchSize = 100000L;
        List<Object[]> batchUpdateArgs = new ArrayList<>();
        boolean existsAssigneeId = Objects.nonNull(assigneeId);
        for (int i = 0; i < totalSize / batchSize + 1; i++) {
            long start = batchSize * i + 1;
            long end = batchSize * (i + 1);
            long startTime = System.currentTimeMillis();
            Set<Long> lstIdUpdate = lstObjectId.stream().filter(item -> item >= start && item <= end).collect(Collectors.toSet());
            if (!lstIdUpdate.isEmpty()) {
                if (existsAssigneeId) {
                    batchUpdateArgs.add(new Object[]{assigneeId, lstIdUpdate.toArray(new Long[0])});
                } else {
                    batchUpdateArgs.add(new Object[]{lstIdUpdate.toArray(new Long[0])});
                }
                var result = jdbcTemplate.batchUpdate(updateQuery, batchUpdateArgs);
                if (result.length > 0) {
                    log.info("handleQueryInBatch: batchUpdate updated {} items from id {}-{} end after {} ms",
                        result.length, start, end, (System.currentTimeMillis() - startTime));
                    if ((vacuumQuery != null) && (updatedCount.addAndGet(result.length) >= 8 * batchSize)) { // Vacuum sau mỗi 800k entry
                        startTime = System.currentTimeMillis();
                        jdbcTemplate.execute(vacuumQuery);
                        int count = updatedCount.getAndSet(0);
                        log.info("handleQueryInBatch: vacuumAnalyze after {} changes, end after {} ms", count,
                            (System.currentTimeMillis() - startTime));
                    }
                }
            }
        }
    }

    private void putDataAssignToBatchArgs(Long ruleId, CrmObjectTypeEnum objectTypeEnum, Timestamp timestampSql, List<Object[]> batchUpdateArgs,
        List<Object[]> batchInsertArgs, Map<Long, Set<Long>> mapObjectIdByAssigneeId) {
        for (Map.Entry<Long, Set<Long>> assigneeEntry : mapObjectIdByAssigneeId.entrySet()) {
            if (!Objects.equals(CrmObjectTypeEnum.USER, objectTypeEnum) && !Objects.equals(CrmObjectTypeEnum.ENTERPRISE, objectTypeEnum)) {
                batchUpdateArgs.add(new Object[]{assigneeEntry.getKey(), assigneeEntry.getValue().toArray(new Long[0])});
            }
            // Thêm thông tin batchInsert
            for (Long objectId : assigneeEntry.getValue()) {
                batchInsertArgs.add(new Object[]{ruleId, assigneeEntry.getKey(), objectTypeEnum.name(), objectId, timestampSql});
            }
        }
    }

    private void putDataReAssignToBatchArgs(Long ruleConditionId, CrmObjectTypeEnum objectTypeEnum, Timestamp timestampSql,
        List<Object[]> batchUpdateArgs,
        List<Object[]> batchInsertArgs, Map<Long, Set<Long>> mapObjectIdByAssigneeId) {
        for (Map.Entry<Long, Set<Long>> assigneeEntry : mapObjectIdByAssigneeId.entrySet()) {
            if (!Objects.equals(CrmObjectTypeEnum.USER, objectTypeEnum) && !Objects.equals(CrmObjectTypeEnum.ENTERPRISE, objectTypeEnum)) {
                batchUpdateArgs.add(new Object[]{assigneeEntry.getKey(), assigneeEntry.getValue().toArray(new Long[0])});
            }
            // Thêm thông tin batchInsert
            for (Long objectId : assigneeEntry.getValue()) {
                batchInsertArgs.add(
                    new Object[]{ruleConditionId, assigneeEntry.getKey(), objectTypeEnum.name(), objectId, timestampSql, timestampSql});
            }
        }
    }

    /**
     * Map thông tin nhân sự phụ trách với các đối tượng được phân giao
     *
     * @param ruleConditionId         Id ruleCondition
     * @param currentAssigneeId       id nhân sự phụ trách
     * @param assignmentMethod        Phương thức phân giao
     * @param lstObjectId             Danh sách id đối tượng thỏa mãn điều kiện quy tắc
     * @param mapObjectIdByAssigneeId map Danh sách ID đối tượng với id nhân sự phụ trách
     */
    private void putDataToMap(Long ruleConditionId, Long currentAssigneeId, String assignmentMethod, Long otherAssigneeId,
        CrmObjectTypeEnum objectTypeEnum, Set<Long> lstObjectId, Map<Long, Set<Long>> mapObjectIdByAssigneeId
    ) {
        int totalObject;
        int numObject = 0;
        Map<String, List<ISubscriptionIdAndCartCodeDTO>> mapCartCodeToSub = new HashMap<>(); // map cho TH subscription

        // THop SUBSCRIPTION:
        // 1 đơn hàng có nhiều thuê bao --> gộp lại các thuê bao đó, coi như là 1 bản ghi
        if (Objects.equals(objectTypeEnum, CrmObjectTypeEnum.SUBSCRIPTION)) {
            List<ISubscriptionIdAndCartCodeDTO> lstSubDetail = automationRuleRepository.getLstSubAndCartCodeBySubId(lstObjectId);
            // gộp các sub chung cart code
            mapCartCodeToSub = lstSubDetail.stream()
                .collect(Collectors.groupingBy(ISubscriptionIdAndCartCodeDTO::getCartCode));

            totalObject = mapCartCodeToSub.size(); // totalObject = số lượng cartCode
        } else {
            totalObject = lstObjectId.size();
        }

        // Tính số lượng
        List<AssignmentRuleAssigneeDTO> lstRuleAssignee;
        if (Objects.isNull(otherAssigneeId)) {
            List<IGetAssignmentRuleAssigneeDTO> lstRuleAssigneeIDTO = assignmentRuleRepository.getListAssigneeIdNotInOrderByPriority(ruleConditionId,
                currentAssigneeId);
            lstRuleAssignee = getNumObjectAssignee(assignmentMethod, totalObject,
                lstRuleAssigneeIDTO.stream().map(AssignmentRuleAssigneeDTO::new).collect(Collectors.toList()));
        } else {
            AssignmentRuleAssigneeDTO assignmentRuleAssignee = new AssignmentRuleAssigneeDTO();
            assignmentRuleAssignee.setIndex(1);
            assignmentRuleAssignee.setAssigneeId(otherAssigneeId);
            assignmentRuleAssignee.setRuleConditionId(ruleConditionId);
            assignmentRuleAssignee.setNumObjectAssignee(totalObject);
            lstRuleAssignee = Collections.singletonList(assignmentRuleAssignee);
        }

        if (Objects.equals(objectTypeEnum, CrmObjectTypeEnum.SUBSCRIPTION)) {
            for (AssignmentRuleAssigneeDTO ruleAssignee : lstRuleAssignee) {
                List<String> lstCurCartCode = new ArrayList<>();
                Set<Long> lstCurrentAssigneeObjectId = new HashSet<>();
                long assigneeId = ruleAssignee.getAssigneeId();
                for (Map.Entry<String, List<ISubscriptionIdAndCartCodeDTO>> entry : mapCartCodeToSub.entrySet()) {
                    // mỗi giỏ hàng -> add lst sub id
                    lstCurrentAssigneeObjectId.addAll(entry.getValue().stream().map(ISubscriptionIdAndCartCodeDTO::getSubId).collect(Collectors.toList()));
                    ++numObject;
                    lstCurCartCode.add(entry.getKey());
                    if (numObject == ruleAssignee.getNumObjectAssignee()) {
                        if (mapObjectIdByAssigneeId.containsKey(assigneeId)) {
                            Set<Long> lstExistsObjectId = mapObjectIdByAssigneeId.get(assigneeId);
                            lstExistsObjectId.addAll(lstCurrentAssigneeObjectId);
                            mapObjectIdByAssigneeId.put(assigneeId, lstExistsObjectId);
                        } else {
                            mapObjectIdByAssigneeId.put(assigneeId, lstCurrentAssigneeObjectId);
                        }
                        numObject = 0;
                        break;
                    }
                }
                removeListKey(mapCartCodeToSub, lstCurCartCode);
            }

        } else {
            for (AssignmentRuleAssigneeDTO ruleAssignee : lstRuleAssignee) {
                Set<Long> lstCurrentAssigneeObjectId = new HashSet<>();
                long assigneeId = ruleAssignee.getAssigneeId();
                for (Long objectId : lstObjectId) {
                    lstCurrentAssigneeObjectId.add(objectId);
                    ++numObject;
                    if (numObject == ruleAssignee.getNumObjectAssignee()) {
                        if (mapObjectIdByAssigneeId.containsKey(assigneeId)) {
                            Set<Long> lstExistsObjectId = mapObjectIdByAssigneeId.get(assigneeId);
                            lstExistsObjectId.addAll(lstCurrentAssigneeObjectId);
                            mapObjectIdByAssigneeId.put(assigneeId, lstExistsObjectId);
                        } else {
                            mapObjectIdByAssigneeId.put(assigneeId, lstCurrentAssigneeObjectId);
                        }
                        numObject = 0;
                        break;
                    }
                }
                lstObjectId.removeAll(lstCurrentAssigneeObjectId);
            }
        }
    }

    public void removeListKey(Map<String, List<ISubscriptionIdAndCartCodeDTO>> map, List<String> lstKey) {
        for (String key : lstKey) {
            map.remove(key);
        }
    }

    public List<AssignmentRuleAssigneeDTO> getNumObjectAssignee(String assignmentMethod, int totalObject,
        List<AssignmentRuleAssigneeDTO> lstRuleAssignee) {
        int overBalance = !lstRuleAssignee.isEmpty() ? totalObject % lstRuleAssignee.size() : 0;
        // Thực hiện tính số đối tượng cần phân giao cho từng nhân viên phụ trách theo chia đều
        if (Objects.equals(AssignmentMethodEnum.EQUALLY_DIVIDED.name(), assignmentMethod)) {
            for (AssignmentRuleAssigneeDTO ruleAssignee : lstRuleAssignee) {
                if (ruleAssignee.getIndex() > overBalance) {
                    ruleAssignee.setNumObjectAssignee(totalObject/lstRuleAssignee.size());
                } else {
                    ruleAssignee.setNumObjectAssignee(totalObject/lstRuleAssignee.size() + 1);
                }
            }
        } else {
            // Thực hiện tính số đối tượng cần phân giao cho từng nhân viên phụ trách theo trọng số
            //todo
        }
        return lstRuleAssignee;
    }


}

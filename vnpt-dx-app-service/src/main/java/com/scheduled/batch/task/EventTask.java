package com.scheduled.batch.task;

import com.config.AsyncConfiguration;
import com.repository.events.EventsRepository;
import com.scheduled.batch.BaseBatch;
import com.scheduled.batch.BatchService;
import com.service.events.EventsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component("batch-event")
@Slf4j
@RequiredArgsConstructor
public class EventTask extends BaseBatch implements BatchService {

    @Autowired
    private EventsService eventsService;

    @Autowired
    private EventsRepository eventsRepository;

    @Transactional(rollbackFor = Exception.class)
    @Async(value = AsyncConfiguration.ASYNC_EXECUTOR_CHANGE_EVENT)
    @EventListener
    public void componentChangedEvent(){

    }
}

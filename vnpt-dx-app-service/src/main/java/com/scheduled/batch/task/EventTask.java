package com.scheduled.batch.task;

import com.config.AsyncConfiguration;
import com.entity.events.Events;
import com.enums.EventTypeEnum;
import com.event.ComponentChangedEvent;
import com.repository.events.EventsRepository;
import com.scheduled.batch.BaseBatch;
import com.scheduled.batch.BatchService;
import com.service.events.EventsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component("batch-event")
@Slf4j
@RequiredArgsConstructor
public class EventTask extends BaseBatch implements BatchService {

    @Autowired
    private EventsService eventsService;

    @Autowired
    private EventsRepository eventsRepository;

    /**
     * Xử lý event khi component thay đổi
     *
     * @param event ComponentChangedEvent
     */
    @Transactional(rollbackFor = Exception.class)
    @Async(value = "asyncExecutorChangeEvent")
    @EventListener
    public void componentChangedEvent(ComponentChangedEvent event) {
        try {
            log.info("Nhận được ComponentChangedEvent: componentId={}, componentType={}, action={}",
                    event.getComponentId(), event.getComponentType(), event.getAction());
            // Xác định loại event dựa trên componentType và action
            EventTypeEnum eventType = determineEventType(event.getAction(), event.getAction());
            // Lưu event vào database
            Events savedEvent = eventsService.saveEvent(eventType, event.getMetadata());

            log.info("Đã lưu thành công ComponentChangedEvent với ID: {}", savedEvent.getId());

        } catch (Exception e) {
            log.error("Lỗi khi xử lý ComponentChangedEvent: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Xác định loại event dựa trên componentType và action
     */
    private EventTypeEnum determineEventType(String componentType, String action) {
        // Mapping componentType và action thành EventTypeEnum
        switch (componentType.toUpperCase()) {
            case "COUPON":
                return mapCouponAction(action);
            case "PRICING":
                return mapPricingAction(action);
            case "VARIANT":
                return mapVariantAction(action);
            case "ADDON":
                return mapAddonAction(action);
            default:
                log.warn("Không xác định được EventType cho componentType: {}, action: {}", componentType, action);
                return EventTypeEnum.COUPON_UPGRADED; // Default fallback
        }
    }

    private EventTypeEnum mapCouponAction(String action) {
        switch (action.toUpperCase()) {
            case "EXPIRED":
                return EventTypeEnum.COUPON_EXPIRED;
            case "APPLY_EXCEED":
                return EventTypeEnum.COUPON_APPLY_EXCEED;
            case "UPGRADED":
            case "UPDATED":
                return EventTypeEnum.COUPON_UPGRADED;
            default:
                return EventTypeEnum.COUPON_UPGRADED;
        }
    }

    private EventTypeEnum mapPricingAction(String action) {
        switch (action.toUpperCase()) {
            case "STATUS_CHANGED":
                return EventTypeEnum.PRICING_STATUS_CHANGED;
            case "UPGRADED":
            case "UPDATED":
                return EventTypeEnum.PRICING_UPGRADED;
            default:
                return EventTypeEnum.PRICING_UPGRADED;
        }
    }

    private EventTypeEnum mapVariantAction(String action) {
        switch (action.toUpperCase()) {
            case "STATUS_CHANGED":
                return EventTypeEnum.VARIANT_STATUS_CHANGED;
            case "UPGRADED":
            case "UPDATED":
                return EventTypeEnum.VARIANT_UPGRADED;
            default:
                return EventTypeEnum.VARIANT_UPGRADED;
        }
    }

    private EventTypeEnum mapAddonAction(String action) {
        switch (action.toUpperCase()) {
            case "STATUS_CHANGED":
                return EventTypeEnum.ADDON_STATUS_CHANGED;
            case "UPGRADED":
            case "UPDATED":
                return EventTypeEnum.ADDON_UPGRADED;
            default:
                return EventTypeEnum.ADDON_UPGRADED;
        }
    }
}

package com.scheduled.batch.task;

import com.entity.events.Events;
import com.event.ComponentChangedEvent;
import com.repository.events.EventsRepository;
import com.scheduled.batch.BaseBatch;
import com.scheduled.batch.BatchService;
import com.service.events.EventsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component("batch-event")
@Slf4j
@RequiredArgsConstructor
public class EventTask extends BaseBatch implements BatchService {

    @Autowired
    private EventsService eventsService;

    @Autowired
    private EventsRepository eventsRepository;

    /**
     * Xử lý event khi component thay đổi
     * @param event ComponentChangedEvent chứa ID của event đã được tạo
     */
    @Transactional(rollbackFor = Exception.class)
    @Async(value = "asyncExecutorChangeEvent")
    @EventListener
    public void componentChangedEvent(ComponentChangedEvent event) {
        try {
            log.info("Nhận được ComponentChangedEvent với eventId: {}", event.getEventId());

            // Cập nhật status của event thành đã xử lý (status = 1)
            Events updatedEvent = eventsService.updateEventStatus(event.getEventId(), 1);

            log.info("Đã cập nhật thành công status cho event ID: {} thành đã xử lý", updatedEvent.getId());

            // TODO: Thêm logic xử lý nghiệp vụ khác nếu cần
            // Ví dụ: gửi notification, sync data, etc.

        } catch (Exception e) {
            log.error("Lỗi khi xử lý ComponentChangedEvent với eventId {}: {}", event.getEventId(), e.getMessage(), e);

            // Cập nhật status thành lỗi (status = -1) nếu xử lý thất bại
            try {
                eventsService.updateEventStatus(event.getEventId(),-1);
            } catch (Exception updateException) {
                log.error("Lỗi khi cập nhật status lỗi cho event ID {}: {}", event.getEventId(), updateException.getMessage());
            }

            throw e;
        }
    }


}

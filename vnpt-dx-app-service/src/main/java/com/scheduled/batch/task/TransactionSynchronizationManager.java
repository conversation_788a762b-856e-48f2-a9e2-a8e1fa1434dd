package com.scheduled.batch.task;

import com.repository.events.EventsRepository;
import com.scheduled.batch.BaseBatch;
import com.scheduled.batch.BatchService;
import com.service.events.EventsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("batch-transaction")
@Slf4j
@RequiredArgsConstructor
public class TransactionSynchronizationManager extends BaseBatch implements BatchService {

    @Autowired
    private EventsService eventsService;

    @Autowired
    private EventsRepository eventsRepository;



}

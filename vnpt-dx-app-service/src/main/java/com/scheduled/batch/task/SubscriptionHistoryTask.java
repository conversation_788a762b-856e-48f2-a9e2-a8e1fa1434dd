package com.scheduled.batch.task;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import javax.sql.DataSource;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.PGConnection;
import org.postgresql.copy.CopyManager;
import org.postgresql.core.BaseConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Description;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.constant.SubscriptionConstant;
import com.constant.SystemParamConstant;
import com.constant.enums.pricing.ChangePricingPaymentTimeEnum;
import com.constant.enums.pricing.PricingCancelTimeActiveEnum;
import com.constant.enums.subscription.ActionTypeEnum;
import com.constant.enums.subscription.RegTypeEnum;
import com.constant.enums.transactionLog.TransactionCodeEnum;
import com.dto.bills.CouponSubscriptionDTO;
import com.dto.coupons.mailParam.MailSendParamDTO;
import com.dto.services.ServicePricingInfo;
import com.dto.subscriptions.SubscriptionBillingDTO;
import com.dto.subscriptions.SubscriptionRemindDayDTO;
import com.dto.subscriptions.mail.NotifScDetailDTO;
import com.dto.subscriptions.mail.NotifScSubDetailDTO;
import com.dto.transaction_log.CommonActivityLogInfoDTO;
import com.entity.combo.ComboPlan;
import com.entity.combo.SubscriptionComboAddonCoupon;
import com.entity.combo.SubscriptionComboCoupon;
import com.entity.department.Department;
import com.entity.orderServiceReceive.OrderServiceReceive;
import com.entity.pricing.Pricing;
import com.entity.services.ServiceEntity;
import com.entity.subscriptions.ChangeSubscription;
import com.entity.subscriptions.Subscription;
import com.entity.subscriptions.SubscriptionAddonCoupon;
import com.entity.subscriptions.SubscriptionCoupons;
import com.entity.subscriptions.SubscriptionPricingCoupon;
import com.entity.transaction_log.TransactionLog;
import com.enums.ActionChangeSubEnum;
import com.enums.ActionNotificationEnum;
import com.exception.ErrorKey;
import com.exception.Resources;
import com.model.entity.security.User;
import com.onedx.common.constants.enums.CustomerTypeEnum;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.StatusEnum;
import com.onedx.common.constants.enums.TimeTypeEnum;
import com.onedx.common.constants.enums.emails.EmailCodeEnum;
import com.onedx.common.constants.enums.integration.backend.IntegrationActionTypeEnum;
import com.onedx.common.constants.enums.migration.ConfirmStatusEnum;
import com.onedx.common.constants.enums.migration.CreatedSourceMigrationEnum;
import com.onedx.common.constants.enums.pricings.CycleTypeEnum;
import com.onedx.common.constants.enums.services.ServiceOwnerEnum;
import com.onedx.common.constants.enums.services.ServiceTypeEnum;
import com.onedx.common.constants.enums.subscriptions.ChangeContinueEnum;
import com.onedx.common.constants.enums.subscriptions.SubscriptionStatusEnum;
import com.onedx.common.constants.enums.theme.MailActionTypeEnum;
import com.onedx.common.constants.values.CharacterConstant;
import com.onedx.common.constants.values.SubscriptionHistoryConstant;
import com.onedx.common.dto.mail.MailParamResDTO;
import com.onedx.common.dto.subscriptions.orders.DHSXKDTrackingResDTO;
import com.onedx.common.dto.subscriptions.orders.TrackingOrderServiceReqDTO;
import com.onedx.common.dto.subscriptions.orders.TrackingOrderServiceResDTO;
import com.onedx.common.entity.subscriptions.SubscriptionHistory;
import com.onedx.common.entity.systemParams.SystemParam;
import com.onedx.common.exception.ExceptionFactory;
import com.onedx.common.repository.contact.ContactProvinceRepository;
import com.onedx.common.repository.emails.mailTemplate.ParamEmailRepository;
import com.onedx.common.repository.schedule.ScheduleStatisticRepository;
import com.onedx.common.repository.subscriptions.SubscriptionHistoryRepository;
import com.onedx.common.utils.DateUtil;
import com.repository.combo.ComboPlanRepository;
import com.repository.combo.SubscriptionComboAddonCouponRepository;
import com.repository.combo.SubscriptionComboCouponRepository;
import com.repository.departments.DepartmentsRepository;
import com.repository.orderService.OrderServiceReceiveRepository;
import com.repository.pricing.PricingMultiPlanRepository;
import com.repository.pricing.PricingRepository;
import com.repository.services.ServiceRepository;
import com.repository.subscriptions.ChangeSubscriptionRepository;
import com.repository.subscriptions.SubscriptionAddonCouponRepository;
import com.repository.subscriptions.SubscriptionCouponsRepository;
import com.repository.subscriptions.SubscriptionPricingCouponRepository;
import com.repository.subscriptions.SubscriptionRepository;
import com.repository.transactionLog.TransactionLogRepository;
import com.repository.users.UserRepository;
import com.scheduled.batch.BaseBatch;
import com.scheduled.batch.BatchService;
import com.service.email.EmailService;
import com.service.integrate.IntegrationService;
import com.service.integrated.ExecutiveProducerService;
import com.service.notification.ActionNotificationService;
import com.service.notification.template.SCD01;
import com.service.notification.template.SCD08;
import com.service.notification.template.SCD09;
import com.service.subscriptions.SubscriptionNotificationService;
import com.service.subscriptions.SubscriptionService;
import com.service.system.param.SystemParamService;
import com.service.transactionLog.TransactionLogService;
import com.service.users.UserService;
import com.util.AuthUtil;
import com.util.FileUtil;
import com.util.NotifyUtil;
import com.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> HaiTD
 * @version : 1.0
 * 06/16/2021
 */
@Component("batch-subscription-history")
@Slf4j
public class SubscriptionHistoryTask extends BaseBatch implements BatchService {

    public static final String RENEWAL = "RENEWAL";
    public static final long BATCH_ID = -1L;
    public static final String SERVICE = "SERVICE";
    public static final String COMBO = "COMBO";
    public static final String COUPON = "COUPON";
    public static final String BATCH_NAME = "subscription-history";
    public static final int ADMIN = 1;
    public static final int DEV = 2;
    public static final int SME = 3;

    @Autowired
    private SubscriptionHistoryRepository subscriptionHistoryRepository;
    @Autowired
    private Environment environment;
    @Autowired
    private SubscriptionRepository subscriptionRepository;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private IntegrationService integrationService;

    @Autowired
    private PricingRepository pricingRepository;

    @Autowired
    private SystemParamService systemParamService;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private SubscriptionService subscriptionService;
    
    @Autowired
    private ComboPlanRepository comboPlanRepository;

    @Autowired
    private ExecutiveProducerService executiveProducerService;

    @Autowired
    private PricingMultiPlanRepository pricingMultiPlanRepository;

    @Autowired
    private TransactionLogRepository transactionLogRepository;
    
    @Autowired
    private TransactionLogService transactionLogService;

    @Autowired
    private SubscriptionAddonCouponRepository subscriptionAddonCouponRepository;

    @Autowired
    private SubscriptionPricingCouponRepository subscriptionPricingCouponsRepository;

    @Autowired
    private SubscriptionCouponsRepository subscriptionCouponsRepository;

    @Autowired
    private SubscriptionComboCouponRepository subscriptionComboCouponsRepository;

    @Autowired
    private SubscriptionComboAddonCouponRepository subscriptionComboAddonCouponsRepository;

    @Autowired
    private ChangeSubscriptionRepository changeSubscriptionRepository;

    @Autowired
    private BillingTask billingTask;

    @Autowired
    private ActionNotificationService actionNotificationService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ParamEmailRepository paramEmailRepository;

    @Autowired
    private SubscriptionNotificationService subscriptionNotificationService;

    @Autowired
    private ServiceRepository serviceRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private DepartmentsRepository departmentsRepository;

    @Autowired
    private ContactProvinceRepository contactProvinceRepository;

    @Autowired
    private ScheduleStatisticRepository scheduleStatisticRepository;

    @Autowired
    private ExceptionFactory exceptionFactory;

    @Autowired
    private OrderServiceReceiveRepository orderServiceReceiveRepository;

    @Value("${web.host}")
    private String webHost;

    /**
     * Tạo lịch sử khi hết hạn thời gian dùng thử
     */
    @Transactional
    @Async(value = "asyncExecutorReadMessage")
    @Description("Batch method for checking remaining trial day")
    public void checkTrailDay() {
        long start = System.currentTimeMillis();
        log.info("checkTrailDay: start");
        List<Subscription> subscriptions = subscriptionRepository.findAllByTrialDayNotNull();
        for (Subscription subscription : subscriptions) {
            // Ngày bắt đầu dùng thử
            LocalDate staredAt = DateUtil.convertDateToLocalDate(subscription.getStartedAt());
            Long trialDay = subscription.getTrialDay();
            // Ngày hết hạn
            LocalDate endDate = staredAt.plusDays(trialDay);
            LocalDate now = LocalDate.now();
            if (endDate.isAfter(now)) {
                applicationEventPublisher.publishEvent(SubscriptionHistory.builder()
                        .subscriptionId(subscription.getId())
                        .createdAt(LocalDateTime.now())
                        .content(SubscriptionHistoryConstant.END_SUBSCRIPTION_TRIAL)
                        .contentType(SubscriptionHistoryConstant.ContentType.END_SUBSCRIPTION_TRIAL)
                        .createdBy(AuthUtil.getCurrentUserId())
                        .build());
            }
        }
        long executionTime = System.currentTimeMillis() - start;
        log.info("checkTrailDay: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "checkTrailDay", executionTime);
    }

    @Async(value = "asyncExecutorSendMessage")
    @EventListener
    public void doorBellEventListener(HistorySubscription historySubscription) {
        historySubscription.setHistory();
    }

    public class HistorySubscription extends ApplicationEvent {

        public Long subscriptionId;
        public String message;


        public HistorySubscription(Object source, Long subscriptionId, String message) {
            super(source);
            this.subscriptionId = subscriptionId;
            this.message = message;
        }

        public void setHistory() {
            SubscriptionHistory subscriptionHistory = new SubscriptionHistory();
            subscriptionHistory.setSubscriptionId(subscriptionId);
            subscriptionHistory.setContent(message);
            subscriptionRepository.updateTrialDay(subscriptionId);
            subscriptionHistoryRepository.save(subscriptionHistory);
        }
    }


    /**
     * tự động kết thúc subscription khi quá ngày kết thúc subscription và quá ngày cho phép thanh toán thành công
     *
     */
    @Transactional
    @Description("Batch method for marking subscriptions expired")
    public void endSubscriptionAuto() {
        long start = System.currentTimeMillis();
        log.info("endSubscriptionAuto: start");
        SystemParam systemParam = systemParamService.findByParamType(COUPON);

        List<Long> ids = new ArrayList<>();
        List<Subscription> subscriptions = subscriptionRepository.getListExpiredSubscription(systemParam.getPaymentDateFailOff());
        subscriptions.forEach(subscription -> {
            ids.add(subscription.getId());
            Optional<Pricing> pricingOptional = pricingRepository
                .findByIdAndDeletedFlag(subscription.getPricingId(),
                    DeletedFlag.NOT_YET_DELETED.getValue());
            if (pricingOptional.isPresent()) {
                Pricing pricing = pricingOptional.get();
                if (Objects.equals(RegTypeEnum.TRIAL, RegTypeEnum.valueOf(subscription.getRegType()))){
                    subscriptionHistoryRepository.save(SubscriptionHistory.builder()
                        .subscriptionId(subscription.getId())
                        .createdAt(LocalDateTime.now())
                        .content(SubscriptionHistoryConstant.END_SUBSCRIPTION_TRIAL)
                        .contentType(SubscriptionHistoryConstant.ContentType.END_SUBSCRIPTION_TRIAL)
                        .createdBy(BATCH_ID)
                        .build());
                }
                integrationService.transactionOneSME("", subscription, pricing,
                    IntegrationActionTypeEnum.CANCEL_SUBSCRIPTION, null, null, null, false, null);
            }
            if (subscription.getAssigneeId() != null) {
                User customer = userRepository.findUserById(subscription.getUserId()).orElse(null);
                userRepository.findByIdAndDeletedFlag(subscription.getAssigneeId(), DeletedFlag.NOT_YET_DELETED.getValue())
                    .ifPresent(assignee -> actionNotificationService.send(new SCD08(assignee, customer, subscription)));
            }
            sendMailAndNotiSubExprise(subscription);
        });
        subscriptionRepository.updateSubscriptionStatus(ids);
        long executionTime = System.currentTimeMillis() - start;
        log.info("endSubscriptionAuto: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "endSubscriptionAuto", executionTime);
    }

    private void sendMailAndNotiSubExprise(Subscription subscription) {
        String title = null;
        String[] paramsContent;
        String content = null;
        //gửi thông báo cho SME admin của user đang dùng sub
        boolean isPersonal = userService.isPersonal(subscription.getUserId());
        List<User> userSmeAdmin = userRepository.getAllSmeAdmin(subscription.getUserId());
        User userUseSub = userRepository.findUserById(subscription.getUserId()).get();
        User receiverDev = userRepository.getProviderByService(subscription.getId());
        List<User> listReceiverDev = receiverDev != null ? userRepository.getAllDevAdmin(receiverDev.getId()) : new ArrayList<>();
        List<User> receiverAdmin = userUseSub != null && userUseSub.getProvinceId() != null ? userRepository.getListAdminProvince(userUseSub.getProvinceId()) : new ArrayList<>();
        ServicePricingInfo getServicePricingName = serviceRepository.getServicePricingName(subscription.getId());

        //gửi thông báo cho sme admin của user đang dùng sub khi sub tự động hủy
        title = NotifyUtil.getContent(ActionNotificationEnum.SC_03.getTitle(), null);
        paramsContent = new String[]{getServicePricingName.getServiceName(), getServicePricingName.getPricingName()};
        content = NotifyUtil.getContent(ActionNotificationEnum.SC_03.getContent(), paramsContent);
        subscriptionNotificationService.sendNotifyToSmeAdmin(userSmeAdmin, title, content, ActionNotificationEnum.SC36, subscription);

        // gửi thông báo cho dev admin/admin tỉnh của user đang dùng sub
        title = NotifyUtil.getContent(ActionNotificationEnum.SC_04.getTitle(), null);
        paramsContent = new String[]{getServicePricingName.getServiceName(), isPersonal ? userUseSub.getLastName() + " " + userUseSub.getFirstName()
                : userUseSub.getName()};
        content = NotifyUtil.getContent(ActionNotificationEnum.SC_04.getContent(), paramsContent);

        subscriptionNotificationService.sendNotifyToDevAdmin(listReceiverDev, title, content, ActionNotificationEnum.SC_04, subscription);
        subscriptionNotificationService.sendNotifyToAdminProvince(receiverAdmin, title, content, ActionNotificationEnum.SC_04, subscription);

        if (userSmeAdmin != null) {
            sendMailAllSmeAdmin(userSmeAdmin, userUseSub, subscription, getServicePricingName.getServiceName(), getServicePricingName.getPricingName(), EmailCodeEnum.SC03, isPersonal, SME);
        }

        // gửi mail cho Dev admin
        if (listReceiverDev != null) {
            sendMailAllSmeAdmin(listReceiverDev, userUseSub, subscription, getServicePricingName.getServiceName(), getServicePricingName.getPricingName(), EmailCodeEnum.SC04, isPersonal, DEV);
        }
        // gửi email cho ADMIN tỉnh
        if (receiverAdmin != null) {
            sendMailAllSmeAdmin(receiverAdmin, userUseSub, subscription, getServicePricingName.getServiceName(), getServicePricingName.getPricingName(), EmailCodeEnum.SC04, isPersonal, ADMIN);
        }
    }

    private void sendMailAllSmeAdmin(List<User> userSmeDevAdmin, User userUseService, Subscription subscription, String serviceName, String pricingName, EmailCodeEnum emailCodeEnum, boolean isPersonal, int type) {
        List<MailSendParamDTO> param = new ArrayList<>();
        Map<String, String> response = new HashMap<>();
        // lấy thông tin user sử dụng dịch vụ
        String smeName = userUseService.getLastName() != null && userUseService.getFirstName() != null ? userUseService.getLastName().concat(CharacterConstant.SPACE).concat(userUseService.getFirstName())
                : userUseService.getName();
        Department department = null;
        if (userUseService.getDepartmentId() != null) {
            department = departmentsRepository.findById(userUseService.getDepartmentId()).orElse(new Department());
        }
        String phoneProvince = department == null || department.getProvinceId() == null ?
                StringUtils.EMPTY : contactProvinceRepository.getPhoneInProvince(department.getProvinceId());
        response.put(SubscriptionConstant.MailParam.NAME_SERVICE, serviceName);
        response.put(SubscriptionConstant.MailParam.NAME_PRICING, pricingName);
        response.put(SubscriptionConstant.MailParam.SME_NAME, smeName == null ? CharacterConstant.BLANK : smeName);
        response.put(SubscriptionConstant.MailParam.HOTLINE_TINH, phoneProvince);
        userSmeDevAdmin.forEach(u -> {
            boolean isPersonalReceiver = userService.isPersonal(u);
            String actorName = isPersonalReceiver || !Objects.equals(type, DEV) || !Objects.equals(type, ADMIN) ? String.join(" ", Optional.ofNullable(u.getLastName()).orElse(""),
                    Optional.ofNullable(u.getFirstName()).orElse("")).trim() : u.getName();
            response.put(SubscriptionConstant.MailParam.USER, actorName);
            List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCodeEnum);
            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(u.getEmail());
            mailSendParamDTO.setListMailParam(mailParam);
            param.add(mailSendParamDTO);
        });
        emailService.sendMultiMail( emailCodeEnum, param);

    }

    private List<MailParamResDTO> getListPramMailDTO(Map<String, String> params, EmailCodeEnum emailCode) {
        // Get parm email
        List<MailParamResDTO> paramNameByCode = paramEmailRepository.findParamNameByCode(emailCode.getValue());
        paramNameByCode.forEach(mailParamResDTO -> {
            mailParamResDTO.setValue(params.getOrDefault(mailParamResDTO.getParamName(), mailParamResDTO.getValue()));
        });
        return paramNameByCode;
    }

    /**
     * tự động nhắc nhở gia hạn subscription khi sắp hết hạn
     *
     */
    @Description("Batch method for reminding renewal for subscription")
    public void renewalReminder() {
        long start = System.currentTimeMillis();
        log.info("renewalReminder: start");
        String linkSME = webHost + "/mail-action?subId=%s&type=" + MailActionTypeEnum.SUB_EXTEND.getValue();
        String linkDev = webHost + "/dev-portal/subscription/service/%s?clickExtendButton=true";
        String linkAdmin = webHost + "/admin-portal/subscription/service/%s?clickExtendButton=true";
        /*
         * Lấy danh sách các thuê bao cần gia hạn theo cấu hình hệ thống
         */
        SystemParam systemParam = systemParamService.findByParamType(RENEWAL);
        if (systemParam.getNumberOfDayRemind() == null) return;
        
        Set<Integer> underDaySet = new HashSet<>(Arrays.asList(1, 2, 3));
        Set<Integer> overDaySet = new HashSet<>(Arrays.asList(1, systemParam.getNumberOfDayRemind()));
        overDaySet.add(systemParam.getNumberOfDayRemind() % 2 == 0 ? systemParam.getNumberOfDayRemind()/2 : (systemParam.getNumberOfDayRemind()+1) / 2);
        List<SubscriptionRemindDayDTO> remindDayDTOListBeforeFilter = subscriptionRepository.getSubscriptionReminder(systemParam.getNumberOfDayRemind());
        // tạm thời chỉ gửi cho thuê bao ON
        List<SubscriptionRemindDayDTO> remindDayDTOListBeforeFilterSend = remindDayDTOListBeforeFilter.stream()
            .filter(i -> (i.getServiceOwner().equals(ServiceOwnerEnum.SAAS.value) || i.getServiceOwner().equals(ServiceOwnerEnum.VNPT.value))
                && Objects.equals(i.getConfirmStatus(), ConfirmStatusEnum.SUCCESS.getValue()) &&
                Objects.equals(i.getInstalled(), StatusEnum.ACTIVE.value)).collect(Collectors.toList());
        // Lấy danh sách thuê bao OS cần gửi thông báo
        List<SubscriptionRemindDayDTO> remindDayDTOListSubOthers = remindDayDTOListBeforeFilter.stream()
            .filter(i -> Objects.equals(ServiceOwnerEnum.OTHER.value, i.getServiceOwner())).collect(Collectors.toList());
        // call smeTrackingOrderDHSXKD cho đơn OS -> push các request theo batch và waiting request cuối cùng hoàn thành
        long startSmeTrackingOrderDHSXKD = System.currentTimeMillis();
        Map<Long, CompletableFuture<DHSXKDTrackingResDTO>> mapSubIdTrackingFutures = new LinkedHashMap<>();
        int maxBulkSize = 50;
        int currentBulkSize = 0;
        for (SubscriptionRemindDayDTO subOther : remindDayDTOListSubOthers) {
            if (!Objects.equals(CreatedSourceMigrationEnum.DHSXKD.getValue(), subOther.getCreatedSourceMigration())) {
                // check orderServiceReceive có tồn tại
                OrderServiceReceive orderServiceReceive = orderServiceReceiveRepository.findBySubscriptionId(subOther.getSubscriptionId())
                    .stream().findFirst().orElse(null);
                if (Objects.nonNull(orderServiceReceive)) {
                    TrackingOrderServiceReqDTO requestToDHSXKD = new TrackingOrderServiceReqDTO(orderServiceReceive.getTransactionCode(),
                        subOther.getProvinceId());
                    if (currentBulkSize < maxBulkSize) {
                        currentBulkSize++;
                    } else {
                        currentBulkSize = 1;
                        // sleep 5s -> next bulk request
                        try {
                            Thread.sleep(5000L);
                        } catch (InterruptedException ignored) {
                        }
                    }
                    mapSubIdTrackingFutures.put(subOther.getSubscriptionId(),
                        CompletableFuture.supplyAsync(() -> executiveProducerService.smeTrackingOrderDHSXKD(requestToDHSXKD, false,
                            subOther.getSubscriptionId(), orderServiceReceive, null, null)));
                }
            }
        }
        // push toàn bộ requests -> đợi request lâu nhất hoàn thành
        long numExpectedRequest = mapSubIdTrackingFutures.size();
        if (ObjectUtils.isNotEmpty(mapSubIdTrackingFutures)) {
            CompletableFuture<Void> combinedFutures = CompletableFuture.allOf(
                mapSubIdTrackingFutures.values().toArray(new CompletableFuture[0]));
            combinedFutures.join();
        }
        // mapping response tracking tương ứng
        long numActualCompletedRequest = 0L;
        for (SubscriptionRemindDayDTO subOther : remindDayDTOListSubOthers) {
            CompletableFuture<DHSXKDTrackingResDTO> trackingFutureItem = mapSubIdTrackingFutures.get(subOther.getSubscriptionId());
            if (Objects.nonNull(trackingFutureItem) && trackingFutureItem.isDone()) {
                numActualCompletedRequest++;
                TrackingOrderServiceResDTO resDTO = null;
                try {
                    resDTO = trackingFutureItem.get().getData().get(0);
                } catch (InterruptedException | ExecutionException ex) {
                    ex.printStackTrace();
                }
                if (Objects.nonNull(resDTO)) {
                    if (Objects.nonNull(resDTO.getEndContractDate()) && (Objects.isNull(subOther.getEndCurrentCycleContract()) ||
                        resDTO.getEndContractDate().compareTo(subOther.getEndCurrentCycleContract()) != 0)) {
                        subscriptionRepository.updateEndCurrentCycleContract(subOther.getSubscriptionId(), resDTO.getEndContractDate());
                    }
                    if (Objects.nonNull(resDTO.getEndContractDate()) && Objects.nonNull(subOther.getEndCurrentCycle()) &&
                        (resDTO.getEndContractDate().compareTo(subOther.getEndCurrentCycle()) <= 0)) {
                        remindDayDTOListBeforeFilterSend.add(subOther);
                    }
                }
            }
        }
        log.info("renewalReminder: smeTrackingOrderDHSXKD - Total {} requests, {} completed, {} not completed", numExpectedRequest,
            numActualCompletedRequest, numExpectedRequest - numActualCompletedRequest);
        log.info("renewalReminder: smeTrackingOrderDHSXKD end after {} ms", System.currentTimeMillis() - startSmeTrackingOrderDHSXKD);

        List<SubscriptionRemindDayDTO> remindDayDTOList = new ArrayList<>();
        remindDayDTOListBeforeFilterSend.forEach(remind -> {
            if (remind.getTotalDayUse() < systemParam.getNumberOfDayRemind()) {
                if (underDaySet.contains(remind.getDaysLeft())) remindDayDTOList.add(remind);
            } else {
                if (overDaySet.contains(remind.getDaysLeft())) remindDayDTOList.add(remind);
            }
        });

        List<SubscriptionRemindDayDTO> sendList = new ArrayList<>();
        List<SubscriptionRemindDayDTO> sendToAssigneeList = new ArrayList<>();
        remindDayDTOList.forEach(remind -> {
            sendList.add(remind);
            if (Objects.nonNull(remind.getAssigneeId()) && !remind.getPortalType().equals(PortalType.SME.getValue())) {
                sendToAssigneeList.add(remind);
            }
        });

        /*
         * Gửi thông báo gia hạn
         */
        long startNotify = System.currentTimeMillis();
        List<User> allUserSmeAdmin = userRepository.getAllSmeAdminByUserIdIn(sendList.stream().map(SubscriptionRemindDayDTO::getUserId).collect(Collectors.toSet()));
        String title = NotifyUtil.getContent(ActionNotificationEnum.SB01.getTitle(), null);
        sendList.forEach(remind -> {
            List<User> userSmeAdmin = allUserSmeAdmin.stream()
                .filter(item -> Objects.equals(item.getParentId(), remind.getUserId()) || Objects.equals(item.getId(), remind.getUserId()))
                .collect(Collectors.toList());
            User userUseSub = userRepository.findUserById(remind.getUserId()).get();
            User receiverDev = userRepository.getProviderByService(remind.getSubscriptionId());
            List<User> listReceiverDev = Objects.nonNull(receiverDev) ? userRepository.getAllDevAdmin(receiverDev.getId()) : new ArrayList<>();
            List<User> listReceiverAdmin =
                Objects.nonNull(userUseSub.getProvinceId()) ? userRepository.getListAdminProvince(
                    userUseSub.getProvinceId()) : new ArrayList<>();
            // thông báo
            String[] paramsContent;
            //gửi thông báo cho sme admin của user đang dùng sub khi sub tự động hủy
            paramsContent = new String[]{remind.getServiceName(), remind.getPricingName(), remind.getDaysLeft().toString()};
            String content = NotifyUtil.getContent(ActionNotificationEnum.SB01.getContent(), paramsContent);

            // email
            SubscriptionBillingDTO lastBill = subscriptionRepository.getSubscriptionBilling(remind.getSubscriptionId());
            String note = Objects.isNull(lastBill) ? CharacterConstant.BLANK : "Thanh toán cho chu kì " + lastBill.getCurrentCycle()
                    + " (từ " + formatDate(lastBill.getBillingDate()) + " đến " + formatDate(lastBill.getEndDate()) + ")";
            BigDecimal totalAmount = Objects.isNull(lastBill) ? BigDecimal.ZERO : lastBill.getTotalAmount();

            if (remind.getActiveDate() != 0 && ObjectUtils.isNotEmpty(userSmeAdmin)) {
                subscriptionNotificationService.sendNotify(userSmeAdmin, title, content, ActionNotificationEnum.SB01, remind.getSubscriptionId(),
                    PortalType.SME);
                sendNotiAndMailRenew(userSmeAdmin, remind, userUseSub, EmailCodeEnum.SB01, linkSME, formatMoneyVietnamese(totalAmount), note,
                    SME);
            }

            if(ObjectUtils.isNotEmpty(listReceiverDev)) {
                subscriptionNotificationService.sendNotify(listReceiverDev, title, content, ActionNotificationEnum.SB01,
                    remind.getSubscriptionId(), PortalType.DEV);
                sendNotiAndMailRenew(listReceiverDev, remind, userUseSub, EmailCodeEnum.SB02, linkDev, formatMoneyVietnamese(totalAmount), note,
                    DEV);
            }
            if (ObjectUtils.isNotEmpty(listReceiverAdmin)) {
                subscriptionNotificationService.sendNotify(listReceiverAdmin, title, content, ActionNotificationEnum.SB01,
                    remind.getSubscriptionId(), PortalType.ADMIN);
                sendNotiAndMailRenew(listReceiverAdmin, remind, userUseSub, EmailCodeEnum.SB02, linkAdmin, formatMoneyVietnamese(totalAmount), note,
                    ADMIN);
            }
        });
        log.info("renewalReminder: sendNotify end after {} ms", System.currentTimeMillis() - startNotify);

        /*
         * Gửi email cho assignee của subscription
         */
        long startEmail = System.currentTimeMillis();

        List<Long> lstAssigneeId = sendToAssigneeList.stream().map(SubscriptionRemindDayDTO::getAssigneeId).distinct()
            .collect(Collectors.toList());

        List<User> lstAssignee = userRepository.findByIdInAndDeletedFlag(lstAssigneeId, DeletedFlag.NOT_YET_DELETED.getValue());

        Map<Long, List<SubscriptionRemindDayDTO>> groupedByAssignee = sendToAssigneeList.stream()
            .collect(Collectors.groupingBy(SubscriptionRemindDayDTO::getAssigneeId));

        List<NotifScDetailDTO> lstMailParam = new ArrayList<>();

        for (User assignee : lstAssignee) {
            NotifScDetailDTO param = new NotifScDetailDTO();
            param.setAssigneeId(assignee.getId());
            param.setAssigneeEmail(assignee.getEmail());
            param.setAssigneeName(assignee.getFullName());
            List<SubscriptionRemindDayDTO> listSubsRemind = groupedByAssignee.get(assignee.getId());
            // lấy danh sách sub của current user
            if (Objects.nonNull(listSubsRemind) && !listSubsRemind.isEmpty()) {
                List<NotifScSubDetailDTO> subDetails = listSubsRemind.stream()
                    .map(subsRemind -> {
                        NotifScSubDetailDTO subDetail = new NotifScSubDetailDTO();
                        subDetail.setServiceName(subsRemind.getServiceName());
                        subDetail.setPricingName(subsRemind.getPricingName());
                        subDetail.setDaysLeft(subsRemind.getDaysLeft());
                        subDetail.setSmeName(subsRemind.getFullNameSme());
                        return subDetail;
                    })
                    .collect(Collectors.toList());
                param.setLstSubDetail(subDetails);
            }
            lstMailParam.add(param);
        }
        actionNotificationService.send(new SCD01(lstMailParam));

        log.info("renewalReminder: sendEmail end after {} ms", System.currentTimeMillis() - startEmail);

        long executionTime = System.currentTimeMillis() - start;
        log.info("renewalReminder: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "renewalReminder", executionTime);
    }

    private void sendNotiAndMailRenew(List<User> userSmeDevAdmin, SubscriptionRemindDayDTO remind, User userUseService, EmailCodeEnum emailCodeEnum, String link, String totalAmount, String note, int type) {
        List<MailSendParamDTO> param = new ArrayList<>();
        Map<String, String> response = new HashMap<>();
        // lấy thông tin user sử dụng dịch vụ
        Department department = null;
        if (userUseService.getDepartmentId() != null) {
            department = departmentsRepository.findById(userUseService.getDepartmentId()).orElse(new Department());
        }
        String phoneProvince = department == null || department.getProvinceId() == null ?
                StringUtils.EMPTY : contactProvinceRepository.getPhoneInProvince(department.getProvinceId());
        response.put(SubscriptionConstant.MailParam.NAME_SERVICE, remind.getServiceName());
        response.put(SubscriptionConstant.MailParam.NAME_PRICING, remind.getPricingName());
        response.put(SubscriptionConstant.MailParam.REMAIN_DAYS, remind.getDaysLeft().toString());
        response.put(SubscriptionConstant.MailParam.SUB_CODE, remind.getSubCode());
        response.put(SubscriptionConstant.MailParam.TOTAL_AMOUNT, totalAmount);
        response.put(SubscriptionConstant.MailParam.NOTE, note);
        response.put(SubscriptionConstant.MailParam.LINK_RENEWING, String.format(link, remind.getSubscriptionId()));
        response.put(
            SubscriptionConstant.MailParam.NAME_COMPANY,
            Objects.equals(remind.getCustomerType(), CustomerTypeEnum.PERSONAL.getValue()) ? remind.getFullNameSme() : remind.getNameSme());
        response.put(SubscriptionConstant.MailParam.HOTLINE_TINH, phoneProvince);
        userSmeDevAdmin.forEach(u -> {
            boolean isPersonalReceiver = userService.isPersonal(u);
            String actorName = isPersonalReceiver || !Objects.equals(type, DEV) || !Objects.equals(type, ADMIN) ? String.join(" ", Optional.ofNullable(u.getLastName()).orElse(""),
                    Optional.ofNullable(u.getFirstName()).orElse("")).trim() : u.getName();
            response.put(SubscriptionConstant.MailParam.USER, actorName);
            List<MailParamResDTO> mailParam = getListPramMailDTO(response, emailCodeEnum);
            MailSendParamDTO mailSendParamDTO = new MailSendParamDTO();
            mailSendParamDTO.setMailToSend(u.getEmail());
            mailSendParamDTO.setListMailParam(mailParam);
            param.add(mailSendParamDTO);
        });
        emailService.sendMultiMail( emailCodeEnum, param);

    }

    private static String formatDate(Date date) {
        return Objects.isNull(date) ? StringUtils.EMPTY : DateUtil
                .convertLocalDateToString(DateUtil.toLocalDate(date), DateUtil.FORMAT_DATE_DD_MM_YYYY_SLASH);
    }

    private static String formatMoneyVietnamese(BigDecimal totalAmount) {
        Locale localeVN = new Locale("vi", "VN");
        NumberFormat currencyVN = NumberFormat.getCurrencyInstance(localeVN);
        return currencyVN.format(totalAmount);
    }

    /**
     * Tự động chuyển trang thái sub từ đang chờ thành hoạt động hoac dung thu
     *
     */
    @Description("Batch method for changing subscription status from WAITING TO ACTIVE/TRIAL")
    public void stateChange() {
        long start = System.currentTimeMillis();
        log.info("stateChange: start");
        subscriptionRepository.updateSubscriptionStatusByFuture();

        long executionTime = System.currentTimeMillis() - start;
        log.info("stateChange: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "stateChange", executionTime);
    }

    /**
     * tự động chuyển trang thái sub từ đã hủy thành hoạt động theo reactive_date
     *
     */
    @Description("Batch method for changing subscription status from CANCELLED to REACTIVE")
    public void stateChangeByReactiveDate() {
        long start = System.currentTimeMillis();
        log.info("stateChangeByReactiveDate: start");
        List<Subscription> subscriptions = subscriptionRepository.getListSubReactive();
        for (Subscription subscription : subscriptions) {
            Optional<Pricing> pricingOptional = pricingRepository
                    .findByIdAndDeletedFlag(subscription.getPricingId(),
                            DeletedFlag.NOT_YET_DELETED.getValue());
            if (pricingOptional.isPresent()) {
                Pricing pricing = pricingOptional.get();
                Long paymentCyclePmp = 1L;
                if (Objects.nonNull(subscription.getPricingMultiPlanId())) {
                    paymentCyclePmp = pricingMultiPlanRepository.getPaymentCycle(subscription.getPricingMultiPlanId());
                }

                Integer paymentCycle = Objects.nonNull(pricing) ? pricing.getPaymentCycle() != null ? pricing.getPaymentCycle() : Math.toIntExact(paymentCyclePmp) : null;
                Integer cycleTypePricing = Objects.nonNull(pricing) ? pricing.getCycleType() : null;
                Integer cycleType = Objects.isNull(subscription.getCycleType()) ? cycleTypePricing : subscription.getCycleType();
                subscription.setStartedAt(new Date());
                subscription.setStartCurrentCycle(new Date()); // ngày bắt đầu sd chu kỳ mới
                subscription.setReactiveStatus(0);
                // ngày thanh toán chu kỳ hiện tại
                subscription.setCurrentPaymentDate(new Date());

                if (Objects.nonNull(subscription.getTypeReactive()) && subscription.getTypeReactive() == 0) {
                    // ngày thanh toán chu kỳ tiếp theo khi sd chu ky thanh toan cu
                    subscription.setNextPaymentTime(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getReactiveDate(),
                                    paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                                    2)));
                } else {
                    // ngày thanh toán chu kỳ tiếp theo
                    subscription.setNextPaymentTime(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getReactiveDate(),
                                    paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                                    1)));
                }

                subscription.setStartedAt(subscription.getReactiveDate());
                if (subscription.getCurrentCycleReactive() != null && subscription.getNumberOfCycles() != null && subscription.getNumberOfCycles() != -1) {
                    subscription.setCurrentCycle(subscription.getCurrentCycleReactive());
                } else if (subscription.getTypeReactive() != null && subscription.getTypeReactive().equals(ChangeContinueEnum.CHANGE.value)) {
                    subscription.setCurrentCycle(subscription.getCurrentCycle() + 1);
                }
                if (Objects.nonNull(subscription.getTypeReactive()) && subscription.getTypeReactive() != 0) { // ko sử dụng chu kỳ cũ
                    subscription.setStartCurrentCycle(subscription.getReactiveDate()); // ngày bắt đầu sd chu kỳ mới
                }
                // ngày kết thúc chu kỳ mới
                LocalDate newEndCurrentCycle = DateUtil
                        .calculateCycleDate(subscription.getReactiveDate(), paymentCycle,
                                CycleTypeEnum.valueOf(cycleType), true, 1);
                subscription.setEndCurrentCycle((DateUtil.toDate(newEndCurrentCycle)));

                // ngày thanh toán chu kỳ hiện tại
                subscription.setCurrentPaymentDate(new Date());

                if (Objects.nonNull(subscription.getTypeReactive()) && subscription.getTypeReactive() == 0) {
                    // ngày thanh toán chu kỳ tiếp theo khi sd chu ky thanh toan cu
                    subscription.setNextPaymentTime(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getReactiveDate(),
                                    paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                                    2)));
                } else {
                    // ngày thanh toán chu kỳ tiếp theo
                    subscription.setNextPaymentTime(
                            DateUtil.convertLocalDateToDate(DateUtil.calculateCycleDate(subscription.getReactiveDate(),
                                    paymentCycle, CycleTypeEnum.valueOf(cycleType), false,
                                    1)));
                }

                if (!Objects.isNull(subscription.getNumberOfCycles()) && subscription.getNumberOfCycles() != -1L) {
                    LocalDate newExpiredTime = DateUtil
                            .calculateCycleDate(subscription.getReactiveDate(), paymentCycle,
                                    CycleTypeEnum.valueOf(subscription.getCycleType()), true,
                                    subscription.getNumberOfCycles() - subscription.getCurrentCycle() +1);
                    subscription.setExpiredTime(DateUtil.toDate(newExpiredTime));
                }

                subscription.setStatus(SubscriptionStatusEnum.ACTIVE.value);
                subscription.setReactiveDate(null); // kích hoạt xong xóa ngày yêu cầu kích hoạt ( TH hủy lại nhưng chưa đc yêu cầu kích hoạt )
                subscriptionRepository.save(subscription);

                // Lưu lịch sử thay đổi trạng thái thuê bao ( gọi sang SPDV)
                integrationService.transactionOneSME("", subscription, pricing,
                        IntegrationActionTypeEnum.REACTIVE_SUBSCRIPTION, null, null, null, false, null);

                // Call API update Subscription DHSXKD
                if (executiveProducerService.checkCallApiDHSXKD(subscription)) {
                    executiveProducerService.callApiUpdateSubDHSXKD(subscriptionRepository.getProvinceIdByCreateBy(subscription.getUserId()),
                            subscription, 1, subscription.getSubscriptionContractId(), null);
                }

                //phần này gửi email cho assignee
                User customer = userRepository.findByIdAndDeletedFlag(subscription.getUserId(), DeletedFlag.NOT_YET_DELETED.getValue())
                    .orElse(null);
                userRepository.findByIdAndDeletedFlag(subscription.getAssigneeId(), DeletedFlag.NOT_YET_DELETED.getValue())
                    .ifPresent(assignee -> actionNotificationService.send(new SCD09(assignee, customer, subscription)));
            }
        }

        long executionTime = System.currentTimeMillis() - start;
        log.info("stateChangeByReactiveDate: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "stateChangeByReactiveDate", executionTime);
    }

    /**
     * tự động đổi gói trong subscription
     *
     */
    @Description("Batch method for changing pricing in subscription")
    public void swapPricingByBatch() {
        long start = System.currentTimeMillis();
        log.info("swapPricingByBatch: start");
        List<Long> subscriptionIds = subscriptionRepository.findBySubscriptionIdsByChangeStatus();
        ArrayList<Subscription> clonedSubscriptionList = new ArrayList<>();

        List<Subscription> lstSubscription = subscriptionRepository.findAllByIdIn(new HashSet<>(subscriptionIds));
        for (Subscription subscription : lstSubscription) {
            swapPricingSingle(clonedSubscriptionList, subscription);
        }

        long executionTime = System.currentTimeMillis() - start;
        log.info("swapPricingByBatch: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "swapPricingByBatch", executionTime);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW) // process each subscription in one transaction
    public void swapPricingSingle(ArrayList<Subscription> clonedSubscriptionList, Subscription subscription) {
        try {
            long startTime = System.currentTimeMillis();
            Long subId = subscription.getId();
            log.info("start swapPricingByBatch subscriptionID: " + subId + " at: " + startTime);
            TransactionLog transactionLog = transactionLogService.addTransactionLog(
                Objects.nonNull(subscription.getComboPlanId())
                    ? subscription.getComboPlanId()
                    : subscription.getPricingId(),
                subscription.getUserId(),
                Objects.nonNull(subscription.getComboPlanId()) ? COMBO : SERVICE,
                TransactionCodeEnum.CHANGE.getValue(), subscription.getId(),
                subscription.getPricingMultiPlanId());
            CommonActivityLogInfoDTO activityLogInfoDTO = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.CHANGE_PLAN, transactionLog);

            Pricing oldPricing = pricingRepository
                .findByIdAndDeletedFlag(subscription.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue())
                .orElseThrow(() -> exceptionFactory.resourceNotFound(Resources.PRICING, ErrorKey.ID,
                    String.valueOf(subscription.getPricingId())));
            boolean checkServiceON = Boolean.TRUE;
            ServiceEntity serviceEntity = subscription.getServiceId() != null ? serviceRepository.findById(subscription.getServiceId()).get() : null;
            if (Objects.nonNull(serviceEntity) && Objects.nonNull(serviceEntity.getServiceOwner())
                    && (Objects.equals(serviceEntity.getServiceOwner(), ServiceTypeEnum.NONE.getValue()) || Objects.equals(serviceEntity.getServiceOwner(), ServiceTypeEnum.OTHER.getValue()))) {
                checkServiceON = Boolean.FALSE;
            }
            ChangeSubscription changeSubscription = changeSubscriptionRepository.findBySubscriptionIdAndStatusAndAction(subId,StatusEnum.INACTIVE.value, ActionChangeSubEnum.END_OF_PERIOD.value);
            boolean createBillEndOfCycle = Objects.nonNull(changeSubscription) && Objects.equals(changeSubscription.getChangePricingPaymentTime(), ChangePricingPaymentTimeEnum.END_OF_PERIOD.getValue());
            if (Objects.nonNull(subscription.getPricingId())) {
                subscriptionService.swapPricingByBatch(subscription, ActionChangeSubEnum.END_OF_PERIOD.value, activityLogInfoDTO);
                addToCloneSubscriptionLst(clonedSubscriptionList, subscription);
                if (checkServiceON) {
                    saveHistoryAndSendKafkaPricing(subscription, clonedSubscriptionList, activityLogInfoDTO);
                }
            } else if (Objects.nonNull(subscription.getComboPlanId())) {
                subscriptionService.swapComboPlanByBatch(subscription, ActionChangeSubEnum.END_OF_PERIOD.value, activityLogInfoDTO);
                addToCloneSubscriptionLst(clonedSubscriptionList, subscription);
                saveHistoryAndSendKafkaComBo(subscription, clonedSubscriptionList, activityLogInfoDTO);
            }
            // Update status transaction
            transactionLogService.updateTransactionLogCompleted(activityLogInfoDTO.getTransactionLogDB(), activityLogInfoDTO);

            List<SubscriptionAddonCoupon> subscriptionAddonCoupons = subscriptionAddonCouponRepository
                .findAllBySubscriptionId(subscription.getId());
            List<SubscriptionPricingCoupon> subscriptionPricingCoupons = subscriptionPricingCouponsRepository
                .findBySubscriptionId(subscription.getId());
            List<SubscriptionCoupons> subscriptionCoupons = subscriptionCouponsRepository.findAllBySubscriptionId(subscription.getId());
            List<SubscriptionComboCoupon> subscriptionComboCoupons = subscriptionComboCouponsRepository
                .findBySubscriptionId(subscription.getId());
            List<SubscriptionComboAddonCoupon> subscriptionComboAddonCoupons = subscriptionComboAddonCouponsRepository
                .findAllBySubscriptionId(subscription.getId());

            CouponSubscriptionDTO couponAll = new CouponSubscriptionDTO(subscriptionAddonCoupons, subscriptionPricingCoupons,
                subscriptionCoupons, subscriptionComboCoupons, subscriptionComboAddonCoupons);
            // Nếu là TH đổi gói cuối chu kỳ nhưng thanh toán ngay (tức là đã thanh toán trước đó rồi, thì không tạo bill nữa)
            // Nếu là TH đổi gói cuối chu kỳ nhưng thanh toán tự động vào cuối chu kỳ - thì sẽ xử lý sau
            Integer changePricingDate = oldPricing.getChangePricingDate();
            if (createBillEndOfCycle || (Objects.nonNull(changePricingDate) &&
                Objects.equals(changePricingDate, PricingCancelTimeActiveEnum.END_OF_PERIOD.value) && Objects
                .equals(oldPricing.getChangePricingPaymentTime(), ChangePricingPaymentTimeEnum.END_OF_PERIOD.getValue()))) {
                billingTask.createBillingAsync(subscription, subscription.getPricing(), subscription.getComboPlan(), couponAll,
                    ActionTypeEnum.CHANGE_PLAN.getValue());
            }
            log.info(" finish swapPricingByBatch subscriptionID: " + subscription.getId() + "  spend(ms) : " + (System.currentTimeMillis()
                - startTime));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void addToCloneSubscriptionLst(ArrayList<Subscription> clonedSubscriptionList, Subscription subscription) {
        try {
            clonedSubscriptionList.add((Subscription) subscription.clone());
        } catch (CloneNotSupportedException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * Lưu lịch sử subs và gưi kafka cho pricing
     *
     */
    private void saveHistoryAndSendKafkaPricing(Subscription subscription,
            ArrayList<Subscription> clonedSubscriptionList, CommonActivityLogInfoDTO activityLogInfoDTO) {
        Optional<Pricing> pricingOptional = pricingRepository.findByIdAndDeletedFlag(
                subscription.getPricingId(), DeletedFlag.NOT_YET_DELETED.getValue());
        Optional<Long> pricingId = clonedSubscriptionList.stream()
                .filter(e -> Objects.equals(subscription.getId(), e.getId()))
                .map(Subscription::getPricingId).findFirst();
        if (pricingOptional.isPresent() && pricingId.isPresent()) {
            Pricing pricing = pricingOptional.get();
            integrationService.transactionOneSME("", subscription, pricing,
                    IntegrationActionTypeEnum.CHANGE_PLAN, null, null, null, false, activityLogInfoDTO);
        }
    }

    /**
     * Lưu lịch sử subs và gưi kafka cho combo
     *
     */
    private void saveHistoryAndSendKafkaComBo(Subscription subscription,
            ArrayList<Subscription> clonedSubscriptionList, CommonActivityLogInfoDTO activityLogInfoDTO) {
        Optional<ComboPlan> comboPlanOptional = comboPlanRepository.findByIdAndDeletedFlag(
                subscription.getComboPlanId(), DeletedFlag.NOT_YET_DELETED.getValue());
        Optional<Long> comboPlanId = clonedSubscriptionList.stream()
                .filter(e -> Objects.equals(subscription.getId(), e.getId()))
                .map(Subscription::getComboPlanId).findFirst();
        if (comboPlanOptional.isPresent() && comboPlanId.isPresent()) {
            Optional<ComboPlan> comboPlanOptionalOld = comboPlanRepository.findByIdAndDeletedFlag(
                    comboPlanId.get(), DeletedFlag.NOT_YET_DELETED.getValue());
            ComboPlan comboPlan = comboPlanOptional.get();
            String hisContent = String.format(SubscriptionHistoryConstant.CHANGE_PRICING,
                    comboPlanOptionalOld.get().getComboName(),
                    comboPlanOptional.get().getComboName());
            subscriptionHistoryRepository
                    .save(SubscriptionHistory.builder().subscriptionId(subscription.getId())
                            .createdAt(LocalDateTime.now()).content(hisContent)
                            .contentType(SubscriptionHistoryConstant.ContentType.CHANGE_PRICING)
                            .createdBy(AuthUtil.getCurrentUserId()).build());
            integrationService.transactionOneSMECombo("", subscription, comboPlan,
                    IntegrationActionTypeEnum.CHANGE_PLAN, null, null, null, null, activityLogInfoDTO);
        }
    }
    
    /**
     * tự động update trong subscription
     *
     */
    @Transactional
    @Description("Batch method for updating pricing in subscription")
    public void updatePricingByBatch() {
        long start = System.currentTimeMillis();
        log.info("updatePricingByBatch: start");
        List<Subscription> subscriptions = subscriptionRepository.findBySubscriptionByUpdateStatus();
        if (!CollectionUtils.isEmpty(subscriptions)) {
            subscriptions.forEach(subscription -> {
                TransactionLog transactionLog = transactionLogService.addTransactionLog(
                        Objects.nonNull(subscription.getComboPlanId())
                                ? subscription.getComboPlanId()
                                : subscription.getPricingId(),
                        subscription.getUserId(),
                        Objects.nonNull(subscription.getComboPlanId()) ? COMBO : SERVICE,
                        TransactionCodeEnum.UPDATE.getValue(), subscription.getId(),
                        subscription.getPricingMultiPlanId());
                CommonActivityLogInfoDTO activityLogInfoDTO = new CommonActivityLogInfoDTO(IntegrationActionTypeEnum.UPDATE_SUBSCRIPTION, transactionLog);

                if (Objects.nonNull(subscription.getPricingId())) {
                    subscriptionService.updatePricingByBatch(subscription.getId(), ActionChangeSubEnum.END_OF_PERIOD.value, activityLogInfoDTO);
                } else if (Objects.nonNull(subscription.getComboPlanId())) {
                    subscriptionService.updateComboByBatch(subscription, ActionChangeSubEnum.END_OF_PERIOD.value, activityLogInfoDTO);
                }
                // Update status transaction
                transactionLogService.updateTransactionLogCompleted(transactionLog, activityLogInfoDTO);
            });
        }

        long executionTime = System.currentTimeMillis() - start;
        log.info("updatePricingByBatch: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "updatePricingByBatch", executionTime);
    }

    /**
     * Cài đặt xóa lịch sử giao dịch
     */
    @Transactional
    @Description("Batch method for deleting transaction logs")
    public void deleteTransactionLogByBatch() {
        long start = System.currentTimeMillis();
        log.info("deleteTransactionLogByBatch: start");
        SystemParam systemParam = systemParamService.findByParamType(SystemParamConstant.PARAM_TRANSACTION_LOG_DELETE_CONFIG);
        LocalDate deleteTimeConfig = LocalDate.now();
        if (Objects.equals(systemParam.getTimeType(), TimeTypeEnum.MONTHLY.value)) {
            deleteTimeConfig = deleteTimeConfig.minusMonths(systemParam.getTimeValue());
        } else if (Objects.equals(systemParam.getTimeType(), TimeTypeEnum.WEEKLY.value)) {
            deleteTimeConfig = deleteTimeConfig.minusWeeks(systemParam.getTimeValue());
        }
        transactionLogRepository.deleteTransactionLogByConfigSystem(systemParam.getStatus(), deleteTimeConfig);

        long executionTime = System.currentTimeMillis() - start;
        log.info("deleteTransactionLogByBatch: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "deleteTransactionLogByBatch", executionTime);
    }

    @Transactional
    @Async(value = "asyncExecutorSingleton")
    @Description("Batch method for create daily subscription report for syncing")
    public void createDailySubscriptionReport() throws SQLException, IOException {
        long start = System.currentTimeMillis();
        log.info("createDailySubscriptionReport: start");

        Connection connection = null;
        try {
            ByteArrayOutputStream outputStream;
            DataSource dataSource = SpringContextUtils.getBean(DataSource.class);
            connection = dataSource.getConnection();
            CopyManager copyManager = new CopyManager((BaseConnection) connection.unwrap(PGConnection.class));
            outputStream = new ByteArrayOutputStream();
            outputStream.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}); // Add UTF-8 BOM marker bytes
            copyManager.copyOut("COPY (SELECT * FROM vnpt_dev.view_spc_supportonesme_429) TO STDOUT WITH (FORMAT CSV, ENCODING UTF8, DELIMITER '|')",
                outputStream);
            // Lưu file vào /resources
            String dirName = "onebss";
            String fileName = "ONEBSS_" + new SimpleDateFormat(DateUtil.FORMAT_DATE_YYYYMMDD_HHMMSS).format(new Date()) + ".csv";
            FileUtil.uploadFile(environment, outputStream, dirName, fileName);
            outputStream.close();
        } finally {
            // Ngăn chặn leak connection
            if(Objects.nonNull(connection)){
                connection.close();
            }
        }

        long executionTime = System.currentTimeMillis() - start;
        log.info("createDailySubscriptionReport: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "createDailySubscriptionReport", executionTime);
    }

    @Transactional
    @Async(value = "asyncExecutorSingleton")
    @Description("Batch method for create daily subscription report for syncing")
    public void createDailySubscriptionReportTest() throws SQLException, IOException {
        long start = System.currentTimeMillis();
        log.info("createDailySubscriptionReportTest: start");

        Connection connection = null;
        try {
            ByteArrayOutputStream outputStream;
            DataSource dataSource = SpringContextUtils.getBean(DataSource.class);
            connection = dataSource.getConnection();
            CopyManager copyManager = new CopyManager((BaseConnection) connection.unwrap(PGConnection.class));
            outputStream = new ByteArrayOutputStream();
            outputStream.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}); // Add UTF-8 BOM marker bytes
            copyManager.copyOut("COPY (SELECT * FROM vnpt_dev.view_spc_supportonesme_429_test) TO STDOUT WITH (FORMAT CSV, ENCODING UTF8, DELIMITER '|')",
                outputStream);
            // Lưu file vào /resources
            String dirName = "onebss";
            String fileName = "ONEBSS_" + new SimpleDateFormat(DateUtil.FORMAT_DATE_YYYYMMDD_HHMMSS).format(new Date()) + ".csv";
            FileUtil.uploadFile(environment, outputStream, dirName, fileName);
            outputStream.close();
        } finally {
            // Ngăn chặn leak connection
            if(Objects.nonNull(connection)){
                connection.close();
            }
        }

        long executionTime = System.currentTimeMillis() - start;
        log.info("createDailySubscriptionReportTest: end after {} ms", executionTime);
        scheduleStatisticRepository.updateExecutionTime(BATCH_NAME, "createDailySubscriptionReport", executionTime);
    }

}

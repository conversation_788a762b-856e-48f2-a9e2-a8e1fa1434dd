package com.onedx.onesme.common.configurations.db;

import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

public class RoutingDataSource extends AbstractRoutingDataSource {

    public enum DBRoute {
        WRITEABLE, READONLY
    }

    private static final ThreadLocal<DBRoute> routeContext = new ThreadLocal<>();

    public static void setSlaveRoute() {
        routeContext.set(DBRoute.READONLY);
    }

    public static void clearSlaveRoute() {
        routeContext.remove();
    }

    @Override
    public Object determineCurrentLookupKey() {
        return routeContext.get();
    }
}

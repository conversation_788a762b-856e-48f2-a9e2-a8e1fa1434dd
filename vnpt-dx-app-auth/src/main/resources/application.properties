spring.profiles.active=db70
spring.main.allow-bean-definition-overriding=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=500MB
storage.path=/
url.download=https://www.journaldev.com/sitemap.xml
file.path = upload/file/
resource.file = {0}/{1}
master.app.jwtSecret= masterSecret
master.app.jwtExpirationMs= 3600000
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
spring.cache.type=redis
# Time to live (5 minutes)
spring.cache.redis.time-to-live=300000
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=dxAuthCache@

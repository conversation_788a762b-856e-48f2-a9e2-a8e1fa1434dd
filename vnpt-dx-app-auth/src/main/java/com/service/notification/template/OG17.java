package com.service.notification.template;

import java.util.HashMap;
import java.util.List;
import com.enums.ActionNotificationEnum;
import com.model.dto.actionNotification.EmailParamDTO;
import com.model.entity.security.User;
import com.service.notification.impl.ActionNotificationTemplateBase;

public class OG17 extends ActionNotificationTemplateBase {
    public OG17(List<User> lstUser, String inviterName, String loginLink) {

        // Type
        this.actionNotificationEnum = ActionNotificationEnum.OG17;

        // Email
        for (User user : lstUser) {
            EmailParamDTO emailParamDTO = new EmailParamDTO();
            HashMap<String, String> lstParam = emailParamDTO.getLstParam();
            emailParamDTO.setEmail(user.getEmail());

            lstParam.put("$USER", user.getFullName());
            lstParam.put("$CUSTOMER_EMAIL", user.getEmail());
            lstParam.put("$ENTERPRISE_NAME", inviterName);
            lstParam.put("$LINK_ACTIVE", loginLink);

            this.lstEmailParam.add(emailParamDTO);

        }
    }
}

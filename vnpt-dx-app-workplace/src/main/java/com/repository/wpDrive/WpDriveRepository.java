package com.repository.wpDrive;

import com.constant.enums.drive.WpDriveFileTypeEnum;
import com.constant.enums.drive.WpDriveShareTypeEnum;
import com.constant.sql.SQLDrive;
import com.dto.drive.*;
import com.entity.wpDrive.WpDrive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import java.util.Date;
import java.util.Optional;

@Repository
public interface WpDriveRepository extends JpaRepository<WpDrive, Long> {
    List<WpDrive> findAllByIdIn(List<Long> ids);

    List<WpDrive> getAllByKeyStartsWithAndFileNameContainsAndCreatedBy(String characterKey, String characterFileName, Long createBy);

    List<WpDrive> getAllByKeyStartsWithAndCreatedBy(String characterKey, Long createBy);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = SQLDrive.UPDATE_DRIVE_SIZE_BY_KEY_NAME_AND_CREATED_BY)
    void updateDriveSizeByKeyNameAndCreatedBy(List<String> keyNames, Long size, Long createdBy);

    List<WpDrive>  getAllByKeyStartsWith(String characterKey);

    List<WpDrive> findAllByFileNameAndKeyAndFileTypeAndCreatedBy(String fileName, String key, WpDriveFileTypeEnum fileType, Long createdBy);

    Optional<WpDrive> findFirstByFileNameAndCreatedByAndKey(String fileName, Long createdBy, String key);

    List<WpDrive> findAllByCreatedBy(Long createdBy);

    @Query(nativeQuery = true, value = SQLDrive.GET_LIST_FOLDER_FILE)
    List<FileFolderListITF> getLstFileFolder(String screen, List<Integer> type, Long fileSize, List<Long> ownerId, Integer searchDateType, Date startDate, Date endDate, String searchValue, Long userId, List<Long> userIdsInCompany);

    @Query(nativeQuery = true, value = SQLDrive.GET_FOLDER_FILE_DETAIL)
    List<FileFolderDetailITF> getFileFolderDetail(String screen, Long userId, List<Long> userIdsInCompany);

    @Query(nativeQuery = true, value = SQLDrive.GET_LIST_FOLDER_FILE_INSIDE_FOLDER)
    List<FileFolderListITF> getLstFileFolderInsideFolder(String screen, Long userId, List<Long> userIdsInCompany);

    @Query(nativeQuery = true, value = SQLDrive.GET_FOLDER_FILE_SHARE)
    List<MemberShareITF> getFileFolderShare(Long id);

    @Query(nativeQuery = true, value = SQLDrive.GET_FOLDER_FILE_ACTIVITY_LOG)
    List<HistoryLogITF> getFileFolderActivityLog(Long id);

    @Query(nativeQuery = true, value = SQLDrive.GET_MAX_PIN)
    Integer getMaxPin(Long userId);

    Optional<WpDrive> findFirstById(Long aLong);

    Optional<WpDrive> findFirstByIdAndCreatedBy(Long id, Long createBy);

    List<WpDrive> findAllByIdInAndCreatedBy(List<Long> ids, Long createBy);

    @Query(nativeQuery = true, value = SQLDrive.GET_ALL_SIZE_BY_USER_ID)
    Long getTotalSizeByUserId(Long userId);

    List<WpDrive> findAllByIdInAndCreatedByInAndShareTypeNot(List<Long> ids, List<Long> userIds, WpDriveShareTypeEnum type);

}

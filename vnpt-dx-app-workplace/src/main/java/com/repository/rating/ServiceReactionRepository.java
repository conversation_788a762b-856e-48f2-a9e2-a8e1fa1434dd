package com.repository.rating;


import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.constant.sql.SQLRating;
import com.dto.services.ApplicationSearchDTO;
import com.entity.rating.ServiceReaction;
import com.onedx.common.repository.CustomJpaRepository;

@Repository
public interface ServiceReactionRepository extends CustomJpaRepository<ServiceReaction, Long> {

    ServiceReaction getByServiceIdAndTypeAndUserId(Long id, Integer type, Long userId);

    @Query(nativeQuery = true, value = SQLRating.GET_REACTION)
    List<ApplicationSearchDTO> getReactionWorkPlace(Long userId);
}

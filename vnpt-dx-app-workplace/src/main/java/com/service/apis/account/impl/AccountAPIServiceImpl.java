package com.service.apis.account.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.dto.apis.ThirdPartyAPIResDTO;
import com.dto.apis.ThirdPartyErrorAPIResDTO;
import com.dto.apis.account.request.AccountAPIReqDTO;
import com.dto.apis.account.request.UserMetadataReqDTO;
import com.dto.apis.account.response.AccountAPIListResDTO;
import com.dto.apis.account.response.AccountAPIResDTO;
import com.dto.apis.account.response.AccountWPAPIResDTO;
import com.dto.apis.account.response.IAccountAPIBaseResDTO;
import com.dto.apis.account.response.IAccountAPIResDTO;
import com.dto.apis.role.response.RoleEmployeeAPIResDTO;
import com.entity.role.WPRole;
import com.entity.role.WPRoleEmployee;
import com.entity.user.User;
import com.entity.user.WPMetadataUser;
import com.onedx.common.constants.enums.DeletedFlag;
import com.onedx.common.constants.enums.security.apis.AccountErrorCodeEnum;
import com.onedx.common.constants.enums.security.apis.RoleErrorCodeEnum;
import com.repository.role.WPRoleEmployeeRepository;
import com.repository.role.WPRoleRepository;
import com.repository.user.UserRepository;
import com.repository.user.WPMetadataUserRepository;
import com.service.apis.account.AccountAPIService;
import com.service.redis.RedisService;
import com.util.AuthUtil;
import com.onedx.common.utils.HttpRestUtil;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AccountAPIServiceImpl implements AccountAPIService {

    @Autowired
    UserRepository userRepository;
    @Autowired
    RedisService redisService;
    @Autowired
    TokenStore tokenStore;
    @Autowired
    WPRoleRepository wpRoleRepository;
    @Autowired
    WPRoleEmployeeRepository wpRoleEmployeeRepository;
    @Autowired
    WPMetadataUserRepository wpMetadataUserRepository;

    @Autowired
    HttpRestUtil httpUtil;

    @Value(value = "${oneSme.url-api-create-employee-3rd-party-endpoint}")
    private String createEmployeeThirdPartyEndPoint;
    @Value(value = "${oneSme.url-api-update-employee-3rd-party-endpoint}")
    private String updateEmployeeThirdPartyEndPoint;
    @Value(value = "${dxservice.auth}")
    private String dxServiceAuth;
    private static final String WORKPLACE_CLIENT_ID = "vnpt_workplaceid";


    @Transactional
    @Override
    public ThirdPartyAPIResDTO registerEmployeeAccount(AccountAPIReqDTO accountAPIReqDTO, String apiKey) {

        AccountWPAPIResDTO accountWPAPIResDTO = httpUtil.callRest(dxServiceAuth + createEmployeeThirdPartyEndPoint, HttpMethod.POST,
            null, accountAPIReqDTO, AccountWPAPIResDTO.class, MediaType.APPLICATION_JSON_VALUE);

        if (Objects.nonNull(accountWPAPIResDTO.getAccountDTO())) {
            Set<String> lstRoleCodeUpdate = accountAPIReqDTO.getLstRoleCode();
            if (Objects.nonNull(accountWPAPIResDTO.getAccountDTO().getId())) {
                Set<String> lstRoleCodeDB = wpRoleRepository.findListRoleCodeByUserId(accountWPAPIResDTO.getAccountDTO().parentId, apiKey);
                lstRoleCodeUpdate.removeIf(roleCode -> !lstRoleCodeDB.contains(roleCode));
            }
            if (Objects.nonNull(accountWPAPIResDTO.getAccountDTO().getId())) {
                addRoleEmployee(accountWPAPIResDTO.getAccountDTO().getId(), accountWPAPIResDTO.getAccountDTO().parentId, lstRoleCodeUpdate, apiKey);
            }
        }
        return getWPAccountAPIResDTO(accountWPAPIResDTO);
    }

    @Transactional
    @Override
    public ThirdPartyErrorAPIResDTO deleteEmployeeAccount(UUID employeeUUID, UUID smeUUID, String apiKey) {

        Long employeeAccountDBId = userRepository.findEmployeeIdByApiKeyAndUUID(employeeUUID, smeUUID, apiKey);
        if(Objects.isNull(employeeAccountDBId)) {
            return new ThirdPartyErrorAPIResDTO(AccountErrorCodeEnum.EMPLOYEE_NOT_FOUND);
        }

        //Xóa nhân viên
        userRepository.deleteEmployeeRoleById(employeeAccountDBId);
        // Xóa employee khỏi role được phân quyền
        wpRoleRepository.removeListEmployeeRoleByEmployeeId(Collections.singletonList(employeeAccountDBId));
        String usernameDelete = userRepository.findUserNameById(employeeAccountDBId);
        userRepository.deleteEmployeeIn(Collections.singletonList(employeeAccountDBId));

        // Xóa token dăng nhập
        revokeAllTokenBy(usernameDelete, WORKPLACE_CLIENT_ID);
        return new ThirdPartyErrorAPIResDTO(AccountErrorCodeEnum.SUCCESS);
    }


    @Transactional
    @Override
    public ThirdPartyAPIResDTO updateEmployeeAccount(UUID employeeUUID, AccountAPIReqDTO accountAPIReqDTO, String apiKey) {

        AccountWPAPIResDTO accountWPAPIResDTO = httpUtil.callRest(dxServiceAuth + updateEmployeeThirdPartyEndPoint,
            HttpMethod.PUT, AuthUtil.getCurrentBearerSMEToken(), accountAPIReqDTO, AccountWPAPIResDTO.class, MediaType.APPLICATION_JSON_VALUE);
        // Lưu thông tin role trên workplace
        if (!accountAPIReqDTO.getLstRoleCode().isEmpty()) {
            if (Objects.equals(accountWPAPIResDTO.getErrorCode(), AccountErrorCodeEnum.SUCCESS.getErrorValue())) {
                Long employeeAccountDBId = userRepository.findEmployeeIdByApiKeyAndUUID(employeeUUID, accountAPIReqDTO.getEnterpriseUUID(), apiKey);
                updateRoleEmployee(employeeAccountDBId, accountWPAPIResDTO.getAccountDTO().getParentId(), accountAPIReqDTO.getLstRoleCode(), apiKey);
            }
        }
        return getWPAccountAPIResDTO(accountWPAPIResDTO);
    }

    @Override
    public ThirdPartyAPIResDTO getDetailEmployee(UUID employeeUUID, UUID smeUUID, String apiKey) {

        // Validate tồn tại thông tin nhân viên
        Long smeId = userRepository.findEnterpriseIdByApiKeyAndUUID(smeUUID, apiKey);
        if(Objects.isNull(smeId)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.ENTERPRISE_NOT_FOUND, null);
        }

        // Validate tồn tại thông tin nhân viên
        Long employeeId = userRepository.findEmployeeIdByApiKeyAndUUID(employeeUUID, smeUUID, apiKey);
        if(Objects.isNull(employeeId)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.EMPLOYEE_NOT_FOUND, null);
        }

        //Lấy thông tin chi tiết nhân viên
        IAccountAPIResDTO iAccountAPIResDTO = userRepository.getDetailEmployee(employeeId, "", "");
        Set<WPRole> wpRoles = wpRoleRepository.findByCodeIn(wpRoleRepository.findListRoleCodeByEmployeeId(employeeId, smeId, apiKey));
        String roleName = String.join(", ", wpRoles.stream().map(WPRole::getName).collect(Collectors.toSet()));
        return getSuccessAccountAPIResDTO(iAccountAPIResDTO, roleName, smeId);
    }

    @Override
    public ThirdPartyAPIResDTO searchEmployee(UUID smeUUID, String name, String email, String phoneNumber, String apiKey) {

        // Validate tồn tại thông tin nhân viên
        Long enterpriseAccountDBId = userRepository.findEnterpriseIdByApiKeyAndUUID(smeUUID, apiKey);
        if(Objects.isNull(enterpriseAccountDBId)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.ENTERPRISE_NOT_FOUND, null);
        }
        //Lấy thông tin chi tiết nhân viên
        List<Long> lstEmployeeId = userRepository.getLstEmployeeThirdPartyId(apiKey, smeUUID);
        // Láy cả thông tin chủ doanh nghiệp
        lstEmployeeId.add(enterpriseAccountDBId);
        List<IAccountAPIBaseResDTO> listApiBaseResIDTO = userRepository.searchEmployee(name, email, phoneNumber, lstEmployeeId);
        return getSuccessAccountAPIListResDTO(listApiBaseResIDTO);
    }

    @Override
    public ThirdPartyAPIResDTO assignRoleForEmployeeApi(Set<String> lstRoleCode, UUID employeeUUID, UUID smeUUID, String apiKey) {

        // Validate tồn tại thông tin nhân viên
        Long smeId = userRepository.findEnterpriseIdByApiKeyAndUUID(smeUUID, apiKey);
        if(Objects.isNull(smeId)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.ENTERPRISE_NOT_FOUND, null);
        }

        Long employeeId = userRepository.findEmployeeIdByApiKeyAndUUID(employeeUUID, smeUUID, apiKey);
        User employeeDB = userRepository.findByIdAndDeletedFlag(employeeId, DeletedFlag.NOT_YET_DELETED.getValue()).orElse(null);
        if(Objects.isNull(employeeDB)) {
            return new ThirdPartyAPIResDTO(RoleErrorCodeEnum.EMPLOYEE_NOT_EXISTS, null);
        }

        addRoleEmployee(employeeId, smeId, lstRoleCode, apiKey);
        ThirdPartyAPIResDTO thirdPartyAPIResDTO = new ThirdPartyAPIResDTO(RoleErrorCodeEnum.SUCCESS, null);
        Set<WPRole> wpRoles = wpRoleRepository.findByCodeIn(wpRoleRepository.findListRoleCodeByEmployeeId(employeeId, smeId, apiKey));
        String employeeName = employeeDB.getLastName() + " " +employeeDB.getFirstName();
        thirdPartyAPIResDTO.setData(new RoleEmployeeAPIResDTO(employeeName, wpRoles));
        return thirdPartyAPIResDTO;
    }

    @Override
    public ThirdPartyAPIResDTO removeEmployeeRoleApi(String roleCode, UUID employeeUUID, UUID smeUUID, String apiKey) {
        WPRole wpRoleDB = wpRoleRepository.findByCodeAndApiKeyAndDeletedFlag(roleCode, apiKey, DeletedFlag.NOT_YET_DELETED.getValue()).orElse(null);
        if(Objects.isNull(wpRoleDB)) {
            return new ThirdPartyAPIResDTO(RoleErrorCodeEnum.ROLE_NOT_EXISTS, null);
        }

        Long smeId = userRepository.findByUuid(smeUUID).map(User::getId).orElse(null);
        if(Objects.isNull(smeId)) {
            return new ThirdPartyAPIResDTO(RoleErrorCodeEnum.ENTERPRISE_NOT_EXISTS, null);
        }

        Long employeeId = userRepository.findEmployeeIdByApiKeyAndUUID(employeeUUID, smeUUID, apiKey);
        User employeeDB = userRepository.findByIdAndDeletedFlag(employeeId, DeletedFlag.NOT_YET_DELETED.getValue()).orElse(null);
        if(Objects.isNull(employeeDB)) {
            return new ThirdPartyAPIResDTO(RoleErrorCodeEnum.EMPLOYEE_NOT_EXISTS, null);
        }

        wpRoleRepository.removeEmployeeRole(Collections.singleton(roleCode), employeeId);
        Set<WPRole> wpRoles = wpRoleRepository.findByCodeIn(wpRoleRepository.findListRoleCodeByEmployeeId(employeeId, smeId, apiKey));
        String employeeName = employeeDB.getLastName() + " " +employeeDB.getFirstName();
        return new ThirdPartyAPIResDTO(RoleErrorCodeEnum.SUCCESS, new RoleEmployeeAPIResDTO(employeeName, wpRoles));
    }

    @Override
    public ThirdPartyAPIResDTO createUserMetadata(UUID employeeUUID, UUID smeUUID, UserMetadataReqDTO userMetadataReqDTO, String apiKey) {
        // Validate tồn tại thông tin nhân viên
        Long smeId = userRepository.findEnterpriseIdByApiKeyAndUUID(smeUUID, apiKey);
        if(Objects.isNull(smeId)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.ENTERPRISE_NOT_FOUND, null);
        }
        // Validate tồn tại thông tin nhân viên
        Long employeeId = userRepository.findEmployeeIdByApiKeyAndUUID(employeeUUID, smeUUID, apiKey);
        if(Objects.isNull(employeeId)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.EMPLOYEE_NOT_FOUND, null);
        }
        //Lưu thông tin vào bảng wp_metadata_user
        WPMetadataUser wpMetadataUser = wpMetadataUserRepository.findByUserUUIDAndSmeUUIDAndApiKey(employeeUUID, smeUUID, apiKey).orElse(null);
        if(Objects.nonNull(wpMetadataUser)) {
            wpMetadataUser.setMetadata(userMetadataReqDTO.getMetadata());
        } else {
            wpMetadataUser = new WPMetadataUser();
            wpMetadataUser.setMetadata(userMetadataReqDTO.getMetadata());
            wpMetadataUser.setUserUUID(employeeUUID);
            wpMetadataUser.setSmeUUID(smeUUID);
            wpMetadataUser.setApiKey(apiKey);
        }
        wpMetadataUserRepository.save(wpMetadataUser);
        return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.SUCCESS, null);
    }

    @Override
    public ThirdPartyAPIResDTO getUserMetadata(UUID employeeUUID, UUID smeUUID, String apiKey) {
        // Validate tồn tại thông tin nhân viên
        Long smeId = userRepository.findEnterpriseIdByApiKeyAndUUID(smeUUID, apiKey);
        if(Objects.isNull(smeId)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.ENTERPRISE_NOT_FOUND, null);
        }
        // Validate tồn tại thông tin nhân viên
        Long employeeId = userRepository.findEmployeeIdByApiKeyAndUUID(employeeUUID, smeUUID, apiKey);
        if(Objects.isNull(employeeId)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.EMPLOYEE_NOT_FOUND, null);
        }
        WPMetadataUser wpMetadataUser = wpMetadataUserRepository.findByUserUUIDAndSmeUUIDAndApiKey(employeeUUID, smeUUID, apiKey).orElse(null);
        if (Objects.nonNull(wpMetadataUser)) {
            return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.SUCCESS, wpMetadataUser.getMetadata());
        }
        return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.SUCCESS, null);
    }

    public ThirdPartyAPIResDTO getSuccessAccountAPIListResDTO(List<IAccountAPIBaseResDTO> listApiBaseResIDTO) {
        return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.SUCCESS,
            listApiBaseResIDTO.stream().map(AccountAPIListResDTO::new).collect(Collectors.toList()));
    }

    public ThirdPartyAPIResDTO getSuccessAccountAPIResDTO(IAccountAPIResDTO iAccountAPIResDTO, String roleName, Long smeId) {
        AccountAPIResDTO accountDTO = new AccountAPIResDTO(iAccountAPIResDTO, smeId);
        Date today = new Date();
        Date startWorkDate = accountDTO.getTimeStartWorking();
        if (Objects.nonNull(startWorkDate) && today.compareTo(startWorkDate) >= 0) {
            accountDTO.setTimeWorking(userRepository.findTimeWorkedEmployee(startWorkDate));
        }
        accountDTO.setRoleName(roleName);
        return new ThirdPartyAPIResDTO(AccountErrorCodeEnum.SUCCESS, accountDTO);
    }

    public ThirdPartyAPIResDTO getWPAccountAPIResDTO(AccountWPAPIResDTO accountWPAPIResDTO) {
        ThirdPartyAPIResDTO thirdPartyAPIResDTO = new ThirdPartyAPIResDTO();
        if(Objects.nonNull(accountWPAPIResDTO.getAccountDTO())) {
            AccountAPIResDTO accountAPIResDTO = new AccountAPIResDTO(accountWPAPIResDTO.getAccountDTO());
            thirdPartyAPIResDTO.setData(accountAPIResDTO);
        }
        thirdPartyAPIResDTO.setMessage(accountWPAPIResDTO.getMessage());
        thirdPartyAPIResDTO.setErrorCode(accountWPAPIResDTO.getErrorCode());
        return thirdPartyAPIResDTO;
    }

    public void addRoleEmployee(Long employeeId, Long smeId, Set<String> lstRoleCode, String apiKey) {
        Set<String> lstRoleCodeUserDB = wpRoleRepository.findListRoleCodeByUserId(smeId, apiKey);
        lstRoleCode.removeIf(roleCode -> !lstRoleCodeUserDB.contains(roleCode));
        Set<String> lstRoleEmployeeDB = wpRoleRepository.findListRoleCodeByEmployeeId(employeeId, smeId, apiKey);
        lstRoleEmployeeDB.forEach(lstRoleCode::remove);
        List<WPRoleEmployee> wpRoleEmployees = new ArrayList<>();
        for (String roleCode : lstRoleCode) {
            WPRoleEmployee wpRoleEmployee = new WPRoleEmployee();
            wpRoleEmployee.setRoleCode(roleCode);
            wpRoleEmployee.setUserId(employeeId);
            wpRoleEmployee.setParentId(smeId);
            wpRoleEmployees.add(wpRoleEmployee);
        }
        wpRoleEmployeeRepository.saveAll(wpRoleEmployees);
    }

    public void updateRoleEmployee(Long employeeId, Long enterpriseId, Set<String> lstRoleCode, String apiKey) {
        wpRoleEmployeeRepository.deleteRoleEmployeeByUserId(employeeId, apiKey);
        addRoleEmployee(employeeId, enterpriseId, lstRoleCode, apiKey);
    }


    public void revokeAllTokenBy(String username, String clientId) {
        RedisTokenStore redisStore = (RedisTokenStore) tokenStore;
        List<OAuth2AccessToken> listToken = new ArrayList<>(redisStore.findTokensByClientIdAndUserName(clientId, username));
        // logout token login master data from all device
        redisService.getKeys(username + "_*").forEach(redisService::removeKey);
        // remove all acess token
        listToken.forEach(x -> {
            redisStore.removeRefreshToken(x.getRefreshToken());
            tokenStore.removeAccessToken(x);
        });
    }
}

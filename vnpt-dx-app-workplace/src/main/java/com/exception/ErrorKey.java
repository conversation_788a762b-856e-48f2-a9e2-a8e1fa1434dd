package com.exception;

public final class Error<PERSON>ey {

    public static final String SORT = "sort";
    public static final String CLIENT_ID = "client_id";
    public static final String SME_UUID = "sme_uuid";
    public static final String USER_UUID = "user_uuid";
    public static final String RANDOM_CODE = "random_code";
    public static final String HASHCODE = "hashcode";

    public static class Role {

        public static final String ID = "id";
        public static final String NAME = "name";
    }

    public static class Service {
        public static final String ID = "id";
    }

    public static class IDP {
        public static final String ID_TOKEN = "idToken";
    }

    public static class DRIVE {
        public static final String ID = "id";
        public static final String FILENAME = "filename";
        public static final String OUTDATE = "outDate";
    }
}

package com.onedx.common.constants.enums.users;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public enum UserGenderEnum {
	FEMALE(0), MALE(1), OTHER(2), ALL(-1);
	
	public final int gender;

	public Integer getValue() {
		return this.gender;
	}
	
	UserGenderEnum(int gender) {
		this.gender = gender;
	}
	
	private static final Map<Integer, UserGenderEnum> map = new HashMap<>();

	static {
		for (UserGenderEnum genderEnum : UserGenderEnum.values()) {
			map.put(genderEnum.gender, genderEnum);
		}
	}

	public static UserGenderEnum valueOf(int genderNo) {
		return map.get(genderNo);
	}

	public static String toString(Integer value) {
		if (Objects.isNull(value)) {
			return "";
		}
		switch (value) {
			case 0:
				return "Nữ";
			case 1:
				return "Nam";
			case 2:
				return "Khác";
			default:
				return "";
		}
	}
}

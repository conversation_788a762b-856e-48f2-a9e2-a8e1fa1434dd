package com.onedx.common.constants.enums.customFields;

public enum CustomFieldValueTypeEnum {
    STRING(0),
    LIST_STRING(1),
    DATE(2),
    DATE_TIME(3),
    TIME_STAMP(4),
    LIST_LONG(5);

    private final Integer value;

    CustomFieldValueTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static CustomFieldValueTypeEnum fromValue(String customFieldTypeEnum) {
        switch (customFieldTypeEnum) {
            case "SINGLE_LINE_TEXT":
            case "NUMBER":
            case "EMAIL":
            case "DROPDOWN_LIST":
            case "RADIOBOX":
            case "MULTI_LINE_TEXT":
            case "UNIT":
            case "SWITCH":
            case "EXTRA_DATA":
            case "SETUP_INFO":
                return STRING;
            case "DATE_PICKER":
                return DATE;
            case "TIMESTAMP":
                return TIME_STAMP;
            case "DATE_TIME":
                return DATE_TIME;
            case "MULTI_SELECT":
            case "CHECKBOX":
            case "INPUT_TAG":
            case "URL":
            case "AVATAR":
            case "UPLOAD_FILE":
            case "UPLOAD_VIDEO":
            case "UPLOAD_IMAGE":
            case "UPLOADMEDIA":
            case "USER":
                return LIST_STRING;
            case "LIST_FEATURE":
            case "TYPE_SUBSCRIPTION":
            case "PRICING_PLAN":
            case "SEO_PRICING":
            case "TAX_INFO":
            case "SETUP_FEE":
                return LIST_LONG;
            default:
                return null;
        }
    }
}

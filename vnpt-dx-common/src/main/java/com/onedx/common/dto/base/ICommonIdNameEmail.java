package com.onedx.common.dto.base;

import java.math.BigDecimal;
import com.onedx.common.constants.enums.PortalType;
import com.onedx.common.constants.enums.history.actionHistory.ActionHistoryObjectTypeEnum;

public interface ICommonIdNameEmail {

    Long getId();
    String getName();
    String getEmail();
    PortalType getPortalName();
    String getUserName();
    String getDateStr();
    Long getAssigneeId(); // id người phụ trách của user

    Long getSubId();
    String getLinkResetPassword();
    String getAssigneeName();
    String getAssigneeEmail();
    Long getSubscriptionId();
    String getSmeName();
    String getCustomerType();
    String getServiceName();
    String getPricingName();
    Integer getDaysLeft();
    String getSubCode();
    Integer getCurrentCycle();
    String getEndDate();
    String getBillingDate();
    BigDecimal getTotalAmount();

    ActionHistoryObjectTypeEnum getActionHistoryObjectType();

}

package com.onedx.common.dto.integration.backend;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import com.onedx.common.constants.enums.coupons.DiscountTypeEnum;
import com.onedx.common.constants.enums.coupons.PromotionTypeEnum;
import com.onedx.common.constants.values.SwaggerConstant;
import com.onedx.common.constants.values.SwaggerConstant.Coupon;
import com.onedx.common.constants.values.SwaggerConstant.Example;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR> VinhNT
 * @version    : 1.0
 * 5/25/2021
 */

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IntegrationCouponDTO {

    @Schema(description = SwaggerConstant.Coupon.ID, example = SwaggerConstant.Example.ID)
    Long id;

    @Schema(description = SwaggerConstant.Coupon.NAME, example = SwaggerConstant.Example.COUPON_NAME)
    String name;

    @Schema(description = Coupon.START_DATE, example = Example.DATE)
    String startDate;

    @Schema(description = Coupon.END_DATE, example = Example.DATE)
    String endDate;

    @Schema(description = Coupon.MINIMUM, example = SwaggerConstant.Example.NUMBER)
    Long minimum;

    @Schema(description = Coupon.MINIMUM_AMOUNT, example = Example.NUMBER)
    Long minimumAmount;

    @Schema(description = Coupon.PROMOTION_TYPE, example = Example.PROMOTION_TYPE)
    String promotionType;

    @Schema(description = Coupon.DISCOUNT_TYPE, example = Example.COUPON_DISCOUNT_TYPE)
    String discountType;

    @Schema(description = Coupon.DISCOUNT_AMOUNT, example = Example.COUPON_DISCOUNT_AMOUNT)
    BigDecimal discountAmount;

    @Schema(description = Coupon.DISCOUNT_VALUE, example = Example.COUPON_DISCOUNT_AMOUNT)
    BigDecimal discountValue;

    List<IntegrationPricingDTO> pricing;

    public IntegrationCouponDTO(Long id, String name, LocalDate startDate, LocalDate endDate,
        Long minimum, Long minimumAmount, Integer promotionType,
        Integer discountType, BigDecimal discountAmount, BigDecimal discountValue) {
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        this.id = id;
        this.name = name;
        this.startDate = startDate != null ? dateFormat.format(startDate) : null;
        this.endDate = endDate != null ? dateFormat.format(endDate) : null;
        this.minimum = minimum;
        this.minimumAmount = minimumAmount;
        this.promotionType = PromotionTypeEnum.valueOf(promotionType).toString();
        this.discountType = discountType == null ? null : DiscountTypeEnum.valueOf(discountType).toString();
        this.discountAmount = discountAmount;
        this.discountValue = discountValue;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof IntegrationCouponDTO)) return false;
        IntegrationCouponDTO that = (IntegrationCouponDTO) o;
        return Objects.equals(id, that.id) && Objects.equals(name, that.name) &&
                Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate) &&
                Objects.equals(minimum, that.minimum) && Objects.equals(minimumAmount, that.minimumAmount) &&
                Objects.equals(promotionType, that.promotionType) && Objects.equals(discountType, that.discountType) &&
                Objects.equals(discountAmount, that.discountAmount) && Objects.equals(pricing, that.pricing);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, startDate, endDate, minimum, minimumAmount, promotionType, discountType, discountAmount, pricing);
    }
}

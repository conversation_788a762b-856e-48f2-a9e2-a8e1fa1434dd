package com.onedx.common.dto.integration.backend.subscription.detail;

import com.onedx.common.constants.values.SwaggerConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 17/06/2021 - 5:58 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SubscriptionSummaryDTO {

    @Schema(description = SwaggerConstant.Subscription.TOTAL_AMOUNT_GROSS, example = SwaggerConstant.Example.AMOUNT)
    BigDecimal totalAmountGross;

    List<SubscriptionPricingCouponDTO> couponList;

    @Schema(description = SwaggerConstant.Subscription.TOTAL_AMOUNT_NET, example = SwaggerConstant.Example.AMOUNT)
    BigDecimal totalAmountNet;
}

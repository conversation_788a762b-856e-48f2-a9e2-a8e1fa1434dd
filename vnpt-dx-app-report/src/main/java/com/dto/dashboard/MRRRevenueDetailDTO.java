package com.dto.dashboard;

import java.math.BigDecimal;
import org.springframework.beans.BeanUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MRRRevenueDetailDTO {

    @Schema(description = "Doanh thu tháng này")
    BigDecimal recentMonth;

    @Schema(description = "Phần trăm chênh lệch")
    String percentage;

    @Schema(description = "Doanh thu tháng trước")
    BigDecimal lastMonth;

    public MRRRevenueDetailDTO(IMRRRevenueAdminDTO iMRRRevenueAdminDTO){
        BeanUtils.copyProperties(iMRRRevenueAdminDTO, this);
    }
}

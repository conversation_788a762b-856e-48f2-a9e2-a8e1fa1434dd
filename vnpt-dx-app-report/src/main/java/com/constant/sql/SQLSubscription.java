package com.constant.sql;

public final class SQLSubscription {

    public  static final String GET_LIST_SUBSCRIPTION_REPORT_O2O =
        "select * FROM "
            + " {h-schema}get_report_subscriptions_o2o_update(:provinceId,:customerType,:createdSource,:migrateStartDate, " +
            ":migrateEndDate,:migrateCodes,:startDate,:endDate,:status,:serviceId,:comboIds,:pricingId,:pricingIds, " +
            ":comboPlanIds,:subscriptionType,:categoryService,:categoryCombo,:employeeCode,:subscriptionState,:creator, " +
            ":cancelledTimeStart, :cancelledTimeEnd, :providerIds) ";

    // move from func get_report_subscriptions_filter_bill_ids
    public static final String GET_REPORT_SUBSCRIPTION_FILTERED_BILL_IDS =
        "WITH subReportCTE AS ( \n" +
            "SELECT\n" +
            "       distinct\n" +
            "       sub.id AS id,\n" +
            "       bill.id AS billId,\n" +
            "       sme.name AS smeName,\n" +
            "       sme.email AS email,\n" +
            "       sub.migrate_time AS migrateTime,\n" +
            "       CASE\n" +
            "           WHEN bill.bill_action_type = 3 THEN 0\n" +
            "           ELSE bill.bill_action_type\n" +
            "       END AS subscriptionState,\n" +
            "       CASE\n" +
            "           WHEN  COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) then 'ON'\n" +
            "           ELSE 'OS'\n" +
            "       END AS serviceOwner,\n" +
            "                 sub.installed_time as installedTime, \n" +
            "                 bill.payment_date as paymentTime,\n" +
            "                 CASE\n" +
            "           WHEN bill.bill_action_type = 0 THEN sub.created_at\n" +
            "           ELSE COALESCE(bill.payment_date, bill.created_at)\n" +
            "       END AS registrationDate,\n" +
            "       CASE\n" +
            "           WHEN sub.traffic_id IS NULL THEN (\n" +
            "                CASE\n" +
            "                     WHEN sub.portal_type = 1 THEN concat('Admin - ', register.email)\n" +
            "                     WHEN sub.portal_type = 2 THEN concat('Dev - ', register.email)\n" +
            "                     WHEN sub.portal_type = 3 THEN 'OneSME'\n" +
            "                     ELSE ''\n" +
            "                END\n" +
            "            )\n" +
            "           WHEN sub.traffic_user IS NOT NULL THEN sub.traffic_id\n" +
            "           ELSE sub.traffic_user\n" +
            "       END AS creator,\n" +
            "       CASE\n" +
            "            WHEN sub.created_source_migration = 1 THEN 5\n" +
            "            WHEN sub.traffic_source = 'accesstrade' THEN 6\n" +
            "            WHEN sub.traffic_source = 'apinfo' THEN 8\n" +
            "            WHEN sub.affiliate_one IS NOT NULL THEN 7\n" +
            "            WHEN sub.traffic_id IS NOT NULL THEN 3\n" +
            "            WHEN sub.employee_code IS NOT NULL THEN 2\n" +
            "            WHEN sub.portal_type IN (1,2) THEN 4\n" +
            "            ELSE 1\n" +
            "       END AS createdSourceMigration,\n" +
            "       CASE\n" +
            "           WHEN sme_progress.name = 'Hủy' OR sub.status IN (3, 4) THEN sub.cancelled_time\n" +
            "       END AS cancelledTime,\n" +
            "       CASE\n" +
            "           WHEN sub.traffic_id IS NOT NULL THEN sub.traffic_user\n" +
            "           WHEN sub.traffic_source = 'accesstrade' THEN sub.traffic_user\n" +
            "           WHEN sub.affiliate_one IS NOT NULL THEN sub.affiliate_one\n" +
            "       END AS affiliateCode\n" +
            "    FROM {h-schema}subscriptions sub\n" +
            "        JOIN {h-schema}view_report_sub_bills AS bill ON bill.subscriptions_id = sub.id\n" +
            "        LEFT JOIN {h-schema}users AS sme ON sub.user_id = sme.id\n" +
            "        LEFT JOIN {h-schema}users AS register ON sub.registed_by = register.id\n" +
            "        LEFT JOIN {h-schema}order_service_receive osServiceReceive ON osServiceReceive.subscription_id = sub.id\n" +
            "        LEFT JOIN {h-schema}order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8)\n" +
            "        LEFT JOIN {h-schema}sme_progress ON osServiceStatus.sme_progress_id = sme_progress.id\n" +
            "        LEFT JOIN {h-schema}view_report_sub_services AS subServices ON (sub.pricing_id IS NOT NULL AND sub.pricing_id = subServices.id) or (sub.pricing_id IS NULL AND sub.service_id = subServices.service_id)\n" +
            "        LEFT JOIN {h-schema}view_report_sub_combo AS subCombo ON sub.combo_plan_id IS NOT NULL AND sub.combo_plan_id = subCombo.id\n" +
            "    WHERE\n" +
            "          sub.deleted_flag = 1 AND\n" +
            "          sub.confirm_status = 1 AND\n" +
            "          (COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) -- ON lay het, OS chi lay neu co trang thai\n" +
            "            OR (osServiceReceive.id is not null and osServiceReceive.order_status is not null and COALESCE(subServices.service_owner, subCombo.combo_owner) = 3)\n" +
            "            OR (sub.os_3rd_status is not null and COALESCE(subServices.service_owner, subCombo.combo_owner) = 2)) AND\n" +
            "          (:provinceId = -1 OR sme.province_id = :provinceId) AND\n" +
            "          (:customerType = 'ALL' OR sme.customer_type = :customerType) AND\n" +
            "          (:userCode = '' OR coalesce(sme.user_code, '') ilike ('%' || :userCode || '%')) AND\n" +
            "          (:amName = '' OR osServiceReceive.am_name ilike ('%' || :amName || '%')) AND\n" +
            "          (:amCode = '' OR osServiceReceive.am_code ilike ('%' || :amCode || '%')) AND\n" +
            "          (:subscriptionType = 'ALL' OR (sub.service_group_id is not null AND 'SERVICE_GROUP'= :subscriptionType) OR (subServices.sub_type is not null AND 'SERVICE'= :subscriptionType) OR (subCombo.sub_type is not null AND 'COMBO'= :subscriptionType)) AND\n" +
            "          (:employeeCode = 'ALL' OR sub.employee_code = :employeeCode) AND\n" +
            "          (:status = -2 OR sub.status = :status) AND\n" +
            "          (:serviceId = -1 OR sub.service_id = :serviceId) AND\n" +
            "          (\n" +
            "            (\n" +
            "                 subServices.sub_type is not null AND\n" +
            "                 (:categoryService = -1 OR subServices.categories_id = :categoryService) AND\n" +
            "                 (:pricingId = -1 OR sub.pricing_id = :pricingId) AND\n" +
            "                 (:pricingIDs = '-1' OR  sub.pricing_id = ANY (cast(('{' || :pricingIDs || '}') as int8[])))\n" +
            "             ) OR\n" +
            "             (\n" +
            "                  subCombo.sub_type is not null AND\n" +
            "                  (:categoryCombo = '-1' OR string_to_array(subCombo.categories_id, ',') && string_to_array(:categoryCombo, ',')) AND\n" +
            "                  (:pricingId = -1 OR :comboPlanIds = '-1' OR sub.combo_plan_id = ANY (cast(('{' || :comboPlanIds || '}') as int8[]))) AND\n" +
            "                  (:serviceId = -1 OR :comboIds = '-1' OR subCombo.combo_id = ANY (cast(('{' || :comboIds || '}') as int8[])))\n" +
            "             )\n" +
            "          ) AND\n" +
            "          (:providerIds = '-1' OR COALESCE(subServices.provider_id, subCombo.provider_id) = ANY(cast(('{' || :providerIds || '}') as int8[]))) AND\n" +
            "          (:affAgencyUserIds = '-1' OR bill.aff_agency_user_id = ANY(cast(('{' || :affAgencyUserIds || '}') as int8[]))) AND\n" +
            "          (:paymentMethod = '-1' OR (bill.payment_method is not null and bill.payment_method = ANY(cast(('{' || :paymentMethod || '}') as int8[])) and ((bill.payment_method = 3 and bill.status = 2) or bill.payment_method != 3)))\n" +
            ")\n" +
            "SELECT\n" +
            "        subReportCTE.id AS subId,\n" +
            "        subReportCTE.billId AS billId\n" +
            "   FROM subReportCTE\n" +
            "   WHERE\n" +
            "        (:subscriptionState = -1 OR subReportCTE.subscriptionState = :subscriptionState) AND\n" +
            "        (:serviceOwner = '-1' OR subReportCTE.serviceOwner = :serviceOwner) AND\n" +
            "        (CAST(:startPaymentDate AS date) = cast('1970-01-01' as date) OR cast(subReportCTE.paymentTime as date) >= CAST(:startPaymentDate AS date)) AND\n" +
            "        (CAST(:endPaymentDate AS date) = cast('3000-01-01' as date) OR cast(subReportCTE.paymentTime as date) <= CAST(:endPaymentDate AS date)) AND\n" +
            "        (CAST(:startInstalledDate AS date) = cast('1970-01-01' as date) OR cast(subReportCTE.installedTime as date) >= CAST(:startInstalledDate AS date)) AND\n" +
            "        (CAST(:endInstalledDate AS date) = cast('3000-01-01' as date) OR cast(subReportCTE.installedTime as date) <= CAST(:endInstalledDate AS date)) AND\n" +
            "        (cast(subReportCTE.registrationDate as date) >= CAST(:startDate AS date)) AND\n" +
            "        (cast(subReportCTE.registrationDate as date) <= CAST(:endDate AS date)) AND\n" +
            "        (:creator = '-1' OR subReportCTE.creator = :creator) AND\n" +
            "        (:cancelledTimeStart = '' OR to_char(subReportCTE.cancelledTime, 'YYYY-MM-DD') >= :cancelledTimeStart) AND\n" +
            "        (:cancelledTimeEnd = '' OR to_char(subReportCTE.cancelledTime, 'YYYY-MM-DD') <= :cancelledTimeEnd) AND\n" +
            "        (:migrateStartDate = '' OR to_char(subReportCTE.migrateTime, 'YYYY-MM-DD') >= :migrateStartDate) AND\n" +
            "        (:migrateEndDate = '' OR to_char(subReportCTE.migrateTime, 'YYYY-MM-DD') >= :migrateEndDate) AND\n" +
            "        (\n" +
            "                (subReportCTE.createdSourceMigration <> 5) AND\n" +
            "                ( -- Do RK lưu created_source khác với code BE --\n" +
            "                    :createdSource = -1 OR (:createdSource IN (1, 2, 3, 4, 6, 7, 8) AND :createdSource = subReportCTE.createdSourceMigration)\n" +
            "                )\n" +
            "        ) AND\n" +
            "        (:affiliateCode = '' OR subReportCTE.affiliateCode = :affiliateCode) AND\n" +
            "        (:customerName = '' OR subReportCTE.smeName ILIKE ('%' || :customerName || '%')) AND\n" +
            "        (:customerEmail = '' OR subReportCTE.email = :customerEmail)\n" +
            "        ORDER BY subReportCTE.registrationDate DESC ";

    public static final String GET_COUNT_SUBSCRIPTION_REPORT =
        "SELECT COUNT(1) FROM ( " + GET_REPORT_SUBSCRIPTION_FILTERED_BILL_IDS + " ) as result ";

    public  static final String GET_LIST_SUBSCRIPTION_REPORT =
        "WITH subReportCTE AS (\n" +
            "SELECT\n" +
            "   distinct\n" +
            "   sub.id AS id,\n" +
            "   sub.sub_code AS subCode,\n" +
            "   sub.created_at AS createdAt,\n" +
            "   sme.province AS provinceName,\n" +
            "   CASE\n" +
            "       WHEN sub.status = -1 THEN 'NOT_SET'\n" +
            "       WHEN sub.status = 0 THEN 'FUTURE'\n" +
            "       WHEN sub.status = 1 THEN 'IN_TRIAL'\n" +
            "       WHEN sub.status = 2 THEN 'ACTIVE'\n" +
            "       WHEN sub.status = 3 THEN 'CANCELED'\n" +
            "       WHEN sub.status = 4 THEN 'NON_RENEWING'\n" +
            "   END AS subsStatus,\n" +
            "   sme.name AS smeName,\n" +
            "   sme.customer_type AS customerType,\n" +
            "   sme.tin AS taxtNo,\n" +
            "   sme.address AS address,\n" +
            "   sme.street AS street,\n" +
            "   sme.ward AS ward,\n" +
            "   sme.district AS district,\n" +
            "   sme.province AS province,\n" +
            "   sme.nation AS nation,\n" +
            "   sme.phone_number AS phoneNo,\n" +
            "   sme.email AS email,\n" +
            "   CASE\n" +
            "       WHEN sg.id IS NOT NULL THEN COALESCE(subServices.service_name, subCombo.service_name) || ' (' || sg.name || ')'\n" +
            "       ELSE COALESCE(subServices.service_name, subCombo.service_name)\n" +
            "   END AS serviceName,\n" +
            "   COALESCE(provider.name, '') AS provider,\n" +
            "   COALESCE(subServices.pricing_name, subCombo.pricing_name) AS pricingName,\n" +
            "   sub.number_of_cycles AS numberOfCycle,\n" +
            "   COALESCE(subServices.plan_payment_cycle, subCombo.plan_payment_cycle) AS planPaymentCycle,\n" +
            "   COALESCE(subServices.plan_cycle_type, subCombo.plan_cycle_type) AS planCycleType,\n" +
            "   prcMultiPlan.payment_cycle AS multiPaymentCycle,\n" +
            "   CASE\n" +
            "            WHEN prcMultiPlan.circle_type = 0 THEN 'DAILY'\n" +
            "            WHEN prcMultiPlan.circle_type = 1 THEN 'WEEKLY'\n" +
            "            WHEN prcMultiPlan.circle_type = 2 THEN 'MONTHLY'\n" +
            "            WHEN prcMultiPlan.circle_type = 3 THEN 'YEARLY'\n" +
            "            ELSE ''\n" +
            "   END AS multiCycleType,\n" +
            "   CASE\n" +
            "            WHEN sub.installed IS NULL AND sub.installed = 0 THEN 'Đang cài đặt'\n" +
            "            WHEN sub.installed = 1 THEN 'Đã cài đặt'\n" +
            "            WHEN sub.installed = 2 THEN 'Gặp sự cố'\n" +
            "            ELSE ''\n" +
            "   END AS subsInstalled,\n" +
            "   sme_progress.name AS smeProgressName,\n" +
            "   COALESCE(subServiceGroup.sub_type, subServices.sub_type, subCombo.sub_type) AS subscriptionType,\n" +
            "   sub.modified_at AS modifiedAt,\n" +
            "   COALESCE(subServices.service_owner_type, subCombo.service_owner_type) AS serviceOwnerType,\n" +
            "   CASE\n" +
            "             WHEN sub.portal_type = 1 THEN concat('Admin - ', register.email)\n" +
            "             WHEN sub.portal_type = 2 THEN concat('Dev - ', register.email)\n" +
            "             ELSE 'OneSME'\n" +
            "   END AS registedBy,\n" +
            "   sub.traffic_id  AS trafficId,\n" +
            "   sub.traffic_user AS trafficUser,\n" +
            "   sub.employee_code AS employeeCode,\n" +
            "   sub.dhsxkd_sub_code AS dhsxkdSubCode,\n" +
            "   CASE\n" +
            "       WHEN sub.created_source_migration = 1 THEN 'ĐHSXKD'\n" +
            "       WHEN sub.traffic_source = 'accesstrade' THEN 'Affiliate AccessTrade'\n" +
            "       WHEN sub.traffic_source = 'apinfo' THEN 'Affiliate Apinfo'\n" +
            "       WHEN sub.affiliate_one IS NOT NULL THEN 'Affiliate Onesme'\n" +
            "       WHEN sub.traffic_id IS NOT NULL THEN 'Affiliate Masoffer'\n" +
            "       WHEN sub.employee_code IS NOT NULL THEN 'AM G.Thiệu'\n" +
            "       WHEN sub.portal_type IN (1,2) OR (bill.portal_type IS NOT NULL AND bill.portal_type IN (1,2)) THEN 'Dev/Admin'\n" +
            "       ELSE 'oneSME'\n" +
            "   END AS createdSource,\n" +
            "   sub.migrate_time AS migrateTime,\n" +
            "   sub.migrate_code AS migrateCode,\n" +
            "   CASE\n" +
            "       WHEN bill.status = 0 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'ON' THEN 'Khởi tạo'\n" +
            "       WHEN bill.status = 1 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'ON' THEN 'Chờ thanh toán'\n" +
            "       WHEN bill.status = 2 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'ON' THEN 'Đã thanh toán'\n" +
            "       WHEN bill.status = 3 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'ON' THEN 'Thanh toán thất bại'\n" +
            "       WHEN bill.status = 4 AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'ON' THEN 'Quá hạn thanh toán'\n" +
            "       WHEN osServiceReceive.payment_status = '1' AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'OS' THEN 'Đã thanh toán'\n" +
            "       WHEN osServiceReceive.payment_status = '0' AND COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'OS' THEN 'Chờ thanh toán'\n" +
            "       WHEN osServiceReceive.payment_status IS NULL AND osServiceReceive.id IS NOT NULL AND\n" +
            "            COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'OS' THEN 'Chờ thanh toán'\n" +
            "       ELSE 'Chờ thanh toán'\n" +
            "   END AS billStatus,\n" +
            "   bill.billing_code AS billCode,\n" +
            "   CASE\n" +
            "       WHEN bill.bill_action_type = 3 THEN 0\n" +
            "       ELSE bill.bill_action_type\n" +
            "   END AS subscriptionState,\n" +
            "   CASE\n" +
            "       WHEN bill.bill_action_type = 0 then 'Đăng ký mới'\n" +
            "       WHEN bill.bill_action_type = 1 then 'Chỉnh sửa'\n" +
            "       WHEN bill.bill_action_type = 2 THEN 'Đổi gói'\n" +
            "       WHEN bill.bill_action_type = 3 then 'Kích hoạt'\n" +
            "       WHEN bill.bill_action_type = 5 then 'Gia hạn'\n" +
            "       ELSE ''\n" +
            "   END AS state,\n" +
            "   CASE\n" +
            "       WHEN bill.bill_action_type = 0 THEN sub.created_at\n" +
            "       ELSE COALESCE(bill.payment_date, bill.created_at)\n" +
            "   END AS registrationDate,\n" +
            "   CASE\n" +
            "       WHEN bill.bill_action_type = 0 THEN sub.started_at\n" +
            "       ELSE COALESCE(bill.payment_date, CAST(bill.created_at AS DATE))\n" +
            "   END AS startAt,\n" +
            "   CASE\n" +
            "       WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'ON' THEN sub.dhsxkd_sub_code\n" +
            "       WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'OS' THEN osServiceReceive.transaction_code\n" +
            "       ELSE ''\n" +
            "   END  AS dhsxkdCode,\n" +
            "   CASE\n" +
            "       WHEN COALESCE(subServices.sub_type, subCombo.sub_type) = 'SERVICE' AND subServices.plan_payment_cycle IS NULL THEN prcMultiPlan.payment_cycle\n" +
            "       ELSE COALESCE(subServices.plan_payment_cycle, subCombo.plan_payment_cycle)\n" +
            "   END AS paymentCycle,\n" +
            "   CASE\n" +
            "       WHEN COALESCE(subServices.sub_type, subCombo.sub_type) = 'SERVICE' AND subServices.plan_cycle_type IS NULL THEN\n" +
            "           (\n" +
            "             CASE\n" +
            "                    WHEN prcMultiPlan.circle_type = 0 THEN 'DAILY'\n" +
            "                    WHEN prcMultiPlan.circle_type = 1 THEN 'WEEKLY'\n" +
            "                    WHEN prcMultiPlan.circle_type = 2 THEN 'MONTHLY'\n" +
            "                    WHEN prcMultiPlan.circle_type = 3 THEN 'YEARLY'\n" +
            "                    ELSE ''\n" +
            "             END\n" +
            "            )\n" +
            "       ELSE COALESCE(subServices.plan_cycle_type, subCombo.plan_cycle_type)\n" +
            "   END AS cycleType,\n" +
            "   CASE\n" +
            "       WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'ON' THEN (\n" +
            "            CASE\n" +
            "                WHEN sub.installed IS NULL OR sub.installed = 0 THEN 'Đang cài đặt'\n" +
            "                WHEN sub.installed = 1 THEN 'Đã cài đặt'\n" +
            "                WHEN sub.installed = 2 THEN 'Gặp sự cố'\n" +
            "                ELSE ''\n" +
            "            END)\n" +
            "                 WHEN COALESCE(subServices.service_owner, subCombo.combo_owner) = 2 THEN (\n" +
            "                            CASE\n" +
            "                                WHEN sub.os_3rd_status = 1 THEN 'Tiếp nhận đơn hàng'\n" +
            "                                WHEN sub.os_3rd_status = 2 THEN 'Đang triển khai'\n" +
            "                                WHEN sub.os_3rd_status = 4 THEN 'Hoàn thành'\n" +
            "                                WHEN sub.os_3rd_status = 3 THEN 'Huỷ đơn hàng'\n" +
            "                                WHEN sub.os_3rd_status = 5 THEN 'Đặt hàng thành công'\n" +
            "                                ELSE ''\n" +
            "                            END\n" +
            "                      )\n" +
            "       WHEN COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'OS' THEN COALESCE(sme_progress.name , '')\n" +
            "       ELSE ''\n" +
            "   END AS installedStatus,\n" +
            "   CASE\n" +
            "       WHEN sme_progress.name = 'Hủy' THEN 'CANCELED'\n" +
            "       ELSE (\n" +
            "                CASE\n" +
            "                    WHEN sub.status = -1 THEN 'NOT_SET'\n" +
            "                    WHEN sub.status = 0 THEN 'FUTURE'\n" +
            "                    WHEN sub.status = 1 THEN 'IN_TRIAL'\n" +
            "                    WHEN sub.status = 2 THEN 'ACTIVE'\n" +
            "                    WHEN sub.status = 3 THEN 'CANCELED'\n" +
            "                    WHEN sub.status = 4 THEN 'NON_RENEWING'\n" +
            "                END\n" +
            "           )\n" +
            "   END AS status,\n" +
            "   CASE\n" +
            "       WHEN sub.traffic_id IS NULL THEN (\n" +
            "            CASE\n" +
            "                 WHEN sub.portal_type = 1 THEN concat('Admin - ', register.email)\n" +
            "                 WHEN sub.portal_type = 2 THEN concat('Dev - ', register.email)\n" +
            "                 WHEN sub.portal_type = 3 THEN 'OneSME'\n" +
            "                 ELSE ''\n" +
            "            END\n" +
            "       )\n" +
            "       WHEN sub.traffic_user IS NOT NULL THEN sub.traffic_id\n" +
            "       ELSE sub.traffic_user\n" +
            "   END AS creator,\n" +
            "   billVnptPay.transaction_code AS payTransactionCode,\n" +
            "   CASE\n" +
            "        WHEN billPayments.promotion_amount < 0 THEN 0\n" +
            "        ELSE billPayments.promotion_amount\n" +
            "   END AS promotionAmount,\n" +
            "   CASE\n" +
            "        WHEN billPayments.unit_amount < 0 THEN 0\n" +
            "        ELSE billPayments.unit_amount\n" +
            "   END AS unitAmount,\n" +
            "   CASE\n" +
            "       WHEN bill.action_type = 1 THEN billPayments.amount_change\n" +
            "       WHEN bill.bill_action_type <> 1 AND billPayments.pre_amount_tax > 0 THEN billPayments.pre_amount_tax\n" +
            "       ELSE 0\n" +
            "       END AS preAmountTax,\n" +
            "             CASE\n" +
            "                     WHEN billPayments.taxable_amount > 0 then billPayments.taxable_amount\n" +
            "                     ELSE 0\n" +
            "   END AS taxableAmount,\n" +
            "   CASE\n" +
            "        WHEN billPayments.amount_tax > 0 then billPayments.amount_tax\n" +
            "        ELSE 0\n" +
            "   END AS amountTax,\n" +
            "   CASE\n" +
            "        WHEN billPayments.after_amount_tax > 0 then billPayments.after_amount_tax\n" +
            "        ELSE 0\n" +
            "   END AS afterAmountTax,\n" +
            "   CASE\n" +
            "        WHEN sub.created_source_migration = 1 THEN 5\n" +
            "        WHEN sub.traffic_source = 'accesstrade' THEN 6\n" +
            "        WHEN sub.traffic_source = 'apinfo' THEN 8\n" +
            "        WHEN sub.affiliate_one IS NOT NULL THEN 7\n" +
            "        WHEN sub.traffic_id IS NOT NULL THEN 3\n" +
            "        WHEN sub.employee_code IS NOT NULL THEN 2\n" +
            "        WHEN sub.portal_type IN (1,2) THEN 4\n" +
            "        ELSE 1\n" +
            "   END AS createdSourceMigration,\n" +
            "   osServiceReceive.setup_code AS setupCode,\n" +
            "   CASE\n" +
            "       WHEN (bill.total_amount = 0 OR bill.total_amount_after_adjustment = 0)\n" +
            "           AND CAST(bill.created_at AS DATE) = bill.billing_date AND COALESCE(subServices.pricing_type, subCombo.pricing_type) = 0\n" +
            "           THEN bill.created_at\n" +
            "       ELSE bill.payment_date\n" +
            "   END paymentDate,\n" +
            "   bill.created_export_invoice AS createdExportInvoice,\n" +
            "   bill.code AS codeInvoice,\n" +
            "   CASE\n" +
            "       WHEN sme_progress.name = 'Hủy' OR sub.status IN (3, 4) THEN sub.cancelled_time\n" +
            "       END AS cancelledTime,\n" +
            "   sub.is_one_time AS isOneTime,\n" +
            "   CASE\n" +
            "       WHEN sub.traffic_id IS NOT NULL THEN sub.traffic_user\n" +
            "       WHEN sub.traffic_source = 'accesstrade' THEN sub.traffic_user\n" +
            "       WHEN sub.affiliate_one IS NOT NULL THEN sub.affiliate_one\n" +
            "   END AS affiliateCode,\n" +
            "   bill.aff_agency_user_id AS affAgencyUserId,\n" +
            "   COALESCE(affiliateAgency.name, CONCAT(' ', affiliateAgency.last_name, affiliateAgency.first_name)) AS affAgencyName,\n" +
            "   COALESCE(bill.aff_agency_code, '') AS affAgencyCode,\n" +
            "   CASE\n" +
            "       WHEN bill.bill_action_type = 5 and bill.end_date_new_renewal is not null THEN bill.end_date_new_renewal\n" +
            "       WHEN bill.bill_action_type = 5 and bill.end_date is not null THEN bill.end_date\n" +
            "       ELSE sub.end_current_cycle\n" +
            "   END AS endCurrentCycle,\n" +
            "             CASE\n" +
            "                WHEN bill.bill_action_type <> 5 THEN ''\n" +
            "                WHEN\n" +
            "                        (bill.status = 2 or osServiceReceive.payment_status = '1')\n" +
            "                        and (sub.end_current_cycle_contract is null\n" +
            "                        or COALESCE(bill.end_date_new_renewal, bill.end_date) < sub.end_current_cycle_contract)\n" +
            "                    THEN 'Có đối soát'\n" +
            "                WHEN (bill.status = 1 or osServiceReceive.payment_status = '0' or (osServiceReceive.payment_status IS NULL AND osServiceReceive.id IS NOT NULL and COALESCE(subServices.service_owner_type, subCombo.service_owner_type) = 'OS'))\n" +
            "                        or COALESCE(bill.end_date_new_renewal, bill.end_date) > sub.end_current_cycle_contract\n" +
            "                    THEN 'Không đối soát'\n" +
            "                ELSE ''\n" +
            "   END AS controlRenew,\n" +
            "             sub.end_current_cycle_contract AS endCurrentCycleContract,\n" +
            "   sme.identity_no AS identityNo,\n" +
            "   bill.id AS billId,\n" +
            "             CASE\n" +
            "       WHEN  COALESCE(subServices.service_owner, subCombo.combo_owner) in (0,1) then 'ON'\n" +
            "       ELSE 'OS'\n" +
            "   END AS serviceOwner,\n" +
            "             sub.installed_time as installedTime,\n" +
            "             bill.payment_date as paymentTime,\n" +
            "   coalesce(sme.user_code, '') as userCode,\n" +
            "             CASE\n" +
            "                 WHEN osServiceReceive.am_name IS NOT NULL AND osServiceReceive.am_phone_no IS NOT NULL THEN concat(osServiceReceive.am_name, '-', osServiceReceive.am_phone_no)\n" +
            "                 WHEN osServiceReceive.am_name IS NOT NULL THEN osServiceReceive.am_name\n" +
            "                 WHEN osServiceReceive.am_phone_no IS NOT NULL THEN osServiceReceive.am_phone_no\n" +
            "                 ELSE ''\n" +
            "            END AS amName,\n" +
            "   osServiceReceive.am_code as amCode\n" +
            "FROM {h-schema}subscriptions sub\n" +
            "    JOIN {h-schema}view_report_sub_bills AS bill ON bill.subscriptions_id = sub.id AND (:billIds = '-1' OR bill.id = ANY(cast(('{' || cast(:billIds as text) || '}') as int8[])))\n" +
            "    LEFT JOIN {h-schema}view_report_sub_sme AS sme ON sub.user_id = sme.id\n" +
            "    LEFT JOIN {h-schema}users AS register ON sub.registed_by = register.id\n" +
            "    LEFT JOIN {h-schema}pricing_multi_plan as prcMultiPlan ON prcMultiPlan.id = sub.pricing_multi_plan_id\n" +
            "    LEFT JOIN {h-schema}order_service_receive osServiceReceive ON osServiceReceive.subscription_id = sub.id\n" +
            "    LEFT JOIN {h-schema}order_service_status osServiceStatus ON osServiceStatus.id = CAST(osServiceReceive.order_status AS int8)\n" +
            "    LEFT JOIN {h-schema}sme_progress ON osServiceStatus.sme_progress_id = sme_progress.id\n" +
            "    LEFT JOIN {h-schema}view_report_sub_services AS subServices ON (sub.pricing_id IS NOT NULL AND sub.pricing_id = subServices.id) or (sub.pricing_id IS NULL AND sub.service_id = subServices.service_id and subServices.id is null)\n" +
            "    LEFT JOIN {h-schema}view_report_sub_combo AS subCombo ON sub.combo_plan_id IS NOT NULL AND sub.combo_plan_id = subCombo.id\n" +
            "    LEFT JOIN {h-schema}view_report_sub_service_group AS subServiceGroup ON sub.service_group_id IS NOT NULL AND sub.service_group_id = subServiceGroup.id\n" +
            "    LEFT JOIN {h-schema}service_group as sg on sg.id = sub.service_group_id\n" +
            "    LEFT JOIN {h-schema}view_report_sub_bill_payment AS billPayments ON bill.id = billPayments.id\n" +
            "    LEFT JOIN {h-schema}view_report_sub_vnpt_pay_response AS billVnptPay ON bill.id = billVnptPay.bill_id\n" +
            "    LEFT JOIN {h-schema}users AS provider ON COALESCE(subServices.provider_id, subCombo.provider_id) = provider.id\n" +
            "    LEFT JOIN {h-schema}users AS affiliateAgency ON affiliateAgency.id = bill.aff_agency_user_id\n" +
            "WHERE\n" +
            "    sub.deleted_flag = 1 AND\n" +
            "    sub.confirm_status = 1 AND\n" +
            "    ((subServices.service_owner in (0,1) OR (subCombo.combo_owner in (0,1)))\n" +
            "    OR (osServiceReceive.id is not null and osServiceReceive.order_status is not null and (subServices.service_owner = 3 OR (subCombo.combo_owner = 3)))\n" +
            "    OR (sub.os_3rd_status is not null and (subServices.service_owner = 2 OR (subCombo.combo_owner = 2))))\n" +
            "    AND (:provinceId = -1 OR sme.province_id = :provinceId)\n" +
            "    AND (:amCode = '' OR osServiceReceive.am_code ilike ('%' || :amCode || '%'))\n" +
            "    AND (:amName = '' OR osServiceReceive.am_name ilike ('%' || :amName || '%') OR osServiceReceive.am_phone_no ilike ('%' || :amName || '%'))\n" +
            "    AND (:customerType = 'ALL' OR sme.customer_type_raw = :customerType)\n" +
            "    AND (:userCode = '' OR coalesce(sme.user_code, '') ilike ('%' || :userCode || '%'))\n" +
            "    AND (:subscriptionType = 'ALL' OR (sub.service_group_id is not null AND 'SERVICE_GROUP'= :subscriptionType) OR (subServices.sub_type is not null AND 'SERVICE'= :subscriptionType) OR (subCombo.sub_type is not null AND 'COMBO'= :subscriptionType))\n" +
            "    AND (:employeeCode = 'ALL' OR sub.employee_code = :employeeCode)\n" +
            "    AND (:status = -2 OR sub.status = :status)\n" +
            "    AND (:serviceId = -1 OR sub.service_id = :serviceId)\n" +
            "    AND (\n" +
            "        (\n" +
            "            subServices.sub_type is not null AND\n" +
            "            (:categoryService = -1 OR subServices.categories_id = :categoryService) AND\n" +
            "            (:pricingId = -1 OR sub.pricing_id = :pricingId) AND\n" +
            "            (:pricingIDs = '-1' OR  sub.pricing_id = ANY (cast(('{' || :pricingIDs || '}') as int8[])))\n" +
            "        ) OR\n" +
            "        (\n" +
            "            subCombo.sub_type is not null AND\n" +
            "            (:categoryCombo = '-1' OR string_to_array(subCombo.categories_id, ',') && string_to_array(:categoryCombo, ',')) AND\n" +
            "            (:pricingId = -1 OR :comboPlanIds = '-1' OR sub.combo_plan_id = ANY (cast(('{' || :comboPlanIds || '}') as int8[]))) AND\n" +
            "            (:serviceId = -1 OR :comboIds = '-1' OR subCombo.combo_id = ANY (cast(('{' || :comboIds || '}') as int8[])))\n" +
            "        )\n" +
            "    )\n" +
            "    AND (:providerIds = '-1' OR COALESCE(subServices.provider_id, subCombo.provider_id) = ANY(cast(('{' || :providerIds || '}') as int8[])))\n" +
            "    AND (:affAgencyUserIds = '-1' OR bill.aff_agency_user_id = ANY(cast(('{' || :affAgencyUserIds || '}') as int8[])))\n" +
            "    AND (:paymentMethod = '-1' OR (bill.payment_method is not null and bill.payment_method = ANY(cast(('{' || :paymentMethod || '}') as int8[])) and ((bill.payment_method = 3 and bill.status = 2) or bill.payment_method != 3)))\n" +
            ")\n" +
            "SELECT distinct * FROM subReportCTE\n" +
            "WHERE\n" +
            "    (:subscriptionState = -1 OR subReportCTE.subscriptionState = :subscriptionState)\n" +
            "    AND (:serviceOwner = '-1' OR subReportCTE.serviceOwner = :serviceOwner)\n" +
            "    AND (CAST(:startPaymentDate AS date) = cast('1970-01-01' as date) OR cast(subReportCTE.paymentTime as date) >= CAST(:startPaymentDate AS date))\n" +
            "    AND (CAST(:endPaymentDate AS date) = cast('3000-01-01' as date) OR cast(subReportCTE.paymentTime as date) <= CAST(:endPaymentDate AS date))\n" +
            "    AND (CAST(:startInstalledDate AS date) = cast('1970-01-01' as date) OR cast(subReportCTE.installedTime as date) >= CAST(:startInstalledDate AS date))\n" +
            "    AND (CAST(:endInstalledDate AS date) = cast('3000-01-01' as date) OR cast(subReportCTE.installedTime as date) <= CAST(:endInstalledDate AS date))\n" +
            "    AND (cast(subReportCTE.registrationDate as date) >= CAST(:startDate AS date))\n" +
            "    AND (cast(subReportCTE.registrationDate as date) <= CAST(:endDate AS date))\n" +
            "    AND (:creator = '-1' OR subReportCTE.creator = :creator)\n" +
            "    AND (:cancelledTimeStart = '' OR to_char(subReportCTE.cancelledTime, 'YYYY-MM-DD') >= :cancelledTimeStart)\n" +
            "    AND (:cancelledTimeEnd = '' OR to_char(subReportCTE.cancelledTime, 'YYYY-MM-DD') <= :cancelledTimeEnd)\n" +
            "    AND (:migrateStartDate = '' OR to_char(subReportCTE.migrateTime, 'YYYY-MM-DD') >= :migrateStartDate)\n" +
            "    AND (:migrateEndDate = '' OR to_char(subReportCTE.migrateTime, 'YYYY-MM-DD') >= :migrateEndDate)\n" +
            "    AND (\n" +
            "        (subReportCTE.createdSourceMigration <> 5)\n" +
            "        AND (\n" +
            "            :createdSource = -1 OR (:createdSource IN (1, 2, 3, 4, 6, 7, 8) AND :createdSource = subReportCTE.createdSourceMigration)\n" +
            "        )\n" +
            "    )\n" +
            "    AND (:affiliateCode = '' OR subReportCTE.affiliateCode = :affiliateCode)\n" +
            "    AND (:customerName = '' OR subReportCTE.smeName ilike ('%' || :customerName || '%'))\n" +
            "    AND (:customerEmail = '' OR subReportCTE.email = :customerEmail)\n" +
            "ORDER BY subReportCTE.registrationDate DESC \n";

    public static final String GET_LIST_SUBSCRIPTION_REPORT_EXPORT_CSV =
        "SELECT \n" +
            "     CAST(row_number() over (ORDER BY  registrationDate DESC) AS TEXT) AS rowNum, \n" +
            "     COALESCE(provinceName, '') AS provinceName, \n" +
            "     CASE\n" +
            "          WHEN  billStatus IS NOT NULL THEN  state \n" +
            "          ELSE '' \n" +
            "     END AS subStatus, \n" +
            "     COALESCE(smeName, '') AS smeName, \n" +
            "     COALESCE(customerType, '') AS customerType, \n" +
            "     '''' || COALESCE(taxtNo, '') || '''' AS taxNo, \n" +
            "     '''' || COALESCE(identityNo, '') || '''' AS identityNo, \n" +
            "     CONCAT_WS(',',  address,  street,  district,  province) AS address, \n" +
            "     '''' || COALESCE(phoneNo, '') || '''' AS phoneNo, \n" +
            "     COALESCE(email, '') AS email, \n" +
            "     COALESCE(employeeCode, '') AS employeeCode, \n" +
            "     COALESCE(serviceName, '') AS serviceName, \n" +
            "     COALESCE(provider, '') AS provider, \n" +
            "     {h-schema}func_convert_installed_status_to_text(installedStatus, serviceOwnerType) AS installedStatus,\n"+
            "     COALESCE(pricingName, '') AS pricingName, \n" +
            "     COALESCE(serviceOwnerType, '') AS serviceOwnerType, \n" +
            "     CASE \n" +
            "           WHEN  registrationDate IS NOT NULL THEN ' ' || CAST(registrationDate AS TEXT) \n" +
            "           ELSE '' \n" +
            "     END AS registrationDate, \n" +
            "     CASE \n" +
            "            WHEN  startAt IS NOT NULL THEN ' ' || CAST(startAt AS TEXT) \n" +
            "            ELSE '' \n" +
            "     END AS startAt, \n" +
            "     CASE \n" +
            "           WHEN  endCurrentCycle IS NOT NULL THEN ' ' || to_char(endCurrentCycle, 'yyyy-MM-dd') \n" +
            "           ELSE '' \n" +
            "      END AS endCurrentCycle, \n" +
            "     CASE \n" +
            "           WHEN  endCurrentCycleContract IS NOT NULL THEN ' ' || to_char(endCurrentCycleContract, 'yyyy-MM-dd') \n" +
            "           ELSE '' \n" +
            "      END AS endCurrentCycleContract, \n" +
            "      '' AS endCurrentCycleContract, \n" +
            "      '' AS renewStatus, \n" +
            "      CASE \n" +
            "           WHEN  paymentDate IS NOT NULL AND  billStatus = 'Đã thanh toán' THEN ' ' || CAST(paymentDate AS TEXT)\n" +
            "           ELSE '' \n" +
            "     END AS paymentDate, \n" +
            "     CASE \n" +
            "            WHEN  cancelledTime IS NOT NULL THEN ' ' || CAST(cancelledTime AS TEXT)\n" +
            "            ELSE '' \n" +
            "     END AS cancelledTime, \n" +
            "     CASE \n" +
            "          WHEN  isOneTime <> 0 THEN {h-schema}func_convert_num_of_payment_cycle_to_text(numberOfCycle) \n"+
            "          ELSE '' \n" +
            "      END AS numberOfCycle , \n" +
            "     CASE \n" +
            "           WHEN  isOneTime <> 0 THEN {h-schema}func_convert_payment_cycle_to_text(paymentCycle, cycleType, status) \n" +
            "           ELSE '' \n" +
            "     END AS paymentCycle, \n" +
            "     CASE \n" +
            "           WHEN  unitAmount IS NOT NULL THEN to_char(unitAmount, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS unitAmount, \n" +
            "     CASE \n" +
            "           WHEN  promotionAmount IS NOT NULL THEN to_char(promotionAmount, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS promotionAmount, \n" +
            "     CASE \n" +
            "           WHEN  preAmountTax IS NOT NULL THEN to_char(preAmountTax, 'FM999G999G999G999')\n" +
            "           ELSE '0' \n" +
            "     END AS preAmountTax, \n" +
            "     CASE \n" +
            "           WHEN  taxableAmount IS NOT NULL THEN to_char(taxableAmount, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS taxableAmount, \n" +
            "     CASE \n" +
            "           WHEN  amountTax IS NOT NULL THEN to_char(amountTax, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS amountTax, \n" +
            "     CASE \n" +
            "           WHEN  afterAmountTax IS NOT NULL THEN to_char(afterAmountTax, 'FM999G999G999G999') \n" +
            "           ELSE '0' \n" +
            "     END AS afterAmountTax, \n" +
            "     COALESCE(payTransactionCode, ''), \n" +
            "     COALESCE(dhsxkdCode, ''), \n" +
            "     CASE \n" +
            "           WHEN  createdExportInvoice IS NOT NULL THEN ' ' || CAST(createdExportInvoice AS TEXT) \n" +
            "           ELSE '' \n" +
            "     END AS createdExportInvoice, \n" +
            "     COALESCE(codeInvoice, '') AS codeInvoice, \n" +
            "     COALESCE(creator, '') AS creator , \n" +
            "     COALESCE(subCode, '') AS subCode, \n" +
            "     COALESCE(setupCode, '') AS setupCode, \n" +
            "     COALESCE(billCode, '') AS billCode, \n" +
            "     COALESCE(billStatus, '') AS billStatus, \n" +
            "     COALESCE(createdSource, '') AS createdSource, \n" +
            "     COALESCE(affAgencyName, '') AS affAgencyName, \n" +
            "     COALESCE(affAgencyCode, '') AS affAgencyCode, \n" +
            "     COALESCE(affiliateCode, '') AS affiliateCode, \n" +
            "     COALESCE(migrateCode, '') AS migrateCode, \n" +
            "     CASE \n" +
            "         WHEN migrateTime IS NOT NULL THEN ' ' || CAST(migrateTime AS TEXT) \n" +
            "         ELSE '' \n" +
            "     END AS migrateTime,  \n" +
            "     coalesce(userCode, '') as userCode, \n" +
            "     CASE \n" +
            "           WHEN  installedTime IS NOT NULL THEN ' ' || CAST(installedTime AS TEXT) \n" +
            "           ELSE '' \n" +
            "     END AS installedTime, \n" +
            "     COALESCE(controlRenew, '') AS controlRenew, \n" +
            "     COALESCE(amName, '') AS amName, \n" +
            "     COALESCE(amCode, '') AS amCode \n" +
            "FROM ( \n" + GET_LIST_SUBSCRIPTION_REPORT +" \n ) as result \n" +
            "ORDER BY registrationDate DESC ";


}

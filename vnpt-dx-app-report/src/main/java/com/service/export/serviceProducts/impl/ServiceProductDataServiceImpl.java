package com.service.export.serviceProducts.impl;

import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import com.constant.CharacterConstant;
import com.constant.sql.SQLExportServiceProduct;
import com.dto.export.ExportDataBaseReqDTO;
import com.dto.export.response.IServiceProductCBPlanDetailDTO;
import com.dto.export.response.IServiceProductNonAddonResDTO;
import com.dto.export.response.IServiceProductPricingDetailDTO;
import com.dto.export.response.IServiceProductWithAddonResDTO;
import com.repository.export.serviceProducts.ServiceProductRepository;
import com.service.export.serviceProducts.ServiceProductDataService;
import com.util.DateUtil;
import com.util.ExcelFileUtil;
import com.util.StringUtils;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ServiceProductDataServiceImpl implements ServiceProductDataService {

    private final ExcelFileUtil excelFileUtil;

    private final ServiceProductRepository serviceProductRepository;

    @Value("${spring.jpa.properties.hibernate.default_schema}")
    private String defaultSchema;

    @Override
    public Page<IServiceProductNonAddonResDTO> getPageServiceProductsNonAddon(ExportDataBaseReqDTO reqDTO) {
        return serviceProductRepository.getPageServiceProductsNonAddon(reqDTO.getLstProviderId(), reqDTO.getLstCategoryId(),
            reqDTO.getLstProductId(), reqDTO.getTimeTypeInt(), reqDTO.getStartTime(), reqDTO.getEndTime(), reqDTO.getLstCustomerType().toString(),
            reqDTO.getServiceType(), PageRequest.of(reqDTO.getPage(), reqDTO.getSize()));
    }

    @Override
    public InputStreamResource exportServiceProductsNonAddon(ExportDataBaseReqDTO reqDTO) throws SQLException, IOException {
        String headerQuery = "select 'STT', 'Tên dịch vụ', 'Mã dịch vụ', 'Trạng thái hiển thị', 'ON/OS', 'Danh mục', 'Nhà cung cấp', 'Số gói dịch vụ', 'Tổng số addon', \n" +
            "'Tổng số addon bắt buộc', 'Tổng số addon lựa chọn', 'Tổng số thuê bao đang sử dụng', 'Số khách hàng đang sử dụng', 'Số lượt đăng ký dùng thử', \n" +
            "'Số lượt đăng ký chính thức', 'Số lượt đang chờ sử dụng', 'Số lượt ra hạn', 'Số lượt hủy', 'Số lượt kích hoạt lại', 'Số lượt đổi gói', 'Ngày tạo', \n" +
            "'Ngày cập nhật', 'Đối tượng khách hàng'";

        SimpleDateFormat dateFormatter = new SimpleDateFormat(DateUtil.FORMAT_YYYY_MM_DD);

        String contentQuery = SQLExportServiceProduct.EXPORT_LIST_SERVICE_PRODUCT_NON_ADDON
            .replace("{h-schema}", defaultSchema + CharacterConstant.DOT)
            .replace(":providerIds", StringUtils.convertLstNumberToString(reqDTO.getLstProviderId()))
            .replace(":categoryIds", StringUtils.convertLstNumberToString(reqDTO.getLstCategoryId()))
            .replace(":serviceIds", StringUtils.convertLstNumberToString(reqDTO.getLstProductId()))
            .replace(":timeType", String.valueOf(reqDTO.getTimeTypeInt()))
            .replace(":startTime", StringUtils.formatSqlText(dateFormatter.format(reqDTO.getStartTime())))
            .replace(":endTime", StringUtils.formatSqlText(dateFormatter.format(reqDTO.getEndTime())))
            .replace(":customerTypes", StringUtils.formatSqlText(reqDTO.getLstCustomerType().toString()))
            .replace(":serviceTypes", StringUtils.convertLstNumberToString(reqDTO.getServiceType()));
        return excelFileUtil.createCSVFile(headerQuery, contentQuery);
    }

    @Override
    public Page<IServiceProductWithAddonResDTO> getPageServiceProductsWithAddon(ExportDataBaseReqDTO reqDTO) {
        return serviceProductRepository.getPageServiceProductsWithAddon(reqDTO.getLstProviderId(), reqDTO.getLstCategoryId(),
            reqDTO.getLstProductId(), reqDTO.getTimeTypeInt(), reqDTO.getStartTime(), reqDTO.getEndTime(), reqDTO.getLstCustomerType().toString(),
            reqDTO.getServiceType(), PageRequest.of(reqDTO.getPage(), reqDTO.getSize()));
    }

    @Override
    public InputStreamResource exportServiceProductsWithAddon(ExportDataBaseReqDTO reqDTO) throws SQLException, IOException {
        String headerQuery = "select 'STT', 'Tên dịch vụ', 'Tên dịch vụ bổ sung', 'Mã dịch vụ bổ sung', 'Nhà cung cấp', 'Danh mục', 'ON/OS', \n" +
            "'Trạng thái duyệt', 'Trạng thái hiển thị', 'Số lượng gói dịch vụ', 'Số khách hàng đã sử dụng', 'Số lượt đăng ký dùng chính thức', 'Số lượt đang chờ sử dụng', \n" +
            "'Số lượng gia hạn', 'Số lượt hủy', 'Số lượt kích hoạt lại', 'Số lượt đổi gói', 'Ngày tạo', 'Ngày cập nhật', 'Đối tượng khách hàng'";

        SimpleDateFormat dateFormatter = new SimpleDateFormat(DateUtil.FORMAT_YYYY_MM_DD);

        String contentQuery = SQLExportServiceProduct.EXPORT_LIST_SERVICE_PRODUCT_WITH_ADDON
            .replace("{h-schema}", defaultSchema + CharacterConstant.DOT)
            .replace(":providerIds", StringUtils.convertLstNumberToString(reqDTO.getLstProviderId()))
            .replace(":categoryIds", StringUtils.convertLstNumberToString(reqDTO.getLstCategoryId()))
            .replace(":serviceIds", StringUtils.convertLstNumberToString(reqDTO.getLstProductId()))
            .replace(":timeType", String.valueOf(reqDTO.getTimeTypeInt()))
            .replace(":startTime", StringUtils.formatSqlText(dateFormatter.format(reqDTO.getStartTime())))
            .replace(":endTime", StringUtils.formatSqlText(dateFormatter.format(reqDTO.getEndTime())))
            .replace(":customerTypes", StringUtils.formatSqlText(reqDTO.getLstCustomerType().toString()))
            .replace(":serviceTypes", StringUtils.convertLstNumberToString(reqDTO.getServiceType()));
        return excelFileUtil.createCSVFile(headerQuery, contentQuery);
    }

    @Override
    public Page<IServiceProductCBPlanDetailDTO> getPageServiceProductsComboDetail(ExportDataBaseReqDTO reqDTO) {
        return serviceProductRepository.getPageServiceProductsComboDetail(reqDTO.getLstProviderId(), reqDTO.getLstCategoryId(),
            reqDTO.getLstProductId(), reqDTO.getTimeTypeInt(), reqDTO.getStartTime(), reqDTO.getEndTime(), reqDTO.getLstCustomerType().toString(),
            reqDTO.getServiceType(), PageRequest.of(reqDTO.getPage(), reqDTO.getSize()));
    }

    @Override
    public InputStreamResource exportServiceProductsComboDetail(ExportDataBaseReqDTO reqDTO) throws SQLException, IOException {
        String headerQuery = "select 'STT', 'Tên sản phẩm', 'Tên gói cước', 'Mã gói cước', 'Trạng thái hiển thị', 'Nhà cung cấp', 'Đơn giá', 'Chu kỳ thanh toán', \n" +
            "'Số chu kỳ thanh toán', 'Mã dịch vụ', 'Tên dịch vụ - Gói dịch vụ', 'Số lượng miễn phí', 'Số lượng', 'Giá <Đơn vị tính>', 'Ngày tạo', 'Ngày cập nhật', \n" +
            "'Đối tượng khách hàng'";

        SimpleDateFormat dateFormatter = new SimpleDateFormat(DateUtil.FORMAT_YYYY_MM_DD);

        String contentQuery = SQLExportServiceProduct.EXPORT_PAGE_SERVICE_PRODUCT_COMBO_DETAIL
            .replace("{h-schema}", defaultSchema + CharacterConstant.DOT)
            .replace(":providerIds", StringUtils.convertLstNumberToString(reqDTO.getLstProviderId()))
            .replace(":categoryIds", StringUtils.convertLstNumberToString(reqDTO.getLstCategoryId()))
            .replace(":serviceIds", StringUtils.convertLstNumberToString(reqDTO.getLstProductId()))
            .replace(":timeType", String.valueOf(reqDTO.getTimeTypeInt()))
            .replace(":startTime", StringUtils.formatSqlText(dateFormatter.format(reqDTO.getStartTime())))
            .replace(":endTime", StringUtils.formatSqlText(dateFormatter.format(reqDTO.getEndTime())))
            .replace(":customerTypes", StringUtils.formatSqlText(reqDTO.getLstCustomerType().toString()))
            .replace(":serviceTypes", StringUtils.convertLstNumberToString(reqDTO.getServiceType()));

        return excelFileUtil.createCSVFile(headerQuery, contentQuery);
    }

    @Override
    public Page<IServiceProductPricingDetailDTO> getPageServiceProductDetail(ExportDataBaseReqDTO reqDTO) {
        return serviceProductRepository.getPageServiceProductDetail(reqDTO.getLstProviderId(), reqDTO.getLstCategoryId(),
            reqDTO.getLstProductId(), reqDTO.getTimeTypeInt(), reqDTO.getStartTime(), reqDTO.getEndTime(), reqDTO.getLstCustomerType().toString(),
            reqDTO.getServiceType(), PageRequest.of(reqDTO.getPage(), reqDTO.getSize()));
    }

    @Override
    public InputStreamResource exportPageServiceProductsDetail(ExportDataBaseReqDTO reqDTO) throws SQLException, IOException {
        String headerQuery = "select 'STT', 'Phân loại', 'Tên gói cước', 'Mã gói cước', 'Trạng thái hiển thị', 'Tên sản phẩm', 'Nhà cung cấp', 'Loại dịch vụ', \n" +
            "'Loại dịch vụ bổ sung', 'Kế hoạch định giá', 'Chu kỳ thanh toán', 'Số chu kỳ thanh toán', 'Đơn vị tính', 'Đơn giá<Gói cố định>', 'Số lượng miễn phí', \n" +
            "'Từ -đến, đơn giá', 'Thời gian dùng thử', 'Phí thiết lập', 'Thuế áp dụng cho gói cước', 'Thuế áp dụng phí thiết lập', 'Số lượng dịch vụ bổ sung', 'Dịch vụ bổ sung bắt buộc', \n" +
            "'Dịch vụ bổ sung tùy chọn', 'Số khách hàng đã sử dụng', 'Số lượt đăng ký dùng thử', 'Số lượt đăng ký dùng chính thức', 'Số lượt đang chờ sử dụng', \n" +
            "'Số lượt gia hạn', 'Số lượt hủy', 'Số lượt kích hoạt lại', 'Số lượt đổi gói', 'Ngày tạo', 'Ngày cập nhật', 'Đối tượng khách hàng'";

        SimpleDateFormat dateFormatter = new SimpleDateFormat(DateUtil.FORMAT_YYYY_MM_DD);

        String contentQuery = SQLExportServiceProduct.EXPORT_LIST_SERVICE_PRODUCT_PRICING_DETAIL
            .replace("{h-schema}", defaultSchema + CharacterConstant.DOT)
            .replace(":providerIds", StringUtils.convertLstNumberToString(reqDTO.getLstProviderId()))
            .replace(":categoryIds", StringUtils.convertLstNumberToString(reqDTO.getLstCategoryId()))
            .replace(":serviceIds", StringUtils.convertLstNumberToString(reqDTO.getLstProductId()))
            .replace(":timeType", String.valueOf(reqDTO.getTimeTypeInt()))
            .replace(":startTime", StringUtils.formatSqlText(dateFormatter.format(reqDTO.getStartTime())))
            .replace(":endTime", StringUtils.formatSqlText(dateFormatter.format(reqDTO.getEndTime())))
            .replace(":customerTypes", StringUtils.formatSqlText(reqDTO.getLstCustomerType().toString()))
            .replace(":serviceTypes", StringUtils.convertLstNumberToString(reqDTO.getServiceType()));

        return excelFileUtil.createCSVFile(headerQuery, contentQuery);
    }
}

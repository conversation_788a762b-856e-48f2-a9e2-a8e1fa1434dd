package com.onedx.services.notifications.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> HaiTD
 * @version : 1.0 14/1/2021
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BaseException extends RuntimeException {

    /**
     *
     */
    private static final long serialVersionUID = 5151078656511954845L;

    private final String entityName;

    private final String field;

    private final String title;

    private final String errorCode;

    public BaseException(String title, String entityName, String field, String errorCode) {
        super();
        this.entityName = entityName;
        this.field = field;
        this.title = title;
        this.errorCode = errorCode;
    }
}

drop TABLE if exists vnpt_dev.wallet_bss;
CREATE TABLE "vnpt_dev"."wallet_bss" (
                                         "id" bigserial NOT NULL,
                                         "purchased_traffic" int8,
                                         "sub_name" varchar(255),
                                         "total_traffic_shared" int8,
                                         "pay_code" varchar(255),
                                         "traffic_type" varchar(255),
                                         "end_date" timestamp(6),
                                         "customer_name" varchar(255),
                                         "total_remaining_traffic" int8,
                                         "tax" varchar(255),
                                         "start_date" timestamp(6),
                                         "time" timestamp(6),
                                         "sub_code" varchar(255),
                                         "phone_active" varchar(255),
                                         "status" int4,
                                         "accuracy_date" timestamp(6),
                                         "user_id" int8,
                                         "package_code" varchar(255),
                                         "package_name" varchar(255),
                                         PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."purchased_traffic" IS 'lưu lượng đã mua';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."sub_name" IS 'Tên thuê bao';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."total_traffic_shared" IS 'Tổng lưu lượng đã chia sẻ';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."pay_code" IS 'Mã thanh toán';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."traffic_type" IS 'Loại lưu lượng';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."end_date" IS 'Ngày kết thúc';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."customer_name" IS 'Tên khách hàng';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."total_remaining_traffic" IS 'Tổng lưu lượng còn lại';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."tax" IS 'Mã số thuế';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."start_date" IS 'Ngày bắt đầu';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."time" IS 'Thời gian';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."sub_code" IS 'Mã thuê bao';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."phone_active" IS 'Số điện thoại kích hoạt';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."status" IS '0: chưa xác thực, status = 1: đã xác thực';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."accuracy_date" IS 'Ngày xác thực';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."user_id" IS 'Id của doanh nghiệp';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."package_code" IS 'Mã gói cước';

COMMENT ON COLUMN "vnpt_dev"."wallet_bss"."package_name" IS 'Tên gói cước';